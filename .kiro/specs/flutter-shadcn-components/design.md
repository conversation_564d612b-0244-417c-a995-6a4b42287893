# Flutter Shadcn Component Library - Design Document

## Overview

This document outlines the design for a Flutter component library that brings shadcn/ui's design philosophy to Flutter while maintaining full compatibility with Material Design themes. The library provides 51 themed components that dynamically derive styling from <PERSON>lut<PERSON>'s theme system using ThemeExtension patterns and Theme.of(context) access.

## Architecture

### Core Architecture Principles

1. **Theme-First Design**: All styling properties are derived from theme context, never hardcoded
2. **Material Compatibility**: Full integration with Flutter's Material Design 3 theming system
3. **Copy-Paste Philosophy**: Components are customizable and extendable, following shadcn's approach
4. **Performance Optimized**: Leverages Flutter's InheritedWidget system for efficient theme propagation

### Library Structure

```
lib/
├── src/
│   ├── components/
│   │   ├── accordion/
│   │   ├── alert/
│   │   ├── alert_dialog/
│   │   ├── button/
│   │   ├── card/
│   │   ├── input/
│   │   └── ... (all 51 components)
│   ├── theme/
│   │   ├── extensions/
│   │   │   ├── shadcn_component_theme.dart
│   │   │   ├── shadcn_button_theme.dart
│   │   │   ├── shadcn_card_theme.dart
│   │   │   └── ... (component-specific themes)
│   │   ├── color_schemes/
│   │   │   ├── shadcn_color_scheme.dart
│   │   │   └── shadcn_tokens.dart
│   │   ├── typography/
│   │   │   └── shadcn_text_theme.dart
│   │   └── spacing/
│   │       └── shadcn_spacing.dart
│   ├── utils/
│   │   ├── theme_resolver.dart
│   │   └── component_builder.dart
│   └── constants/
│       └── shadcn_constants.dart
└── flutter_shadcn.dart
```

## Components and Interfaces

### Theme Extension System

#### Base Theme Extension
```dart
abstract class ShadcnThemeExtension<T extends ShadcnThemeExtension<T>> extends ThemeExtension<T> {
  const ShadcnThemeExtension();
  
  /// Resolves theme property with fallback chain
  Color resolveColor(BuildContext context, Color? customColor, Color materialFallback);
  
  /// Resolves text style with theme inheritance
  TextStyle resolveTextStyle(BuildContext context, TextStyle? customStyle);
  
  /// Resolves spacing using consistent token system
  EdgeInsets resolveSpacing(BuildContext context, EdgeInsets? customSpacing);
}
```

#### Component-Specific Theme Extensions
```dart
class ShadcnButtonTheme extends ShadcnThemeExtension<ShadcnButtonTheme> {
  final Color? primaryBackground;
  final Color? primaryForeground;
  final Color? secondaryBackground;
  final Color? secondaryForeground;
  final Color? destructiveBackground;
  final Color? destructiveForeground;
  final Color? outlineBorder;
  final Color? ghostHover;
  final double? height;
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;
  final TextStyle? textStyle;
  
  const ShadcnButtonTheme({...});
  
  @override
  ShadcnButtonTheme copyWith({...}) => ShadcnButtonTheme(...);
  
  @override
  ShadcnButtonTheme lerp(ShadcnButtonTheme? other, double t) => ShadcnButtonTheme(...);
  
  /// Default theme based on shadcn design tokens
  static ShadcnButtonTheme defaultTheme(ColorScheme colorScheme) => ShadcnButtonTheme(
    height: 40.0, // shadcn default
    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    borderRadius: BorderRadius.circular(6),
    primaryBackground: colorScheme.primary,
    primaryForeground: colorScheme.onPrimary,
    // ... other defaults
  );
}
```

### Component Interface Pattern

#### Base Component Interface
```dart
abstract class ShadcnComponent extends StatelessWidget {
  const ShadcnComponent({Key? key}) : super(key: key);
  
  /// Resolves the component's theme extension
  T resolveTheme<T extends ThemeExtension<T>>(BuildContext context);
  
  /// Builds component with resolved theme
  Widget buildWithTheme(BuildContext context, ThemeData theme);
  
  @override
  Widget build(BuildContext context) {
    return buildWithTheme(context, Theme.of(context));
  }
}
```

#### Example Component Implementation
```dart
class ShadcnButton extends ShadcnComponent {
  final String text;
  final VoidCallback? onPressed;
  final ShadcnButtonVariant variant;
  final ShadcnButtonSize size;
  final Widget? icon;
  final bool disabled;
  
  const ShadcnButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.variant = ShadcnButtonVariant.primary,
    this.size = ShadcnButtonSize.medium,
    this.icon,
    this.disabled = false,
  }) : super(key: key);
  
  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    final buttonTheme = theme.extension<ShadcnButtonTheme>() ?? 
                      ShadcnButtonTheme.defaultTheme(theme.colorScheme);
    
    final resolvedColors = _resolveVariantColors(buttonTheme, variant);
    final resolvedSize = _resolveSizeProperties(buttonTheme, size);
    
    return Material(
      color: disabled ? buttonTheme.resolveColor(context, null, theme.disabledColor) 
                     : resolvedColors.background,
      borderRadius: buttonTheme.borderRadius,
      child: InkWell(
        onTap: disabled ? null : onPressed,
        borderRadius: buttonTheme.borderRadius,
        child: Container(
          height: resolvedSize.height,
          padding: resolvedSize.padding,
          child: _buildButtonContent(context, resolvedColors, buttonTheme),
        ),
      ),
    );
  }
}
```

### Variant System Design

#### Variant Enumerations
```dart
enum ShadcnButtonVariant { primary, secondary, destructive, outline, ghost, link }
enum ShadcnButtonSize { small, medium, large }
enum ShadcnAlertVariant { default, destructive }
enum ShadcnCardVariant { default, outlined, elevated }
```

#### Variant Resolution System
```dart
class ShadcnVariantResolver {
  static ButtonColors resolveButtonVariant(
    ShadcnButtonTheme theme, 
    ShadcnButtonVariant variant,
    ColorScheme colorScheme
  ) {
    switch (variant) {
      case ShadcnButtonVariant.primary:
        return ButtonColors(
          background: theme.primaryBackground ?? colorScheme.primary,
          foreground: theme.primaryForeground ?? colorScheme.onPrimary,
        );
      case ShadcnButtonVariant.destructive:
        return ButtonColors(
          background: theme.destructiveBackground ?? colorScheme.error,
          foreground: theme.destructiveForeground ?? colorScheme.onError,
        );
      // ... other variants
    }
  }
}
```

## Data Models

### Theme Token System
```dart
class ShadcnTokens {
  // Spacing tokens (matching shadcn design system)
  static const double spacing0 = 0;
  static const double spacing1 = 4;
  static const double spacing2 = 8;
  static const double spacing3 = 12;
  static const double spacing4 = 16;
  static const double spacing6 = 24;
  static const double spacing8 = 32;
  
  // Size tokens
  static const double buttonHeightSm = 36;
  static const double buttonHeightMd = 40; // default
  static const double buttonHeightLg = 44;
  
  static const double inputHeightSm = 32;
  static const double inputHeightMd = 36; // default
  static const double inputHeightLg = 40;
  
  // Border radius tokens
  static const double radiusSm = 4;
  static const double radiusMd = 6;
  static const double radiusLg = 8;
  static const double radiusXl = 12;
  
  // Font size tokens
  static const double fontSizeSm = 12;
  static const double fontSizeMd = 14;
  static const double fontSizeLg = 16;
  static const double fontSizeXl = 18;
}
```

### Color Scheme Extension
```dart
class ShadcnColorScheme extends ThemeExtension<ShadcnColorScheme> {
  final Color border;
  final Color input;
  final Color ring;
  final Color background;
  final Color foreground;
  final Color card;
  final Color cardForeground;
  final Color popover;
  final Color popoverForeground;
  final Color muted;
  final Color mutedForeground;
  final Color accent;
  final Color accentForeground;
  final Color destructive;
  final Color destructiveForeground;
  
  const ShadcnColorScheme({...});
  
  /// Creates shadcn color scheme from Material ColorScheme
  factory ShadcnColorScheme.fromMaterial(ColorScheme colorScheme) {
    return ShadcnColorScheme(
      border: colorScheme.outline,
      input: colorScheme.surface,
      ring: colorScheme.primary,
      background: colorScheme.background,
      foreground: colorScheme.onBackground,
      card: colorScheme.surface,
      cardForeground: colorScheme.onSurface,
      popover: colorScheme.surface,
      popoverForeground: colorScheme.onSurface,
      muted: colorScheme.surfaceVariant,
      mutedForeground: colorScheme.onSurfaceVariant,
      accent: colorScheme.secondary,
      accentForeground: colorScheme.onSecondary,
      destructive: colorScheme.error,
      destructiveForeground: colorScheme.onError,
    );
  }
}
```

## Error Handling

### Theme Resolution Error Handling
```dart
class ShadcnThemeResolver {
  /// Safely resolves theme with fallback chain
  static T resolveThemeExtension<T extends ThemeExtension<T>>(
    BuildContext context,
    T Function(ColorScheme) defaultFactory,
  ) {
    try {
      final theme = Theme.of(context);
      return theme.extension<T>() ?? defaultFactory(theme.colorScheme);
    } catch (e) {
      debugPrint('Failed to resolve theme extension $T: $e');
      // Fallback to Material theme
      final materialTheme = ThemeData.from(colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue));
      return defaultFactory(materialTheme.colorScheme);
    }
  }
}
```

### Component Validation
```dart
mixin ShadcnComponentValidation {
  void validateThemeProperties(BuildContext context) {
    assert(Theme.of(context) != null, 'ShadcnComponent requires a Theme ancestor');
    
    final theme = Theme.of(context);
    assert(theme.colorScheme != null, 'ColorScheme is required for component styling');
    assert(theme.textTheme != null, 'TextTheme is required for typography');
  }
}
```

## Testing Strategy

### Theme Testing
```dart
class ShadcnTestTheme {
  /// Creates consistent test theme for component testing
  static ThemeData createTestTheme({
    Brightness brightness = Brightness.light,
    Color? seedColor,
  }) {
    return ThemeData(
      colorScheme: ColorScheme.fromSeed(
        seedColor: seedColor ?? Colors.blue,
        brightness: brightness,
      ),
      extensions: [
        ShadcnButtonTheme.defaultTheme(ColorScheme.fromSeed(seedColor: Colors.blue)),
        ShadcnCardTheme.defaultTheme(ColorScheme.fromSeed(seedColor: Colors.blue)),
        // ... other default themes
      ],
    );
  }
}
```

### Component Testing Pattern
```dart
testWidgets('ShadcnButton applies theme correctly', (WidgetTester tester) async {
  final testTheme = ShadcnTestTheme.createTestTheme();
  
  await tester.pumpWidget(
    MaterialApp(
      theme: testTheme,
      home: ShadcnButton(
        text: 'Test Button',
        onPressed: () {},
      ),
    ),
  );
  
  // Verify theme application
  final buttonFinder = find.byType(ShadcnButton);
  expect(buttonFinder, findsOneWidget);
  
  // Test theme inheritance
  final resolvedTheme = testTheme.extension<ShadcnButtonTheme>();
  expect(resolvedTheme, isNotNull);
  expect(resolvedTheme!.height, equals(40.0));
});
```

### Integration Testing
```dart
testWidgets('All components work together with consistent theming', (WidgetTester tester) async {
  await tester.pumpWidget(
    MaterialApp(
      theme: ShadcnTheme.lightTheme(),
      darkTheme: ShadcnTheme.darkTheme(),
      home: Scaffold(
        body: Column(
          children: [
            ShadcnButton(text: 'Button'),
            ShadcnCard(child: Text('Card')),
            ShadcnInput(placeholder: 'Input'),
            // ... test multiple components
          ],
        ),
      ),
    ),
  );
  
  // Verify all components render without errors
  expect(find.byType(ShadcnButton), findsOneWidget);
  expect(find.byType(ShadcnCard), findsOneWidget);
  expect(find.byType(ShadcnInput), findsOneWidget);
});
```

## Performance Considerations

### Theme Optimization
- Use `Theme.of(context)` efficiently by caching resolved themes when possible
- Implement proper `updateShouldNotify` in custom InheritedWidgets
- Avoid unnecessary theme extension lookups in build methods
- Use const constructors wherever possible for theme data classes

### Component Optimization
- Implement proper `shouldRepaint` methods for custom painters
- Use `RepaintBoundary` for complex components that don't need frequent repaints
- Cache computed values like resolved colors and dimensions
- Minimize widget rebuilds by using specific theme data dependencies