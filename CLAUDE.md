# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This repository contains a Flutter component library inspired by shadcn/ui that provides 51 themed UI components with full Material Design integration. The library follows a theme-first design approach where all styling is derived from <PERSON><PERSON><PERSON>'s theme system using ThemeExtension patterns.

## Development Requirements and Constraints

### Strict Development Rules
- **NO SIMPLIFICATION**: Developers must implement ALL requirements exactly as specified in the spec documents
- **NO OMISSIONS**: Every component, variant, and feature mentioned in requirements must be implemented
- **NO TODOS**: Code must be complete - no placeholder comments, unfinished functions, or TODO markers
- **NO EXTRA FEATURES**: Only implement what is explicitly defined in the spec documents

### Component Implementation Standards
- All components must derive styling from `Theme.of(context)` - never hardcode values
- Every component requires a corresponding `ShadcnXxxTheme` extension class
- Components must support all shadcn variants: primary, secondary, destructive, outline, ghost, link
- Size variants must use exact shadcn dimensions: Button 40px, Input 36px, small/medium/large variants
- All interactive states required: hover, pressed, focused, disabled
- Full Material compatibility must be maintained

### Theme System Architecture
- Use `ThemeExtension<T>` pattern for all custom theming
- Implement proper `copyWith()` and `lerp()` methods for theme transitions
- Theme resolution follows: custom theme → Material theme → hardcoded defaults
- All 51 components must be covered in the theme system

## Development Commands

Since this is a new Flutter package project, common commands will be:

```bash
# Create Flutter package
flutter create --template=package .

# Get dependencies
flutter pub get

# Run tests
flutter test

# Run specific test file
flutter test test/components/button_test.dart

# Run tests with coverage
flutter test --coverage

# Analyze code
flutter analyze

# Format code
dart format .

# Build documentation
dart doc

# Check pub score
pana .
```

## Architecture Overview

### Core Theme System
- `ShadcnThemeExtension<T>` - Abstract base for all component themes
- `ShadcnColorScheme` - Extends Material colors with shadcn design tokens
- `ShadcnTokens` - Contains spacing, sizing, and styling constants
- `ShadcnThemeResolver` - Safe theme resolution with fallback chain

### Component Structure
Each of the 51 components follows this pattern:
- Component widget extending `ShadcnComponent` base class
- Dedicated theme extension (e.g., `ShadcnButtonTheme`)
- Theme-aware styling resolution
- Material Design integration
- Comprehensive unit tests

### Package Organization
```
lib/src/
├── components/           # All 51 component implementations
├── theme/               
│   ├── extensions/      # Component-specific theme extensions
│   ├── color_schemes/   # Color system and tokens
│   ├── typography/      # Text styling
│   └── spacing/         # Layout and sizing
├── utils/               # Theme resolution utilities
└── constants/           # Design tokens and constants
```

## Spec Document References

The project follows a spec-driven development approach with three critical documents:

### Requirements Document (`.kiro/specs/flutter-shadcn-components/requirements.md`)
Contains 7 major requirements covering:
- All 51 shadcn components implementation
- Material theme integration and inheritance
- Theme.of(context) pattern usage
- Component customization capabilities
- shadcn-standard default sizing
- Comprehensive variants and states
- Documentation and examples

### Design Document (`.kiro/specs/flutter-shadcn-components/design.md`)
Defines the technical architecture including:
- ThemeExtension system design
- Component interface patterns
- Theme token system
- Error handling strategies
- Performance optimization approaches

### Tasks Document (`.kiro/specs/flutter-shadcn-components/tasks.md`)
Contains 77 specific implementation tasks organized in 11 phases:
1. Project structure and theme foundation
2. Core theme system implementation
3-5. Component implementation (foundational, layout, form components)
6-8. Advanced components (feedback, layout, utility components)
9-11. Specialized components and finalization

## Testing Requirements

- Unit tests required for every component and theme extension
- Integration tests for theme switching and component interactions
- Performance tests for theme resolution
- All tests must validate theme inheritance and Material compatibility
- Test coverage for all variants, sizes, and interactive states

## Critical Implementation Notes

- Components must work in both light and dark themes automatically
- All animations and transitions must be theme-consistent
- Accessibility features must be maintained from Material components
- Performance optimization through efficient InheritedWidget usage
- No component should have hardcoded styling values