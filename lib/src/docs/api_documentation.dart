// This file serves as the main entry point for API documentation
// All dartdoc comments are written directly in the source files

/// # Flutter Shadcn Components Library
/// 
/// A comprehensive Flutter component library inspired by shadcn/ui that provides
/// 51 themed UI components with full Material Design integration. The library
/// follows a theme-first design approach where all styling is derived from 
/// Flutter's theme system using ThemeExtension patterns.
/// 
/// ## Features
/// 
/// - **51 Components**: Complete set of shadcn-inspired components
/// - **Theme Integration**: Full Material Design theme compatibility
/// - **Dark Mode**: Automatic light/dark theme support
/// - **Customization**: Easy component and theme customization
/// - **Accessibility**: Built-in accessibility support
/// - **Performance**: Optimized theme resolution and rendering
/// 
/// ## Quick Start
/// 
/// ```dart
/// import 'package:shadcn/shadcn.dart';
/// 
/// // Basic usage with default theme
/// ShadcnButton(
///   onPressed: () => print('But<PERSON> pressed'),
///   child: Text('Click me'),
/// )
/// 
/// // With custom theme
/// MaterialApp(
///   theme: ThemeData.light().copyWith(
///     extensions: [
///       ShadcnButtonTheme.defaultTheme(
///         ColorScheme.fromSeed(seedColor: Colors.blue),
///       ),
///     ],
///   ),
///   home: MyApp(),
/// )
/// ```
/// 
/// ## Architecture
/// 
/// The library is built around several key concepts:
/// 
/// ### Theme Extensions
/// 
/// Each component has a corresponding theme extension that defines its styling:
/// 
/// ```dart
/// // Button theme extension
/// final buttonTheme = ShadcnButtonTheme(
///   primaryBackground: Colors.blue,
///   primaryForeground: Colors.white,
///   height: 40.0,
///   padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
/// );
/// ```
/// 
/// ### Component Base Classes
/// 
/// All components extend [ShadcnComponent] for consistent behavior:
/// 
/// ```dart
/// class ShadcnButton extends ShadcnComponent {
///   @override
///   Widget buildWithTheme(BuildContext context, ThemeData theme) {
///     final buttonTheme = resolveTheme<ShadcnButtonTheme>(
///       context, 
///       ShadcnButtonTheme.defaultTheme,
///     );
///     return // ... build button widget
///   }
/// }
/// ```
/// 
/// ### Theme Resolution
/// 
/// The [ShadcnThemeResolver] provides robust theme resolution with fallbacks:
/// 
/// 1. Custom theme extension (if provided)
/// 2. Material theme values 
/// 3. Hardcoded defaults
/// 
/// ## Component Categories
/// 
/// ### Form Components
/// - [ShadcnButton] - Versatile button with multiple variants
/// - [ShadcnInput] - Text input with Material integration
/// - [ShadcnCheckbox] - Checkbox with custom styling
/// - [ShadcnSwitch] - Toggle switch component
/// - [ShadcnSelect] - Dropdown selection component
/// - [ShadcnRadioGroup] - Radio button group
/// 
/// ### Layout Components  
/// - [ShadcnCard] - Container with elevation and borders
/// - [ShadcnSeparator] - Horizontal/vertical divider
/// - [ShadcnTabs] - Tab navigation component
/// - [ShadcnAccordion] - Collapsible content sections
/// 
/// ### Feedback Components
/// - [ShadcnAlert] - Alert messages and notifications
/// - [ShadcnToast] - Temporary notification messages
/// - [ShadcnDialog] - Modal dialog windows
/// - [ShadcnProgress] - Progress indicators
/// - [ShadcnSkeleton] - Loading state placeholders
/// 
/// ### Navigation Components
/// - [ShadcnBreadcrumb] - Navigation breadcrumbs  
/// - [ShadcnNavigationMenu] - Navigation menu
/// - [ShadcnContextMenu] - Right-click context menus
/// - [ShadcnCommand] - Command palette interface
/// 
/// ### Overlay Components
/// - [ShadcnPopover] - Floating content containers
/// - [ShadcnTooltip] - Hover information tooltips
/// - [ShadcnHoverCard] - Hover-activated cards
/// - [ShadcnSheet] - Bottom sheet modals
/// 
/// ### Data Display Components  
/// - [ShadcnTable] - Data tables with sorting
/// - [ShadcnDataTable] - Advanced data grid
/// - [ShadcnBadge] - Labels and status indicators
/// - [ShadcnAvatar] - User profile images
/// 
/// ### Utility Components
/// - [ShadcnLabel] - Form field labels
/// - [ShadcnAspectRatio] - Aspect ratio containers
/// - [ShadcnScrollArea] - Custom scrollable areas
/// - [ShadcnResizable] - Resizable panels
/// - [ShadcnCalendar] - Date picker calendar
/// 
/// ## Theme Customization
/// 
/// ### Creating Custom Themes
/// 
/// ```dart
/// // Create custom color scheme
/// final customColorScheme = ColorScheme.fromSeed(
///   seedColor: Colors.purple,
///   brightness: Brightness.light,
/// );
/// 
/// // Create custom theme extensions
/// final customButtonTheme = ShadcnButtonTheme(
///   primaryBackground: Colors.purple,
///   primaryForeground: Colors.white,
///   secondaryBackground: Colors.purple.shade100,
///   height: 48.0, // Larger buttons
/// );
/// 
/// // Apply to MaterialApp
/// MaterialApp(
///   theme: ThemeData(
///     colorScheme: customColorScheme,
///     extensions: [customButtonTheme],
///   ),
///   home: MyApp(),
/// )
/// ```
/// 
/// ### Theme Switching
/// 
/// ```dart
/// class ThemedApp extends StatefulWidget {
///   @override
///   State<ThemedApp> createState() => _ThemedAppState();
/// }
/// 
/// class _ThemedAppState extends State<ThemedApp> {
///   ThemeMode themeMode = ThemeMode.light;
/// 
///   @override
///   Widget build(BuildContext context) {
///     return MaterialApp(
///       theme: createLightTheme(),
///       darkTheme: createDarkTheme(),  
///       themeMode: themeMode,
///       home: Scaffold(
///         appBar: AppBar(
///           actions: [
///             ShadcnButton(
///               onPressed: () {
///                 setState(() {
///                   themeMode = themeMode == ThemeMode.light 
///                     ? ThemeMode.dark : ThemeMode.light;
///                 });
///               },
///               child: Text('Toggle Theme'),
///             ),
///           ],
///         ),
///         body: MyContent(),
///       ),
///     );
///   }
/// }
/// ```
/// 
/// ## Best Practices
/// 
/// ### Theme Integration
/// 
/// Always use theme-aware components:
/// 
/// ```dart
/// // Good - uses theme colors
/// ShadcnButton(
///   variant: ShadcnButtonVariant.primary,
///   onPressed: onPressed,
///   child: Text('Submit'),
/// )
/// 
/// // Avoid - hardcoded colors
/// Container(
///   color: Colors.blue, // Don't do this
///   child: TextButton(onPressed: onPressed, child: Text('Submit')),
/// )
/// ```
/// 
/// ### Component Composition
/// 
/// Compose components for complex layouts:
/// 
/// ```dart
/// ShadcnCard(
///   child: Column(
///     children: [
///       ShadcnAlert(
///         title: 'Important Notice',
///         description: 'Please review the information below.',
///       ),
///       ShadcnSeparator(),
///       ShadcnButton(
///         onPressed: onAcknowledge,
///         child: Text('I Understand'),
///       ),
///     ],
///   ),
/// )
/// ```
/// 
/// ### Accessibility
/// 
/// Always provide semantic information:
/// 
/// ```dart
/// ShadcnButton(
///   onPressed: onDelete,
///   semanticLabel: 'Delete item',
///   child: Icon(Icons.delete),
/// )
/// 
/// ShadcnInput(
///   placeholder: 'Enter your name',
///   semanticLabel: 'Full name input field',
/// )
/// ```
/// 
/// ## Migration Guide
/// 
/// See [migration_guide.md] for detailed migration instructions from 
/// Material components to shadcn components.
/// 
/// ## Performance
/// 
/// The library is optimized for performance:
/// 
/// - Efficient theme resolution using [InheritedWidget]
/// - Minimal rebuilds during theme changes  
/// - Optimized component rendering
/// - Memory-efficient theme caching
/// 
/// ## Testing
/// 
/// Use the provided test helpers for component testing:
/// 
/// ```dart
/// testWidgets('Button responds to tap', (tester) async {
///   bool tapped = false;
///   
///   await tester.pumpWidget(
///     TestApp(
///       child: ShadcnButton(
///         onPressed: () => tapped = true,
///         child: Text('Test'),
///       ),
///     ),
///   );
///   
///   await tester.tap(find.byType(ShadcnButton));
///   expect(tapped, isTrue);
/// });
/// ```
/// 
/// ## Contributing
/// 
/// Contributions are welcome! Please see the contributing guidelines
/// for information on how to contribute to the library.
/// 
/// ## License
/// 
/// This library is licensed under the MIT License.
library flutter_shadcn_docs;