import 'package:flutter/material.dart';
import 'shadcn_theme.dart';
import '../utils/theme_resolver.dart';

/// Utility class for theme switching functionality and helper methods.
/// 
/// This class provides convenient methods for theme switching, theme comparison,
/// and theme mode management. It's designed to work seamlessly with the ShadcnTheme
/// system while maintaining Material Design compatibility.
class ShadcnThemeSwitcher {
  ShadcnThemeSwitcher._();
  
  /// Creates an animated theme switcher that can smoothly transition between themes.
  /// 
  /// This method creates a ThemeData with customized transition durations and curves
  /// that work well with shadcn components. The animation affects color transitions,
  /// elevation changes, and other theme-dependent properties.
  /// 
  /// [lightTheme] - The light theme to use
  /// [darkTheme] - The dark theme to use
  /// [themeMode] - The current theme mode
  /// [animationDuration] - Duration for theme transition animation
  /// [animationCurve] - Curve for theme transition animation
  /// 
  /// Returns the appropriate [ThemeData] for the current mode with animation support.
  static ThemeData createAnimatedTheme({
    required ThemeData lightTheme,
    required ThemeData darkTheme,
    required ThemeMode themeMode,
    Duration animationDuration = const Duration(milliseconds: 300),
    Curve animationCurve = Curves.easeInOut,
  }) {
    // Get base theme based on mode
    final ThemeData baseTheme;
    switch (themeMode) {
      case ThemeMode.light:
        baseTheme = lightTheme;
        break;
      case ThemeMode.dark:
        baseTheme = darkTheme;
        break;
      case ThemeMode.system:
        // This will be handled by MaterialApp's themeMode
        baseTheme = lightTheme;
        break;
    }
    
    // Enhance theme with animation properties
    return baseTheme.copyWith(
      // Set animation duration for theme transitions
      materialTapTargetSize: baseTheme.materialTapTargetSize,
      visualDensity: baseTheme.visualDensity,
      
      // Ensure smooth transitions for elevated surfaces
      cardTheme: baseTheme.cardTheme,
      
      // Enhance dialog theme for smooth transitions
      dialogTheme: baseTheme.dialogTheme.copyWith(
        // Dialogs should have smooth theme transitions
        elevation: baseTheme.dialogTheme.elevation,
      ),
      
      // Enhance bottom sheet theme
      bottomSheetTheme: baseTheme.bottomSheetTheme,
    );
  }
  
  /// Determines if the current theme is dark mode.
  /// 
  /// This helper method checks the theme's brightness to determine if it's
  /// currently in dark mode. Useful for conditional UI logic based on theme.
  /// 
  /// [context] - The build context to get theme from
  /// 
  /// Returns true if the current theme is dark mode, false otherwise.
  static bool isDarkMode(BuildContext context) {
    try {
      return Theme.of(context).brightness == Brightness.dark;
    } catch (e) {
      debugPrint('Failed to determine theme brightness: $e');
      return false; // Default to light mode on error
    }
  }
  
  /// Gets the opposite theme mode for the current theme.
  /// 
  /// This is useful for implementing theme toggle functionality where you
  /// need to switch to the opposite of the current theme.
  /// 
  /// [context] - The build context to get current theme from
  /// 
  /// Returns [ThemeMode.dark] if current theme is light, [ThemeMode.light] if dark.
  static ThemeMode getOppositeThemeMode(BuildContext context) {
    return isDarkMode(context) ? ThemeMode.light : ThemeMode.dark;
  }
  
  /// Creates a theme toggle button widget.
  /// 
  /// This is a convenient pre-built widget that provides theme switching
  /// functionality. It displays an appropriate icon based on current theme
  /// and calls the provided callback when tapped.
  /// 
  /// [onToggle] - Callback function called when theme toggle is requested
  /// [lightIcon] - Icon to show in light mode (defaults to dark_mode)
  /// [darkIcon] - Icon to show in dark mode (defaults to light_mode)
  /// [tooltip] - Tooltip text for the button
  /// 
  /// Returns an [IconButton] that can be used in app bars or other UI locations.
  static Widget createThemeToggleButton({
    required VoidCallback onToggle,
    IconData lightIcon = Icons.dark_mode,
    IconData darkIcon = Icons.light_mode,
    String? tooltip,
  }) {
    return Builder(
      builder: (context) {
        final bool isDark = isDarkMode(context);
        
        return IconButton(
          onPressed: onToggle,
          icon: Icon(isDark ? darkIcon : lightIcon),
          tooltip: tooltip ?? (isDark ? 'Switch to light theme' : 'Switch to dark theme'),
        );
      },
    );
  }
  
  /// Validates that both light and dark themes are properly configured.
  /// 
  /// This method checks that both themes have the necessary shadcn extensions
  /// and that they're compatible with each other for smooth switching.
  /// 
  /// [lightTheme] - The light theme to validate
  /// [darkTheme] - The dark theme to validate
  /// [throwOnError] - Whether to throw exceptions on validation failures
  /// 
  /// Returns true if both themes are valid, false otherwise.
  static bool validateThemes({
    required ThemeData lightTheme,
    required ThemeData darkTheme,
    bool throwOnError = false,
  }) {
    try {
      // Validate that both themes have the correct brightness
      if (lightTheme.brightness != Brightness.light) {
        final error = 'Light theme must have Brightness.light';
        if (throwOnError) throw ThemeException(error);
        debugPrint('Theme validation warning: $error');
        return false;
      }
      
      if (darkTheme.brightness != Brightness.dark) {
        final error = 'Dark theme must have Brightness.dark';
        if (throwOnError) throw ThemeException(error);
        debugPrint('Theme validation warning: $error');
        return false;
      }
      
      // Validate that both themes have shadcn extensions
      final lightExtensions = lightTheme.extensions;
      final darkExtensions = darkTheme.extensions;
      
      if (lightExtensions.isEmpty) {
        final error = 'Light theme has no theme extensions';
        if (throwOnError) throw ThemeException(error);
        debugPrint('Theme validation warning: $error');
        return false;
      }
      
      if (darkExtensions.isEmpty) {
        final error = 'Dark theme has no theme extensions';
        if (throwOnError) throw ThemeException(error);
        debugPrint('Theme validation warning: $error');
        return false;
      }
      
      // Check that both themes have compatible extension types
      final lightExtensionTypes = lightExtensions.keys.toSet();
      final darkExtensionTypes = darkExtensions.keys.toSet();
      
      if (lightExtensionTypes != darkExtensionTypes) {
        final missingInLight = darkExtensionTypes.difference(lightExtensionTypes);
        final missingInDark = lightExtensionTypes.difference(darkExtensionTypes);
        
        final error = 'Theme extensions mismatch. '
            'Missing in light: $missingInLight, '
            'Missing in dark: $missingInDark';
            
        if (throwOnError) throw ThemeException(error);
        debugPrint('Theme validation warning: $error');
        return false;
      }
      
      return true;
    } catch (e) {
      if (throwOnError) rethrow;
      debugPrint('Theme validation error: $e');
      return false;
    }
  }
  
  /// Compares two themes and returns a list of differences.
  /// 
  /// This method performs a detailed comparison between two themes and
  /// returns a list of property differences. Useful for debugging theme
  /// issues or understanding theme variations.
  /// 
  /// [theme1] - First theme to compare
  /// [theme2] - Second theme to compare
  /// 
  /// Returns a list of string descriptions of the differences found.
  static List<String> compareThemes(ThemeData theme1, ThemeData theme2) {
    final List<String> differences = [];
    
    try {
      // Compare basic properties
      if (theme1.brightness != theme2.brightness) {
        differences.add('Brightness: ${theme1.brightness} vs ${theme2.brightness}');
      }
      
      if (theme1.textTheme.bodyMedium?.fontFamily != theme2.textTheme.bodyMedium?.fontFamily) {
        differences.add('Font family: ${theme1.textTheme.bodyMedium?.fontFamily} vs ${theme2.textTheme.bodyMedium?.fontFamily}');
      }
      
      // Compare color schemes
      final color1 = theme1.colorScheme;
      final color2 = theme2.colorScheme;
      
      if (color1.primary != color2.primary) {
        differences.add('Primary color: ${color1.primary} vs ${color2.primary}');
      }
      
      if (color1.secondary != color2.secondary) {
        differences.add('Secondary color: ${color1.secondary} vs ${color2.secondary}');
      }
      
      if (color1.surface != color2.surface) {
        differences.add('Surface color: ${color1.surface} vs ${color2.surface}');
      }
      
      // Compare extensions
      final ext1 = theme1.extensions;
      final ext2 = theme2.extensions;
      
      final types1 = ext1.keys.toSet();
      final types2 = ext2.keys.toSet();
      
      final onlyInFirst = types1.difference(types2);
      final onlyInSecond = types2.difference(types1);
      
      if (onlyInFirst.isNotEmpty) {
        differences.add('Extensions only in first theme: $onlyInFirst');
      }
      
      if (onlyInSecond.isNotEmpty) {
        differences.add('Extensions only in second theme: $onlyInSecond');
      }
      
    } catch (e) {
      differences.add('Error comparing themes: $e');
    }
    
    return differences;
  }
  
  /// Creates a theme with interpolated values between light and dark themes.
  /// 
  /// This method creates a theme that represents a blend between light and dark
  /// themes based on the provided factor. Useful for creating smooth theme
  /// transitions or intermediate theme states.
  /// 
  /// [lightTheme] - The light theme to interpolate from
  /// [darkTheme] - The dark theme to interpolate to  
  /// [factor] - Interpolation factor (0.0 = full light, 1.0 = full dark)
  /// 
  /// Returns a [ThemeData] with interpolated properties.
  static ThemeData interpolateThemes({
    required ThemeData lightTheme,
    required ThemeData darkTheme,
    required double factor,
  }) {
    // Clamp factor to valid range
    final t = factor.clamp(0.0, 1.0);
    
    try {
      // Interpolate color schemes
      final ColorScheme interpolatedColorScheme = ColorScheme.lerp(
        lightTheme.colorScheme,
        darkTheme.colorScheme,
        t,
      )!;
      
      // Create base theme with interpolated color scheme
      final ThemeData baseTheme = ThemeData(
        colorScheme: interpolatedColorScheme,
        brightness: t < 0.5 ? Brightness.light : Brightness.dark,
        textTheme: TextTheme.lerp(lightTheme.textTheme, darkTheme.textTheme, t),
        useMaterial3: true,
      );
      
      // Interpolate extensions
      final List<ThemeExtension<dynamic>> interpolatedExtensions = [];
      
      for (final type in lightTheme.extensions.keys) {
        final lightExt = lightTheme.extensions[type];
        final darkExt = darkTheme.extensions[type];
        
        if (lightExt != null && darkExt != null) {
          final interpolated = lightExt.lerp(darkExt, t);
          if (interpolated != null) {
            interpolatedExtensions.add(interpolated);
          }
        } else if (lightExt != null) {
          interpolatedExtensions.add(lightExt);
        } else if (darkExt != null) {
          interpolatedExtensions.add(darkExt);
        }
      }
      
      return baseTheme.copyWith(extensions: interpolatedExtensions);
    } catch (e) {
      debugPrint('Error interpolating themes: $e');
      // Fallback to appropriate theme based on factor
      return t < 0.5 ? lightTheme : darkTheme;
    }
  }
  
  /// Gets a summary of theme properties for debugging.
  /// 
  /// This method extracts key properties from a theme and formats them
  /// into a readable summary. Useful for debugging theme configuration issues.
  /// 
  /// [theme] - The theme to summarize
  /// 
  /// Returns a formatted string with theme property information.
  static String getThemeSummary(ThemeData theme) {
    final buffer = StringBuffer();
    
    try {
      buffer.writeln('Theme Summary:');
      buffer.writeln('  Brightness: ${theme.brightness}');
      buffer.writeln('  Font Family: ${theme.textTheme.bodyMedium?.fontFamily ?? 'default'}');
      buffer.writeln('  Use Material 3: ${theme.useMaterial3}');
      buffer.writeln('  Primary Color: ${theme.colorScheme.primary}');
      buffer.writeln('  Secondary Color: ${theme.colorScheme.secondary}');
      buffer.writeln('  Surface Color: ${theme.colorScheme.surface}');
      buffer.writeln('  Extensions Count: ${theme.extensions.length}');
      
      if (theme.extensions.isNotEmpty) {
        buffer.writeln('  Extension Types:');
        for (final type in theme.extensions.keys) {
          buffer.writeln('    - ${type.toString()}');
        }
      }
    } catch (e) {
      buffer.writeln('Error generating theme summary: $e');
    }
    
    return buffer.toString();
  }
}

/// Helper widget that provides theme switching functionality to descendant widgets.
/// 
/// This widget maintains theme state and provides methods to switch between
/// light and dark themes. It's designed to be placed high in the widget tree
/// to provide theme switching capabilities throughout the app.
class ShadcnThemeProvider extends StatefulWidget {
  /// The child widget that will have access to theme switching functionality
  final Widget child;
  
  /// Initial theme mode
  final ThemeMode initialThemeMode;
  
  /// Light theme to use
  final ThemeData lightTheme;
  
  /// Dark theme to use
  final ThemeData darkTheme;
  
  /// Callback called when theme mode changes
  final ValueChanged<ThemeMode>? onThemeModeChanged;
  
  const ShadcnThemeProvider({
    super.key,
    required this.child,
    required this.lightTheme,
    required this.darkTheme,
    this.initialThemeMode = ThemeMode.system,
    this.onThemeModeChanged,
  });
  
  @override
  State<ShadcnThemeProvider> createState() => _ShadcnThemeProviderState();
  
  /// Gets the current theme provider from the widget tree
  static _ShadcnThemeProviderState? of(BuildContext context) {
    return context.findAncestorStateOfType<_ShadcnThemeProviderState>();
  }
}

class _ShadcnThemeProviderState extends State<ShadcnThemeProvider> {
  late ThemeMode _themeMode;
  
  @override
  void initState() {
    super.initState();
    _themeMode = widget.initialThemeMode;
  }
  
  /// Gets the current theme mode
  ThemeMode get themeMode => _themeMode;
  
  /// Sets the theme mode
  void setThemeMode(ThemeMode mode) {
    if (_themeMode != mode) {
      setState(() {
        _themeMode = mode;
      });
      widget.onThemeModeChanged?.call(mode);
    }
  }
  
  /// Toggles between light and dark theme
  void toggleTheme() {
    final newMode = _themeMode == ThemeMode.light 
        ? ThemeMode.dark 
        : ThemeMode.light;
    setThemeMode(newMode);
  }
  
  /// Gets the current effective theme based on mode and system settings
  ThemeData getCurrentTheme(BuildContext context) {
    switch (_themeMode) {
      case ThemeMode.light:
        return widget.lightTheme;
      case ThemeMode.dark:
        return widget.darkTheme;
      case ThemeMode.system:
        final brightness = MediaQuery.platformBrightnessOf(context);
        return brightness == Brightness.dark 
            ? widget.darkTheme 
            : widget.lightTheme;
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}