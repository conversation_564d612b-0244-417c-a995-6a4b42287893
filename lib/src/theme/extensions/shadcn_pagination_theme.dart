import 'package:flutter/material.dart';
import '../../constants/shadcn_tokens.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for the ShadcnPagination component.
class ShadcnPaginationTheme extends ShadcnThemeExtension<ShadcnPaginationTheme> {
  final Color? backgroundColor;
  final Color? foregroundColor;
  final Color? borderColor;
  final Color? selectedBackgroundColor;
  final Color? selectedForegroundColor;
  final Color? hoverBackgroundColor;
  final Color? disabledBackgroundColor;
  final Color? disabledForegroundColor;
  final double? itemSize;
  final EdgeInsets? itemPadding;
  final BorderRadius? itemBorderRadius;
  final double? itemSpacing;
  final TextStyle? textStyle;
  final double? borderWidth;

  const ShadcnPaginationTheme({
    this.backgroundColor,
    this.foregroundColor,
    this.borderColor,
    this.selectedBackgroundColor,
    this.selectedForegroundColor,
    this.hoverBackgroundColor,
    this.disabledBackgroundColor,
    this.disabledForegroundColor,
    this.itemSize,
    this.itemPadding,
    this.itemBorderRadius,
    this.itemSpacing,
    this.textStyle,
    this.borderWidth,
  });

  @override
  ShadcnPaginationTheme copyWith({
    Color? backgroundColor,
    Color? foregroundColor,
    Color? borderColor,
    Color? selectedBackgroundColor,
    Color? selectedForegroundColor,
    Color? hoverBackgroundColor,
    Color? disabledBackgroundColor,
    Color? disabledForegroundColor,
    double? itemSize,
    EdgeInsets? itemPadding,
    BorderRadius? itemBorderRadius,
    double? itemSpacing,
    TextStyle? textStyle,
    double? borderWidth,
  }) {
    return ShadcnPaginationTheme(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      foregroundColor: foregroundColor ?? this.foregroundColor,
      borderColor: borderColor ?? this.borderColor,
      selectedBackgroundColor: selectedBackgroundColor ?? this.selectedBackgroundColor,
      selectedForegroundColor: selectedForegroundColor ?? this.selectedForegroundColor,
      hoverBackgroundColor: hoverBackgroundColor ?? this.hoverBackgroundColor,
      disabledBackgroundColor: disabledBackgroundColor ?? this.disabledBackgroundColor,
      disabledForegroundColor: disabledForegroundColor ?? this.disabledForegroundColor,
      itemSize: itemSize ?? this.itemSize,
      itemPadding: itemPadding ?? this.itemPadding,
      itemBorderRadius: itemBorderRadius ?? this.itemBorderRadius,
      itemSpacing: itemSpacing ?? this.itemSpacing,
      textStyle: textStyle ?? this.textStyle,
      borderWidth: borderWidth ?? this.borderWidth,
    );
  }

  @override
  ShadcnPaginationTheme lerp(ShadcnPaginationTheme? other, double t) {
    if (other is! ShadcnPaginationTheme) return this;
    return ShadcnPaginationTheme(
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t),
      foregroundColor: Color.lerp(foregroundColor, other.foregroundColor, t),
      borderColor: Color.lerp(borderColor, other.borderColor, t),
      selectedBackgroundColor: Color.lerp(selectedBackgroundColor, other.selectedBackgroundColor, t),
      selectedForegroundColor: Color.lerp(selectedForegroundColor, other.selectedForegroundColor, t),
      hoverBackgroundColor: Color.lerp(hoverBackgroundColor, other.hoverBackgroundColor, t),
      disabledBackgroundColor: Color.lerp(disabledBackgroundColor, other.disabledBackgroundColor, t),
      disabledForegroundColor: Color.lerp(disabledForegroundColor, other.disabledForegroundColor, t),
      itemSize: lerpDouble(itemSize, other.itemSize, t),
      itemPadding: EdgeInsets.lerp(itemPadding, other.itemPadding, t),
      itemBorderRadius: BorderRadius.lerp(itemBorderRadius, other.itemBorderRadius, t),
      itemSpacing: lerpDouble(itemSpacing, other.itemSpacing, t),
      textStyle: t < 0.5 ? textStyle : other.textStyle,
      borderWidth: lerpDouble(borderWidth, other.borderWidth, t),
    );
  }

  static ShadcnPaginationTheme defaultTheme(ColorScheme colorScheme) {
    return ShadcnPaginationTheme(
      backgroundColor: colorScheme.surface,
      foregroundColor: colorScheme.onSurface,
      borderColor: colorScheme.outline,
      selectedBackgroundColor: colorScheme.primary,
      selectedForegroundColor: colorScheme.onPrimary,
      hoverBackgroundColor: colorScheme.onSurface.withOpacity(0.04),
      disabledBackgroundColor: colorScheme.onSurface.withOpacity(0.04),
      disabledForegroundColor: colorScheme.onSurface.withOpacity(0.38),
      itemSize: 40.0,
      itemPadding: const EdgeInsets.all(8.0),
      itemBorderRadius: BorderRadius.circular(ShadcnTokens.radiusMd),
      itemSpacing: 4.0,
      textStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightMedium,
      ),
      borderWidth: ShadcnTokens.borderWidth,
    );
  }
}

double? lerpDouble(double? a, double? b, double t) {
  if (a == null && b == null) return null;
  a ??= 0.0;
  b ??= 0.0;
  return a + (b - a) * t;
}