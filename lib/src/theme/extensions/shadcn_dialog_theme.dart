import 'dart:ui' show lerpDouble;
import 'package:flutter/material.dart';
import 'shadcn_theme_extension.dart';
import '../../constants/shadcn_tokens.dart';

/// Theme extension for the ShadcnDialog component.
/// 
/// Defines styling properties for dialog components that build upon Material
/// Design's dialog system while providing shadcn-specific visual styling.
/// This theme extension ensures proper integration with Flutter's dialog
/// overlays and Material Design principles.
class ShadcnDialogTheme extends ShadcnThemeExtension<ShadcnDialogTheme> {
  /// Background color for the dialog surface
  final Color? backgroundColor;
  
  /// Foreground color for text content in the dialog
  final Color? foregroundColor;
  
  /// Border color for the dialog container
  final Color? borderColor;
  
  /// Shadow color for dialog elevation
  final Color? shadowColor;
  
  /// Surface tint color (set to transparent to disable Material 3 tinting)
  final Color? surfaceTintColor;
  
  /// Text style for the dialog title
  final TextStyle? titleTextStyle;
  
  /// Text style for the dialog content/body
  final TextStyle? contentTextStyle;
  
  /// Border radius for the dialog container
  final BorderRadius? borderRadius;
  
  /// Border width for the dialog container
  final double? borderWidth;
  
  /// Elevation depth for the dialog shadow
  final double? elevation;
  
  /// Content padding inside the dialog
  final EdgeInsets? contentPadding;
  
  /// Title padding inside the dialog
  final EdgeInsets? titlePadding;
  
  /// Actions padding (buttons area)
  final EdgeInsets? actionsPadding;
  
  /// Padding around individual action buttons
  final EdgeInsets? buttonPadding;
  
  /// Maximum width constraint for the dialog
  final double? maxWidth;
  
  /// Maximum height constraint for the dialog
  final double? maxHeight;
  
  /// Minimum width constraint for the dialog
  final double? minWidth;
  
  /// Minimum height constraint for the dialog  
  final double? minHeight;
  
  /// Spacing between title and content
  final double? titleContentGap;
  
  /// Spacing between content and actions
  final double? contentActionsGap;
  
  /// Spacing between action buttons
  final double? actionButtonGap;
  
  /// Clip behavior for the dialog container
  final Clip? clipBehavior;
  
  /// Alignment for action buttons (start, end, center, spaceEvenly, etc.)
  final MainAxisAlignment? actionsAlignment;
  
  /// Whether to arrange action buttons vertically instead of horizontally
  final bool? verticalActions;
  
  /// Shape for the dialog container (overrides border radius if provided)
  final ShapeBorder? shape;
  
  /// Inset animations configuration
  final EdgeInsets? insetPadding;
  
  /// Animation duration for dialog show/hide
  final Duration? animationDuration;
  
  /// Animation curve for dialog transitions
  final Curve? animationCurve;
  
  /// Barrier color (background overlay color)
  final Color? barrierColor;
  
  /// Whether the dialog can be dismissed by tapping outside
  final bool? barrierDismissible;
  
  const ShadcnDialogTheme({
    // Colors
    this.backgroundColor,
    this.foregroundColor,
    this.borderColor,
    this.shadowColor,
    this.surfaceTintColor,
    
    // Typography
    this.titleTextStyle,
    this.contentTextStyle,
    
    // Shape and borders
    this.borderRadius,
    this.borderWidth,
    this.elevation,
    
    // Layout and spacing
    this.contentPadding,
    this.titlePadding,
    this.actionsPadding,
    this.buttonPadding,
    this.maxWidth,
    this.maxHeight,
    this.minWidth,
    this.minHeight,
    this.titleContentGap,
    this.contentActionsGap,
    this.actionButtonGap,
    
    // Behavior
    this.clipBehavior,
    this.actionsAlignment,
    this.verticalActions,
    this.shape,
    this.insetPadding,
    
    // Animation and interaction
    this.animationDuration,
    this.animationCurve,
    this.barrierColor,
    this.barrierDismissible,
  });
  
  /// Creates a default dialog theme from a Material ColorScheme.
  /// 
  /// This factory method generates sensible defaults that integrate with
  /// Material Design dialog system while following shadcn design patterns.
  static ShadcnDialogTheme defaultTheme(ColorScheme colorScheme) {
    final isLight = colorScheme.brightness == Brightness.light;
    
    return ShadcnDialogTheme(
      // Colors based on Material 3 specifications
      backgroundColor: colorScheme.surface,
      foregroundColor: colorScheme.onSurface,
      borderColor: colorScheme.outline.withValues(alpha: 0.2),
      shadowColor: colorScheme.shadow,
      surfaceTintColor: Colors.transparent, // Disable Material 3 tinting for shadcn look
      
      // Shape and elevation using shadcn tokens
      borderRadius: ShadcnTokens.borderRadius(ShadcnTokens.radiusLg),
      borderWidth: ShadcnTokens.borderWidth,
      elevation: ShadcnTokens.elevationLg, // Higher elevation for dialogs
      
      // Layout constraints - reasonable dialog sizing
      maxWidth: 512.0, // Reasonable max width for dialogs
      maxHeight: null, // Allow natural height expansion
      minWidth: 280.0, // Minimum usable width
      minHeight: null, // Allow natural height contraction
      
      // Padding following Material 3 specifications but adapted for shadcn
      contentPadding: const EdgeInsets.fromLTRB(24.0, 20.0, 24.0, 24.0),
      titlePadding: const EdgeInsets.fromLTRB(24.0, 24.0, 24.0, 0.0),
      actionsPadding: const EdgeInsets.fromLTRB(24.0, 0.0, 24.0, 24.0),
      buttonPadding: EdgeInsets.zero,
      
      // Spacing using shadcn tokens
      titleContentGap: ShadcnTokens.spacing4,
      contentActionsGap: ShadcnTokens.spacing6,
      actionButtonGap: ShadcnTokens.spacing2,
      
      // Behavior settings
      clipBehavior: Clip.hardEdge,
      actionsAlignment: MainAxisAlignment.end,
      verticalActions: false,
      
      // Dialog positioning and insets
      insetPadding: const EdgeInsets.symmetric(
        horizontal: 40.0,
        vertical: 24.0,
      ),
      
      // Animation properties
      animationDuration: ShadcnTokens.durationNormal,
      animationCurve: Curves.easeInOut,
      
      // Barrier properties
      barrierColor: Colors.black54, // Standard Material dialog barrier
      barrierDismissible: true,
    );
  }
  
  @override
  ShadcnDialogTheme copyWith({
    // Colors
    Color? backgroundColor,
    Color? foregroundColor,
    Color? borderColor,
    Color? shadowColor,
    Color? surfaceTintColor,
    
    // Typography
    TextStyle? titleTextStyle,
    TextStyle? contentTextStyle,
    
    // Shape and borders
    BorderRadius? borderRadius,
    double? borderWidth,
    double? elevation,
    
    // Layout and spacing
    EdgeInsets? contentPadding,
    EdgeInsets? titlePadding,
    EdgeInsets? actionsPadding,
    EdgeInsets? buttonPadding,
    double? maxWidth,
    double? maxHeight,
    double? minWidth,
    double? minHeight,
    double? titleContentGap,
    double? contentActionsGap,
    double? actionButtonGap,
    
    // Behavior
    Clip? clipBehavior,
    MainAxisAlignment? actionsAlignment,
    bool? verticalActions,
    ShapeBorder? shape,
    EdgeInsets? insetPadding,
    
    // Animation and interaction
    Duration? animationDuration,
    Curve? animationCurve,
    Color? barrierColor,
    bool? barrierDismissible,
  }) {
    return ShadcnDialogTheme(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      foregroundColor: foregroundColor ?? this.foregroundColor,
      borderColor: borderColor ?? this.borderColor,
      shadowColor: shadowColor ?? this.shadowColor,
      surfaceTintColor: surfaceTintColor ?? this.surfaceTintColor,
      
      titleTextStyle: titleTextStyle ?? this.titleTextStyle,
      contentTextStyle: contentTextStyle ?? this.contentTextStyle,
      
      borderRadius: borderRadius ?? this.borderRadius,
      borderWidth: borderWidth ?? this.borderWidth,
      elevation: elevation ?? this.elevation,
      
      contentPadding: contentPadding ?? this.contentPadding,
      titlePadding: titlePadding ?? this.titlePadding,
      actionsPadding: actionsPadding ?? this.actionsPadding,
      buttonPadding: buttonPadding ?? this.buttonPadding,
      maxWidth: maxWidth ?? this.maxWidth,
      maxHeight: maxHeight ?? this.maxHeight,
      minWidth: minWidth ?? this.minWidth,
      minHeight: minHeight ?? this.minHeight,
      titleContentGap: titleContentGap ?? this.titleContentGap,
      contentActionsGap: contentActionsGap ?? this.contentActionsGap,
      actionButtonGap: actionButtonGap ?? this.actionButtonGap,
      
      clipBehavior: clipBehavior ?? this.clipBehavior,
      actionsAlignment: actionsAlignment ?? this.actionsAlignment,
      verticalActions: verticalActions ?? this.verticalActions,
      shape: shape ?? this.shape,
      insetPadding: insetPadding ?? this.insetPadding,
      
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
      barrierColor: barrierColor ?? this.barrierColor,
      barrierDismissible: barrierDismissible ?? this.barrierDismissible,
    );
  }
  
  @override
  ShadcnDialogTheme lerp(ShadcnDialogTheme? other, double t) {
    if (other == null) return this;
    
    return ShadcnDialogTheme(
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t),
      foregroundColor: Color.lerp(foregroundColor, other.foregroundColor, t),
      borderColor: Color.lerp(borderColor, other.borderColor, t),
      shadowColor: Color.lerp(shadowColor, other.shadowColor, t),
      surfaceTintColor: Color.lerp(surfaceTintColor, other.surfaceTintColor, t),
      
      titleTextStyle: TextStyle.lerp(titleTextStyle, other.titleTextStyle, t),
      contentTextStyle: TextStyle.lerp(contentTextStyle, other.contentTextStyle, t),
      
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      borderWidth: lerpDouble(borderWidth, other.borderWidth, t),
      elevation: lerpDouble(elevation, other.elevation, t),
      
      contentPadding: EdgeInsets.lerp(contentPadding, other.contentPadding, t),
      titlePadding: EdgeInsets.lerp(titlePadding, other.titlePadding, t),
      actionsPadding: EdgeInsets.lerp(actionsPadding, other.actionsPadding, t),
      buttonPadding: EdgeInsets.lerp(buttonPadding, other.buttonPadding, t),
      maxWidth: lerpDouble(maxWidth, other.maxWidth, t),
      maxHeight: lerpDouble(maxHeight, other.maxHeight, t),
      minWidth: lerpDouble(minWidth, other.minWidth, t),
      minHeight: lerpDouble(minHeight, other.minHeight, t),
      titleContentGap: lerpDouble(titleContentGap, other.titleContentGap, t),
      contentActionsGap: lerpDouble(contentActionsGap, other.contentActionsGap, t),
      actionButtonGap: lerpDouble(actionButtonGap, other.actionButtonGap, t),
      
      clipBehavior: t < 0.5 ? clipBehavior : other.clipBehavior,
      actionsAlignment: t < 0.5 ? actionsAlignment : other.actionsAlignment,
      verticalActions: t < 0.5 ? verticalActions : other.verticalActions,
      shape: ShapeBorder.lerp(shape, other.shape, t),
      insetPadding: EdgeInsets.lerp(insetPadding, other.insetPadding, t),
      
      animationDuration: t < 0.5 ? animationDuration : other.animationDuration,
      animationCurve: t < 0.5 ? animationCurve : other.animationCurve,
      barrierColor: Color.lerp(barrierColor, other.barrierColor, t),
      barrierDismissible: t < 0.5 ? barrierDismissible : other.barrierDismissible,
    );
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is ShadcnDialogTheme &&
        backgroundColor == other.backgroundColor &&
        foregroundColor == other.foregroundColor &&
        borderColor == other.borderColor &&
        shadowColor == other.shadowColor &&
        surfaceTintColor == other.surfaceTintColor &&
        titleTextStyle == other.titleTextStyle &&
        contentTextStyle == other.contentTextStyle &&
        borderRadius == other.borderRadius &&
        borderWidth == other.borderWidth &&
        elevation == other.elevation &&
        contentPadding == other.contentPadding &&
        titlePadding == other.titlePadding &&
        actionsPadding == other.actionsPadding &&
        buttonPadding == other.buttonPadding &&
        maxWidth == other.maxWidth &&
        maxHeight == other.maxHeight &&
        minWidth == other.minWidth &&
        minHeight == other.minHeight &&
        titleContentGap == other.titleContentGap &&
        contentActionsGap == other.contentActionsGap &&
        actionButtonGap == other.actionButtonGap &&
        clipBehavior == other.clipBehavior &&
        actionsAlignment == other.actionsAlignment &&
        verticalActions == other.verticalActions &&
        shape == other.shape &&
        insetPadding == other.insetPadding &&
        animationDuration == other.animationDuration &&
        animationCurve == other.animationCurve &&
        barrierColor == other.barrierColor &&
        barrierDismissible == other.barrierDismissible;
  }
  
  @override
  int get hashCode {
    return Object.hashAll([
      backgroundColor,
      foregroundColor,
      borderColor,
      shadowColor,
      surfaceTintColor,
      titleTextStyle,
      contentTextStyle,
      borderRadius,
      borderWidth,
      elevation,
      contentPadding,
      titlePadding,
      actionsPadding,
      buttonPadding,
      maxWidth,
      maxHeight,
      minWidth,
      minHeight,
      titleContentGap,
      contentActionsGap,
      actionButtonGap,
      clipBehavior,
      actionsAlignment,
      verticalActions,
      shape,
      insetPadding,
      animationDuration,
      animationCurve,
      barrierColor,
      barrierDismissible,
    ]);
  }
}
