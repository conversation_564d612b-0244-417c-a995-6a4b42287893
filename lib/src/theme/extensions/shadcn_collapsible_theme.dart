import 'package:flutter/material.dart';
import '../../constants/shadcn_tokens.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for the ShadcnCollapsible component.
/// 
/// This theme extension provides styling properties for collapsible content areas,
/// including trigger styling, content padding, animations, and interactive states.
/// It follows shadcn design principles while integrating with Material Design theming.
class ShadcnCollapsibleTheme extends ShadcnThemeExtension<ShadcnCollapsibleTheme> {
  // Trigger styling
  final Color? triggerBackground;
  final Color? triggerForeground;
  final Color? triggerHoverBackground;
  final Color? triggerHoverForeground;
  final Color? triggerPressedBackground;
  final Color? triggerPressedForeground;
  final Color? triggerDisabledBackground;
  final Color? triggerDisabledForeground;
  
  // Content styling
  final Color? contentBackground;
  final Color? contentForeground;
  final EdgeInsets? contentPadding;
  
  // Border and shape
  final Color? borderColor;
  final double? borderWidth;
  final BorderRadius? borderRadius;
  
  // Typography
  final TextStyle? triggerTextStyle;
  final TextStyle? contentTextStyle;
  final FontWeight? triggerFontWeight;
  
  // Spacing and layout
  final EdgeInsets? triggerPadding;
  final double? triggerHeight;
  final double? iconSize;
  final double? iconSpacing;
  final MainAxisAlignment? triggerAlignment;
  final CrossAxisAlignment? triggerCrossAlignment;
  
  // Animation properties
  final Duration? animationDuration;
  final Curve? animationCurve;
  
  // Icon properties
  final Widget? expandIcon;
  final Widget? collapseIcon;
  final double? iconRotation;
  
  // State properties
  final bool? showBorder;
  final bool? animateIcon;
  
  const ShadcnCollapsibleTheme({
    // Trigger styling
    this.triggerBackground,
    this.triggerForeground,
    this.triggerHoverBackground,
    this.triggerHoverForeground,
    this.triggerPressedBackground,
    this.triggerPressedForeground,
    this.triggerDisabledBackground,
    this.triggerDisabledForeground,
    
    // Content styling
    this.contentBackground,
    this.contentForeground,
    this.contentPadding,
    
    // Border and shape
    this.borderColor,
    this.borderWidth,
    this.borderRadius,
    
    // Typography
    this.triggerTextStyle,
    this.contentTextStyle,
    this.triggerFontWeight,
    
    // Spacing and layout
    this.triggerPadding,
    this.triggerHeight,
    this.iconSize,
    this.iconSpacing,
    this.triggerAlignment,
    this.triggerCrossAlignment,
    
    // Animation properties
    this.animationDuration,
    this.animationCurve,
    
    // Icon properties
    this.expandIcon,
    this.collapseIcon,
    this.iconRotation,
    
    // State properties
    this.showBorder,
    this.animateIcon,
  });

  @override
  ShadcnCollapsibleTheme copyWith({
    // Trigger styling
    Color? triggerBackground,
    Color? triggerForeground,
    Color? triggerHoverBackground,
    Color? triggerHoverForeground,
    Color? triggerPressedBackground,
    Color? triggerPressedForeground,
    Color? triggerDisabledBackground,
    Color? triggerDisabledForeground,
    
    // Content styling
    Color? contentBackground,
    Color? contentForeground,
    EdgeInsets? contentPadding,
    
    // Border and shape
    Color? borderColor,
    double? borderWidth,
    BorderRadius? borderRadius,
    
    // Typography
    TextStyle? triggerTextStyle,
    TextStyle? contentTextStyle,
    FontWeight? triggerFontWeight,
    
    // Spacing and layout
    EdgeInsets? triggerPadding,
    double? triggerHeight,
    double? iconSize,
    double? iconSpacing,
    MainAxisAlignment? triggerAlignment,
    CrossAxisAlignment? triggerCrossAlignment,
    
    // Animation properties
    Duration? animationDuration,
    Curve? animationCurve,
    
    // Icon properties
    Widget? expandIcon,
    Widget? collapseIcon,
    double? iconRotation,
    
    // State properties
    bool? showBorder,
    bool? animateIcon,
  }) {
    return ShadcnCollapsibleTheme(
      // Trigger styling
      triggerBackground: triggerBackground ?? this.triggerBackground,
      triggerForeground: triggerForeground ?? this.triggerForeground,
      triggerHoverBackground: triggerHoverBackground ?? this.triggerHoverBackground,
      triggerHoverForeground: triggerHoverForeground ?? this.triggerHoverForeground,
      triggerPressedBackground: triggerPressedBackground ?? this.triggerPressedBackground,
      triggerPressedForeground: triggerPressedForeground ?? this.triggerPressedForeground,
      triggerDisabledBackground: triggerDisabledBackground ?? this.triggerDisabledBackground,
      triggerDisabledForeground: triggerDisabledForeground ?? this.triggerDisabledForeground,
      
      // Content styling
      contentBackground: contentBackground ?? this.contentBackground,
      contentForeground: contentForeground ?? this.contentForeground,
      contentPadding: contentPadding ?? this.contentPadding,
      
      // Border and shape
      borderColor: borderColor ?? this.borderColor,
      borderWidth: borderWidth ?? this.borderWidth,
      borderRadius: borderRadius ?? this.borderRadius,
      
      // Typography
      triggerTextStyle: triggerTextStyle ?? this.triggerTextStyle,
      contentTextStyle: contentTextStyle ?? this.contentTextStyle,
      triggerFontWeight: triggerFontWeight ?? this.triggerFontWeight,
      
      // Spacing and layout
      triggerPadding: triggerPadding ?? this.triggerPadding,
      triggerHeight: triggerHeight ?? this.triggerHeight,
      iconSize: iconSize ?? this.iconSize,
      iconSpacing: iconSpacing ?? this.iconSpacing,
      triggerAlignment: triggerAlignment ?? this.triggerAlignment,
      triggerCrossAlignment: triggerCrossAlignment ?? this.triggerCrossAlignment,
      
      // Animation properties
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
      
      // Icon properties
      expandIcon: expandIcon ?? this.expandIcon,
      collapseIcon: collapseIcon ?? this.collapseIcon,
      iconRotation: iconRotation ?? this.iconRotation,
      
      // State properties
      showBorder: showBorder ?? this.showBorder,
      animateIcon: animateIcon ?? this.animateIcon,
    );
  }

  @override
  ShadcnCollapsibleTheme lerp(ShadcnCollapsibleTheme? other, double t) {
    if (other is! ShadcnCollapsibleTheme) return this;
    
    return ShadcnCollapsibleTheme(
      // Trigger styling
      triggerBackground: Color.lerp(triggerBackground, other.triggerBackground, t),
      triggerForeground: Color.lerp(triggerForeground, other.triggerForeground, t),
      triggerHoverBackground: Color.lerp(triggerHoverBackground, other.triggerHoverBackground, t),
      triggerHoverForeground: Color.lerp(triggerHoverForeground, other.triggerHoverForeground, t),
      triggerPressedBackground: Color.lerp(triggerPressedBackground, other.triggerPressedBackground, t),
      triggerPressedForeground: Color.lerp(triggerPressedForeground, other.triggerPressedForeground, t),
      triggerDisabledBackground: Color.lerp(triggerDisabledBackground, other.triggerDisabledBackground, t),
      triggerDisabledForeground: Color.lerp(triggerDisabledForeground, other.triggerDisabledForeground, t),
      
      // Content styling
      contentBackground: Color.lerp(contentBackground, other.contentBackground, t),
      contentForeground: Color.lerp(contentForeground, other.contentForeground, t),
      contentPadding: EdgeInsets.lerp(contentPadding, other.contentPadding, t),
      
      // Border and shape
      borderColor: Color.lerp(borderColor, other.borderColor, t),
      borderWidth: lerpDouble(borderWidth, other.borderWidth, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      
      // Typography - text styles don't lerp well, so we use threshold
      triggerTextStyle: t < 0.5 ? triggerTextStyle : other.triggerTextStyle,
      contentTextStyle: t < 0.5 ? contentTextStyle : other.contentTextStyle,
      triggerFontWeight: t < 0.5 ? triggerFontWeight : other.triggerFontWeight,
      
      // Spacing and layout
      triggerPadding: EdgeInsets.lerp(triggerPadding, other.triggerPadding, t),
      triggerHeight: lerpDouble(triggerHeight, other.triggerHeight, t),
      iconSize: lerpDouble(iconSize, other.iconSize, t),
      iconSpacing: lerpDouble(iconSpacing, other.iconSpacing, t),
      triggerAlignment: t < 0.5 ? triggerAlignment : other.triggerAlignment,
      triggerCrossAlignment: t < 0.5 ? triggerCrossAlignment : other.triggerCrossAlignment,
      
      // Animation properties
      animationDuration: lerpDuration(animationDuration ?? Duration.zero, other.animationDuration ?? Duration.zero, t),
      animationCurve: t < 0.5 ? animationCurve : other.animationCurve,
      
      // Icon properties - widgets don't lerp, use threshold
      expandIcon: t < 0.5 ? expandIcon : other.expandIcon,
      collapseIcon: t < 0.5 ? collapseIcon : other.collapseIcon,
      iconRotation: lerpDouble(iconRotation, other.iconRotation, t),
      
      // State properties
      showBorder: t < 0.5 ? showBorder : other.showBorder,
      animateIcon: t < 0.5 ? animateIcon : other.animateIcon,
    );
  }

  /// Creates a default theme based on the provided color scheme.
  /// 
  /// This factory constructor provides sensible defaults that follow
  /// shadcn design principles while integrating with Material Design.
  static ShadcnCollapsibleTheme defaultTheme(ColorScheme colorScheme) {
    return ShadcnCollapsibleTheme(
      // Trigger styling
      triggerBackground: Colors.transparent,
      triggerForeground: colorScheme.onSurface,
      triggerHoverBackground: colorScheme.onSurface.withOpacity(0.04),
      triggerHoverForeground: colorScheme.onSurface,
      triggerPressedBackground: colorScheme.onSurface.withOpacity(0.08),
      triggerPressedForeground: colorScheme.onSurface,
      triggerDisabledBackground: Colors.transparent,
      triggerDisabledForeground: colorScheme.onSurface.withOpacity(0.38),
      
      // Content styling
      contentBackground: Colors.transparent,
      contentForeground: colorScheme.onSurface,
      contentPadding: const EdgeInsets.all(ShadcnTokens.spacing4),
      
      // Border and shape
      borderColor: colorScheme.outline,
      borderWidth: ShadcnTokens.borderWidth,
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusMd),
      
      // Typography
      triggerTextStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightMedium,
      ),
      contentTextStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightNormal,
      ),
      triggerFontWeight: ShadcnTokens.fontWeightMedium,
      
      // Spacing and layout
      triggerPadding: const EdgeInsets.symmetric(
        horizontal: ShadcnTokens.spacing4,
        vertical: ShadcnTokens.spacing2,
      ),
      triggerHeight: 44.0,
      iconSize: ShadcnTokens.iconSizeMd,
      iconSpacing: ShadcnTokens.spacing2,
      triggerAlignment: MainAxisAlignment.spaceBetween,
      triggerCrossAlignment: CrossAxisAlignment.center,
      
      // Animation properties
      animationDuration: ShadcnTokens.durationNormal,
      animationCurve: Curves.easeInOut,
      
      // Icon properties
      expandIcon: const Icon(Icons.keyboard_arrow_down),
      collapseIcon: const Icon(Icons.keyboard_arrow_up),
      iconRotation: 0.5, // 180 degrees for rotation animation
      
      // State properties
      showBorder: false,
      animateIcon: true,
    );
  }

  @override
  bool validate({bool throwOnError = false}) {
    // Validate required properties
    if (animationDuration != null && animationDuration!.isNegative) {
      if (throwOnError) {
        throw ArgumentError('animationDuration cannot be negative');
      }
      return false;
    }
    
    if (iconRotation != null && (iconRotation! < 0 || iconRotation! > 1)) {
      if (throwOnError) {
        throw ArgumentError('iconRotation must be between 0 and 1');
      }
      return false;
    }
    
    return true;
  }
}

/// Helper function to lerp Duration values
Duration lerpDuration(Duration a, Duration b, double t) {
  return Duration(
    microseconds: (a.inMicroseconds + ((b.inMicroseconds - a.inMicroseconds) * t)).round(),
  );
}

/// Helper function to lerp double values with null safety
double? lerpDouble(double? a, double? b, double t) {
  if (a == null && b == null) return null;
  a ??= 0.0;
  b ??= 0.0;
  return a + (b - a) * t;
}