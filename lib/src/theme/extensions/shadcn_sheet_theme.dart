import 'dart:ui' show lerpDouble;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../constants/shadcn_tokens.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for the ShadcnSheet component.
/// 
/// This theme extension provides comprehensive styling properties for sheets,
/// including backdrop colors, positioning, Material bottom sheet integration,
/// and animation configurations. It follows shadcn design principles while
/// ensuring Material Design patterns and accessibility compliance.
/// 
/// All properties are nullable to support fallback to Material theme values
/// and provide flexible customization options for developers.
class ShadcnSheetTheme extends ShadcnThemeExtension<ShadcnSheetTheme> {
  // Sheet container properties
  final Color? backgroundColor;
  final Color? foregroundColor;
  final Color? borderColor;
  final BorderRadius? borderRadius;
  final double? borderWidth;
  final EdgeInsets? padding;
  final double? elevation;
  final Color? shadowColor;
  
  // Backdrop properties
  final Color? backdropColor;
  final double? backdropOpacity;
  final bool? barrierDismissible;
  final String? barrierLabel;
  
  // Size and positioning properties
  final double? maxWidth;
  final double? maxHeight;
  final double? minWidth;
  final double? minHeight;
  final EdgeInsets? margin;
  final AlignmentGeometry? alignment;
  
  // Header properties
  final Color? headerBackground;
  final Color? headerForeground;
  final EdgeInsets? headerPadding;
  final double? headerHeight;
  final BorderRadius? headerBorderRadius;
  final TextStyle? titleTextStyle;
  final TextStyle? descriptionTextStyle;
  
  // Content properties
  final Color? contentBackground;
  final Color? contentForeground;
  final EdgeInsets? contentPadding;
  final double? contentMaxHeight;
  
  // Footer properties
  final Color? footerBackground;
  final Color? footerForeground;
  final EdgeInsets? footerPadding;
  final double? footerHeight;
  final BorderRadius? footerBorderRadius;
  final MainAxisAlignment? footerButtonAlignment;
  
  // Close button properties
  final Color? closeButtonBackground;
  final Color? closeButtonForeground;
  final Color? closeButtonHoverBackground;
  final Color? closeButtonHoverForeground;
  final double? closeButtonSize;
  final EdgeInsets? closeButtonPadding;
  final BorderRadius? closeButtonBorderRadius;
  
  // Drag handle properties
  final Color? dragHandleColor;
  final double? dragHandleWidth;
  final double? dragHandleHeight;
  final BorderRadius? dragHandleBorderRadius;
  
  // Animation properties
  final Duration? animationDuration;
  final Curve? animationCurve;
  final Duration? backdropAnimationDuration;
  final Curve? backdropAnimationCurve;
  
  // Side sheet properties (for non-bottom sheets)
  final double? sideSheetWidth;
  final EdgeInsets? sideSheetMargin;
  
  // Responsive properties
  final bool? isModal;
  final bool? isDismissible;
  final bool? enableDrag;
  final bool? showDragHandle;
  final bool? showCloseButton;
  
  const ShadcnSheetTheme({
    // Sheet container properties
    this.backgroundColor,
    this.foregroundColor,
    this.borderColor,
    this.borderRadius,
    this.borderWidth,
    this.padding,
    this.elevation,
    this.shadowColor,
    
    // Backdrop properties
    this.backdropColor,
    this.backdropOpacity,
    this.barrierDismissible,
    this.barrierLabel,
    
    // Size and positioning properties
    this.maxWidth,
    this.maxHeight,
    this.minWidth,
    this.minHeight,
    this.margin,
    this.alignment,
    
    // Header properties
    this.headerBackground,
    this.headerForeground,
    this.headerPadding,
    this.headerHeight,
    this.headerBorderRadius,
    this.titleTextStyle,
    this.descriptionTextStyle,
    
    // Content properties
    this.contentBackground,
    this.contentForeground,
    this.contentPadding,
    this.contentMaxHeight,
    
    // Footer properties
    this.footerBackground,
    this.footerForeground,
    this.footerPadding,
    this.footerHeight,
    this.footerBorderRadius,
    this.footerButtonAlignment,
    
    // Close button properties
    this.closeButtonBackground,
    this.closeButtonForeground,
    this.closeButtonHoverBackground,
    this.closeButtonHoverForeground,
    this.closeButtonSize,
    this.closeButtonPadding,
    this.closeButtonBorderRadius,
    
    // Drag handle properties
    this.dragHandleColor,
    this.dragHandleWidth,
    this.dragHandleHeight,
    this.dragHandleBorderRadius,
    
    // Animation properties
    this.animationDuration,
    this.animationCurve,
    this.backdropAnimationDuration,
    this.backdropAnimationCurve,
    
    // Side sheet properties
    this.sideSheetWidth,
    this.sideSheetMargin,
    
    // Responsive properties
    this.isModal,
    this.isDismissible,
    this.enableDrag,
    this.showDragHandle,
    this.showCloseButton,
  });

  /// Creates a default theme based on the provided color scheme.
  /// 
  /// This factory constructor provides sensible defaults that follow shadcn
  /// design principles while integrating with Material Design patterns.
  /// All values are designed to work well with both light and dark themes.
  static ShadcnSheetTheme defaultTheme(ColorScheme colorScheme) {
    final isDark = colorScheme.brightness == Brightness.dark;
    
    return ShadcnSheetTheme(
      // Sheet container properties
      backgroundColor: colorScheme.surface,
      foregroundColor: colorScheme.onSurface,
      borderColor: colorScheme.outline.withOpacity(0.2),
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(ShadcnTokens.radiusXl),
        topRight: Radius.circular(ShadcnTokens.radiusXl),
      ),
      borderWidth: ShadcnTokens.borderWidth,
      padding: const EdgeInsets.all(ShadcnTokens.spacing6),
      elevation: ShadcnTokens.elevationLg,
      shadowColor: colorScheme.shadow,
      
      // Backdrop properties
      backdropColor: Colors.black,
      backdropOpacity: isDark ? 0.8 : 0.5,
      barrierDismissible: true,
      barrierLabel: 'Close sheet',
      
      // Size and positioning properties
      maxWidth: 600.0,
      maxHeight: null, // Use 90% of screen height by default
      minWidth: 300.0,
      minHeight: 200.0,
      margin: const EdgeInsets.all(ShadcnTokens.spacing4),
      alignment: Alignment.bottomCenter,
      
      // Header properties
      headerBackground: Colors.transparent,
      headerForeground: colorScheme.onSurface,
      headerPadding: const EdgeInsets.only(
        bottom: ShadcnTokens.spacing4,
      ),
      headerHeight: 60.0,
      titleTextStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeXl,
        fontWeight: ShadcnTokens.fontWeightSemibold,
        color: colorScheme.onSurface,
      ),
      descriptionTextStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        color: colorScheme.onSurface.withOpacity(0.7),
      ),
      
      // Content properties
      contentBackground: Colors.transparent,
      contentForeground: colorScheme.onSurface,
      contentPadding: const EdgeInsets.symmetric(vertical: ShadcnTokens.spacing4),
      contentMaxHeight: null, // Unlimited by default
      
      // Footer properties
      footerBackground: Colors.transparent,
      footerForeground: colorScheme.onSurface,
      footerPadding: const EdgeInsets.only(
        top: ShadcnTokens.spacing4,
      ),
      footerHeight: 60.0,
      footerButtonAlignment: MainAxisAlignment.end,
      
      // Close button properties
      closeButtonBackground: Colors.transparent,
      closeButtonForeground: colorScheme.onSurface.withOpacity(0.5),
      closeButtonHoverBackground: colorScheme.onSurface.withOpacity(0.08),
      closeButtonHoverForeground: colorScheme.onSurface,
      closeButtonSize: 32.0,
      closeButtonPadding: const EdgeInsets.all(ShadcnTokens.spacing2),
      closeButtonBorderRadius: BorderRadius.circular(ShadcnTokens.radiusMd),
      
      // Drag handle properties
      dragHandleColor: colorScheme.onSurface.withOpacity(0.3),
      dragHandleWidth: 32.0,
      dragHandleHeight: 4.0,
      dragHandleBorderRadius: BorderRadius.circular(ShadcnTokens.radiusSm),
      
      // Animation properties
      animationDuration: ShadcnTokens.durationNormal,
      animationCurve: Curves.easeOutCubic,
      backdropAnimationDuration: ShadcnTokens.durationFast,
      backdropAnimationCurve: Curves.easeInOut,
      
      // Side sheet properties
      sideSheetWidth: 400.0,
      sideSheetMargin: const EdgeInsets.all(ShadcnTokens.spacing4),
      
      // Responsive properties
      isModal: true,
      isDismissible: true,
      enableDrag: true,
      showDragHandle: true,
      showCloseButton: true,
    );
  }

  @override
  ShadcnSheetTheme copyWith({
    // Sheet container properties
    Color? backgroundColor,
    Color? foregroundColor,
    Color? borderColor,
    BorderRadius? borderRadius,
    double? borderWidth,
    EdgeInsets? padding,
    double? elevation,
    Color? shadowColor,
    
    // Backdrop properties
    Color? backdropColor,
    double? backdropOpacity,
    bool? barrierDismissible,
    String? barrierLabel,
    
    // Size and positioning properties
    double? maxWidth,
    double? maxHeight,
    double? minWidth,
    double? minHeight,
    EdgeInsets? margin,
    AlignmentGeometry? alignment,
    
    // Header properties
    Color? headerBackground,
    Color? headerForeground,
    EdgeInsets? headerPadding,
    double? headerHeight,
    BorderRadius? headerBorderRadius,
    TextStyle? titleTextStyle,
    TextStyle? descriptionTextStyle,
    
    // Content properties
    Color? contentBackground,
    Color? contentForeground,
    EdgeInsets? contentPadding,
    double? contentMaxHeight,
    
    // Footer properties
    Color? footerBackground,
    Color? footerForeground,
    EdgeInsets? footerPadding,
    double? footerHeight,
    BorderRadius? footerBorderRadius,
    MainAxisAlignment? footerButtonAlignment,
    
    // Close button properties
    Color? closeButtonBackground,
    Color? closeButtonForeground,
    Color? closeButtonHoverBackground,
    Color? closeButtonHoverForeground,
    double? closeButtonSize,
    EdgeInsets? closeButtonPadding,
    BorderRadius? closeButtonBorderRadius,
    
    // Drag handle properties
    Color? dragHandleColor,
    double? dragHandleWidth,
    double? dragHandleHeight,
    BorderRadius? dragHandleBorderRadius,
    
    // Animation properties
    Duration? animationDuration,
    Curve? animationCurve,
    Duration? backdropAnimationDuration,
    Curve? backdropAnimationCurve,
    
    // Side sheet properties
    double? sideSheetWidth,
    EdgeInsets? sideSheetMargin,
    
    // Responsive properties
    bool? isModal,
    bool? isDismissible,
    bool? enableDrag,
    bool? showDragHandle,
    bool? showCloseButton,
  }) {
    return ShadcnSheetTheme(
      // Sheet container properties
      backgroundColor: backgroundColor ?? this.backgroundColor,
      foregroundColor: foregroundColor ?? this.foregroundColor,
      borderColor: borderColor ?? this.borderColor,
      borderRadius: borderRadius ?? this.borderRadius,
      borderWidth: borderWidth ?? this.borderWidth,
      padding: padding ?? this.padding,
      elevation: elevation ?? this.elevation,
      shadowColor: shadowColor ?? this.shadowColor,
      
      // Backdrop properties
      backdropColor: backdropColor ?? this.backdropColor,
      backdropOpacity: backdropOpacity ?? this.backdropOpacity,
      barrierDismissible: barrierDismissible ?? this.barrierDismissible,
      barrierLabel: barrierLabel ?? this.barrierLabel,
      
      // Size and positioning properties
      maxWidth: maxWidth ?? this.maxWidth,
      maxHeight: maxHeight ?? this.maxHeight,
      minWidth: minWidth ?? this.minWidth,
      minHeight: minHeight ?? this.minHeight,
      margin: margin ?? this.margin,
      alignment: alignment ?? this.alignment,
      
      // Header properties
      headerBackground: headerBackground ?? this.headerBackground,
      headerForeground: headerForeground ?? this.headerForeground,
      headerPadding: headerPadding ?? this.headerPadding,
      headerHeight: headerHeight ?? this.headerHeight,
      headerBorderRadius: headerBorderRadius ?? this.headerBorderRadius,
      titleTextStyle: titleTextStyle ?? this.titleTextStyle,
      descriptionTextStyle: descriptionTextStyle ?? this.descriptionTextStyle,
      
      // Content properties
      contentBackground: contentBackground ?? this.contentBackground,
      contentForeground: contentForeground ?? this.contentForeground,
      contentPadding: contentPadding ?? this.contentPadding,
      contentMaxHeight: contentMaxHeight ?? this.contentMaxHeight,
      
      // Footer properties
      footerBackground: footerBackground ?? this.footerBackground,
      footerForeground: footerForeground ?? this.footerForeground,
      footerPadding: footerPadding ?? this.footerPadding,
      footerHeight: footerHeight ?? this.footerHeight,
      footerBorderRadius: footerBorderRadius ?? this.footerBorderRadius,
      footerButtonAlignment: footerButtonAlignment ?? this.footerButtonAlignment,
      
      // Close button properties
      closeButtonBackground: closeButtonBackground ?? this.closeButtonBackground,
      closeButtonForeground: closeButtonForeground ?? this.closeButtonForeground,
      closeButtonHoverBackground: closeButtonHoverBackground ?? this.closeButtonHoverBackground,
      closeButtonHoverForeground: closeButtonHoverForeground ?? this.closeButtonHoverForeground,
      closeButtonSize: closeButtonSize ?? this.closeButtonSize,
      closeButtonPadding: closeButtonPadding ?? this.closeButtonPadding,
      closeButtonBorderRadius: closeButtonBorderRadius ?? this.closeButtonBorderRadius,
      
      // Drag handle properties
      dragHandleColor: dragHandleColor ?? this.dragHandleColor,
      dragHandleWidth: dragHandleWidth ?? this.dragHandleWidth,
      dragHandleHeight: dragHandleHeight ?? this.dragHandleHeight,
      dragHandleBorderRadius: dragHandleBorderRadius ?? this.dragHandleBorderRadius,
      
      // Animation properties
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
      backdropAnimationDuration: backdropAnimationDuration ?? this.backdropAnimationDuration,
      backdropAnimationCurve: backdropAnimationCurve ?? this.backdropAnimationCurve,
      
      // Side sheet properties
      sideSheetWidth: sideSheetWidth ?? this.sideSheetWidth,
      sideSheetMargin: sideSheetMargin ?? this.sideSheetMargin,
      
      // Responsive properties
      isModal: isModal ?? this.isModal,
      isDismissible: isDismissible ?? this.isDismissible,
      enableDrag: enableDrag ?? this.enableDrag,
      showDragHandle: showDragHandle ?? this.showDragHandle,
      showCloseButton: showCloseButton ?? this.showCloseButton,
    );
  }

  @override
  ShadcnSheetTheme lerp(ShadcnSheetTheme? other, double t) {
    if (other is! ShadcnSheetTheme) return this;
    
    return ShadcnSheetTheme(
      // Sheet container properties
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t),
      foregroundColor: Color.lerp(foregroundColor, other.foregroundColor, t),
      borderColor: Color.lerp(borderColor, other.borderColor, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      borderWidth: lerpDouble(borderWidth, other.borderWidth, t),
      padding: EdgeInsets.lerp(padding, other.padding, t),
      elevation: lerpDouble(elevation, other.elevation, t),
      shadowColor: Color.lerp(shadowColor, other.shadowColor, t),
      
      // Backdrop properties
      backdropColor: Color.lerp(backdropColor, other.backdropColor, t),
      backdropOpacity: lerpDouble(backdropOpacity, other.backdropOpacity, t),
      barrierDismissible: t < 0.5 ? barrierDismissible : other.barrierDismissible,
      barrierLabel: t < 0.5 ? barrierLabel : other.barrierLabel,
      
      // Size and positioning properties
      maxWidth: lerpDouble(maxWidth, other.maxWidth, t),
      maxHeight: lerpDouble(maxHeight, other.maxHeight, t),
      minWidth: lerpDouble(minWidth, other.minWidth, t),
      minHeight: lerpDouble(minHeight, other.minHeight, t),
      margin: EdgeInsets.lerp(margin, other.margin, t),
      alignment: AlignmentGeometry.lerp(alignment, other.alignment, t),
      
      // Header properties
      headerBackground: Color.lerp(headerBackground, other.headerBackground, t),
      headerForeground: Color.lerp(headerForeground, other.headerForeground, t),
      headerPadding: EdgeInsets.lerp(headerPadding, other.headerPadding, t),
      headerHeight: lerpDouble(headerHeight, other.headerHeight, t),
      headerBorderRadius: BorderRadius.lerp(headerBorderRadius, other.headerBorderRadius, t),
      titleTextStyle: TextStyle.lerp(titleTextStyle, other.titleTextStyle, t),
      descriptionTextStyle: TextStyle.lerp(descriptionTextStyle, other.descriptionTextStyle, t),
      
      // Content properties
      contentBackground: Color.lerp(contentBackground, other.contentBackground, t),
      contentForeground: Color.lerp(contentForeground, other.contentForeground, t),
      contentPadding: EdgeInsets.lerp(contentPadding, other.contentPadding, t),
      contentMaxHeight: lerpDouble(contentMaxHeight, other.contentMaxHeight, t),
      
      // Footer properties
      footerBackground: Color.lerp(footerBackground, other.footerBackground, t),
      footerForeground: Color.lerp(footerForeground, other.footerForeground, t),
      footerPadding: EdgeInsets.lerp(footerPadding, other.footerPadding, t),
      footerHeight: lerpDouble(footerHeight, other.footerHeight, t),
      footerBorderRadius: BorderRadius.lerp(footerBorderRadius, other.footerBorderRadius, t),
      footerButtonAlignment: t < 0.5 ? footerButtonAlignment : other.footerButtonAlignment,
      
      // Close button properties
      closeButtonBackground: Color.lerp(closeButtonBackground, other.closeButtonBackground, t),
      closeButtonForeground: Color.lerp(closeButtonForeground, other.closeButtonForeground, t),
      closeButtonHoverBackground: Color.lerp(closeButtonHoverBackground, other.closeButtonHoverBackground, t),
      closeButtonHoverForeground: Color.lerp(closeButtonHoverForeground, other.closeButtonHoverForeground, t),
      closeButtonSize: lerpDouble(closeButtonSize, other.closeButtonSize, t),
      closeButtonPadding: EdgeInsets.lerp(closeButtonPadding, other.closeButtonPadding, t),
      closeButtonBorderRadius: BorderRadius.lerp(closeButtonBorderRadius, other.closeButtonBorderRadius, t),
      
      // Drag handle properties
      dragHandleColor: Color.lerp(dragHandleColor, other.dragHandleColor, t),
      dragHandleWidth: lerpDouble(dragHandleWidth, other.dragHandleWidth, t),
      dragHandleHeight: lerpDouble(dragHandleHeight, other.dragHandleHeight, t),
      dragHandleBorderRadius: BorderRadius.lerp(dragHandleBorderRadius, other.dragHandleBorderRadius, t),
      
      // Animation properties
      animationDuration: t < 0.5 ? animationDuration : other.animationDuration,
      animationCurve: t < 0.5 ? animationCurve : other.animationCurve,
      backdropAnimationDuration: t < 0.5 ? backdropAnimationDuration : other.backdropAnimationDuration,
      backdropAnimationCurve: t < 0.5 ? backdropAnimationCurve : other.backdropAnimationCurve,
      
      // Side sheet properties
      sideSheetWidth: lerpDouble(sideSheetWidth, other.sideSheetWidth, t),
      sideSheetMargin: EdgeInsets.lerp(sideSheetMargin, other.sideSheetMargin, t),
      
      // Responsive properties
      isModal: t < 0.5 ? isModal : other.isModal,
      isDismissible: t < 0.5 ? isDismissible : other.isDismissible,
      enableDrag: t < 0.5 ? enableDrag : other.enableDrag,
      showDragHandle: t < 0.5 ? showDragHandle : other.showDragHandle,
      showCloseButton: t < 0.5 ? showCloseButton : other.showCloseButton,
    );
  }

  @override
  bool validate({bool throwOnError = false}) {
    try {
      // Validate that critical properties have reasonable values
      assert(borderWidth == null || borderWidth! >= 0, 'borderWidth must be non-negative');
      assert(elevation == null || elevation! >= 0, 'elevation must be non-negative');
      assert(backdropOpacity == null || (backdropOpacity! >= 0 && backdropOpacity! <= 1), 'backdropOpacity must be between 0 and 1');
      assert(maxWidth == null || maxWidth! > 0, 'maxWidth must be positive');
      assert(maxHeight == null || maxHeight! > 0, 'maxHeight must be positive');
      assert(minWidth == null || minWidth! >= 0, 'minWidth must be non-negative');
      assert(minHeight == null || minHeight! >= 0, 'minHeight must be non-negative');
      assert(headerHeight == null || headerHeight! >= 0, 'headerHeight must be non-negative');
      assert(footerHeight == null || footerHeight! >= 0, 'footerHeight must be non-negative');
      assert(closeButtonSize == null || closeButtonSize! > 0, 'closeButtonSize must be positive');
      assert(dragHandleWidth == null || dragHandleWidth! > 0, 'dragHandleWidth must be positive');
      assert(dragHandleHeight == null || dragHandleHeight! > 0, 'dragHandleHeight must be positive');
      assert(sideSheetWidth == null || sideSheetWidth! > 0, 'sideSheetWidth must be positive');
      
      return true;
    } catch (e) {
      if (throwOnError) {
        throw FlutterError('ShadcnSheetTheme validation failed: $e');
      }
      return false;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other.runtimeType != runtimeType) return false;
    
    return other is ShadcnSheetTheme &&
        other.backgroundColor == backgroundColor &&
        other.foregroundColor == foregroundColor &&
        other.borderColor == borderColor &&
        other.borderRadius == borderRadius &&
        other.borderWidth == borderWidth &&
        other.padding == padding &&
        other.elevation == elevation &&
        other.shadowColor == shadowColor &&
        other.backdropColor == backdropColor &&
        other.backdropOpacity == backdropOpacity &&
        other.barrierDismissible == barrierDismissible &&
        other.barrierLabel == barrierLabel &&
        other.maxWidth == maxWidth &&
        other.maxHeight == maxHeight &&
        other.minWidth == minWidth &&
        other.minHeight == minHeight &&
        other.margin == margin &&
        other.alignment == alignment &&
        other.headerBackground == headerBackground &&
        other.headerForeground == headerForeground &&
        other.headerPadding == headerPadding &&
        other.headerHeight == headerHeight &&
        other.headerBorderRadius == headerBorderRadius &&
        other.titleTextStyle == titleTextStyle &&
        other.descriptionTextStyle == descriptionTextStyle &&
        other.contentBackground == contentBackground &&
        other.contentForeground == contentForeground &&
        other.contentPadding == contentPadding &&
        other.contentMaxHeight == contentMaxHeight &&
        other.footerBackground == footerBackground &&
        other.footerForeground == footerForeground &&
        other.footerPadding == footerPadding &&
        other.footerHeight == footerHeight &&
        other.footerBorderRadius == footerBorderRadius &&
        other.footerButtonAlignment == footerButtonAlignment &&
        other.closeButtonBackground == closeButtonBackground &&
        other.closeButtonForeground == closeButtonForeground &&
        other.closeButtonHoverBackground == closeButtonHoverBackground &&
        other.closeButtonHoverForeground == closeButtonHoverForeground &&
        other.closeButtonSize == closeButtonSize &&
        other.closeButtonPadding == closeButtonPadding &&
        other.closeButtonBorderRadius == closeButtonBorderRadius &&
        other.dragHandleColor == dragHandleColor &&
        other.dragHandleWidth == dragHandleWidth &&
        other.dragHandleHeight == dragHandleHeight &&
        other.dragHandleBorderRadius == dragHandleBorderRadius &&
        other.animationDuration == animationDuration &&
        other.animationCurve == animationCurve &&
        other.backdropAnimationDuration == backdropAnimationDuration &&
        other.backdropAnimationCurve == backdropAnimationCurve &&
        other.sideSheetWidth == sideSheetWidth &&
        other.sideSheetMargin == sideSheetMargin &&
        other.isModal == isModal &&
        other.isDismissible == isDismissible &&
        other.enableDrag == enableDrag &&
        other.showDragHandle == showDragHandle &&
        other.showCloseButton == showCloseButton;
  }

  @override
  int get hashCode => Object.hashAll([
    backgroundColor,
    foregroundColor,
    borderColor,
    borderRadius,
    borderWidth,
    padding,
    elevation,
    shadowColor,
    backdropColor,
    backdropOpacity,
    barrierDismissible,
    barrierLabel,
    maxWidth,
    maxHeight,
    minWidth,
    minHeight,
    margin,
    alignment,
    headerBackground,
    headerForeground,
    headerPadding,
    headerHeight,
    headerBorderRadius,
    titleTextStyle,
    descriptionTextStyle,
    contentBackground,
    contentForeground,
    contentPadding,
    contentMaxHeight,
    footerBackground,
    footerForeground,
    footerPadding,
    footerHeight,
    footerBorderRadius,
    footerButtonAlignment,
    closeButtonBackground,
    closeButtonForeground,
    closeButtonHoverBackground,
    closeButtonHoverForeground,
    closeButtonSize,
    closeButtonPadding,
    closeButtonBorderRadius,
    dragHandleColor,
    dragHandleWidth,
    dragHandleHeight,
    dragHandleBorderRadius,
    animationDuration,
    animationCurve,
    backdropAnimationDuration,
    backdropAnimationCurve,
    sideSheetWidth,
    sideSheetMargin,
    isModal,
    isDismissible,
    enableDrag,
    showDragHandle,
    showCloseButton,
  ]);

}

/// Enumeration for sheet positions.
enum ShadcnSheetPosition {
  /// Bottom sheet (default).
  bottom,
  
  /// Top sheet.
  top,
  
  /// Left side sheet.
  left,
  
  /// Right side sheet.
  right,
  
  /// Center modal sheet.
  center,
}