import 'package:flutter/material.dart';
import 'dart:ui' show lerpDouble;
import 'shadcn_theme_extension.dart';
import '../../constants/shadcn_tokens.dart';

/// Theme extension for ShadcnAspectRatio component.
/// 
/// Provides theming properties for aspect ratio containers with flexible
/// constraint handling. Integrates with Material Design theming while
/// providing shadcn-specific styling and behavior options.
class ShadcnAspectRatioTheme extends ShadcnThemeExtension<ShadcnAspectRatioTheme> {
  /// Background color of the aspect ratio container
  final Color? backgroundColor;
  
  /// Border color of the aspect ratio container
  final Color? borderColor;
  
  /// Border width of the aspect ratio container
  final double? borderWidth;
  
  /// Border radius of the aspect ratio container
  final BorderRadius? borderRadius;
  
  /// Padding inside the aspect ratio container
  final EdgeInsets? padding;
  
  /// Margin outside the aspect ratio container
  final EdgeInsets? margin;
  
  /// Box shadows for the aspect ratio container
  final List<BoxShadow>? boxShadow;
  
  /// Clip behavior for the aspect ratio container
  final Clip? clipBehavior;
  
  /// Whether to apply Material elevation styling
  final bool? useMaterialElevation;
  
  /// Material elevation value (if useMaterialElevation is true)
  final double? elevation;
  
  /// Alignment of the child within the aspect ratio container
  final AlignmentGeometry? alignment;
  
  const ShadcnAspectRatioTheme({
    this.backgroundColor,
    this.borderColor,
    this.borderWidth,
    this.borderRadius,
    this.padding,
    this.margin,
    this.boxShadow,
    this.clipBehavior,
    this.useMaterialElevation,
    this.elevation,
    this.alignment,
  });

  @override
  ShadcnAspectRatioTheme copyWith({
    Color? backgroundColor,
    Color? borderColor,
    double? borderWidth,
    BorderRadius? borderRadius,
    EdgeInsets? padding,
    EdgeInsets? margin,
    List<BoxShadow>? boxShadow,
    Clip? clipBehavior,
    bool? useMaterialElevation,
    double? elevation,
    AlignmentGeometry? alignment,
  }) {
    return ShadcnAspectRatioTheme(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      borderColor: borderColor ?? this.borderColor,
      borderWidth: borderWidth ?? this.borderWidth,
      borderRadius: borderRadius ?? this.borderRadius,
      padding: padding ?? this.padding,
      margin: margin ?? this.margin,
      boxShadow: boxShadow ?? this.boxShadow,
      clipBehavior: clipBehavior ?? this.clipBehavior,
      useMaterialElevation: useMaterialElevation ?? this.useMaterialElevation,
      elevation: elevation ?? this.elevation,
      alignment: alignment ?? this.alignment,
    );
  }

  @override
  ShadcnAspectRatioTheme lerp(
    covariant ThemeExtension<ShadcnAspectRatioTheme>? other, 
    double t,
  ) {
    if (other is! ShadcnAspectRatioTheme) {
      return this;
    }

    return ShadcnAspectRatioTheme(
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t),
      borderColor: Color.lerp(borderColor, other.borderColor, t),
      borderWidth: lerpDouble(borderWidth, other.borderWidth, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      padding: EdgeInsets.lerp(padding, other.padding, t),
      margin: EdgeInsets.lerp(margin, other.margin, t),
      boxShadow: BoxShadow.lerpList(boxShadow, other.boxShadow, t),
      clipBehavior: t < 0.5 ? clipBehavior : other.clipBehavior,
      useMaterialElevation: t < 0.5 ? useMaterialElevation : other.useMaterialElevation,
      elevation: lerpDouble(elevation, other.elevation, t),
      alignment: AlignmentGeometry.lerp(alignment, other.alignment, t),
    );
  }

  /// Creates a default theme based on the provided color scheme.
  /// 
  /// Uses shadcn design tokens and Material color schemes to provide
  /// consistent default styling that matches shadcn specifications.
  static ShadcnAspectRatioTheme defaultTheme(ColorScheme colorScheme) {
    return ShadcnAspectRatioTheme(
      backgroundColor: null, // Transparent by default
      borderColor: null, // No border by default
      borderWidth: ShadcnTokens.borderWidth,
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusMd),
      padding: EdgeInsets.zero,
      margin: EdgeInsets.zero,
      boxShadow: null,
      clipBehavior: Clip.antiAlias,
      useMaterialElevation: false,
      elevation: ShadcnTokens.elevationNone,
      alignment: Alignment.center,
    );
  }

  /// Creates a light theme variant with subtle background.
  static ShadcnAspectRatioTheme lightTheme(ColorScheme colorScheme) {
    return ShadcnAspectRatioTheme(
      backgroundColor: colorScheme.surface,
      borderColor: colorScheme.outlineVariant,
      borderWidth: ShadcnTokens.borderWidth,
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusMd),
      padding: EdgeInsets.zero,
      margin: EdgeInsets.zero,
      boxShadow: [
        BoxShadow(
          color: colorScheme.shadow.withOpacity(0.05),
          offset: const Offset(0, 1),
          blurRadius: 2,
        ),
      ],
      clipBehavior: Clip.antiAlias,
      useMaterialElevation: false,
      elevation: ShadcnTokens.elevationSm,
      alignment: Alignment.center,
    );
  }

  /// Creates a dark theme variant.
  static ShadcnAspectRatioTheme darkTheme(ColorScheme colorScheme) {
    return ShadcnAspectRatioTheme(
      backgroundColor: colorScheme.surface,
      borderColor: colorScheme.outline.withOpacity(0.3),
      borderWidth: ShadcnTokens.borderWidth,
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusMd),
      padding: EdgeInsets.zero,
      margin: EdgeInsets.zero,
      boxShadow: [
        BoxShadow(
          color: Colors.black.withOpacity(0.15),
          offset: const Offset(0, 2),
          blurRadius: 4,
        ),
      ],
      clipBehavior: Clip.antiAlias,
      useMaterialElevation: false,
      elevation: ShadcnTokens.elevationSm,
      alignment: Alignment.center,
    );
  }

  /// Gets the resolved background color for the aspect ratio container.
  /// 
  /// Returns null if no background color is specified, allowing the
  /// container to be transparent.
  Color? getBackgroundColor(BuildContext context) {
    if (backgroundColor == null) return null;
    
    return resolveColor(
      context,
      backgroundColor,
      (theme) => theme.colorScheme.surface,
      Colors.transparent,
    );
  }

  /// Gets the resolved border color for the aspect ratio container.
  /// 
  /// Returns null if no border color is specified.
  Color? getBorderColor(BuildContext context) {
    if (borderColor == null) return null;
    
    return resolveColor(
      context,
      borderColor,
      (theme) => theme.colorScheme.outline,
      Colors.grey.shade400,
    );
  }

  /// Gets the resolved border width for the aspect ratio container.
  /// 
  /// Applies visual density adjustments while maintaining minimum
  /// width for visibility when border is present.
  double getBorderWidth(BuildContext context) {
    if (borderColor == null) return 0.0;
    
    return resolveDouble(
      context,
      borderWidth,
      ShadcnTokens.borderWidth,
      applyVerticalDensity: false,
    ).clamp(0.0, double.infinity);
  }

  /// Gets the resolved border radius for the aspect ratio container.
  /// 
  /// Provides consistent border radius resolution with fallback support.
  BorderRadius getBorderRadius(BuildContext context) {
    return resolveBorderRadius(
      context,
      borderRadius,
      BorderRadius.circular(ShadcnTokens.radiusMd),
    );
  }

  /// Gets the resolved padding for the aspect ratio container.
  /// 
  /// Applies visual density adjustments to ensure proper spacing.
  EdgeInsets getPadding(BuildContext context) {
    return resolveSpacing(
      context,
      padding,
      EdgeInsets.zero,
    );
  }

  /// Gets the resolved margin for the aspect ratio container.
  /// 
  /// Applies visual density adjustments to ensure proper spacing.
  EdgeInsets getMargin(BuildContext context) {
    return resolveSpacing(
      context,
      margin,
      EdgeInsets.zero,
    );
  }

  /// Gets the resolved clip behavior for the aspect ratio container.
  /// 
  /// Provides sensible defaults for different styling scenarios.
  Clip getClipBehavior(BuildContext context) {
    final hasRadius = getBorderRadius(context) != BorderRadius.zero;
    return clipBehavior ?? (hasRadius ? Clip.antiAlias : Clip.none);
  }

  /// Gets whether to use Material elevation styling.
  /// 
  /// Determines if the container should use Material elevation or
  /// custom box shadows.
  bool getUseMaterialElevation(BuildContext context) {
    return useMaterialElevation ?? false;
  }

  /// Gets the resolved elevation value.
  /// 
  /// Applies visual density adjustments while maintaining reasonable bounds.
  double getElevation(BuildContext context) {
    return resolveDouble(
      context,
      elevation,
      ShadcnTokens.elevationNone,
      applyVerticalDensity: true,
    ).clamp(0.0, 24.0); // Material design max elevation
  }

  /// Gets the resolved alignment for the child within the container.
  /// 
  /// Provides consistent alignment with fallback support.
  AlignmentGeometry getAlignment(BuildContext context) {
    return alignment ?? Alignment.center;
  }

  /// Gets the resolved box shadows for the aspect ratio container.
  /// 
  /// Returns null if no box shadows are specified or if Material
  /// elevation is being used instead.
  List<BoxShadow>? getBoxShadow(BuildContext context) {
    if (getUseMaterialElevation(context)) return null;
    return boxShadow;
  }

  @override
  bool validate({bool throwOnError = false}) {
    try {
      // Validate border width
      if (borderWidth != null && borderWidth! < 0) {
        if (throwOnError) {
          throw ArgumentError('Aspect ratio border width must be non-negative');
        }
        return false;
      }

      // Validate elevation
      if (elevation != null && elevation! < 0) {
        if (throwOnError) {
          throw ArgumentError('Aspect ratio elevation must be non-negative');
        }
        return false;
      }

      // Validate box shadow values
      if (boxShadow != null) {
        for (final shadow in boxShadow!) {
          if (shadow.blurRadius < 0) {
            if (throwOnError) {
              throw ArgumentError('Box shadow blur radius must be non-negative');
            }
            return false;
          }
        }
      }

      return true;
    } catch (e) {
      if (throwOnError) rethrow;
      return false;
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ShadcnAspectRatioTheme &&
          runtimeType == other.runtimeType &&
          backgroundColor == other.backgroundColor &&
          borderColor == other.borderColor &&
          borderWidth == other.borderWidth &&
          borderRadius == other.borderRadius &&
          padding == other.padding &&
          margin == other.margin &&
          _listEquals(boxShadow, other.boxShadow) &&
          clipBehavior == other.clipBehavior &&
          useMaterialElevation == other.useMaterialElevation &&
          elevation == other.elevation &&
          alignment == other.alignment;

  @override
  int get hashCode => Object.hash(
      backgroundColor,
      borderColor,
      borderWidth,
      borderRadius,
      padding,
      margin,
      boxShadow,
      clipBehavior,
      useMaterialElevation,
      elevation,
      alignment,
    );

  @override
  String toString() {
    return 'ShadcnAspectRatioTheme('
        'backgroundColor: $backgroundColor, '
        'borderColor: $borderColor, '
        'borderWidth: $borderWidth, '
        'borderRadius: $borderRadius, '
        'padding: $padding, '
        'margin: $margin, '
        'boxShadow: $boxShadow, '
        'clipBehavior: $clipBehavior, '
        'useMaterialElevation: $useMaterialElevation, '
        'elevation: $elevation, '
        'alignment: $alignment'
        ')';
  }

  /// Helper method to compare lists of box shadows
  bool _listEquals(List<BoxShadow>? a, List<BoxShadow>? b) {
    if (a == null) return b == null;
    if (b == null) return false;
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }
}

/// Common aspect ratios used in design and media.
/// 
/// Provides pre-defined aspect ratios for common use cases,
/// following design system conventions and media standards.
class ShadcnAspectRatios {
  ShadcnAspectRatios._();

  /// Square aspect ratio (1:1)
  static const double square = 1.0;

  /// Landscape aspect ratio (4:3)
  static const double landscape = 4.0 / 3.0;

  /// Portrait aspect ratio (3:4)
  static const double portrait = 3.0 / 4.0;

  /// Widescreen aspect ratio (16:9)
  static const double widescreen = 16.0 / 9.0;

  /// Ultra-wide aspect ratio (21:9)
  static const double ultraWide = 21.0 / 9.0;

  /// Golden ratio (1.618:1)
  static const double golden = 1.618;

  /// Video aspect ratio (16:10)
  static const double video = 16.0 / 10.0;

  /// Photo aspect ratio (3:2)
  static const double photo = 3.0 / 2.0;

  /// A4 paper aspect ratio
  static const double a4 = 210.0 / 297.0;

  /// iPhone aspect ratio (19.5:9)
  static const double iphone = 19.5 / 9.0;

  /// Creates a custom aspect ratio from width and height.
  static double custom(double width, double height) {
    assert(width > 0, 'Width must be positive');
    assert(height > 0, 'Height must be positive');
    return width / height;
  }

  /// Gets the inverse of an aspect ratio (useful for rotating layouts).
  static double inverse(double aspectRatio) {
    assert(aspectRatio > 0, 'Aspect ratio must be positive');
    return 1.0 / aspectRatio;
  }
}

/// Extension for convenient aspect ratio theme access.
/// 
/// Provides convenient methods for accessing aspect ratio theme properties
/// from the current theme context.
extension ShadcnAspectRatioThemeExtension on ThemeData {
  /// Gets the aspect ratio theme extension from this theme data.
  /// 
  /// Returns null if no aspect ratio theme is configured.
  ShadcnAspectRatioTheme? get aspectRatioTheme => extension<ShadcnAspectRatioTheme>();

  /// Gets the aspect ratio theme extension or creates a default one.
  /// 
  /// Ensures a valid aspect ratio theme is always available.
  ShadcnAspectRatioTheme aspectRatioThemeOrDefault() =>
      aspectRatioTheme ?? ShadcnAspectRatioTheme.defaultTheme(colorScheme);
}