import 'package:flutter/material.dart';
import '../../constants/shadcn_tokens.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for the ShadcnSlider component.
class ShadcnSliderTheme extends ShadcnThemeExtension<ShadcnSliderTheme> {
  final Color? trackColor;
  final Color? activeTrackColor;
  final Color? inactiveTrackColor;
  final Color? thumbColor;
  final Color? disabledTrackColor;
  final Color? disabledThumbColor;
  final double? trackHeight;
  final double? thumbRadius;
  final double? thumbElevation;
  final EdgeInsets? padding;
  final bool? showValue;
  final TextStyle? valueTextStyle;
  final Color? valueBackgroundColor;
  final BorderRadius? valueBorderRadius;
  final EdgeInsets? valuePadding;

  const ShadcnSliderTheme({
    this.trackColor,
    this.activeTrackColor,
    this.inactiveTrackColor,
    this.thumbColor,
    this.disabledTrackColor,
    this.disabledThumbColor,
    this.trackHeight,
    this.thumbRadius,
    this.thumbElevation,
    this.padding,
    this.showValue,
    this.valueTextStyle,
    this.valueBackgroundColor,
    this.valueBorderRadius,
    this.valuePadding,
  });

  @override
  ShadcnSliderTheme copyWith({
    Color? trackColor,
    Color? activeTrackColor,
    Color? inactiveTrackColor,
    Color? thumbColor,
    Color? disabledTrackColor,
    Color? disabledThumbColor,
    double? trackHeight,
    double? thumbRadius,
    double? thumbElevation,
    EdgeInsets? padding,
    bool? showValue,
    TextStyle? valueTextStyle,
    Color? valueBackgroundColor,
    BorderRadius? valueBorderRadius,
    EdgeInsets? valuePadding,
  }) {
    return ShadcnSliderTheme(
      trackColor: trackColor ?? this.trackColor,
      activeTrackColor: activeTrackColor ?? this.activeTrackColor,
      inactiveTrackColor: inactiveTrackColor ?? this.inactiveTrackColor,
      thumbColor: thumbColor ?? this.thumbColor,
      disabledTrackColor: disabledTrackColor ?? this.disabledTrackColor,
      disabledThumbColor: disabledThumbColor ?? this.disabledThumbColor,
      trackHeight: trackHeight ?? this.trackHeight,
      thumbRadius: thumbRadius ?? this.thumbRadius,
      thumbElevation: thumbElevation ?? this.thumbElevation,
      padding: padding ?? this.padding,
      showValue: showValue ?? this.showValue,
      valueTextStyle: valueTextStyle ?? this.valueTextStyle,
      valueBackgroundColor: valueBackgroundColor ?? this.valueBackgroundColor,
      valueBorderRadius: valueBorderRadius ?? this.valueBorderRadius,
      valuePadding: valuePadding ?? this.valuePadding,
    );
  }

  @override
  ShadcnSliderTheme lerp(ShadcnSliderTheme? other, double t) {
    if (other is! ShadcnSliderTheme) return this;
    return ShadcnSliderTheme(
      trackColor: Color.lerp(trackColor, other.trackColor, t),
      activeTrackColor: Color.lerp(activeTrackColor, other.activeTrackColor, t),
      inactiveTrackColor: Color.lerp(inactiveTrackColor, other.inactiveTrackColor, t),
      thumbColor: Color.lerp(thumbColor, other.thumbColor, t),
      disabledTrackColor: Color.lerp(disabledTrackColor, other.disabledTrackColor, t),
      disabledThumbColor: Color.lerp(disabledThumbColor, other.disabledThumbColor, t),
      trackHeight: lerpDouble(trackHeight, other.trackHeight, t),
      thumbRadius: lerpDouble(thumbRadius, other.thumbRadius, t),
      thumbElevation: lerpDouble(thumbElevation, other.thumbElevation, t),
      padding: EdgeInsets.lerp(padding, other.padding, t),
      showValue: t < 0.5 ? showValue : other.showValue,
      valueTextStyle: TextStyle.lerp(valueTextStyle, other.valueTextStyle, t),
      valueBackgroundColor: Color.lerp(valueBackgroundColor, other.valueBackgroundColor, t),
      valueBorderRadius: BorderRadius.lerp(valueBorderRadius, other.valueBorderRadius, t),
      valuePadding: EdgeInsets.lerp(valuePadding, other.valuePadding, t),
    );
  }

  static ShadcnSliderTheme defaultTheme(ColorScheme colorScheme) {
    return ShadcnSliderTheme(
      trackColor: colorScheme.outline,
      activeTrackColor: colorScheme.primary,
      inactiveTrackColor: colorScheme.surfaceVariant,
      thumbColor: colorScheme.primary,
      disabledTrackColor: colorScheme.onSurface.withOpacity(0.12),
      disabledThumbColor: colorScheme.onSurface.withOpacity(0.38),
      trackHeight: 4.0,
      thumbRadius: 10.0,
      thumbElevation: 1.0,
      padding: const EdgeInsets.symmetric(horizontal: ShadcnTokens.spacing2),
      showValue: false,
      valueTextStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeSm,
        fontWeight: ShadcnTokens.fontWeightMedium,
        color: colorScheme.onSurface,
      ),
      valueBackgroundColor: colorScheme.surface,
      valueBorderRadius: BorderRadius.circular(ShadcnTokens.radiusSm),
      valuePadding: const EdgeInsets.symmetric(
        horizontal: ShadcnTokens.spacing2,
        vertical: ShadcnTokens.spacing1,
      ),
    );
  }
}

double? lerpDouble(double? a, double? b, double t) {
  if (a == null && b == null) return null;
  a ??= 0.0;
  b ??= 0.0;
  return a + (b - a) * t;
}