import 'dart:ui' show lerpDouble;
import 'package:flutter/material.dart';
import 'shadcn_theme_extension.dart';
import '../../constants/shadcn_tokens.dart';
import '../color_schemes/shadcn_color_scheme.dart';

/// Theme configuration for ShadcnCalendar and ShadcnDatePicker components.
class ShadcnCalendarTheme extends ShadcnThemeExtension<ShadcnCalendarTheme> {
  final Color? backgroundColor;
  final Color? borderColor;
  final double? borderWidth;
  final BorderRadius? borderRadius;
  final EdgeInsets? padding;
  final Color? dateCellTextColor;
  final Color? dateCellBackgroundColor;
  final double? dateCellSize;
  final BorderRadius? dateCellBorderRadius;
  final Color? selectedDateTextColor;
  final Color? selectedDateBackgroundColor;
  final Color? selectedDateBorderColor;
  final Color? todayTextColor;
  final Color? todayBorderColor;
  final Color? disabledDateTextColor;
  final double? disabledDateOpacity;

  const ShadcnCalendarTheme({
    this.backgroundColor,
    this.borderColor,
    this.borderWidth,
    this.borderRadius,
    this.padding,
    this.dateCellTextColor,
    this.dateCellBackgroundColor,
    this.dateCellSize,
    this.dateCellBorderRadius,
    this.selectedDateTextColor,
    this.selectedDateBackgroundColor,
    this.selectedDateBorderColor,
    this.todayTextColor,
    this.todayBorderColor,
    this.disabledDateTextColor,
    this.disabledDateOpacity,
  });

  factory ShadcnCalendarTheme.defaultTheme(ColorScheme colorScheme) {
    final shadcnColors = ShadcnColorScheme.fromMaterial(colorScheme);
    
    return ShadcnCalendarTheme(
      backgroundColor: shadcnColors.card,
      borderColor: shadcnColors.border,
      borderWidth: ShadcnTokens.borderWidth,
      borderRadius: ShadcnTokens.borderRadius(ShadcnTokens.radiusMd),
      padding: ShadcnTokens.paddingAll(ShadcnTokens.spacing4),
      dateCellTextColor: shadcnColors.foreground,
      dateCellBackgroundColor: Colors.transparent,
      dateCellSize: 32.0,
      dateCellBorderRadius: ShadcnTokens.borderRadius(ShadcnTokens.radiusSm),
      selectedDateTextColor: colorScheme.onPrimary,
      selectedDateBackgroundColor: colorScheme.primary,
      selectedDateBorderColor: colorScheme.primary,
      todayTextColor: colorScheme.primary,
      todayBorderColor: colorScheme.primary,
      disabledDateTextColor: shadcnColors.mutedForeground,
      disabledDateOpacity: ShadcnTokens.opacityDisabled,
    );
  }

  @override
  ShadcnCalendarTheme copyWith({
    Color? backgroundColor,
    Color? borderColor,
    double? borderWidth,
    BorderRadius? borderRadius,
    EdgeInsets? padding,
    Color? dateCellTextColor,
    Color? dateCellBackgroundColor,
    double? dateCellSize,
    BorderRadius? dateCellBorderRadius,
    Color? selectedDateTextColor,
    Color? selectedDateBackgroundColor,
    Color? selectedDateBorderColor,
    Color? todayTextColor,
    Color? todayBorderColor,
    Color? disabledDateTextColor,
    double? disabledDateOpacity,
  }) {
    return ShadcnCalendarTheme(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      borderColor: borderColor ?? this.borderColor,
      borderWidth: borderWidth ?? this.borderWidth,
      borderRadius: borderRadius ?? this.borderRadius,
      padding: padding ?? this.padding,
      dateCellTextColor: dateCellTextColor ?? this.dateCellTextColor,
      dateCellBackgroundColor: dateCellBackgroundColor ?? this.dateCellBackgroundColor,
      dateCellSize: dateCellSize ?? this.dateCellSize,
      dateCellBorderRadius: dateCellBorderRadius ?? this.dateCellBorderRadius,
      selectedDateTextColor: selectedDateTextColor ?? this.selectedDateTextColor,
      selectedDateBackgroundColor: selectedDateBackgroundColor ?? this.selectedDateBackgroundColor,
      selectedDateBorderColor: selectedDateBorderColor ?? this.selectedDateBorderColor,
      todayTextColor: todayTextColor ?? this.todayTextColor,
      todayBorderColor: todayBorderColor ?? this.todayBorderColor,
      disabledDateTextColor: disabledDateTextColor ?? this.disabledDateTextColor,
      disabledDateOpacity: disabledDateOpacity ?? this.disabledDateOpacity,
    );
  }

  @override
  ShadcnCalendarTheme lerp(ShadcnCalendarTheme? other, double t) {
    if (other is! ShadcnCalendarTheme) return this;
    return ShadcnCalendarTheme(
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t),
      borderColor: Color.lerp(borderColor, other.borderColor, t),
      borderWidth: lerpDouble(borderWidth, other.borderWidth, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      padding: EdgeInsets.lerp(padding, other.padding, t),
      dateCellTextColor: Color.lerp(dateCellTextColor, other.dateCellTextColor, t),
      dateCellBackgroundColor: Color.lerp(dateCellBackgroundColor, other.dateCellBackgroundColor, t),
      dateCellSize: lerpDouble(dateCellSize, other.dateCellSize, t),
      dateCellBorderRadius: BorderRadius.lerp(dateCellBorderRadius, other.dateCellBorderRadius, t),
      selectedDateTextColor: Color.lerp(selectedDateTextColor, other.selectedDateTextColor, t),
      selectedDateBackgroundColor: Color.lerp(selectedDateBackgroundColor, other.selectedDateBackgroundColor, t),
      selectedDateBorderColor: Color.lerp(selectedDateBorderColor, other.selectedDateBorderColor, t),
      todayTextColor: Color.lerp(todayTextColor, other.todayTextColor, t),
      todayBorderColor: Color.lerp(todayBorderColor, other.todayBorderColor, t),
      disabledDateTextColor: Color.lerp(disabledDateTextColor, other.disabledDateTextColor, t),
      disabledDateOpacity: lerpDouble(disabledDateOpacity, other.disabledDateOpacity, t),
    );
  }

  @override
  bool validate({bool throwOnError = false}) => true;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ShadcnCalendarTheme &&
        other.backgroundColor == backgroundColor &&
        other.borderColor == borderColor &&
        other.selectedDateBackgroundColor == selectedDateBackgroundColor &&
        other.dateCellSize == dateCellSize;
  }

  @override
  int get hashCode => Object.hash(backgroundColor, borderColor, selectedDateBackgroundColor, dateCellSize);
}