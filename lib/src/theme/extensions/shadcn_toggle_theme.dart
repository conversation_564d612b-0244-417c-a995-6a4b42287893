import 'package:flutter/material.dart';
import '../../constants/shadcn_tokens.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for the ShadcnToggle component.
class ShadcnToggleTheme extends ShadcnThemeExtension<ShadcnToggleTheme> {
  final Color? backgroundColor;
  final Color? foregroundColor;
  final Color? selectedBackgroundColor;
  final Color? selectedForegroundColor;
  final Color? borderColor;
  final Color? selectedBorderColor;
  final Color? hoverBackgroundColor;
  final Color? disabledBackgroundColor;
  final Color? disabledForegroundColor;
  final double? height;
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;
  final double? borderWidth;
  final TextStyle? textStyle;
  final double? iconSize;
  final double? iconSpacing;

  const ShadcnToggleTheme({
    this.backgroundColor,
    this.foregroundColor,
    this.selectedBackgroundColor,
    this.selectedForegroundColor,
    this.borderColor,
    this.selectedBorderColor,
    this.hoverBackgroundColor,
    this.disabledBackgroundColor,
    this.disabledForegroundColor,
    this.height,
    this.padding,
    this.borderRadius,
    this.borderWidth,
    this.textStyle,
    this.iconSize,
    this.iconSpacing,
  });

  @override
  ShadcnToggleTheme copyWith({
    Color? backgroundColor,
    Color? foregroundColor,
    Color? selectedBackgroundColor,
    Color? selectedForegroundColor,
    Color? borderColor,
    Color? selectedBorderColor,
    Color? hoverBackgroundColor,
    Color? disabledBackgroundColor,
    Color? disabledForegroundColor,
    double? height,
    EdgeInsets? padding,
    BorderRadius? borderRadius,
    double? borderWidth,
    TextStyle? textStyle,
    double? iconSize,
    double? iconSpacing,
  }) {
    return ShadcnToggleTheme(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      foregroundColor: foregroundColor ?? this.foregroundColor,
      selectedBackgroundColor: selectedBackgroundColor ?? this.selectedBackgroundColor,
      selectedForegroundColor: selectedForegroundColor ?? this.selectedForegroundColor,
      borderColor: borderColor ?? this.borderColor,
      selectedBorderColor: selectedBorderColor ?? this.selectedBorderColor,
      hoverBackgroundColor: hoverBackgroundColor ?? this.hoverBackgroundColor,
      disabledBackgroundColor: disabledBackgroundColor ?? this.disabledBackgroundColor,
      disabledForegroundColor: disabledForegroundColor ?? this.disabledForegroundColor,
      height: height ?? this.height,
      padding: padding ?? this.padding,
      borderRadius: borderRadius ?? this.borderRadius,
      borderWidth: borderWidth ?? this.borderWidth,
      textStyle: textStyle ?? this.textStyle,
      iconSize: iconSize ?? this.iconSize,
      iconSpacing: iconSpacing ?? this.iconSpacing,
    );
  }

  @override
  ShadcnToggleTheme lerp(ShadcnToggleTheme? other, double t) {
    if (other is! ShadcnToggleTheme) return this;
    return ShadcnToggleTheme(
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t),
      foregroundColor: Color.lerp(foregroundColor, other.foregroundColor, t),
      selectedBackgroundColor: Color.lerp(selectedBackgroundColor, other.selectedBackgroundColor, t),
      selectedForegroundColor: Color.lerp(selectedForegroundColor, other.selectedForegroundColor, t),
      borderColor: Color.lerp(borderColor, other.borderColor, t),
      selectedBorderColor: Color.lerp(selectedBorderColor, other.selectedBorderColor, t),
      hoverBackgroundColor: Color.lerp(hoverBackgroundColor, other.hoverBackgroundColor, t),
      disabledBackgroundColor: Color.lerp(disabledBackgroundColor, other.disabledBackgroundColor, t),
      disabledForegroundColor: Color.lerp(disabledForegroundColor, other.disabledForegroundColor, t),
      height: lerpDouble(height, other.height, t),
      padding: EdgeInsets.lerp(padding, other.padding, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      borderWidth: lerpDouble(borderWidth, other.borderWidth, t),
      textStyle: t < 0.5 ? textStyle : other.textStyle,
      iconSize: lerpDouble(iconSize, other.iconSize, t),
      iconSpacing: lerpDouble(iconSpacing, other.iconSpacing, t),
    );
  }

  static ShadcnToggleTheme defaultTheme(ColorScheme colorScheme) {
    return ShadcnToggleTheme(
      backgroundColor: Colors.transparent,
      foregroundColor: colorScheme.onSurface,
      selectedBackgroundColor: colorScheme.primary,
      selectedForegroundColor: colorScheme.onPrimary,
      borderColor: colorScheme.outline,
      selectedBorderColor: colorScheme.primary,
      hoverBackgroundColor: colorScheme.onSurface.withOpacity(0.04),
      disabledBackgroundColor: colorScheme.onSurface.withOpacity(0.04),
      disabledForegroundColor: colorScheme.onSurface.withOpacity(0.38),
      height: ShadcnTokens.buttonHeightMd,
      padding: const EdgeInsets.symmetric(
        horizontal: ShadcnTokens.spacing3,
        vertical: ShadcnTokens.spacing2,
      ),
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusMd),
      borderWidth: ShadcnTokens.borderWidth,
      textStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightMedium,
      ),
      iconSize: ShadcnTokens.iconSizeMd,
      iconSpacing: ShadcnTokens.spacing2,
    );
  }
}

double? lerpDouble(double? a, double? b, double t) {
  if (a == null && b == null) return null;
  a ??= 0.0;
  b ??= 0.0;
  return a + (b - a) * t;
}