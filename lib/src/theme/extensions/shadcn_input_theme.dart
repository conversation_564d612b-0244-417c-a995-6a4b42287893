import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'shadcn_theme_extension.dart';
import '../../constants/shadcn_tokens.dart';

/// Theme extension for ShadcnInput component styling.
/// 
/// This theme extension provides comprehensive styling options for input components
/// including background colors, borders, text styling, and state-specific appearances.
/// It follows the shadcn design system while maintaining Material Design compatibility.
/// 
/// The theme automatically handles light/dark mode switching and provides
/// sensible defaults based on the Material ColorScheme when custom values
/// are not provided.
class ShadcnInputTheme extends ShadcnThemeExtension<ShadcnInputTheme> {
  // Background colors
  /// Background color for the input field
  final Color? backgroundColor;
  
  /// Background color for disabled state
  final Color? disabledBackgroundColor;
  
  /// Background color for error state  
  final Color? errorBackgroundColor;

  // Border styling
  /// Default border color
  final Color? borderColor;
  
  /// Border color when focused
  final Color? focusedBorderColor;
  
  /// Border color when hovered
  final Color? hoveredBorderColor;
  
  /// Border color for error state
  final Color? errorBorderColor;
  
  /// Border color for disabled state
  final Color? disabledBorderColor;
  
  /// Border width
  final double? borderWidth;
  
  /// Border radius for the input field
  final BorderRadius? borderRadius;

  // Text styling
  /// Text color for input content
  final Color? textColor;
  
  /// Text color for placeholder text
  final Color? placeholderColor;
  
  /// Text color for disabled state
  final Color? disabledTextColor;
  
  /// Text color for error state
  final Color? errorTextColor;
  
  /// Text style for input content
  final TextStyle? textStyle;
  
  /// Text style for placeholder text
  final TextStyle? placeholderStyle;

  // Size variants
  /// Height for small input variant
  final double? smallHeight;
  
  /// Height for medium input variant (default)
  final double? mediumHeight;
  
  /// Height for large input variant
  final double? largeHeight;

  // Padding
  /// Content padding for small inputs
  final EdgeInsets? smallPadding;
  
  /// Content padding for medium inputs (default)
  final EdgeInsets? mediumPadding;
  
  /// Content padding for large inputs
  final EdgeInsets? largePadding;

  // Icon styling
  /// Color for prefix/suffix icons
  final Color? iconColor;
  
  /// Color for prefix/suffix icons in disabled state
  final Color? disabledIconColor;
  
  /// Size for prefix/suffix icons in small inputs
  final double? smallIconSize;
  
  /// Size for prefix/suffix icons in medium inputs
  final double? mediumIconSize;
  
  /// Size for prefix/suffix icons in large inputs
  final double? largeIconSize;

  // Error styling
  /// Text style for error messages
  final TextStyle? errorTextStyle;
  
  /// Color for error text
  final Color? errorMessageColor;

  // Animation
  /// Duration for state transition animations
  final Duration? animationDuration;
  
  /// Animation curve for state transitions
  final Curve? animationCurve;

  // Layout
  /// Spacing between icon and text
  final double? iconSpacing;
  
  /// Height for error text area
  final double? errorTextHeight;

  const ShadcnInputTheme({
    // Background colors
    this.backgroundColor,
    this.disabledBackgroundColor,
    this.errorBackgroundColor,
    
    // Border styling
    this.borderColor,
    this.focusedBorderColor,
    this.hoveredBorderColor,
    this.errorBorderColor,
    this.disabledBorderColor,
    this.borderWidth,
    this.borderRadius,
    
    // Text styling
    this.textColor,
    this.placeholderColor,
    this.disabledTextColor,
    this.errorTextColor,
    this.textStyle,
    this.placeholderStyle,
    
    // Size variants
    this.smallHeight,
    this.mediumHeight,
    this.largeHeight,
    
    // Padding
    this.smallPadding,
    this.mediumPadding,
    this.largePadding,
    
    // Icon styling
    this.iconColor,
    this.disabledIconColor,
    this.smallIconSize,
    this.mediumIconSize,
    this.largeIconSize,
    
    // Error styling
    this.errorTextStyle,
    this.errorMessageColor,
    
    // Animation
    this.animationDuration,
    this.animationCurve,
    
    // Layout
    this.iconSpacing,
    this.errorTextHeight,
  });

  /// Creates a default input theme based on the provided color scheme.
  /// 
  /// This factory method generates a complete input theme using Material Design
  /// principles and shadcn design tokens. It provides sensible defaults that
  /// work well in both light and dark themes.
  static ShadcnInputTheme defaultTheme(ColorScheme colorScheme) {
    final isDark = colorScheme.brightness == Brightness.dark;
    
    return ShadcnInputTheme(
      // Background colors
      backgroundColor: colorScheme.surface,
      disabledBackgroundColor: isDark 
          ? colorScheme.surface.withOpacity(0.12)
          : colorScheme.onSurface.withOpacity(0.04),
      errorBackgroundColor: colorScheme.surface,
      
      // Border styling
      borderColor: colorScheme.outline,
      focusedBorderColor: colorScheme.primary,
      hoveredBorderColor: colorScheme.onSurface,
      errorBorderColor: colorScheme.error,
      disabledBorderColor: colorScheme.onSurface.withOpacity(0.12),
      borderWidth: ShadcnTokens.borderWidth,
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusMd),
      
      // Text styling
      textColor: colorScheme.onSurface,
      placeholderColor: colorScheme.onSurface.withOpacity(0.6),
      disabledTextColor: colorScheme.onSurface.withOpacity(0.38),
      errorTextColor: colorScheme.onSurface,
      textStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightNormal,
        height: ShadcnTokens.lineHeightNormal,
      ),
      placeholderStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightNormal,
        height: ShadcnTokens.lineHeightNormal,
      ),
      
      // Size variants (shadcn-standard heights)
      smallHeight: ShadcnTokens.inputHeightSm,
      mediumHeight: ShadcnTokens.inputHeightMd,
      largeHeight: ShadcnTokens.inputHeightLg,
      
      // Padding
      smallPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      mediumPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      largePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      
      // Icon styling
      iconColor: colorScheme.onSurface.withOpacity(0.6),
      disabledIconColor: colorScheme.onSurface.withOpacity(0.38),
      smallIconSize: ShadcnTokens.iconSizeSm,
      mediumIconSize: ShadcnTokens.iconSizeMd,
      largeIconSize: ShadcnTokens.iconSizeLg,
      
      // Error styling
      errorTextStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeSm,
        fontWeight: ShadcnTokens.fontWeightNormal,
        height: ShadcnTokens.lineHeightTight,
      ),
      errorMessageColor: colorScheme.error,
      
      // Animation
      animationDuration: ShadcnTokens.durationFast,
      animationCurve: Curves.easeInOut,
      
      // Layout
      iconSpacing: ShadcnTokens.spacing2,
      errorTextHeight: 20.0,
    );
  }

  @override
  ShadcnInputTheme copyWith({
    // Background colors
    Color? backgroundColor,
    Color? disabledBackgroundColor,
    Color? errorBackgroundColor,
    
    // Border styling
    Color? borderColor,
    Color? focusedBorderColor,
    Color? hoveredBorderColor,
    Color? errorBorderColor,
    Color? disabledBorderColor,
    double? borderWidth,
    BorderRadius? borderRadius,
    
    // Text styling
    Color? textColor,
    Color? placeholderColor,
    Color? disabledTextColor,
    Color? errorTextColor,
    TextStyle? textStyle,
    TextStyle? placeholderStyle,
    
    // Size variants
    double? smallHeight,
    double? mediumHeight,
    double? largeHeight,
    
    // Padding
    EdgeInsets? smallPadding,
    EdgeInsets? mediumPadding,
    EdgeInsets? largePadding,
    
    // Icon styling
    Color? iconColor,
    Color? disabledIconColor,
    double? smallIconSize,
    double? mediumIconSize,
    double? largeIconSize,
    
    // Error styling
    TextStyle? errorTextStyle,
    Color? errorMessageColor,
    
    // Animation
    Duration? animationDuration,
    Curve? animationCurve,
    
    // Layout
    double? iconSpacing,
    double? errorTextHeight,
  }) {
    return ShadcnInputTheme(
      // Background colors
      backgroundColor: backgroundColor ?? this.backgroundColor,
      disabledBackgroundColor: disabledBackgroundColor ?? this.disabledBackgroundColor,
      errorBackgroundColor: errorBackgroundColor ?? this.errorBackgroundColor,
      
      // Border styling
      borderColor: borderColor ?? this.borderColor,
      focusedBorderColor: focusedBorderColor ?? this.focusedBorderColor,
      hoveredBorderColor: hoveredBorderColor ?? this.hoveredBorderColor,
      errorBorderColor: errorBorderColor ?? this.errorBorderColor,
      disabledBorderColor: disabledBorderColor ?? this.disabledBorderColor,
      borderWidth: borderWidth ?? this.borderWidth,
      borderRadius: borderRadius ?? this.borderRadius,
      
      // Text styling
      textColor: textColor ?? this.textColor,
      placeholderColor: placeholderColor ?? this.placeholderColor,
      disabledTextColor: disabledTextColor ?? this.disabledTextColor,
      errorTextColor: errorTextColor ?? this.errorTextColor,
      textStyle: textStyle ?? this.textStyle,
      placeholderStyle: placeholderStyle ?? this.placeholderStyle,
      
      // Size variants
      smallHeight: smallHeight ?? this.smallHeight,
      mediumHeight: mediumHeight ?? this.mediumHeight,
      largeHeight: largeHeight ?? this.largeHeight,
      
      // Padding
      smallPadding: smallPadding ?? this.smallPadding,
      mediumPadding: mediumPadding ?? this.mediumPadding,
      largePadding: largePadding ?? this.largePadding,
      
      // Icon styling
      iconColor: iconColor ?? this.iconColor,
      disabledIconColor: disabledIconColor ?? this.disabledIconColor,
      smallIconSize: smallIconSize ?? this.smallIconSize,
      mediumIconSize: mediumIconSize ?? this.mediumIconSize,
      largeIconSize: largeIconSize ?? this.largeIconSize,
      
      // Error styling
      errorTextStyle: errorTextStyle ?? this.errorTextStyle,
      errorMessageColor: errorMessageColor ?? this.errorMessageColor,
      
      // Animation
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
      
      // Layout
      iconSpacing: iconSpacing ?? this.iconSpacing,
      errorTextHeight: errorTextHeight ?? this.errorTextHeight,
    );
  }

  @override
  ShadcnInputTheme lerp(ShadcnInputTheme? other, double t) {
    if (other == null) return this;

    return ShadcnInputTheme(
      // Background colors
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t),
      disabledBackgroundColor: Color.lerp(disabledBackgroundColor, other.disabledBackgroundColor, t),
      errorBackgroundColor: Color.lerp(errorBackgroundColor, other.errorBackgroundColor, t),
      
      // Border styling
      borderColor: Color.lerp(borderColor, other.borderColor, t),
      focusedBorderColor: Color.lerp(focusedBorderColor, other.focusedBorderColor, t),
      hoveredBorderColor: Color.lerp(hoveredBorderColor, other.hoveredBorderColor, t),
      errorBorderColor: Color.lerp(errorBorderColor, other.errorBorderColor, t),
      disabledBorderColor: Color.lerp(disabledBorderColor, other.disabledBorderColor, t),
      borderWidth: lerpDouble(borderWidth, other.borderWidth, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      
      // Text styling
      textColor: Color.lerp(textColor, other.textColor, t),
      placeholderColor: Color.lerp(placeholderColor, other.placeholderColor, t),
      disabledTextColor: Color.lerp(disabledTextColor, other.disabledTextColor, t),
      errorTextColor: Color.lerp(errorTextColor, other.errorTextColor, t),
      textStyle: TextStyle.lerp(textStyle, other.textStyle, t),
      placeholderStyle: TextStyle.lerp(placeholderStyle, other.placeholderStyle, t),
      
      // Size variants
      smallHeight: lerpDouble(smallHeight, other.smallHeight, t),
      mediumHeight: lerpDouble(mediumHeight, other.mediumHeight, t),
      largeHeight: lerpDouble(largeHeight, other.largeHeight, t),
      
      // Padding
      smallPadding: EdgeInsets.lerp(smallPadding, other.smallPadding, t),
      mediumPadding: EdgeInsets.lerp(mediumPadding, other.mediumPadding, t),
      largePadding: EdgeInsets.lerp(largePadding, other.largePadding, t),
      
      // Icon styling
      iconColor: Color.lerp(iconColor, other.iconColor, t),
      disabledIconColor: Color.lerp(disabledIconColor, other.disabledIconColor, t),
      smallIconSize: lerpDouble(smallIconSize, other.smallIconSize, t),
      mediumIconSize: lerpDouble(mediumIconSize, other.mediumIconSize, t),
      largeIconSize: lerpDouble(largeIconSize, other.largeIconSize, t),
      
      // Error styling
      errorTextStyle: TextStyle.lerp(errorTextStyle, other.errorTextStyle, t),
      errorMessageColor: Color.lerp(errorMessageColor, other.errorMessageColor, t),
      
      // Animation - these don't lerp, just use the target values
      animationDuration: t < 0.5 ? animationDuration : other.animationDuration,
      animationCurve: t < 0.5 ? animationCurve : other.animationCurve,
      
      // Layout
      iconSpacing: lerpDouble(iconSpacing, other.iconSpacing, t),
      errorTextHeight: lerpDouble(errorTextHeight, other.errorTextHeight, t),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (runtimeType != other.runtimeType) return false;

    return other is ShadcnInputTheme &&
        // Background colors
        other.backgroundColor == backgroundColor &&
        other.disabledBackgroundColor == disabledBackgroundColor &&
        other.errorBackgroundColor == errorBackgroundColor &&
        
        // Border styling
        other.borderColor == borderColor &&
        other.focusedBorderColor == focusedBorderColor &&
        other.hoveredBorderColor == hoveredBorderColor &&
        other.errorBorderColor == errorBorderColor &&
        other.disabledBorderColor == disabledBorderColor &&
        other.borderWidth == borderWidth &&
        other.borderRadius == borderRadius &&
        
        // Text styling
        other.textColor == textColor &&
        other.placeholderColor == placeholderColor &&
        other.disabledTextColor == disabledTextColor &&
        other.errorTextColor == errorTextColor &&
        other.textStyle == textStyle &&
        other.placeholderStyle == placeholderStyle &&
        
        // Size variants
        other.smallHeight == smallHeight &&
        other.mediumHeight == mediumHeight &&
        other.largeHeight == largeHeight &&
        
        // Padding
        other.smallPadding == smallPadding &&
        other.mediumPadding == mediumPadding &&
        other.largePadding == largePadding &&
        
        // Icon styling
        other.iconColor == iconColor &&
        other.disabledIconColor == disabledIconColor &&
        other.smallIconSize == smallIconSize &&
        other.mediumIconSize == mediumIconSize &&
        other.largeIconSize == largeIconSize &&
        
        // Error styling
        other.errorTextStyle == errorTextStyle &&
        other.errorMessageColor == errorMessageColor &&
        
        // Animation
        other.animationDuration == animationDuration &&
        other.animationCurve == animationCurve &&
        
        // Layout
        other.iconSpacing == iconSpacing &&
        other.errorTextHeight == errorTextHeight;
  }

  @override
  int get hashCode {
    return Object.hashAll([
      // Background colors
      backgroundColor,
      disabledBackgroundColor,
      errorBackgroundColor,
      
      // Border styling
      borderColor,
      focusedBorderColor,
      hoveredBorderColor,
      errorBorderColor,
      disabledBorderColor,
      borderWidth,
      borderRadius,
      
      // Text styling
      textColor,
      placeholderColor,
      disabledTextColor,
      errorTextColor,
      textStyle,
      placeholderStyle,
      
      // Size variants
      smallHeight,
      mediumHeight,
      largeHeight,
      
      // Padding
      smallPadding,
      mediumPadding,
      largePadding,
      
      // Icon styling
      iconColor,
      disabledIconColor,
      smallIconSize,
      mediumIconSize,
      largeIconSize,
      
      // Error styling
      errorTextStyle,
      errorMessageColor,
      
      // Animation
      animationDuration,
      animationCurve,
      
      // Layout
      iconSpacing,
      errorTextHeight,
    ]);
  }

}

/// Size variants for ShadcnInput component.
/// 
/// These sizes correspond to shadcn-standard heights:
/// - small: 32px
/// - medium: 36px (default) 
/// - large: 40px
enum ShadcnInputSize {
  /// Small input size (32px height)
  small,
  
  /// Medium input size (36px height) - default
  medium,
  
  /// Large input size (40px height)
  large,
}