import 'dart:ui' show lerpDouble;

import 'package:flutter/material.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for [ShadcnCarousel] component.
/// 
/// This theme controls the visual appearance of carousel components including
/// indicators, navigation buttons, and item spacing.
class ShadcnCarouselTheme extends ShadcnThemeExtension<ShadcnCarouselTheme> {
  /// Background color for carousel indicators
  final Color? indicatorBackground;
  
  /// Active indicator color
  final Color? indicatorActiveColor;
  
  /// Inactive indicator color  
  final Color? indicatorInactiveColor;
  
  /// Size of carousel indicators
  final double? indicatorSize;
  
  /// Spacing between indicators
  final double? indicatorSpacing;
  
  /// Border radius for indicators
  final BorderRadius? indicatorBorderRadius;
  
  /// Navigation button background color
  final Color? navigationButtonBackground;
  
  /// Navigation button foreground color
  final Color? navigationButtonForeground;
  
  /// Navigation button hover color
  final Color? navigationButtonHover;
  
  /// Size of navigation buttons
  final double? navigationButtonSize;
  
  /// Border radius for navigation buttons
  final BorderRadius? navigationButtonBorderRadius;
  
  /// Shadow for navigation buttons
  final List<BoxShadow>? navigationButtonShadow;
  
  /// Spacing between carousel items
  final double? itemSpacing;
  
  /// Animation duration for carousel transitions
  final Duration? animationDuration;
  
  /// Animation curve for carousel transitions
  final Curve? animationCurve;
  
  /// Height of the indicator container
  final double? indicatorContainerHeight;
  
  /// Padding for the indicator container
  final EdgeInsets? indicatorContainerPadding;

  const ShadcnCarouselTheme({
    this.indicatorBackground,
    this.indicatorActiveColor,
    this.indicatorInactiveColor,
    this.indicatorSize,
    this.indicatorSpacing,
    this.indicatorBorderRadius,
    this.navigationButtonBackground,
    this.navigationButtonForeground,
    this.navigationButtonHover,
    this.navigationButtonSize,
    this.navigationButtonBorderRadius,
    this.navigationButtonShadow,
    this.itemSpacing,
    this.animationDuration,
    this.animationCurve,
    this.indicatorContainerHeight,
    this.indicatorContainerPadding,
  });

  @override
  ShadcnCarouselTheme copyWith({
    Color? indicatorBackground,
    Color? indicatorActiveColor,
    Color? indicatorInactiveColor,
    double? indicatorSize,
    double? indicatorSpacing,
    BorderRadius? indicatorBorderRadius,
    Color? navigationButtonBackground,
    Color? navigationButtonForeground,
    Color? navigationButtonHover,
    double? navigationButtonSize,
    BorderRadius? navigationButtonBorderRadius,
    List<BoxShadow>? navigationButtonShadow,
    double? itemSpacing,
    Duration? animationDuration,
    Curve? animationCurve,
    double? indicatorContainerHeight,
    EdgeInsets? indicatorContainerPadding,
  }) {
    return ShadcnCarouselTheme(
      indicatorBackground: indicatorBackground ?? this.indicatorBackground,
      indicatorActiveColor: indicatorActiveColor ?? this.indicatorActiveColor,
      indicatorInactiveColor: indicatorInactiveColor ?? this.indicatorInactiveColor,
      indicatorSize: indicatorSize ?? this.indicatorSize,
      indicatorSpacing: indicatorSpacing ?? this.indicatorSpacing,
      indicatorBorderRadius: indicatorBorderRadius ?? this.indicatorBorderRadius,
      navigationButtonBackground: navigationButtonBackground ?? this.navigationButtonBackground,
      navigationButtonForeground: navigationButtonForeground ?? this.navigationButtonForeground,
      navigationButtonHover: navigationButtonHover ?? this.navigationButtonHover,
      navigationButtonSize: navigationButtonSize ?? this.navigationButtonSize,
      navigationButtonBorderRadius: navigationButtonBorderRadius ?? this.navigationButtonBorderRadius,
      navigationButtonShadow: navigationButtonShadow ?? this.navigationButtonShadow,
      itemSpacing: itemSpacing ?? this.itemSpacing,
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
      indicatorContainerHeight: indicatorContainerHeight ?? this.indicatorContainerHeight,
      indicatorContainerPadding: indicatorContainerPadding ?? this.indicatorContainerPadding,
    );
  }

  @override
  ShadcnCarouselTheme lerp(ShadcnCarouselTheme? other, double t) {
    if (other == null) return this;

    return ShadcnCarouselTheme(
      indicatorBackground: Color.lerp(indicatorBackground, other.indicatorBackground, t),
      indicatorActiveColor: Color.lerp(indicatorActiveColor, other.indicatorActiveColor, t),
      indicatorInactiveColor: Color.lerp(indicatorInactiveColor, other.indicatorInactiveColor, t),
      indicatorSize: lerpDouble(indicatorSize, other.indicatorSize, t),
      indicatorSpacing: lerpDouble(indicatorSpacing, other.indicatorSpacing, t),
      indicatorBorderRadius: BorderRadius.lerp(indicatorBorderRadius, other.indicatorBorderRadius, t),
      navigationButtonBackground: Color.lerp(navigationButtonBackground, other.navigationButtonBackground, t),
      navigationButtonForeground: Color.lerp(navigationButtonForeground, other.navigationButtonForeground, t),
      navigationButtonHover: Color.lerp(navigationButtonHover, other.navigationButtonHover, t),
      navigationButtonSize: lerpDouble(navigationButtonSize, other.navigationButtonSize, t),
      navigationButtonBorderRadius: BorderRadius.lerp(navigationButtonBorderRadius, other.navigationButtonBorderRadius, t),
      navigationButtonShadow: BoxShadow.lerpList(navigationButtonShadow, other.navigationButtonShadow, t),
      itemSpacing: lerpDouble(itemSpacing, other.itemSpacing, t),
      animationDuration: t < 0.5 ? animationDuration : other.animationDuration,
      animationCurve: t < 0.5 ? animationCurve : other.animationCurve,
      indicatorContainerHeight: lerpDouble(indicatorContainerHeight, other.indicatorContainerHeight, t),
      indicatorContainerPadding: EdgeInsets.lerp(indicatorContainerPadding, other.indicatorContainerPadding, t),
    );
  }

  /// Creates a default carousel theme based on the provided [ColorScheme].
  static ShadcnCarouselTheme defaultTheme(ColorScheme colorScheme) {
    return ShadcnCarouselTheme(
      indicatorBackground: colorScheme.surface,
      indicatorActiveColor: colorScheme.primary,
      indicatorInactiveColor: colorScheme.onSurface.withOpacity(0.3),
      indicatorSize: 8.0,
      indicatorSpacing: 8.0,
      indicatorBorderRadius: BorderRadius.circular(4.0),
      navigationButtonBackground: colorScheme.surface,
      navigationButtonForeground: colorScheme.onSurface,
      navigationButtonHover: colorScheme.onSurface.withOpacity(0.1),
      navigationButtonSize: 40.0,
      navigationButtonBorderRadius: BorderRadius.circular(6.0),
      navigationButtonShadow: [
        BoxShadow(
          color: colorScheme.shadow.withOpacity(0.1),
          offset: const Offset(0, 2),
          blurRadius: 4,
        ),
      ],
      itemSpacing: 16.0,
      animationDuration: const Duration(milliseconds: 300),
      animationCurve: Curves.easeInOut,
      indicatorContainerHeight: 48.0,
      indicatorContainerPadding: const EdgeInsets.symmetric(vertical: 12.0),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other.runtimeType != runtimeType) return false;
    return other is ShadcnCarouselTheme &&
        other.indicatorBackground == indicatorBackground &&
        other.indicatorActiveColor == indicatorActiveColor &&
        other.indicatorInactiveColor == indicatorInactiveColor &&
        other.indicatorSize == indicatorSize &&
        other.indicatorSpacing == indicatorSpacing &&
        other.indicatorBorderRadius == indicatorBorderRadius &&
        other.navigationButtonBackground == navigationButtonBackground &&
        other.navigationButtonForeground == navigationButtonForeground &&
        other.navigationButtonHover == navigationButtonHover &&
        other.navigationButtonSize == navigationButtonSize &&
        other.navigationButtonBorderRadius == navigationButtonBorderRadius &&
        other.navigationButtonShadow == navigationButtonShadow &&
        other.itemSpacing == itemSpacing &&
        other.animationDuration == animationDuration &&
        other.animationCurve == animationCurve &&
        other.indicatorContainerHeight == indicatorContainerHeight &&
        other.indicatorContainerPadding == indicatorContainerPadding;
  }

  @override
  int get hashCode {
    return Object.hash(
      indicatorBackground,
      indicatorActiveColor,
      indicatorInactiveColor,
      indicatorSize,
      indicatorSpacing,
      indicatorBorderRadius,
      navigationButtonBackground,
      navigationButtonForeground,
      navigationButtonHover,
      navigationButtonSize,
      navigationButtonBorderRadius,
      navigationButtonShadow,
      itemSpacing,
      animationDuration,
      animationCurve,
      indicatorContainerHeight,
      indicatorContainerPadding,
    );
  }
}

