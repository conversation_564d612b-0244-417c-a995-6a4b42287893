import 'package:flutter/material.dart';
import '../../constants/shadcn_tokens.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for the ShadcnBreadcrumb component.
/// 
/// This theme extension provides all styling properties for breadcrumb navigation,
/// including separator styling, active/inactive states, and interactive hover effects.
/// It follows shadcn design principles while integrating with Material Design theming patterns.
/// 
/// All properties are nullable to support fallback to Material theme values
/// and provide flexible customization options for developers.
class ShadcnBreadcrumbTheme extends ShadcnThemeExtension<ShadcnBreadcrumbTheme> {
  // Default text colors
  final Color? itemColor;
  final Color? activeItemColor;
  final Color? disabledItemColor;
  
  // Hover and focus states
  final Color? hoverColor;
  final Color? focusColor;
  final Color? pressedColor;
  
  // Separator styling
  final Color? separatorColor;
  final Widget? separatorIcon;
  final double? separatorSize;
  final EdgeInsets? separatorPadding;
  
  // Container properties
  final EdgeInsets? containerPadding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final double? minHeight;
  
  // Text styling
  final TextStyle? textStyle;
  final TextStyle? activeTextStyle;
  final FontWeight? fontWeight;
  final double? fontSize;
  
  // Interactive states
  final double? itemSpacing;
  final BorderRadius? itemBorderRadius;
  final EdgeInsets? itemPadding;
  final double? itemMinHeight;
  
  // Animation properties
  final Duration? animationDuration;
  final Curve? animationCurve;
  
  // Focus properties
  final double? focusWidth;
  final double? focusOffset;
  
  const ShadcnBreadcrumbTheme({
    // Colors
    this.itemColor,
    this.activeItemColor,
    this.disabledItemColor,
    this.hoverColor,
    this.focusColor,
    this.pressedColor,
    
    // Separator
    this.separatorColor,
    this.separatorIcon,
    this.separatorSize,
    this.separatorPadding,
    
    // Container
    this.containerPadding,
    this.backgroundColor,
    this.borderRadius,
    this.minHeight,
    
    // Text
    this.textStyle,
    this.activeTextStyle,
    this.fontWeight,
    this.fontSize,
    
    // Interactive
    this.itemSpacing,
    this.itemBorderRadius,
    this.itemPadding,
    this.itemMinHeight,
    
    // Animation
    this.animationDuration,
    this.animationCurve,
    
    // Focus
    this.focusWidth,
    this.focusOffset,
  });

  /// Creates a default breadcrumb theme based on the provided ColorScheme.
  /// 
  /// This factory method generates a complete breadcrumb theme that follows
  /// shadcn design principles while integrating with Material Design colors.
  /// All properties are set to shadcn-standard values with proper fallbacks.
  static ShadcnBreadcrumbTheme defaultTheme(ColorScheme colorScheme) {
    final isDark = colorScheme.brightness == Brightness.dark;
    
    return ShadcnBreadcrumbTheme(
      // Text colors
      itemColor: isDark 
          ? colorScheme.onSurface.withOpacity(0.7)
          : colorScheme.onSurface.withOpacity(0.6),
      activeItemColor: colorScheme.onSurface,
      disabledItemColor: colorScheme.onSurface.withOpacity(0.38),
      
      // Interactive states
      hoverColor: colorScheme.onSurface.withOpacity(0.04),
      focusColor: colorScheme.primary.withOpacity(0.12),
      pressedColor: colorScheme.onSurface.withOpacity(0.08),
      
      // Separator
      separatorColor: colorScheme.onSurface.withOpacity(0.4),
      separatorIcon: Icon(
        Icons.chevron_right,
        size: ShadcnTokens.iconSizeSm,
      ),
      separatorSize: ShadcnTokens.iconSizeSm,
      separatorPadding: const EdgeInsets.symmetric(horizontal: ShadcnTokens.spacing1),
      
      // Container
      containerPadding: const EdgeInsets.symmetric(
        horizontal: ShadcnTokens.spacing0,
        vertical: ShadcnTokens.spacing1,
      ),
      backgroundColor: Colors.transparent,
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusSm),
      minHeight: ShadcnTokens.buttonHeightSm,
      
      // Text styling
      textStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightNormal,
        height: ShadcnTokens.lineHeightNormal,
      ),
      activeTextStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightMedium,
        height: ShadcnTokens.lineHeightNormal,
      ),
      fontWeight: ShadcnTokens.fontWeightNormal,
      fontSize: ShadcnTokens.fontSizeMd,
      
      // Interactive properties
      itemSpacing: ShadcnTokens.spacing0,
      itemBorderRadius: BorderRadius.circular(ShadcnTokens.radiusSm),
      itemPadding: const EdgeInsets.symmetric(
        horizontal: ShadcnTokens.spacing2,
        vertical: ShadcnTokens.spacing1,
      ),
      itemMinHeight: 24.0,
      
      // Animation
      animationDuration: ShadcnTokens.durationFast,
      animationCurve: Curves.easeInOut,
      
      // Focus
      focusWidth: 2.0,
      focusOffset: 2.0,
    );
  }

  @override
  ShadcnBreadcrumbTheme copyWith({
    // Colors
    Color? itemColor,
    Color? activeItemColor,
    Color? disabledItemColor,
    Color? hoverColor,
    Color? focusColor,
    Color? pressedColor,
    
    // Separator
    Color? separatorColor,
    Widget? separatorIcon,
    double? separatorSize,
    EdgeInsets? separatorPadding,
    
    // Container
    EdgeInsets? containerPadding,
    Color? backgroundColor,
    BorderRadius? borderRadius,
    double? minHeight,
    
    // Text
    TextStyle? textStyle,
    TextStyle? activeTextStyle,
    FontWeight? fontWeight,
    double? fontSize,
    
    // Interactive
    double? itemSpacing,
    BorderRadius? itemBorderRadius,
    EdgeInsets? itemPadding,
    double? itemMinHeight,
    
    // Animation
    Duration? animationDuration,
    Curve? animationCurve,
    
    // Focus
    double? focusWidth,
    double? focusOffset,
  }) {
    return ShadcnBreadcrumbTheme(
      // Colors
      itemColor: itemColor ?? this.itemColor,
      activeItemColor: activeItemColor ?? this.activeItemColor,
      disabledItemColor: disabledItemColor ?? this.disabledItemColor,
      hoverColor: hoverColor ?? this.hoverColor,
      focusColor: focusColor ?? this.focusColor,
      pressedColor: pressedColor ?? this.pressedColor,
      
      // Separator
      separatorColor: separatorColor ?? this.separatorColor,
      separatorIcon: separatorIcon ?? this.separatorIcon,
      separatorSize: separatorSize ?? this.separatorSize,
      separatorPadding: separatorPadding ?? this.separatorPadding,
      
      // Container
      containerPadding: containerPadding ?? this.containerPadding,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      borderRadius: borderRadius ?? this.borderRadius,
      minHeight: minHeight ?? this.minHeight,
      
      // Text
      textStyle: textStyle ?? this.textStyle,
      activeTextStyle: activeTextStyle ?? this.activeTextStyle,
      fontWeight: fontWeight ?? this.fontWeight,
      fontSize: fontSize ?? this.fontSize,
      
      // Interactive
      itemSpacing: itemSpacing ?? this.itemSpacing,
      itemBorderRadius: itemBorderRadius ?? this.itemBorderRadius,
      itemPadding: itemPadding ?? this.itemPadding,
      itemMinHeight: itemMinHeight ?? this.itemMinHeight,
      
      // Animation
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
      
      // Focus
      focusWidth: focusWidth ?? this.focusWidth,
      focusOffset: focusOffset ?? this.focusOffset,
    );
  }

  @override
  ShadcnBreadcrumbTheme lerp(ThemeExtension<ShadcnBreadcrumbTheme>? other, double t) {
    if (other is! ShadcnBreadcrumbTheme) {
      return this;
    }
    
    return ShadcnBreadcrumbTheme(
      // Colors
      itemColor: Color.lerp(itemColor, other.itemColor, t),
      activeItemColor: Color.lerp(activeItemColor, other.activeItemColor, t),
      disabledItemColor: Color.lerp(disabledItemColor, other.disabledItemColor, t),
      hoverColor: Color.lerp(hoverColor, other.hoverColor, t),
      focusColor: Color.lerp(focusColor, other.focusColor, t),
      pressedColor: Color.lerp(pressedColor, other.pressedColor, t),
      
      // Separator
      separatorColor: Color.lerp(separatorColor, other.separatorColor, t),
      separatorIcon: t < 0.5 ? separatorIcon : other.separatorIcon,
      separatorSize: t < 0.5 ? separatorSize : other.separatorSize,
      separatorPadding: EdgeInsets.lerp(separatorPadding, other.separatorPadding, t),
      
      // Container
      containerPadding: EdgeInsets.lerp(containerPadding, other.containerPadding, t),
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      minHeight: t < 0.5 ? minHeight : other.minHeight,
      
      // Text
      textStyle: TextStyle.lerp(textStyle, other.textStyle, t),
      activeTextStyle: TextStyle.lerp(activeTextStyle, other.activeTextStyle, t),
      fontWeight: t < 0.5 ? fontWeight : other.fontWeight,
      fontSize: t < 0.5 ? fontSize : other.fontSize,
      
      // Interactive
      itemSpacing: t < 0.5 ? itemSpacing : other.itemSpacing,
      itemBorderRadius: BorderRadius.lerp(itemBorderRadius, other.itemBorderRadius, t),
      itemPadding: EdgeInsets.lerp(itemPadding, other.itemPadding, t),
      itemMinHeight: t < 0.5 ? itemMinHeight : other.itemMinHeight,
      
      // Animation
      animationDuration: t < 0.5 ? animationDuration : other.animationDuration,
      animationCurve: t < 0.5 ? animationCurve : other.animationCurve,
      
      // Focus
      focusWidth: t < 0.5 ? focusWidth : other.focusWidth,
      focusOffset: t < 0.5 ? focusOffset : other.focusOffset,
    );
  }

  @override
  bool validate({bool throwOnError = false}) {
    try {
      // Validate that required color properties have non-null values
      final requiredColors = {
        'itemColor': itemColor,
        'activeItemColor': activeItemColor,
        'separatorColor': separatorColor,
      };
      
      for (final entry in requiredColors.entries) {
        if (entry.value == null) {
          final error = 'ShadcnBreadcrumbTheme: ${entry.key} cannot be null';
          if (throwOnError) {
            throw FlutterError(error);
          }
          debugPrint('Warning: $error');
          return false;
        }
      }
      
      // Validate size properties
      final sizes = [minHeight, itemMinHeight, separatorSize];
      for (final size in sizes) {
        if (size != null && size <= 0) {
          final error = 'ShadcnBreadcrumbTheme: Size values must be positive';
          if (throwOnError) {
            throw FlutterError(error);
          }
          debugPrint('Warning: $error');
          return false;
        }
      }
      
      return true;
    } catch (e) {
      if (throwOnError) {
        rethrow;
      }
      debugPrint('ShadcnBreadcrumbTheme validation error: $e');
      return false;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other.runtimeType != runtimeType) return false;
    
    return other is ShadcnBreadcrumbTheme &&
        // Colors
        other.itemColor == itemColor &&
        other.activeItemColor == activeItemColor &&
        other.disabledItemColor == disabledItemColor &&
        other.hoverColor == hoverColor &&
        other.focusColor == focusColor &&
        other.pressedColor == pressedColor &&
        // Separator
        other.separatorColor == separatorColor &&
        other.separatorIcon == separatorIcon &&
        other.separatorSize == separatorSize &&
        other.separatorPadding == separatorPadding &&
        // Container
        other.containerPadding == containerPadding &&
        other.backgroundColor == backgroundColor &&
        other.borderRadius == borderRadius &&
        other.minHeight == minHeight &&
        // Text
        other.textStyle == textStyle &&
        other.activeTextStyle == activeTextStyle &&
        other.fontWeight == fontWeight &&
        other.fontSize == fontSize &&
        // Interactive
        other.itemSpacing == itemSpacing &&
        other.itemBorderRadius == itemBorderRadius &&
        other.itemPadding == itemPadding &&
        other.itemMinHeight == itemMinHeight &&
        // Animation
        other.animationDuration == animationDuration &&
        other.animationCurve == animationCurve &&
        // Focus
        other.focusWidth == focusWidth &&
        other.focusOffset == focusOffset;
  }

  @override
  int get hashCode {
    return Object.hashAll([
      // Colors
      itemColor,
      activeItemColor,
      disabledItemColor,
      hoverColor,
      focusColor,
      pressedColor,
      // Separator
      separatorColor,
      separatorIcon,
      separatorSize,
      separatorPadding,
      // Container
      containerPadding,
      backgroundColor,
      borderRadius,
      minHeight,
      // Text
      textStyle,
      activeTextStyle,
      fontWeight,
      fontSize,
      // Interactive
      itemSpacing,
      itemBorderRadius,
      itemPadding,
      itemMinHeight,
      // Animation
      animationDuration,
      animationCurve,
      // Focus
      focusWidth,
      focusOffset,
    ]);
  }

  @override
  String toString() {
    return 'ShadcnBreadcrumbTheme('
        'itemColor: $itemColor, '
        'activeItemColor: $activeItemColor, '
        'separatorColor: $separatorColor, '
        'minHeight: $minHeight'
        ')';
  }
}

/// Data class representing a breadcrumb item.
/// 
/// Each breadcrumb item can contain text, an optional icon, navigation callback,
/// and disabled state for comprehensive breadcrumb navigation support.
class ShadcnBreadcrumbItem {
  /// The text label for this breadcrumb item
  final String text;
  
  /// Optional icon to display alongside the text
  final Widget? icon;
  
  /// Callback executed when the breadcrumb item is tapped
  /// If null, the item is not interactive
  final VoidCallback? onTap;
  
  /// Whether this breadcrumb item is disabled
  final bool disabled;
  
  /// Optional semantic label for accessibility
  final String? semanticLabel;
  
  /// Optional tooltip text
  final String? tooltip;
  
  const ShadcnBreadcrumbItem({
    required this.text,
    this.icon,
    this.onTap,
    this.disabled = false,
    this.semanticLabel,
    this.tooltip,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other.runtimeType != runtimeType) return false;
    
    return other is ShadcnBreadcrumbItem &&
        other.text == text &&
        other.icon == icon &&
        other.onTap == onTap &&
        other.disabled == disabled &&
        other.semanticLabel == semanticLabel &&
        other.tooltip == tooltip;
  }

  @override
  int get hashCode {
    return Object.hash(
      text,
      icon,
      onTap,
      disabled,
      semanticLabel,
      tooltip,
    );
  }

  @override
  String toString() {
    return 'ShadcnBreadcrumbItem('
        'text: $text, '
        'disabled: $disabled, '
        'hasCallback: ${onTap != null}'
        ')';
  }
}