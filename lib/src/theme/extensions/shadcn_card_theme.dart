import 'package:flutter/material.dart';
import 'dart:ui' show lerpDouble;
import '../../constants/shadcn_tokens.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for the ShadcnCard component.
/// 
/// This extension provides comprehensive theming for card components including
/// background colors, border properties, shadow configurations, and spacing.
/// It supports Material Design elevation system while maintaining shadcn aesthetics.
class ShadcnCardTheme extends ShadcnThemeExtension<ShadcnCardTheme> {
  /// Background color for the card surface
  final Color? backgroundColor;
  
  /// Border color for outlined variant
  final Color? borderColor;
  
  /// Text color for content within the card
  final Color? foregroundColor;
  
  /// Border width for outlined cards
  final double? borderWidth;
  
  /// Border radius for the card
  final BorderRadius? borderRadius;
  
  /// Content padding inside the card
  final EdgeInsets? padding;
  
  /// Margin around the card
  final EdgeInsets? margin;
  
  /// Shadow color for elevated cards
  final Color? shadowColor;
  
  /// Elevation for elevated variant
  final double? elevation;
  
  /// Shadow configuration for custom shadow effects
  final List<BoxShadow>? boxShadow;
  
  /// Minimum height constraint for the card
  final double? minHeight;
  
  /// Maximum height constraint for the card
  final double? maxHeight;
  
  /// Minimum width constraint for the card
  final double? minWidth;
  
  /// Maximum width constraint for the card
  final double? maxWidth;
  
  /// Creates a ShadcnCardTheme with optional customization parameters.
  const ShadcnCardTheme({
    this.backgroundColor,
    this.borderColor,
    this.foregroundColor,
    this.borderWidth,
    this.borderRadius,
    this.padding,
    this.margin,
    this.shadowColor,
    this.elevation,
    this.boxShadow,
    this.minHeight,
    this.maxHeight,
    this.minWidth,
    this.maxWidth,
  });
  
  /// Creates the default theme based on Material color scheme.
  /// 
  /// This factory provides shadcn-standard defaults while integrating with
  /// Material Design color schemes. All dimensions follow shadcn specifications.
  static ShadcnCardTheme defaultTheme(ColorScheme colorScheme) {
    return ShadcnCardTheme(
      backgroundColor: colorScheme.surface,
      borderColor: colorScheme.outline,
      foregroundColor: colorScheme.onSurface,
      borderWidth: ShadcnTokens.borderWidth,
      borderRadius: ShadcnTokens.borderRadius(ShadcnTokens.radiusLg),
      padding: ShadcnTokens.cardPadding,
      margin: EdgeInsets.zero,
      shadowColor: colorScheme.shadow,
      elevation: ShadcnTokens.elevationNone,
      minHeight: null,
      maxHeight: null,
      minWidth: null,
      maxWidth: null,
    );
  }
  
  /// Creates a light theme optimized for light color schemes.
  static ShadcnCardTheme lightTheme(ColorScheme colorScheme) {
    return ShadcnCardTheme(
      backgroundColor: colorScheme.surface,
      borderColor: colorScheme.outline.withValues(alpha: 0.2),
      foregroundColor: colorScheme.onSurface,
      borderWidth: ShadcnTokens.borderWidth,
      borderRadius: ShadcnTokens.borderRadius(ShadcnTokens.radiusLg),
      padding: ShadcnTokens.cardPadding,
      margin: EdgeInsets.zero,
      shadowColor: Colors.black.withValues(alpha: 0.1),
      elevation: ShadcnTokens.elevationSm,
    );
  }
  
  /// Creates a dark theme optimized for dark color schemes.
  static ShadcnCardTheme darkTheme(ColorScheme colorScheme) {
    return ShadcnCardTheme(
      backgroundColor: colorScheme.surface,
      borderColor: colorScheme.outline.withValues(alpha: 0.3),
      foregroundColor: colorScheme.onSurface,
      borderWidth: ShadcnTokens.borderWidth,
      borderRadius: ShadcnTokens.borderRadius(ShadcnTokens.radiusLg),
      padding: ShadcnTokens.cardPadding,
      margin: EdgeInsets.zero,
      shadowColor: Colors.black.withValues(alpha: 0.3),
      elevation: ShadcnTokens.elevationSm,
    );
  }
  
  /// Resolves the background color with proper fallback chain.
  Color resolveBackgroundColor(BuildContext context) {
    return resolveColor(
      context,
      backgroundColor,
      (theme) => theme.colorScheme.surface,
      Colors.white,
    );
  }
  
  /// Resolves the border color with proper fallback chain.
  Color resolveBorderColor(BuildContext context) {
    return resolveColor(
      context,
      borderColor,
      (theme) => theme.colorScheme.outline,
      Colors.grey.shade300,
    );
  }
  
  /// Resolves the foreground color with proper fallback chain.
  Color resolveForegroundColor(BuildContext context) {
    return resolveColor(
      context,
      foregroundColor,
      (theme) => theme.colorScheme.onSurface,
      Colors.black87,
    );
  }
  
  /// Resolves the shadow color with proper fallback chain.
  Color resolveShadowColor(BuildContext context) {
    return resolveColor(
      context,
      shadowColor,
      (theme) => theme.colorScheme.shadow,
      Colors.black26,
    );
  }
  
  /// Resolves the border radius with theme-aware adjustments.
  BorderRadius resolveCardBorderRadius(BuildContext context) {
    final defaultRadius = ShadcnTokens.borderRadius(ShadcnTokens.radiusLg);
    return super.resolveBorderRadius(context, borderRadius, defaultRadius);
  }
  
  /// Resolves the content padding with visual density adjustments.
  EdgeInsets resolvePadding(BuildContext context) {
    final defaultPadding = ShadcnTokens.cardPadding;
    return resolveSpacing(context, padding, defaultPadding);
  }
  
  /// Resolves the card margin with visual density adjustments.
  EdgeInsets resolveMargin(BuildContext context) {
    final defaultMargin = EdgeInsets.zero;
    return resolveSpacing(context, margin, defaultMargin);
  }
  
  /// Resolves the elevation value with visual density consideration.
  double resolveElevation(BuildContext context) {
    return resolveDouble(
      context,
      elevation,
      ShadcnTokens.elevationNone,
      applyVerticalDensity: false,
    );
  }
  
  /// Resolves the border width with theme consistency.
  double resolveBorderWidth(BuildContext context) {
    return resolveDouble(
      context,
      borderWidth,
      ShadcnTokens.borderWidth,
      applyVerticalDensity: false,
    );
  }
  
  /// Gets the box shadow configuration for elevated cards.
  List<BoxShadow>? resolveBoxShadow(BuildContext context) {
    if (boxShadow != null) {
      return boxShadow;
    }
    
    final resolvedElevation = resolveElevation(context);
    if (resolvedElevation <= 0) {
      return null;
    }
    
    final resolvedShadowColor = resolveShadowColor(context);
    
    return [
      BoxShadow(
        color: resolvedShadowColor.withValues(alpha: 0.1),
        blurRadius: resolvedElevation * 2,
        offset: Offset(0, resolvedElevation / 2),
      ),
      BoxShadow(
        color: resolvedShadowColor.withValues(alpha: 0.05),
        blurRadius: resolvedElevation,
        offset: const Offset(0, 1),
      ),
    ];
  }
  
  /// Resolves box constraints for the card.
  BoxConstraints? resolveConstraints(BuildContext context) {
    final resolvedMinHeight = minHeight != null
        ? resolveDouble(context, minHeight, minHeight!)
        : null;
    final resolvedMaxHeight = maxHeight != null
        ? resolveDouble(context, maxHeight, maxHeight!)
        : null;
    final resolvedMinWidth = minWidth != null
        ? resolveDouble(context, minWidth, minWidth!, applyVerticalDensity: false)
        : null;
    final resolvedMaxWidth = maxWidth != null
        ? resolveDouble(context, maxWidth, maxWidth!, applyVerticalDensity: false)
        : null;
    
    if (resolvedMinHeight == null && resolvedMaxHeight == null &&
        resolvedMinWidth == null && resolvedMaxWidth == null) {
      return null;
    }
    
    return BoxConstraints(
      minWidth: resolvedMinWidth ?? 0,
      maxWidth: resolvedMaxWidth ?? double.infinity,
      minHeight: resolvedMinHeight ?? 0,
      maxHeight: resolvedMaxHeight ?? double.infinity,
    );
  }
  
  @override
  ShadcnCardTheme copyWith({
    Color? backgroundColor,
    Color? borderColor,
    Color? foregroundColor,
    double? borderWidth,
    BorderRadius? borderRadius,
    EdgeInsets? padding,
    EdgeInsets? margin,
    Color? shadowColor,
    double? elevation,
    List<BoxShadow>? boxShadow,
    double? minHeight,
    double? maxHeight,
    double? minWidth,
    double? maxWidth,
  }) {
    return ShadcnCardTheme(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      borderColor: borderColor ?? this.borderColor,
      foregroundColor: foregroundColor ?? this.foregroundColor,
      borderWidth: borderWidth ?? this.borderWidth,
      borderRadius: borderRadius ?? this.borderRadius,
      padding: padding ?? this.padding,
      margin: margin ?? this.margin,
      shadowColor: shadowColor ?? this.shadowColor,
      elevation: elevation ?? this.elevation,
      boxShadow: boxShadow ?? this.boxShadow,
      minHeight: minHeight ?? this.minHeight,
      maxHeight: maxHeight ?? this.maxHeight,
      minWidth: minWidth ?? this.minWidth,
      maxWidth: maxWidth ?? this.maxWidth,
    );
  }
  
  @override
  ShadcnCardTheme lerp(ShadcnCardTheme? other, double t) {
    if (other is! ShadcnCardTheme) return this;
    
    return ShadcnCardTheme(
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t),
      borderColor: Color.lerp(borderColor, other.borderColor, t),
      foregroundColor: Color.lerp(foregroundColor, other.foregroundColor, t),
      borderWidth: _lerpDouble(borderWidth, other.borderWidth, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      padding: EdgeInsets.lerp(padding, other.padding, t),
      margin: EdgeInsets.lerp(margin, other.margin, t),
      shadowColor: Color.lerp(shadowColor, other.shadowColor, t),
      elevation: _lerpDouble(elevation, other.elevation, t),
      boxShadow: _lerpBoxShadowList(boxShadow, other.boxShadow, t),
      minHeight: _lerpDouble(minHeight, other.minHeight, t),
      maxHeight: _lerpDouble(maxHeight, other.maxHeight, t),
      minWidth: _lerpDouble(minWidth, other.minWidth, t),
      maxWidth: _lerpDouble(maxWidth, other.maxWidth, t),
    );
  }
  
  @override
  bool validate({bool throwOnError = false}) {
    final errors = <String>[];
    
    // Validate dimensions
    if (minHeight != null && minHeight! < 0) {
      errors.add('minHeight cannot be negative: $minHeight');
    }
    if (maxHeight != null && maxHeight! < 0) {
      errors.add('maxHeight cannot be negative: $maxHeight');
    }
    if (minWidth != null && minWidth! < 0) {
      errors.add('minWidth cannot be negative: $minWidth');
    }
    if (maxWidth != null && maxWidth! < 0) {
      errors.add('maxWidth cannot be negative: $maxWidth');
    }
    if (minHeight != null && maxHeight != null && minHeight! > maxHeight!) {
      errors.add('minHeight ($minHeight) cannot be greater than maxHeight ($maxHeight)');
    }
    if (minWidth != null && maxWidth != null && minWidth! > maxWidth!) {
      errors.add('minWidth ($minWidth) cannot be greater than maxWidth ($maxWidth)');
    }
    
    // Validate border width
    if (borderWidth != null && borderWidth! < 0) {
      errors.add('borderWidth cannot be negative: $borderWidth');
    }
    
    // Validate elevation
    if (elevation != null && elevation! < 0) {
      errors.add('elevation cannot be negative: $elevation');
    }
    
    if (errors.isNotEmpty) {
      if (throwOnError) {
        throw ArgumentError('ShadcnCardTheme validation failed:\n${errors.join('\n')}');
      }
      return false;
    }
    
    return true;
  }
  
  @override
  String toString() {
    return 'ShadcnCardTheme('
        'backgroundColor: $backgroundColor, '
        'borderColor: $borderColor, '
        'foregroundColor: $foregroundColor, '
        'borderWidth: $borderWidth, '
        'borderRadius: $borderRadius, '
        'padding: $padding, '
        'margin: $margin, '
        'shadowColor: $shadowColor, '
        'elevation: $elevation, '
        'boxShadow: $boxShadow, '
        'minHeight: $minHeight, '
        'maxHeight: $maxHeight, '
        'minWidth: $minWidth, '
        'maxWidth: $maxWidth'
        ')';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is ShadcnCardTheme &&
        other.backgroundColor == backgroundColor &&
        other.borderColor == borderColor &&
        other.foregroundColor == foregroundColor &&
        other.borderWidth == borderWidth &&
        other.borderRadius == borderRadius &&
        other.padding == padding &&
        other.margin == margin &&
        other.shadowColor == shadowColor &&
        other.elevation == elevation &&
        _listEquals(other.boxShadow, boxShadow) &&
        other.minHeight == minHeight &&
        other.maxHeight == maxHeight &&
        other.minWidth == minWidth &&
        other.maxWidth == maxWidth;
  }
  
  @override
  int get hashCode {
    return Object.hash(
      backgroundColor,
      borderColor,
      foregroundColor,
      borderWidth,
      borderRadius,
      padding,
      margin,
      shadowColor,
      elevation,
      boxShadow,
      minHeight,
      maxHeight,
      minWidth,
      maxWidth,
    );
  }
  
  // Helper methods for interpolation
  
  double? _lerpDouble(double? a, double? b, double t) {
    if (a == null && b == null) return null;
    return lerpDouble(a ?? 0.0, b ?? 0.0, t);
  }
  
  List<BoxShadow>? _lerpBoxShadowList(List<BoxShadow>? a, List<BoxShadow>? b, double t) {
    if (a == null && b == null) return null;
    if (a == null) return b!.map((shadow) => BoxShadow.lerp(null, shadow, t)!).toList();
    if (b == null) return a.map((shadow) => BoxShadow.lerp(shadow, null, t)!).toList();
    
    final length = a.length > b.length ? a.length : b.length;
    final result = <BoxShadow>[];
    
    for (int i = 0; i < length; i++) {
      final shadowA = i < a.length ? a[i] : null;
      final shadowB = i < b.length ? b[i] : null;
      final lerped = BoxShadow.lerp(shadowA, shadowB, t);
      if (lerped != null) {
        result.add(lerped);
      }
    }
    
    return result.isEmpty ? null : result;
  }
  
  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null && b == null) return true;
    if (a == null || b == null) return false;
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }
}