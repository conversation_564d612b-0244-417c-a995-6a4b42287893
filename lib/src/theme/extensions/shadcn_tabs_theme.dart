import 'dart:ui' show lerpDouble;
import 'package:flutter/material.dart';
import '../../constants/shadcn_tokens.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for the ShadcnTabs component.
class ShadcnTabsTheme extends ShadcnThemeExtension<ShadcnTabsTheme> {
  // Container properties
  final Color? backgroundColor;
  final Color? borderColor;
  final BorderRadius? borderRadius;
  final EdgeInsets? padding;
  
  // Tab properties
  final Color? tabBackground;
  final Color? tabForeground;
  final Color? tabActiveBackground;
  final Color? tabActiveForeground;
  final Color? tabHoverBackground;
  final EdgeInsets? tabPadding;
  final double? tabHeight;
  final TextStyle? tabTextStyle;
  
  // Indicator properties
  final Color? indicatorColor;
  final double? indicatorHeight;

  const ShadcnTabsTheme({
    this.backgroundColor,
    this.borderColor,
    this.borderRadius,
    this.padding,
    this.tabBackground,
    this.tabForeground,
    this.tabActiveBackground,
    this.tabActiveForeground,
    this.tabHoverBackground,
    this.tabPadding,
    this.tabHeight,
    this.tabTextStyle,
    this.indicatorColor,
    this.indicatorHeight,
  });

  /// Creates a default theme based on the provided color scheme.
  static ShadcnTabsTheme defaultTheme(ColorScheme colorScheme) {
    return ShadcnTabsTheme(
      backgroundColor: colorScheme.surface,
      borderColor: colorScheme.outline.withValues(alpha: 0.2),
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusLg),
      padding: const EdgeInsets.all(ShadcnTokens.spacing2),
      
      tabBackground: Colors.transparent,
      tabForeground: colorScheme.onSurface.withValues(alpha: 0.7),
      tabActiveBackground: colorScheme.surface,
      tabActiveForeground: colorScheme.onSurface,
      tabHoverBackground: colorScheme.onSurface.withValues(alpha: 0.05),
      tabPadding: const EdgeInsets.symmetric(
        horizontal: ShadcnTokens.spacing3,
        vertical: ShadcnTokens.spacing2,
      ),
      tabHeight: 36.0,
      tabTextStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightMedium,
      ),
      
      indicatorColor: colorScheme.primary,
      indicatorHeight: 2.0,
    );
  }

  @override
  ShadcnTabsTheme copyWith({
    Color? backgroundColor,
    Color? borderColor,
    BorderRadius? borderRadius,
    EdgeInsets? padding,
    Color? tabBackground,
    Color? tabForeground,
    Color? tabActiveBackground,
    Color? tabActiveForeground,
    Color? tabHoverBackground,
    EdgeInsets? tabPadding,
    double? tabHeight,
    TextStyle? tabTextStyle,
    Color? indicatorColor,
    double? indicatorHeight,
  }) {
    return ShadcnTabsTheme(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      borderColor: borderColor ?? this.borderColor,
      borderRadius: borderRadius ?? this.borderRadius,
      padding: padding ?? this.padding,
      tabBackground: tabBackground ?? this.tabBackground,
      tabForeground: tabForeground ?? this.tabForeground,
      tabActiveBackground: tabActiveBackground ?? this.tabActiveBackground,
      tabActiveForeground: tabActiveForeground ?? this.tabActiveForeground,
      tabHoverBackground: tabHoverBackground ?? this.tabHoverBackground,
      tabPadding: tabPadding ?? this.tabPadding,
      tabHeight: tabHeight ?? this.tabHeight,
      tabTextStyle: tabTextStyle ?? this.tabTextStyle,
      indicatorColor: indicatorColor ?? this.indicatorColor,
      indicatorHeight: indicatorHeight ?? this.indicatorHeight,
    );
  }

  @override
  ShadcnTabsTheme lerp(ShadcnTabsTheme? other, double t) {
    if (other is! ShadcnTabsTheme) return this;
    
    return ShadcnTabsTheme(
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t),
      borderColor: Color.lerp(borderColor, other.borderColor, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      padding: EdgeInsets.lerp(padding, other.padding, t),
      tabBackground: Color.lerp(tabBackground, other.tabBackground, t),
      tabForeground: Color.lerp(tabForeground, other.tabForeground, t),
      tabActiveBackground: Color.lerp(tabActiveBackground, other.tabActiveBackground, t),
      tabActiveForeground: Color.lerp(tabActiveForeground, other.tabActiveForeground, t),
      tabHoverBackground: Color.lerp(tabHoverBackground, other.tabHoverBackground, t),
      tabPadding: EdgeInsets.lerp(tabPadding, other.tabPadding, t),
      tabHeight: lerpDouble(tabHeight, other.tabHeight, t),
      tabTextStyle: TextStyle.lerp(tabTextStyle, other.tabTextStyle, t),
      indicatorColor: Color.lerp(indicatorColor, other.indicatorColor, t),
      indicatorHeight: lerpDouble(indicatorHeight, other.indicatorHeight, t),
    );
  }

  @override
  bool validate({bool throwOnError = false}) {
    try {
      assert(tabHeight == null || tabHeight! > 0, 'tabHeight must be positive');
      assert(indicatorHeight == null || indicatorHeight! > 0, 'indicatorHeight must be positive');
      return true;
    } catch (e) {
      if (throwOnError) {
        throw FlutterError('ShadcnTabsTheme validation failed: $e');
      }
      return false;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other.runtimeType != runtimeType) return false;
    
    return other is ShadcnTabsTheme &&
        other.backgroundColor == backgroundColor &&
        other.borderColor == borderColor &&
        other.borderRadius == borderRadius &&
        other.padding == padding &&
        other.tabBackground == tabBackground &&
        other.tabForeground == tabForeground &&
        other.tabActiveBackground == tabActiveBackground &&
        other.tabActiveForeground == tabActiveForeground &&
        other.tabHoverBackground == tabHoverBackground &&
        other.tabPadding == tabPadding &&
        other.tabHeight == tabHeight &&
        other.tabTextStyle == tabTextStyle &&
        other.indicatorColor == indicatorColor &&
        other.indicatorHeight == indicatorHeight;
  }

  @override
  int get hashCode => Object.hash(
    backgroundColor,
    borderColor,
    borderRadius,
    padding,
    tabBackground,
    tabForeground,
    tabActiveBackground,
    tabActiveForeground,
    tabHoverBackground,
    tabPadding,
    tabHeight,
    tabTextStyle,
    indicatorColor,
    indicatorHeight,
  );
}