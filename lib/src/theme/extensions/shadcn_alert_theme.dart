import 'dart:ui' show lerpDouble;
import 'package:flutter/material.dart';
import '../../constants/shadcn_tokens.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for ShadcnAlert components.
/// 
/// This theme extension provides styling properties for Alert components
/// including backgrounds, borders, text colors, and spacing for both
/// default and destructive variants. It integrates with Flutter's Material
/// theme system while providing shadcn-specific design tokens.
class ShadcnAlertTheme extends ShadcnThemeExtension<ShadcnAlertTheme> {
  // =============================================================================
  // DEFAULT VARIANT PROPERTIES
  // =============================================================================
  
  /// Background color for default alert variant
  final Color? defaultBackground;
  
  /// Foreground color for default alert variant
  final Color? defaultForeground;
  
  /// Border color for default alert variant
  final Color? defaultBorder;
  
  /// Icon color for default alert variant
  final Color? defaultIconColor;
  
  // =============================================================================
  // DESTRUCTIVE VARIANT PROPERTIES
  // =============================================================================
  
  /// Background color for destructive alert variant
  final Color? destructiveBackground;
  
  /// Foreground color for destructive alert variant
  final Color? destructiveForeground;
  
  /// Border color for destructive alert variant
  final Color? destructiveBorder;
  
  /// Icon color for destructive alert variant
  final Color? destructiveIconColor;
  
  // =============================================================================
  // LAYOUT PROPERTIES
  // =============================================================================
  
  /// Padding inside the alert container
  final EdgeInsets? padding;
  
  /// Border radius for the alert container
  final BorderRadius? borderRadius;
  
  /// Border width for the alert container
  final double? borderWidth;
  
  /// Gap between icon and content
  final double? iconGap;
  
  /// Gap between title and description
  final double? contentGap;
  
  // =============================================================================
  // TYPOGRAPHY PROPERTIES
  // =============================================================================
  
  /// Text style for alert title
  final TextStyle? titleTextStyle;
  
  /// Text style for alert description
  final TextStyle? descriptionTextStyle;
  
  /// Icon size for alert icons
  final double? iconSize;
  
  const ShadcnAlertTheme({
    // Default variant colors
    this.defaultBackground,
    this.defaultForeground,
    this.defaultBorder,
    this.defaultIconColor,
    
    // Destructive variant colors
    this.destructiveBackground,
    this.destructiveForeground,
    this.destructiveBorder,
    this.destructiveIconColor,
    
    // Layout properties
    this.padding,
    this.borderRadius,
    this.borderWidth,
    this.iconGap,
    this.contentGap,
    
    // Typography properties
    this.titleTextStyle,
    this.descriptionTextStyle,
    this.iconSize,
  });
  
  /// Creates a default Alert theme based on the provided ColorScheme.
  /// 
  /// This factory provides shadcn-standard styling that integrates with
  /// Material Design colors while maintaining the shadcn aesthetic.
  /// 
  /// The default theme includes:
  /// - Subtle background colors derived from Material surface colors
  /// - Border colors based on Material outline colors
  /// - Text colors that ensure proper contrast
  /// - Spacing based on shadcn design tokens
  factory ShadcnAlertTheme.defaultTheme(ColorScheme colorScheme) {
    final brightness = colorScheme.brightness;
    
    return ShadcnAlertTheme(
      // Default variant - light, subtle styling
      defaultBackground: brightness == Brightness.light
          ? colorScheme.surface.withValues(alpha: 0.1)
          : colorScheme.surface.withValues(alpha: 0.15),
      defaultForeground: colorScheme.onSurface,
      defaultBorder: brightness == Brightness.light
          ? colorScheme.outline.withValues(alpha: 0.2)
          : colorScheme.outline.withValues(alpha: 0.3),
      defaultIconColor: colorScheme.onSurface.withValues(alpha: 0.6),
      
      // Destructive variant - error/warning colors
      destructiveBackground: brightness == Brightness.light
          ? colorScheme.error.withValues(alpha: 0.1)
          : colorScheme.error.withValues(alpha: 0.15),
      destructiveForeground: colorScheme.error,
      destructiveBorder: brightness == Brightness.light
          ? colorScheme.error.withValues(alpha: 0.2)
          : colorScheme.error.withValues(alpha: 0.3),
      destructiveIconColor: colorScheme.error,
      
      // Layout properties based on shadcn tokens
      padding: ShadcnTokens.paddingAll(ShadcnTokens.spacing4),
      borderRadius: ShadcnTokens.borderRadius(ShadcnTokens.radiusMd),
      borderWidth: ShadcnTokens.borderWidth,
      iconGap: ShadcnTokens.spacing3,
      contentGap: ShadcnTokens.spacing1,
      
      // Typography properties
      titleTextStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightMedium,
        height: ShadcnTokens.lineHeightTight,
      ),
      descriptionTextStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeSm,
        fontWeight: ShadcnTokens.fontWeightNormal,
        height: ShadcnTokens.lineHeightNormal,
      ),
      iconSize: ShadcnTokens.iconSizeMd,
    );
  }
  
  @override
  ShadcnAlertTheme copyWith({
    // Default variant colors
    Color? defaultBackground,
    Color? defaultForeground,
    Color? defaultBorder,
    Color? defaultIconColor,
    
    // Destructive variant colors
    Color? destructiveBackground,
    Color? destructiveForeground,
    Color? destructiveBorder,
    Color? destructiveIconColor,
    
    // Layout properties
    EdgeInsets? padding,
    BorderRadius? borderRadius,
    double? borderWidth,
    double? iconGap,
    double? contentGap,
    
    // Typography properties
    TextStyle? titleTextStyle,
    TextStyle? descriptionTextStyle,
    double? iconSize,
  }) {
    return ShadcnAlertTheme(
      // Default variant colors
      defaultBackground: defaultBackground ?? this.defaultBackground,
      defaultForeground: defaultForeground ?? this.defaultForeground,
      defaultBorder: defaultBorder ?? this.defaultBorder,
      defaultIconColor: defaultIconColor ?? this.defaultIconColor,
      
      // Destructive variant colors
      destructiveBackground: destructiveBackground ?? this.destructiveBackground,
      destructiveForeground: destructiveForeground ?? this.destructiveForeground,
      destructiveBorder: destructiveBorder ?? this.destructiveBorder,
      destructiveIconColor: destructiveIconColor ?? this.destructiveIconColor,
      
      // Layout properties
      padding: padding ?? this.padding,
      borderRadius: borderRadius ?? this.borderRadius,
      borderWidth: borderWidth ?? this.borderWidth,
      iconGap: iconGap ?? this.iconGap,
      contentGap: contentGap ?? this.contentGap,
      
      // Typography properties
      titleTextStyle: titleTextStyle ?? this.titleTextStyle,
      descriptionTextStyle: descriptionTextStyle ?? this.descriptionTextStyle,
      iconSize: iconSize ?? this.iconSize,
    );
  }
  
  @override
  ShadcnAlertTheme lerp(ShadcnAlertTheme? other, double t) {
    if (other is! ShadcnAlertTheme) {
      return this;
    }
    
    return ShadcnAlertTheme(
      // Default variant colors
      defaultBackground: Color.lerp(defaultBackground, other.defaultBackground, t),
      defaultForeground: Color.lerp(defaultForeground, other.defaultForeground, t),
      defaultBorder: Color.lerp(defaultBorder, other.defaultBorder, t),
      defaultIconColor: Color.lerp(defaultIconColor, other.defaultIconColor, t),
      
      // Destructive variant colors
      destructiveBackground: Color.lerp(destructiveBackground, other.destructiveBackground, t),
      destructiveForeground: Color.lerp(destructiveForeground, other.destructiveForeground, t),
      destructiveBorder: Color.lerp(destructiveBorder, other.destructiveBorder, t),
      destructiveIconColor: Color.lerp(destructiveIconColor, other.destructiveIconColor, t),
      
      // Layout properties
      padding: EdgeInsets.lerp(padding, other.padding, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      borderWidth: lerpDouble(borderWidth, other.borderWidth, t),
      iconGap: lerpDouble(iconGap, other.iconGap, t),
      contentGap: lerpDouble(contentGap, other.contentGap, t),
      
      // Typography properties
      titleTextStyle: TextStyle.lerp(titleTextStyle, other.titleTextStyle, t),
      descriptionTextStyle: TextStyle.lerp(descriptionTextStyle, other.descriptionTextStyle, t),
      iconSize: lerpDouble(iconSize, other.iconSize, t),
    );
  }
  
  @override
  bool validate({bool throwOnError = false}) {
    try {
      // Validate that essential properties have reasonable values
      assert(borderWidth == null || borderWidth! >= 0, 'Border width cannot be negative');
      assert(iconGap == null || iconGap! >= 0, 'Icon gap cannot be negative');
      assert(contentGap == null || contentGap! >= 0, 'Content gap cannot be negative');
      assert(iconSize == null || iconSize! > 0, 'Icon size must be positive');
      
      return true;
    } catch (e) {
      if (throwOnError) {
        throw FlutterError('ShadcnAlertTheme validation failed: $e');
      }
      return false;
    }
  }
  
  @override
  int get hashCode => Object.hashAll([
    // Default variant colors
    defaultBackground,
    defaultForeground,
    defaultBorder,
    defaultIconColor,
    
    // Destructive variant colors
    destructiveBackground,
    destructiveForeground,
    destructiveBorder,
    destructiveIconColor,
    
    // Layout properties
    padding,
    borderRadius,
    borderWidth,
    iconGap,
    contentGap,
    
    // Typography properties
    titleTextStyle,
    descriptionTextStyle,
    iconSize,
  ]);
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other.runtimeType != runtimeType) return false;
    
    return other is ShadcnAlertTheme &&
        // Default variant colors
        other.defaultBackground == defaultBackground &&
        other.defaultForeground == defaultForeground &&
        other.defaultBorder == defaultBorder &&
        other.defaultIconColor == defaultIconColor &&
        
        // Destructive variant colors
        other.destructiveBackground == destructiveBackground &&
        other.destructiveForeground == destructiveForeground &&
        other.destructiveBorder == destructiveBorder &&
        other.destructiveIconColor == destructiveIconColor &&
        
        // Layout properties
        other.padding == padding &&
        other.borderRadius == borderRadius &&
        other.borderWidth == borderWidth &&
        other.iconGap == iconGap &&
        other.contentGap == contentGap &&
        
        // Typography properties
        other.titleTextStyle == titleTextStyle &&
        other.descriptionTextStyle == descriptionTextStyle &&
        other.iconSize == iconSize;
  }
  
  @override
  String toString() {
    return 'ShadcnAlertTheme('
        'defaultBackground: $defaultBackground, '
        'defaultForeground: $defaultForeground, '
        'defaultBorder: $defaultBorder, '
        'defaultIconColor: $defaultIconColor, '
        'destructiveBackground: $destructiveBackground, '
        'destructiveForeground: $destructiveForeground, '
        'destructiveBorder: $destructiveBorder, '
        'destructiveIconColor: $destructiveIconColor, '
        'padding: $padding, '
        'borderRadius: $borderRadius, '
        'borderWidth: $borderWidth, '
        'iconGap: $iconGap, '
        'contentGap: $contentGap, '
        'titleTextStyle: $titleTextStyle, '
        'descriptionTextStyle: $descriptionTextStyle, '
        'iconSize: $iconSize'
        ')';
  }
}

/// Enumeration for Alert variants.
/// 
/// Defines the available variants for Alert components, each with
/// its own visual styling and semantic meaning.
enum ShadcnAlertVariant {
  /// Default alert variant for general information
  defaultVariant,
  
  /// Destructive alert variant for warnings, errors, or critical information
  destructive,
}

/// Extension methods for ShadcnAlertVariant to provide convenient access to variant properties.
extension ShadcnAlertVariantExtension on ShadcnAlertVariant {
  /// Returns the default icon for each alert variant.
  /// 
  /// Provides sensible default icons that match the semantic meaning
  /// of each variant.
  IconData get defaultIcon {
    switch (this) {
      case ShadcnAlertVariant.defaultVariant:
        return Icons.info_outline;
      case ShadcnAlertVariant.destructive:
        return Icons.warning_amber_outlined;
    }
  }
  
  /// Returns a human-readable name for the variant.
  String get displayName {
    switch (this) {
      case ShadcnAlertVariant.defaultVariant:
        return 'Default';
      case ShadcnAlertVariant.destructive:
        return 'Destructive';
    }
  }
  
  /// Returns whether this variant represents a critical or error state.
  bool get isCritical {
    switch (this) {
      case ShadcnAlertVariant.defaultVariant:
        return false;
      case ShadcnAlertVariant.destructive:
        return true;
    }
  }
}