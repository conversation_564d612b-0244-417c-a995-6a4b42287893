import 'package:flutter/material.dart';
import '../../constants/shadcn_tokens.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for the ShadcnSidebar component.
class ShadcnSidebarTheme extends ShadcnThemeExtension<ShadcnSidebarTheme> {
  final Color? backgroundColor;
  final Color? foregroundColor;
  final Color? borderColor;
  final Color? headerBackground;
  final Color? itemHoverColor;
  final Color? itemSelectedColor;
  final Color? itemSelectedForegroundColor;
  final double? width;
  final double? borderWidth;
  final EdgeInsets? padding;
  final EdgeInsets? headerPadding;
  final EdgeInsets? itemPadding;
  final BorderRadius? borderRadius;
  final TextStyle? headerTextStyle;
  final TextStyle? itemTextStyle;
  final double? iconSize;
  final double? itemSpacing;
  final Duration? animationDuration;

  const ShadcnSidebarTheme({
    this.backgroundColor,
    this.foregroundColor,
    this.borderColor,
    this.headerBackground,
    this.itemHoverColor,
    this.itemSelectedColor,
    this.itemSelectedForegroundColor,
    this.width,
    this.borderWidth,
    this.padding,
    this.headerPadding,
    this.itemPadding,
    this.borderRadius,
    this.headerTextStyle,
    this.itemTextStyle,
    this.iconSize,
    this.itemSpacing,
    this.animationDuration,
  });

  @override
  ShadcnSidebarTheme copyWith({
    Color? backgroundColor,
    Color? foregroundColor,
    Color? borderColor,
    Color? headerBackground,
    Color? itemHoverColor,
    Color? itemSelectedColor,
    Color? itemSelectedForegroundColor,
    double? width,
    double? borderWidth,
    EdgeInsets? padding,
    EdgeInsets? headerPadding,
    EdgeInsets? itemPadding,
    BorderRadius? borderRadius,
    TextStyle? headerTextStyle,
    TextStyle? itemTextStyle,
    double? iconSize,
    double? itemSpacing,
    Duration? animationDuration,
  }) {
    return ShadcnSidebarTheme(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      foregroundColor: foregroundColor ?? this.foregroundColor,
      borderColor: borderColor ?? this.borderColor,
      headerBackground: headerBackground ?? this.headerBackground,
      itemHoverColor: itemHoverColor ?? this.itemHoverColor,
      itemSelectedColor: itemSelectedColor ?? this.itemSelectedColor,
      itemSelectedForegroundColor: itemSelectedForegroundColor ?? this.itemSelectedForegroundColor,
      width: width ?? this.width,
      borderWidth: borderWidth ?? this.borderWidth,
      padding: padding ?? this.padding,
      headerPadding: headerPadding ?? this.headerPadding,
      itemPadding: itemPadding ?? this.itemPadding,
      borderRadius: borderRadius ?? this.borderRadius,
      headerTextStyle: headerTextStyle ?? this.headerTextStyle,
      itemTextStyle: itemTextStyle ?? this.itemTextStyle,
      iconSize: iconSize ?? this.iconSize,
      itemSpacing: itemSpacing ?? this.itemSpacing,
      animationDuration: animationDuration ?? this.animationDuration,
    );
  }

  @override
  ShadcnSidebarTheme lerp(ShadcnSidebarTheme? other, double t) {
    if (other is! ShadcnSidebarTheme) return this;
    return ShadcnSidebarTheme(
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t),
      foregroundColor: Color.lerp(foregroundColor, other.foregroundColor, t),
      borderColor: Color.lerp(borderColor, other.borderColor, t),
      headerBackground: Color.lerp(headerBackground, other.headerBackground, t),
      itemHoverColor: Color.lerp(itemHoverColor, other.itemHoverColor, t),
      itemSelectedColor: Color.lerp(itemSelectedColor, other.itemSelectedColor, t),
      itemSelectedForegroundColor: Color.lerp(itemSelectedForegroundColor, other.itemSelectedForegroundColor, t),
      width: lerpDouble(width, other.width, t),
      borderWidth: lerpDouble(borderWidth, other.borderWidth, t),
      padding: EdgeInsets.lerp(padding, other.padding, t),
      headerPadding: EdgeInsets.lerp(headerPadding, other.headerPadding, t),
      itemPadding: EdgeInsets.lerp(itemPadding, other.itemPadding, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      headerTextStyle: TextStyle.lerp(headerTextStyle, other.headerTextStyle, t),
      itemTextStyle: TextStyle.lerp(itemTextStyle, other.itemTextStyle, t),
      iconSize: lerpDouble(iconSize, other.iconSize, t),
      itemSpacing: lerpDouble(itemSpacing, other.itemSpacing, t),
      animationDuration: lerpDuration(animationDuration ?? Duration.zero, other.animationDuration ?? Duration.zero, t),
    );
  }

  static ShadcnSidebarTheme defaultTheme(ColorScheme colorScheme) {
    return ShadcnSidebarTheme(
      backgroundColor: colorScheme.surface,
      foregroundColor: colorScheme.onSurface,
      borderColor: colorScheme.outline,
      headerBackground: colorScheme.surface,
      itemHoverColor: colorScheme.onSurface.withOpacity(0.04),
      itemSelectedColor: colorScheme.primary,
      itemSelectedForegroundColor: colorScheme.onPrimary,
      width: 280.0,
      borderWidth: ShadcnTokens.borderWidth,
      padding: const EdgeInsets.all(ShadcnTokens.spacing4),
      headerPadding: const EdgeInsets.all(ShadcnTokens.spacing4),
      itemPadding: const EdgeInsets.symmetric(
        horizontal: ShadcnTokens.spacing3,
        vertical: ShadcnTokens.spacing2,
      ),
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusMd),
      headerTextStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeLg,
        fontWeight: ShadcnTokens.fontWeightSemibold,
      ),
      itemTextStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightMedium,
      ),
      iconSize: ShadcnTokens.iconSizeMd,
      itemSpacing: ShadcnTokens.spacing1,
      animationDuration: ShadcnTokens.durationFast,
    );
  }
}

// Helper functions
Duration lerpDuration(Duration a, Duration b, double t) {
  return Duration(
    microseconds: (a.inMicroseconds + ((b.inMicroseconds - a.inMicroseconds) * t)).round(),
  );
}

double? lerpDouble(double? a, double? b, double t) {
  if (a == null && b == null) return null;
  a ??= 0.0;
  b ??= 0.0;
  return a + (b - a) * t;
}