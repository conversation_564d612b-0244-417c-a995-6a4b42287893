import 'package:flutter/material.dart';
import '../../constants/shadcn_tokens.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for the ShadcnCheckbox component.
/// 
/// This theme extension provides all styling properties for checkbox variants,
/// sizes, and interactive states. It follows shadcn design principles while
/// integrating with Material Design theming patterns.
/// 
/// All properties are nullable to support fallback to Material theme values
/// and provide flexible customization options for developers.
class ShadcnCheckboxTheme extends ShadcnThemeExtension<ShadcnCheckboxTheme> {
  // Checked state colors
  final Color? checkedBackground;
  final Color? checkedForeground;
  final Color? checkedBorder;
  
  // Unchecked state colors
  final Color? uncheckedBackground;
  final Color? uncheckedForeground;
  final Color? uncheckedBorder;
  
  // Intermediate state colors (for tristate checkboxes)
  final Color? intermediateBackground;
  final Color? intermediateForeground;
  final Color? intermediateBorder;
  
  // Interactive state overlays
  final Color? hoverOverlay;
  final Color? pressedOverlay;
  final Color? focusedOverlay;
  
  // Disabled state colors
  final Color? disabledCheckedBackground;
  final Color? disabledCheckedForeground;
  final Color? disabledCheckedBorder;
  final Color? disabledUncheckedBackground;
  final Color? disabledUncheckedForeground;
  final Color? disabledUncheckedBorder;
  
  // Sizing properties
  final double? size;
  final double? smallSize;
  final double? largeSize;
  
  // Border properties
  final double? borderWidth;
  final BorderRadius? borderRadius;
  
  // Spacing and layout
  final EdgeInsets? padding;
  final double? labelSpacing;
  
  // Text styling
  final TextStyle? labelStyle;
  final TextStyle? helperStyle;
  final TextStyle? errorStyle;
  
  // Animation properties
  final Duration? animationDuration;
  final Curve? animationCurve;
  
  // Focus ring properties
  final double? focusRingWidth;
  final double? focusRingOffset;
  
  // Check mark properties
  final double? checkMarkStrokeWidth;
  final Color? checkMarkColor;
  final Color? disabledCheckMarkColor;

  const ShadcnCheckboxTheme({
    // Checked state colors
    this.checkedBackground,
    this.checkedForeground,
    this.checkedBorder,
    
    // Unchecked state colors
    this.uncheckedBackground,
    this.uncheckedForeground,
    this.uncheckedBorder,
    
    // Intermediate state colors
    this.intermediateBackground,
    this.intermediateForeground,
    this.intermediateBorder,
    
    // Interactive state overlays
    this.hoverOverlay,
    this.pressedOverlay,
    this.focusedOverlay,
    
    // Disabled state colors
    this.disabledCheckedBackground,
    this.disabledCheckedForeground,
    this.disabledCheckedBorder,
    this.disabledUncheckedBackground,
    this.disabledUncheckedForeground,
    this.disabledUncheckedBorder,
    
    // Sizing properties
    this.size,
    this.smallSize,
    this.largeSize,
    
    // Border properties
    this.borderWidth,
    this.borderRadius,
    
    // Spacing and layout
    this.padding,
    this.labelSpacing,
    
    // Text styling
    this.labelStyle,
    this.helperStyle,
    this.errorStyle,
    
    // Animation properties
    this.animationDuration,
    this.animationCurve,
    
    // Focus ring properties
    this.focusRingWidth,
    this.focusRingOffset,
    
    // Check mark properties
    this.checkMarkStrokeWidth,
    this.checkMarkColor,
    this.disabledCheckMarkColor,
  });

  /// Creates a ShadcnCheckboxTheme with default values based on the provided ColorScheme.
  /// 
  /// This factory constructor provides sensible defaults that follow shadcn design
  /// principles while integrating with Material Design color schemes.
  static ShadcnCheckboxTheme defaultTheme(ColorScheme colorScheme) {
    final brightness = colorScheme.brightness;
    final isDark = brightness == Brightness.dark;
    
    return ShadcnCheckboxTheme(
      // Checked state colors
      checkedBackground: colorScheme.primary,
      checkedForeground: colorScheme.onPrimary,
      checkedBorder: colorScheme.primary,
      
      // Unchecked state colors
      uncheckedBackground: Colors.transparent,
      uncheckedForeground: colorScheme.onSurface,
      uncheckedBorder: isDark 
        ? colorScheme.outline.withOpacity(0.6)
        : colorScheme.outline,
      
      // Intermediate state colors
      intermediateBackground: colorScheme.primary,
      intermediateForeground: colorScheme.onPrimary,
      intermediateBorder: colorScheme.primary,
      
      // Interactive state overlays
      hoverOverlay: colorScheme.primary.withOpacity(0.08),
      pressedOverlay: colorScheme.primary.withOpacity(0.12),
      focusedOverlay: colorScheme.primary.withOpacity(0.12),
      
      // Disabled state colors
      disabledCheckedBackground: colorScheme.onSurface.withOpacity(0.12),
      disabledCheckedForeground: colorScheme.surface,
      disabledCheckedBorder: colorScheme.onSurface.withOpacity(0.12),
      disabledUncheckedBackground: Colors.transparent,
      disabledUncheckedForeground: colorScheme.onSurface.withOpacity(0.38),
      disabledUncheckedBorder: colorScheme.onSurface.withOpacity(0.12),
      
      // Sizing properties
      size: 20.0,
      smallSize: 16.0,
      largeSize: 24.0,
      
      // Border properties
      borderWidth: ShadcnTokens.borderWidth,
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusSm),
      
      // Spacing and layout
      padding: EdgeInsets.all(ShadcnTokens.spacing1),
      labelSpacing: ShadcnTokens.spacing2,
      
      // Text styling
      labelStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightNormal,
        color: colorScheme.onSurface,
        height: ShadcnTokens.lineHeightNormal,
      ),
      helperStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeSm,
        fontWeight: ShadcnTokens.fontWeightNormal,
        color: colorScheme.onSurface.withOpacity(0.6),
        height: ShadcnTokens.lineHeightNormal,
      ),
      errorStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeSm,
        fontWeight: ShadcnTokens.fontWeightNormal,
        color: colorScheme.error,
        height: ShadcnTokens.lineHeightNormal,
      ),
      
      // Animation properties
      animationDuration: ShadcnTokens.durationFast,
      animationCurve: Curves.easeInOut,
      
      // Focus ring properties
      focusRingWidth: 2.0,
      focusRingOffset: 2.0,
      
      // Check mark properties
      checkMarkStrokeWidth: 2.0,
      checkMarkColor: colorScheme.onPrimary,
      disabledCheckMarkColor: colorScheme.surface,
    );
  }

  @override
  ShadcnCheckboxTheme copyWith({
    // Checked state colors
    Color? checkedBackground,
    Color? checkedForeground,
    Color? checkedBorder,
    
    // Unchecked state colors
    Color? uncheckedBackground,
    Color? uncheckedForeground,
    Color? uncheckedBorder,
    
    // Intermediate state colors
    Color? intermediateBackground,
    Color? intermediateForeground,
    Color? intermediateBorder,
    
    // Interactive state overlays
    Color? hoverOverlay,
    Color? pressedOverlay,
    Color? focusedOverlay,
    
    // Disabled state colors
    Color? disabledCheckedBackground,
    Color? disabledCheckedForeground,
    Color? disabledCheckedBorder,
    Color? disabledUncheckedBackground,
    Color? disabledUncheckedForeground,
    Color? disabledUncheckedBorder,
    
    // Sizing properties
    double? size,
    double? smallSize,
    double? largeSize,
    
    // Border properties
    double? borderWidth,
    BorderRadius? borderRadius,
    
    // Spacing and layout
    EdgeInsets? padding,
    double? labelSpacing,
    
    // Text styling
    TextStyle? labelStyle,
    TextStyle? helperStyle,
    TextStyle? errorStyle,
    
    // Animation properties
    Duration? animationDuration,
    Curve? animationCurve,
    
    // Focus ring properties
    double? focusRingWidth,
    double? focusRingOffset,
    
    // Check mark properties
    double? checkMarkStrokeWidth,
    Color? checkMarkColor,
    Color? disabledCheckMarkColor,
  }) {
    return ShadcnCheckboxTheme(
      // Checked state colors
      checkedBackground: checkedBackground ?? this.checkedBackground,
      checkedForeground: checkedForeground ?? this.checkedForeground,
      checkedBorder: checkedBorder ?? this.checkedBorder,
      
      // Unchecked state colors
      uncheckedBackground: uncheckedBackground ?? this.uncheckedBackground,
      uncheckedForeground: uncheckedForeground ?? this.uncheckedForeground,
      uncheckedBorder: uncheckedBorder ?? this.uncheckedBorder,
      
      // Intermediate state colors
      intermediateBackground: intermediateBackground ?? this.intermediateBackground,
      intermediateForeground: intermediateForeground ?? this.intermediateForeground,
      intermediateBorder: intermediateBorder ?? this.intermediateBorder,
      
      // Interactive state overlays
      hoverOverlay: hoverOverlay ?? this.hoverOverlay,
      pressedOverlay: pressedOverlay ?? this.pressedOverlay,
      focusedOverlay: focusedOverlay ?? this.focusedOverlay,
      
      // Disabled state colors
      disabledCheckedBackground: disabledCheckedBackground ?? this.disabledCheckedBackground,
      disabledCheckedForeground: disabledCheckedForeground ?? this.disabledCheckedForeground,
      disabledCheckedBorder: disabledCheckedBorder ?? this.disabledCheckedBorder,
      disabledUncheckedBackground: disabledUncheckedBackground ?? this.disabledUncheckedBackground,
      disabledUncheckedForeground: disabledUncheckedForeground ?? this.disabledUncheckedForeground,
      disabledUncheckedBorder: disabledUncheckedBorder ?? this.disabledUncheckedBorder,
      
      // Sizing properties
      size: size ?? this.size,
      smallSize: smallSize ?? this.smallSize,
      largeSize: largeSize ?? this.largeSize,
      
      // Border properties
      borderWidth: borderWidth ?? this.borderWidth,
      borderRadius: borderRadius ?? this.borderRadius,
      
      // Spacing and layout
      padding: padding ?? this.padding,
      labelSpacing: labelSpacing ?? this.labelSpacing,
      
      // Text styling
      labelStyle: labelStyle ?? this.labelStyle,
      helperStyle: helperStyle ?? this.helperStyle,
      errorStyle: errorStyle ?? this.errorStyle,
      
      // Animation properties
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
      
      // Focus ring properties
      focusRingWidth: focusRingWidth ?? this.focusRingWidth,
      focusRingOffset: focusRingOffset ?? this.focusRingOffset,
      
      // Check mark properties
      checkMarkStrokeWidth: checkMarkStrokeWidth ?? this.checkMarkStrokeWidth,
      checkMarkColor: checkMarkColor ?? this.checkMarkColor,
      disabledCheckMarkColor: disabledCheckMarkColor ?? this.disabledCheckMarkColor,
    );
  }

  @override
  ShadcnCheckboxTheme lerp(ShadcnCheckboxTheme? other, double t) {
    if (other is! ShadcnCheckboxTheme) {
      return this;
    }
    
    return ShadcnCheckboxTheme(
      // Checked state colors
      checkedBackground: Color.lerp(checkedBackground, other.checkedBackground, t),
      checkedForeground: Color.lerp(checkedForeground, other.checkedForeground, t),
      checkedBorder: Color.lerp(checkedBorder, other.checkedBorder, t),
      
      // Unchecked state colors
      uncheckedBackground: Color.lerp(uncheckedBackground, other.uncheckedBackground, t),
      uncheckedForeground: Color.lerp(uncheckedForeground, other.uncheckedForeground, t),
      uncheckedBorder: Color.lerp(uncheckedBorder, other.uncheckedBorder, t),
      
      // Intermediate state colors
      intermediateBackground: Color.lerp(intermediateBackground, other.intermediateBackground, t),
      intermediateForeground: Color.lerp(intermediateForeground, other.intermediateForeground, t),
      intermediateBorder: Color.lerp(intermediateBorder, other.intermediateBorder, t),
      
      // Interactive state overlays
      hoverOverlay: Color.lerp(hoverOverlay, other.hoverOverlay, t),
      pressedOverlay: Color.lerp(pressedOverlay, other.pressedOverlay, t),
      focusedOverlay: Color.lerp(focusedOverlay, other.focusedOverlay, t),
      
      // Disabled state colors
      disabledCheckedBackground: Color.lerp(disabledCheckedBackground, other.disabledCheckedBackground, t),
      disabledCheckedForeground: Color.lerp(disabledCheckedForeground, other.disabledCheckedForeground, t),
      disabledCheckedBorder: Color.lerp(disabledCheckedBorder, other.disabledCheckedBorder, t),
      disabledUncheckedBackground: Color.lerp(disabledUncheckedBackground, other.disabledUncheckedBackground, t),
      disabledUncheckedForeground: Color.lerp(disabledUncheckedForeground, other.disabledUncheckedForeground, t),
      disabledUncheckedBorder: Color.lerp(disabledUncheckedBorder, other.disabledUncheckedBorder, t),
      
      // Sizing properties
      size: lerpDouble(size, other.size, t),
      smallSize: lerpDouble(smallSize, other.smallSize, t),
      largeSize: lerpDouble(largeSize, other.largeSize, t),
      
      // Border properties
      borderWidth: lerpDouble(borderWidth, other.borderWidth, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      
      // Spacing and layout
      padding: EdgeInsets.lerp(padding, other.padding, t),
      labelSpacing: lerpDouble(labelSpacing, other.labelSpacing, t),
      
      // Text styling
      labelStyle: TextStyle.lerp(labelStyle, other.labelStyle, t),
      helperStyle: TextStyle.lerp(helperStyle, other.helperStyle, t),
      errorStyle: TextStyle.lerp(errorStyle, other.errorStyle, t),
      
      // Animation properties
      animationDuration: lerpDuration(animationDuration, other.animationDuration, t),
      animationCurve: other.animationCurve ?? animationCurve,
      
      // Focus ring properties
      focusRingWidth: lerpDouble(focusRingWidth, other.focusRingWidth, t),
      focusRingOffset: lerpDouble(focusRingOffset, other.focusRingOffset, t),
      
      // Check mark properties
      checkMarkStrokeWidth: lerpDouble(checkMarkStrokeWidth, other.checkMarkStrokeWidth, t),
      checkMarkColor: Color.lerp(checkMarkColor, other.checkMarkColor, t),
      disabledCheckMarkColor: Color.lerp(disabledCheckMarkColor, other.disabledCheckMarkColor, t),
    );
  }

  @override
  bool validate({bool throwOnError = false}) {
    try {
      // Validate that essential colors are not null in a complete theme
      if (checkedBackground != null && checkedForeground == null) {
        if (throwOnError) {
          throw ThemeException('ShadcnCheckboxTheme: checkedForeground cannot be null when checkedBackground is provided');
        }
        return false;
      }
      
      if (size != null && size! <= 0) {
        if (throwOnError) {
          throw ThemeException('ShadcnCheckboxTheme: size must be greater than 0');
        }
        return false;
      }
      
      if (borderWidth != null && borderWidth! < 0) {
        if (throwOnError) {
          throw ThemeException('ShadcnCheckboxTheme: borderWidth cannot be negative');
        }
        return false;
      }
      
      return true;
    } catch (e) {
      if (throwOnError) rethrow;
      return false;
    }
  }

  /// Helper method to resolve size based on the size variant
  double resolveSizeForVariant(ShadcnCheckboxSize sizeVariant) {
    switch (sizeVariant) {
      case ShadcnCheckboxSize.small:
        return smallSize ?? 16.0;
      case ShadcnCheckboxSize.medium:
        return size ?? 20.0;
      case ShadcnCheckboxSize.large:
        return largeSize ?? 24.0;
    }
  }
}

/// Helper method for lerping durations
Duration? lerpDuration(Duration? a, Duration? b, double t) {
  if (a == null && b == null) return null;
  if (a == null) return b;
  if (b == null) return a;
  return Duration(
    milliseconds: (a.inMilliseconds + ((b.inMilliseconds - a.inMilliseconds) * t)).round(),
  );
}

/// Helper method for lerping double values
double? lerpDouble(double? a, double? b, double t) {
  if (a == null && b == null) return null;
  if (a == null) return b;
  if (b == null) return a;
  return a + (b - a) * t;
}

/// Exception thrown when theme validation fails
class ThemeException implements Exception {
  final String message;
  
  const ThemeException(this.message);
  
  @override
  String toString() => message;
}

/// Enum for checkbox size variants
enum ShadcnCheckboxSize {
  small,
  medium,
  large,
}