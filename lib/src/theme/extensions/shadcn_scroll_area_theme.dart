import 'package:flutter/material.dart';
import 'dart:ui' show lerpDouble;
import 'shadcn_theme_extension.dart';
import '../../constants/shadcn_tokens.dart';

/// Theme extension for ShadcnScrollArea component.
/// 
/// Provides theming properties for custom scrollable areas with shadcn-styled
/// scrollbars. Integrates with Material Design theming while providing
/// enhanced scrollbar customization beyond standard Material scrollbars.
class ShadcnScrollAreaTheme extends ShadcnThemeExtension<ShadcnScrollAreaTheme> {
  /// Background color of the scroll area container
  final Color? backgroundColor;
  
  /// Border color of the scroll area container
  final Color? borderColor;
  
  /// Border width of the scroll area container
  final double? borderWidth;
  
  /// Border radius of the scroll area container
  final BorderRadius? borderRadius;
  
  /// Padding inside the scroll area container
  final EdgeInsets? padding;
  
  /// Margin outside the scroll area container
  final EdgeInsets? margin;
  
  /// Scrollbar track color (background of the scrollbar area)
  final Color? scrollbarTrackColor;
  
  /// Scrollbar thumb color (the draggable part)
  final Color? scrollbarThumbColor;
  
  /// Scrollbar thumb color when hovered
  final Color? scrollbarThumbHoverColor;
  
  /// Scrollbar thumb color when active/dragged
  final Color? scrollbarThumbActiveColor;
  
  /// Width of the scrollbar
  final double? scrollbarWidth;
  
  /// Thickness of the scrollbar (synonym for width, for clarity)
  final double? scrollbarThickness;
  
  /// Border radius of the scrollbar thumb
  final BorderRadius? scrollbarThumbRadius;
  
  /// Border radius of the scrollbar track
  final BorderRadius? scrollbarTrackRadius;
  
  /// Minimum height of the scrollbar thumb
  final double? scrollbarThumbMinHeight;
  
  /// Whether scrollbar should always be visible
  final bool? scrollbarAlwaysVisible;
  
  /// Whether scrollbar should show track
  final bool? scrollbarShowTrack;
  
  /// Duration for scrollbar fade animations
  final Duration? scrollbarFadeDuration;
  
  /// Cross-axis margin for the scrollbar
  final EdgeInsets? scrollbarMargin;
  
  /// Interactive scrollbar behavior (hover, click to jump)
  final bool? scrollbarInteractive;
  
  /// Minimum thumb extent as fraction of viewport
  final double? scrollbarMinThumbExtent;
  
  const ShadcnScrollAreaTheme({
    this.backgroundColor,
    this.borderColor,
    this.borderWidth,
    this.borderRadius,
    this.padding,
    this.margin,
    this.scrollbarTrackColor,
    this.scrollbarThumbColor,
    this.scrollbarThumbHoverColor,
    this.scrollbarThumbActiveColor,
    this.scrollbarWidth,
    this.scrollbarThickness,
    this.scrollbarThumbRadius,
    this.scrollbarTrackRadius,
    this.scrollbarThumbMinHeight,
    this.scrollbarAlwaysVisible,
    this.scrollbarShowTrack,
    this.scrollbarFadeDuration,
    this.scrollbarMargin,
    this.scrollbarInteractive,
    this.scrollbarMinThumbExtent,
  });

  @override
  ShadcnScrollAreaTheme copyWith({
    Color? backgroundColor,
    Color? borderColor,
    double? borderWidth,
    BorderRadius? borderRadius,
    EdgeInsets? padding,
    EdgeInsets? margin,
    Color? scrollbarTrackColor,
    Color? scrollbarThumbColor,
    Color? scrollbarThumbHoverColor,
    Color? scrollbarThumbActiveColor,
    double? scrollbarWidth,
    double? scrollbarThickness,
    BorderRadius? scrollbarThumbRadius,
    BorderRadius? scrollbarTrackRadius,
    double? scrollbarThumbMinHeight,
    bool? scrollbarAlwaysVisible,
    bool? scrollbarShowTrack,
    Duration? scrollbarFadeDuration,
    EdgeInsets? scrollbarMargin,
    bool? scrollbarInteractive,
    double? scrollbarMinThumbExtent,
  }) {
    return ShadcnScrollAreaTheme(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      borderColor: borderColor ?? this.borderColor,
      borderWidth: borderWidth ?? this.borderWidth,
      borderRadius: borderRadius ?? this.borderRadius,
      padding: padding ?? this.padding,
      margin: margin ?? this.margin,
      scrollbarTrackColor: scrollbarTrackColor ?? this.scrollbarTrackColor,
      scrollbarThumbColor: scrollbarThumbColor ?? this.scrollbarThumbColor,
      scrollbarThumbHoverColor: scrollbarThumbHoverColor ?? this.scrollbarThumbHoverColor,
      scrollbarThumbActiveColor: scrollbarThumbActiveColor ?? this.scrollbarThumbActiveColor,
      scrollbarWidth: scrollbarWidth ?? this.scrollbarWidth,
      scrollbarThickness: scrollbarThickness ?? this.scrollbarThickness,
      scrollbarThumbRadius: scrollbarThumbRadius ?? this.scrollbarThumbRadius,
      scrollbarTrackRadius: scrollbarTrackRadius ?? this.scrollbarTrackRadius,
      scrollbarThumbMinHeight: scrollbarThumbMinHeight ?? this.scrollbarThumbMinHeight,
      scrollbarAlwaysVisible: scrollbarAlwaysVisible ?? this.scrollbarAlwaysVisible,
      scrollbarShowTrack: scrollbarShowTrack ?? this.scrollbarShowTrack,
      scrollbarFadeDuration: scrollbarFadeDuration ?? this.scrollbarFadeDuration,
      scrollbarMargin: scrollbarMargin ?? this.scrollbarMargin,
      scrollbarInteractive: scrollbarInteractive ?? this.scrollbarInteractive,
      scrollbarMinThumbExtent: scrollbarMinThumbExtent ?? this.scrollbarMinThumbExtent,
    );
  }

  @override
  ShadcnScrollAreaTheme lerp(
    covariant ThemeExtension<ShadcnScrollAreaTheme>? other, 
    double t,
  ) {
    if (other is! ShadcnScrollAreaTheme) {
      return this;
    }

    return ShadcnScrollAreaTheme(
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t),
      borderColor: Color.lerp(borderColor, other.borderColor, t),
      borderWidth: lerpDouble(borderWidth, other.borderWidth, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      padding: EdgeInsets.lerp(padding, other.padding, t),
      margin: EdgeInsets.lerp(margin, other.margin, t),
      scrollbarTrackColor: Color.lerp(scrollbarTrackColor, other.scrollbarTrackColor, t),
      scrollbarThumbColor: Color.lerp(scrollbarThumbColor, other.scrollbarThumbColor, t),
      scrollbarThumbHoverColor: Color.lerp(scrollbarThumbHoverColor, other.scrollbarThumbHoverColor, t),
      scrollbarThumbActiveColor: Color.lerp(scrollbarThumbActiveColor, other.scrollbarThumbActiveColor, t),
      scrollbarWidth: lerpDouble(scrollbarWidth, other.scrollbarWidth, t),
      scrollbarThickness: lerpDouble(scrollbarThickness, other.scrollbarThickness, t),
      scrollbarThumbRadius: BorderRadius.lerp(scrollbarThumbRadius, other.scrollbarThumbRadius, t),
      scrollbarTrackRadius: BorderRadius.lerp(scrollbarTrackRadius, other.scrollbarTrackRadius, t),
      scrollbarThumbMinHeight: lerpDouble(scrollbarThumbMinHeight, other.scrollbarThumbMinHeight, t),
      scrollbarAlwaysVisible: t < 0.5 ? scrollbarAlwaysVisible : other.scrollbarAlwaysVisible,
      scrollbarShowTrack: t < 0.5 ? scrollbarShowTrack : other.scrollbarShowTrack,
      scrollbarFadeDuration: Duration(milliseconds: lerpDouble(
        scrollbarFadeDuration?.inMilliseconds.toDouble(),
        other.scrollbarFadeDuration?.inMilliseconds.toDouble(),
        t,
      )?.toInt() ?? 300),
      scrollbarMargin: EdgeInsets.lerp(scrollbarMargin, other.scrollbarMargin, t),
      scrollbarInteractive: t < 0.5 ? scrollbarInteractive : other.scrollbarInteractive,
      scrollbarMinThumbExtent: lerpDouble(scrollbarMinThumbExtent, other.scrollbarMinThumbExtent, t),
    );
  }

  /// Creates a default theme based on the provided color scheme.
  /// 
  /// Uses shadcn design tokens and Material color schemes to provide
  /// consistent default styling that matches shadcn specifications.
  static ShadcnScrollAreaTheme defaultTheme(ColorScheme colorScheme) {
    return ShadcnScrollAreaTheme(
      backgroundColor: null, // Transparent by default
      borderColor: null, // No border by default
      borderWidth: ShadcnTokens.borderWidth,
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusMd),
      padding: EdgeInsets.zero,
      margin: EdgeInsets.zero,
      scrollbarTrackColor: colorScheme.surfaceVariant.withOpacity(0.3),
      scrollbarThumbColor: colorScheme.outline.withOpacity(0.5),
      scrollbarThumbHoverColor: colorScheme.outline.withOpacity(0.7),
      scrollbarThumbActiveColor: colorScheme.outline,
      scrollbarWidth: 6.0,
      scrollbarThickness: 6.0,
      scrollbarThumbRadius: BorderRadius.circular(ShadcnTokens.radiusFull),
      scrollbarTrackRadius: BorderRadius.circular(ShadcnTokens.radiusFull),
      scrollbarThumbMinHeight: 20.0,
      scrollbarAlwaysVisible: false,
      scrollbarShowTrack: false,
      scrollbarFadeDuration: ShadcnTokens.durationFast,
      scrollbarMargin: EdgeInsets.all(ShadcnTokens.spacing1),
      scrollbarInteractive: true,
      scrollbarMinThumbExtent: 0.1,
    );
  }

  /// Creates a light theme variant with subtle scrollbar styling.
  static ShadcnScrollAreaTheme lightTheme(ColorScheme colorScheme) {
    return ShadcnScrollAreaTheme(
      backgroundColor: colorScheme.surface,
      borderColor: colorScheme.outlineVariant,
      borderWidth: ShadcnTokens.borderWidth,
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusMd),
      padding: ShadcnTokens.paddingAll(ShadcnTokens.spacing2),
      margin: EdgeInsets.zero,
      scrollbarTrackColor: colorScheme.surfaceVariant.withOpacity(0.2),
      scrollbarThumbColor: colorScheme.outline.withOpacity(0.4),
      scrollbarThumbHoverColor: colorScheme.outline.withOpacity(0.6),
      scrollbarThumbActiveColor: colorScheme.outline.withOpacity(0.8),
      scrollbarWidth: 8.0,
      scrollbarThickness: 8.0,
      scrollbarThumbRadius: BorderRadius.circular(ShadcnTokens.radiusSm),
      scrollbarTrackRadius: BorderRadius.circular(ShadcnTokens.radiusSm),
      scrollbarThumbMinHeight: 24.0,
      scrollbarAlwaysVisible: false,
      scrollbarShowTrack: true,
      scrollbarFadeDuration: ShadcnTokens.durationNormal,
      scrollbarMargin: EdgeInsets.all(ShadcnTokens.spacing1),
      scrollbarInteractive: true,
      scrollbarMinThumbExtent: 0.1,
    );
  }

  /// Creates a dark theme variant with enhanced scrollbar visibility.
  static ShadcnScrollAreaTheme darkTheme(ColorScheme colorScheme) {
    return ShadcnScrollAreaTheme(
      backgroundColor: colorScheme.surface,
      borderColor: colorScheme.outline.withOpacity(0.3),
      borderWidth: ShadcnTokens.borderWidth,
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusMd),
      padding: ShadcnTokens.paddingAll(ShadcnTokens.spacing2),
      margin: EdgeInsets.zero,
      scrollbarTrackColor: colorScheme.surfaceVariant.withOpacity(0.1),
      scrollbarThumbColor: colorScheme.outline.withOpacity(0.6),
      scrollbarThumbHoverColor: colorScheme.outline.withOpacity(0.8),
      scrollbarThumbActiveColor: colorScheme.outline,
      scrollbarWidth: 8.0,
      scrollbarThickness: 8.0,
      scrollbarThumbRadius: BorderRadius.circular(ShadcnTokens.radiusSm),
      scrollbarTrackRadius: BorderRadius.circular(ShadcnTokens.radiusSm),
      scrollbarThumbMinHeight: 24.0,
      scrollbarAlwaysVisible: false,
      scrollbarShowTrack: true,
      scrollbarFadeDuration: ShadcnTokens.durationNormal,
      scrollbarMargin: EdgeInsets.all(ShadcnTokens.spacing1),
      scrollbarInteractive: true,
      scrollbarMinThumbExtent: 0.1,
    );
  }

  /// Gets the resolved background color for the scroll area container.
  Color? getBackgroundColor(BuildContext context) {
    if (backgroundColor == null) return null;
    
    return resolveColor(
      context,
      backgroundColor,
      (theme) => theme.colorScheme.surface,
      Colors.transparent,
    );
  }

  /// Gets the resolved border color for the scroll area container.
  Color? getBorderColor(BuildContext context) {
    if (borderColor == null) return null;
    
    return resolveColor(
      context,
      borderColor,
      (theme) => theme.colorScheme.outline,
      Colors.grey.shade400,
    );
  }

  /// Gets the resolved scrollbar thickness (prioritizes scrollbarThickness over scrollbarWidth).
  double getScrollbarThickness(BuildContext context) {
    final thickness = scrollbarThickness ?? scrollbarWidth ?? 6.0;
    return resolveDouble(
      context,
      thickness,
      6.0,
      applyVerticalDensity: false,
    ).clamp(2.0, 20.0);
  }

  /// Gets the resolved scrollbar track color.
  Color getScrollbarTrackColor(BuildContext context) {
    return resolveColor(
      context,
      scrollbarTrackColor,
      (theme) => theme.colorScheme.surfaceVariant.withOpacity(0.3),
      Colors.grey.shade300.withOpacity(0.3),
    );
  }

  /// Gets the resolved scrollbar thumb color.
  Color getScrollbarThumbColor(BuildContext context) {
    return resolveColor(
      context,
      scrollbarThumbColor,
      (theme) => theme.colorScheme.outline.withOpacity(0.5),
      Colors.grey.shade500.withOpacity(0.5),
    );
  }

  /// Gets the resolved scrollbar thumb hover color.
  Color getScrollbarThumbHoverColor(BuildContext context) {
    return resolveColor(
      context,
      scrollbarThumbHoverColor,
      (theme) => theme.colorScheme.outline.withOpacity(0.7),
      Colors.grey.shade600.withOpacity(0.7),
    );
  }

  /// Gets the resolved scrollbar thumb active color.
  Color getScrollbarThumbActiveColor(BuildContext context) {
    return resolveColor(
      context,
      scrollbarThumbActiveColor,
      (theme) => theme.colorScheme.outline,
      Colors.grey.shade700,
    );
  }

  /// Gets the resolved scrollbar thumb radius.
  BorderRadius getScrollbarThumbRadius(BuildContext context) {
    return resolveBorderRadius(
      context,
      scrollbarThumbRadius,
      BorderRadius.circular(ShadcnTokens.radiusFull),
    );
  }

  /// Gets the resolved scrollbar track radius.
  BorderRadius getScrollbarTrackRadius(BuildContext context) {
    return resolveBorderRadius(
      context,
      scrollbarTrackRadius,
      BorderRadius.circular(ShadcnTokens.radiusFull),
    );
  }

  /// Gets the resolved minimum thumb height.
  double getScrollbarThumbMinHeight(BuildContext context) {
    return resolveDouble(
      context,
      scrollbarThumbMinHeight,
      20.0,
      applyVerticalDensity: true,
    ).clamp(12.0, 100.0);
  }

  /// Gets whether the scrollbar should always be visible.
  bool getScrollbarAlwaysVisible(BuildContext context) {
    return scrollbarAlwaysVisible ?? false;
  }

  /// Gets whether the scrollbar should show the track.
  bool getScrollbarShowTrack(BuildContext context) {
    return scrollbarShowTrack ?? false;
  }

  /// Gets the scrollbar fade animation duration.
  Duration getScrollbarFadeDuration(BuildContext context) {
    return scrollbarFadeDuration ?? ShadcnTokens.durationFast;
  }

  /// Gets the resolved scrollbar margin.
  EdgeInsets getScrollbarMargin(BuildContext context) {
    return resolveSpacing(
      context,
      scrollbarMargin,
      EdgeInsets.all(ShadcnTokens.spacing1),
    );
  }

  /// Gets whether the scrollbar should be interactive.
  bool getScrollbarInteractive(BuildContext context) {
    return scrollbarInteractive ?? true;
  }

  /// Gets the minimum thumb extent as a fraction.
  double getScrollbarMinThumbExtent(BuildContext context) {
    final extent = scrollbarMinThumbExtent ?? 0.1;
    return extent.clamp(0.05, 0.5);
  }

  /// Gets the resolved padding for the scroll area container.
  EdgeInsets getPadding(BuildContext context) {
    return resolveSpacing(
      context,
      padding,
      EdgeInsets.zero,
    );
  }

  /// Gets the resolved margin for the scroll area container.
  EdgeInsets getMargin(BuildContext context) {
    return resolveSpacing(
      context,
      margin,
      EdgeInsets.zero,
    );
  }

  /// Gets the resolved border radius for the scroll area container.
  BorderRadius getBorderRadius(BuildContext context) {
    return resolveBorderRadius(
      context,
      borderRadius,
      BorderRadius.circular(ShadcnTokens.radiusMd),
    );
  }

  /// Gets the resolved border width for the scroll area container.
  double getBorderWidth(BuildContext context) {
    if (borderColor == null) return 0.0;
    
    return resolveDouble(
      context,
      borderWidth,
      ShadcnTokens.borderWidth,
      applyVerticalDensity: false,
    ).clamp(0.0, double.infinity);
  }

  @override
  bool validate({bool throwOnError = false}) {
    try {
      // Validate scrollbar thickness
      final thickness = scrollbarThickness ?? scrollbarWidth;
      if (thickness != null && thickness < 0) {
        if (throwOnError) {
          throw ArgumentError('Scrollbar thickness must be non-negative');
        }
        return false;
      }

      // Validate minimum thumb height
      if (scrollbarThumbMinHeight != null && scrollbarThumbMinHeight! < 0) {
        if (throwOnError) {
          throw ArgumentError('Scrollbar thumb minimum height must be non-negative');
        }
        return false;
      }

      // Validate minimum thumb extent
      if (scrollbarMinThumbExtent != null && 
          (scrollbarMinThumbExtent! < 0 || scrollbarMinThumbExtent! > 1)) {
        if (throwOnError) {
          throw ArgumentError('Scrollbar minimum thumb extent must be between 0 and 1');
        }
        return false;
      }

      // Validate border width
      if (borderWidth != null && borderWidth! < 0) {
        if (throwOnError) {
          throw ArgumentError('Border width must be non-negative');
        }
        return false;
      }

      return true;
    } catch (e) {
      if (throwOnError) rethrow;
      return false;
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ShadcnScrollAreaTheme &&
          runtimeType == other.runtimeType &&
          backgroundColor == other.backgroundColor &&
          borderColor == other.borderColor &&
          borderWidth == other.borderWidth &&
          borderRadius == other.borderRadius &&
          padding == other.padding &&
          margin == other.margin &&
          scrollbarTrackColor == other.scrollbarTrackColor &&
          scrollbarThumbColor == other.scrollbarThumbColor &&
          scrollbarThumbHoverColor == other.scrollbarThumbHoverColor &&
          scrollbarThumbActiveColor == other.scrollbarThumbActiveColor &&
          scrollbarWidth == other.scrollbarWidth &&
          scrollbarThickness == other.scrollbarThickness &&
          scrollbarThumbRadius == other.scrollbarThumbRadius &&
          scrollbarTrackRadius == other.scrollbarTrackRadius &&
          scrollbarThumbMinHeight == other.scrollbarThumbMinHeight &&
          scrollbarAlwaysVisible == other.scrollbarAlwaysVisible &&
          scrollbarShowTrack == other.scrollbarShowTrack &&
          scrollbarFadeDuration == other.scrollbarFadeDuration &&
          scrollbarMargin == other.scrollbarMargin &&
          scrollbarInteractive == other.scrollbarInteractive &&
          scrollbarMinThumbExtent == other.scrollbarMinThumbExtent;

  @override
  int get hashCode => Object.hashAll([
      backgroundColor,
      borderColor,
      borderWidth,
      borderRadius,
      padding,
      margin,
      scrollbarTrackColor,
      scrollbarThumbColor,
      scrollbarThumbHoverColor,
      scrollbarThumbActiveColor,
      scrollbarWidth,
      scrollbarThickness,
      scrollbarThumbRadius,
      scrollbarTrackRadius,
      scrollbarThumbMinHeight,
      scrollbarAlwaysVisible,
      scrollbarShowTrack,
      scrollbarFadeDuration,
      scrollbarMargin,
      scrollbarInteractive,
      scrollbarMinThumbExtent,
    ]);

  @override
  String toString() {
    return 'ShadcnScrollAreaTheme('
        'backgroundColor: $backgroundColor, '
        'borderColor: $borderColor, '
        'scrollbarThickness: ${scrollbarThickness ?? scrollbarWidth}, '
        'scrollbarThumbColor: $scrollbarThumbColor, '
        'scrollbarAlwaysVisible: $scrollbarAlwaysVisible'
        ')';
  }
}

/// Extension for convenient scroll area theme access.
/// 
/// Provides convenient methods for accessing scroll area theme properties
/// from the current theme context.
extension ShadcnScrollAreaThemeExtension on ThemeData {
  /// Gets the scroll area theme extension from this theme data.
  ShadcnScrollAreaTheme? get scrollAreaTheme => extension<ShadcnScrollAreaTheme>();

  /// Gets the scroll area theme extension or creates a default one.
  ShadcnScrollAreaTheme scrollAreaThemeOrDefault() =>
      scrollAreaTheme ?? ShadcnScrollAreaTheme.defaultTheme(colorScheme);
}