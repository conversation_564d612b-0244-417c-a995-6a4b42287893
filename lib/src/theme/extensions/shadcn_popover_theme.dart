import 'package:flutter/material.dart';
import '../../../shadcn.dart';

/// Theme extension for Popover components.
/// 
/// This extension provides comprehensive theming for popover overlays including
/// positioning, arrow styling, shadows, and background colors. All properties
/// integrate with Material Design while providing shadcn-specific styling.
class ShadcnPopoverTheme extends ShadcnThemeExtension<ShadcnPopoverTheme> {
  /// Background color of the popover.
  final Color? backgroundColor;
  
  /// Foreground color for text content.
  final Color? foregroundColor;
  
  /// Border color of the popover.
  final Color? borderColor;
  
  /// Shadow color for elevation effect.
  final Color? shadowColor;
  
  /// Border radius of the popover.
  final BorderRadius? borderRadius;
  
  /// Border width of the popover.
  final double? borderWidth;
  
  /// Elevation of the popover.
  final double? elevation;
  
  /// Padding inside the popover content.
  final EdgeInsets? contentPadding;
  
  /// Maximum width of the popover.
  final double? maxWidth;
  
  /// Maximum height of the popover.
  final double? maxHeight;
  
  /// Minimum width of the popover.
  final double? minWidth;
  
  /// Size of the arrow (width and height).
  final double? arrowSize;
  
  /// Color of the arrow.
  final Color? arrowColor;
  
  /// Offset of the popover from the target widget.
  final double? offset;
  
  /// Animation duration for popover transitions.
  final Duration? animationDuration;
  
  /// Animation curve for popover transitions.
  final Curve? animationCurve;
  
  /// Barrier color (overlay background).
  final Color? barrierColor;
  
  /// Text style for the popover content.
  final TextStyle? textStyle;

  const ShadcnPopoverTheme({
    this.backgroundColor,
    this.foregroundColor,
    this.borderColor,
    this.shadowColor,
    this.borderRadius,
    this.borderWidth,
    this.elevation,
    this.contentPadding,
    this.maxWidth,
    this.maxHeight,
    this.minWidth,
    this.arrowSize,
    this.arrowColor,
    this.offset,
    this.animationDuration,
    this.animationCurve,
    this.barrierColor,
    this.textStyle,
  });

  @override
  ShadcnPopoverTheme copyWith({
    Color? backgroundColor,
    Color? foregroundColor,
    Color? borderColor,
    Color? shadowColor,
    BorderRadius? borderRadius,
    double? borderWidth,
    double? elevation,
    EdgeInsets? contentPadding,
    double? maxWidth,
    double? maxHeight,
    double? minWidth,
    double? arrowSize,
    Color? arrowColor,
    double? offset,
    Duration? animationDuration,
    Curve? animationCurve,
    Color? barrierColor,
    TextStyle? textStyle,
  }) {
    return ShadcnPopoverTheme(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      foregroundColor: foregroundColor ?? this.foregroundColor,
      borderColor: borderColor ?? this.borderColor,
      shadowColor: shadowColor ?? this.shadowColor,
      borderRadius: borderRadius ?? this.borderRadius,
      borderWidth: borderWidth ?? this.borderWidth,
      elevation: elevation ?? this.elevation,
      contentPadding: contentPadding ?? this.contentPadding,
      maxWidth: maxWidth ?? this.maxWidth,
      maxHeight: maxHeight ?? this.maxHeight,
      minWidth: minWidth ?? this.minWidth,
      arrowSize: arrowSize ?? this.arrowSize,
      arrowColor: arrowColor ?? this.arrowColor,
      offset: offset ?? this.offset,
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
      barrierColor: barrierColor ?? this.barrierColor,
      textStyle: textStyle ?? this.textStyle,
    );
  }

  @override
  ShadcnPopoverTheme lerp(ShadcnPopoverTheme? other, double t) {
    if (other == null) return this;
    
    return ShadcnPopoverTheme(
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t),
      foregroundColor: Color.lerp(foregroundColor, other.foregroundColor, t),
      borderColor: Color.lerp(borderColor, other.borderColor, t),
      shadowColor: Color.lerp(shadowColor, other.shadowColor, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      borderWidth: t < 0.5 ? borderWidth : other.borderWidth,
      elevation: t < 0.5 ? elevation : other.elevation,
      contentPadding: EdgeInsets.lerp(contentPadding, other.contentPadding, t),
      maxWidth: t < 0.5 ? maxWidth : other.maxWidth,
      maxHeight: t < 0.5 ? maxHeight : other.maxHeight,
      minWidth: t < 0.5 ? minWidth : other.minWidth,
      arrowSize: t < 0.5 ? arrowSize : other.arrowSize,
      arrowColor: Color.lerp(arrowColor, other.arrowColor, t),
      offset: t < 0.5 ? offset : other.offset,
      animationDuration: t < 0.5 ? animationDuration : other.animationDuration,
      animationCurve: t < 0.5 ? animationCurve : other.animationCurve,
      barrierColor: Color.lerp(barrierColor, other.barrierColor, t),
      textStyle: TextStyle.lerp(textStyle, other.textStyle, t),
    );
  }

  /// Creates a default theme based on the provided color scheme.
  /// 
  /// This factory ensures that all popover components have sensible defaults
  /// that integrate well with Material Design while maintaining shadcn aesthetics.
  static ShadcnPopoverTheme defaultTheme(ColorScheme colorScheme) {
    return ShadcnPopoverTheme(
      backgroundColor: colorScheme.surface,
      foregroundColor: colorScheme.onSurface,
      borderColor: colorScheme.outline,
      shadowColor: colorScheme.shadow,
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusLg),
      borderWidth: ShadcnTokens.borderWidth,
      elevation: ShadcnTokens.elevationLg,
      contentPadding: EdgeInsets.all(ShadcnTokens.spacing4),
      maxWidth: 320.0,
      maxHeight: 400.0,
      minWidth: 160.0,
      arrowSize: 12.0,
      arrowColor: colorScheme.surface,
      offset: 8.0,
      animationDuration: ShadcnTokens.durationFast,
      animationCurve: Curves.easeOut,
      barrierColor: Colors.transparent,
      textStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightNormal,
        color: colorScheme.onSurface,
        height: ShadcnTokens.lineHeightNormal,
      ),
    );
  }

  /// Creates a theme with custom background and foreground colors.
  static ShadcnPopoverTheme customColors({
    required ColorScheme colorScheme,
    Color? backgroundColor,
    Color? foregroundColor,
    Color? borderColor,
  }) {
    final defaultTheme = ShadcnPopoverTheme.defaultTheme(colorScheme);
    return defaultTheme.copyWith(
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      borderColor: borderColor,
      arrowColor: backgroundColor,
      textStyle: defaultTheme.textStyle?.copyWith(
        color: foregroundColor,
      ),
    );
  }

  /// Resolves the background color using the theme-aware fallback chain.
  Color resolveBackgroundColor(BuildContext context) {
    return resolveColor(
      context,
      backgroundColor,
      (theme) => theme.colorScheme.surface,
    );
  }

  /// Resolves the foreground color using the theme-aware fallback chain.
  Color resolveForegroundColor(BuildContext context) {
    return resolveColor(
      context,
      foregroundColor,
      (theme) => theme.colorScheme.onSurface,
    );
  }

  /// Resolves the border color using the theme-aware fallback chain.
  Color resolveBorderColor(BuildContext context) {
    return resolveColor(
      context,
      borderColor,
      (theme) => theme.colorScheme.outline,
    );
  }

  /// Resolves the arrow color, defaulting to background color.
  Color resolveArrowColor(BuildContext context) {
    return arrowColor ?? resolveBackgroundColor(context);
  }

  /// Resolves the content padding with visual density adjustments.
  EdgeInsets resolveContentPadding(BuildContext context) {
    return resolveSpacing(
      context,
      contentPadding,
      EdgeInsets.all(ShadcnTokens.spacing4),
    );
  }

  /// Resolves the border radius with theme consistency.
  BorderRadius resolvePopoverBorderRadius(BuildContext context) {
    return super.resolveBorderRadius(
      context,
      borderRadius,
      BorderRadius.circular(ShadcnTokens.radiusLg),
    );
  }

  /// Resolves the text style with theme inheritance.
  TextStyle resolvePopoverTextStyle(BuildContext context) {
    return super.resolveTextStyle(
      context,
      textStyle,
      (textTheme) => textTheme.bodyMedium ?? const TextStyle(),
    );
  }

  @override
  bool validate({bool throwOnError = false}) {
    try {
      // Validate size constraints
      if (maxWidth != null && maxWidth! <= 0) {
        throw StateError('maxWidth must be positive');
      }
      if (maxHeight != null && maxHeight! <= 0) {
        throw StateError('maxHeight must be positive');
      }
      if (minWidth != null && minWidth! <= 0) {
        throw StateError('minWidth must be positive');
      }
      if (maxWidth != null && minWidth != null && maxWidth! < minWidth!) {
        throw StateError('maxWidth must be greater than or equal to minWidth');
      }
      
      // Validate border properties
      if (borderWidth != null && borderWidth! < 0) {
        throw StateError('borderWidth cannot be negative');
      }
      
      // Validate elevation
      if (elevation != null && elevation! < 0) {
        throw StateError('elevation cannot be negative');
      }
      
      // Validate arrow size
      if (arrowSize != null && arrowSize! <= 0) {
        throw StateError('arrowSize must be positive');
      }
      
      // Validate offset
      if (offset != null && offset! < 0) {
        throw StateError('offset cannot be negative');
      }
      
      return true;
    } catch (e) {
      if (throwOnError) {
        rethrow;
      }
      debugPrint('ShadcnPopoverTheme validation failed: $e');
      return false;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is ShadcnPopoverTheme &&
        backgroundColor == other.backgroundColor &&
        foregroundColor == other.foregroundColor &&
        borderColor == other.borderColor &&
        shadowColor == other.shadowColor &&
        borderRadius == other.borderRadius &&
        borderWidth == other.borderWidth &&
        elevation == other.elevation &&
        contentPadding == other.contentPadding &&
        maxWidth == other.maxWidth &&
        maxHeight == other.maxHeight &&
        minWidth == other.minWidth &&
        arrowSize == other.arrowSize &&
        arrowColor == other.arrowColor &&
        offset == other.offset &&
        animationDuration == other.animationDuration &&
        animationCurve == other.animationCurve &&
        barrierColor == other.barrierColor &&
        textStyle == other.textStyle;
  }

  @override
  int get hashCode {
    return Object.hash(
      backgroundColor,
      foregroundColor,
      borderColor,
      shadowColor,
      borderRadius,
      borderWidth,
      elevation,
      contentPadding,
      maxWidth,
      maxHeight,
      minWidth,
      arrowSize,
      arrowColor,
      offset,
      animationDuration,
      animationCurve,
      barrierColor,
      textStyle,
    );
  }
}