import 'dart:ui' show lerpDouble;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'shadcn_theme_extension.dart';
import '../../constants/shadcn_tokens.dart';

/// Theme extension for [ShadcnLabel] component.
/// 
/// This theme extension provides comprehensive theming support for label components,
/// including text styling, colors, spacing, and interaction states. Labels are
/// designed to work seamlessly with form fields and provide proper accessibility
/// support.
/// 
/// The label theme supports different visual states including normal, disabled,
/// required, and error states with consistent Material Design integration.
/// 
/// Example usage:
/// ```dart
/// Theme(
///   data: ThemeData(
///     extensions: [
///       ShadcnLabelTheme(
///         textColor: Colors.black87,
///         requiredColor: Colors.red,
///         fontSize: 14.0,
///       ),
///     ],
///   ),
///   child: ShadcnLabel(text: 'Email'),
/// )
/// ```
class ShadcnLabelTheme extends ShadcnThemeExtension<ShadcnLabelTheme> {
  /// Text color for normal state.
  final Color? textColor;
  
  /// Text color for disabled state.
  final Color? disabledTextColor;
  
  /// Text color for error state.
  final Color? errorTextColor;
  
  /// Color for required indicator (*).
  final Color? requiredColor;
  
  /// Color for optional indicator.
  final Color? optionalColor;
  
  // Typography properties
  
  /// Base text style for the label.
  final TextStyle? textStyle;
  
  /// Font size for the label text.
  final double? fontSize;
  
  /// Font weight for the label text.
  final FontWeight? fontWeight;
  
  /// Letter spacing for the label text.
  final double? letterSpacing;
  
  /// Line height multiplier for the label text.
  final double? height;
  
  // Spacing properties
  
  /// Margin around the label.
  final EdgeInsets? margin;
  
  /// Padding within the label.
  final EdgeInsets? padding;
  
  /// Spacing between label text and required/optional indicator.
  final double? indicatorSpacing;
  
  // Required/Optional indicator properties
  
  /// Text style for required indicator.
  final TextStyle? requiredStyle;
  
  /// Text style for optional indicator.
  final TextStyle? optionalStyle;
  
  /// Text to display for required indicator.
  final String? requiredText;
  
  /// Text to display for optional indicator.
  final String? optionalText;
  
  // Animation properties
  
  /// Duration for state transitions.
  final Duration? animationDuration;
  
  /// Animation curve for transitions.
  final Curve? animationCurve;
  
  // Form field association properties
  
  /// Whether to show focus indicator when associated field is focused.
  final bool? showFocusIndicator;
  
  /// Color for focus indicator.
  final Color? focusIndicatorColor;
  
  /// Width of focus indicator underline.
  final double? focusIndicatorWidth;

  const ShadcnLabelTheme({
    this.textColor,
    this.disabledTextColor,
    this.errorTextColor,
    this.requiredColor,
    this.optionalColor,
    this.textStyle,
    this.fontSize,
    this.fontWeight,
    this.letterSpacing,
    this.height,
    this.margin,
    this.padding,
    this.indicatorSpacing,
    this.requiredStyle,
    this.optionalStyle,
    this.requiredText,
    this.optionalText,
    this.animationDuration,
    this.animationCurve,
    this.showFocusIndicator,
    this.focusIndicatorColor,
    this.focusIndicatorWidth,
  });

  @override
  ShadcnLabelTheme copyWith({
    Color? textColor,
    Color? disabledTextColor,
    Color? errorTextColor,
    Color? requiredColor,
    Color? optionalColor,
    TextStyle? textStyle,
    double? fontSize,
    FontWeight? fontWeight,
    double? letterSpacing,
    double? height,
    EdgeInsets? margin,
    EdgeInsets? padding,
    double? indicatorSpacing,
    TextStyle? requiredStyle,
    TextStyle? optionalStyle,
    String? requiredText,
    String? optionalText,
    Duration? animationDuration,
    Curve? animationCurve,
    bool? showFocusIndicator,
    Color? focusIndicatorColor,
    double? focusIndicatorWidth,
  }) {
    return ShadcnLabelTheme(
      textColor: textColor ?? this.textColor,
      disabledTextColor: disabledTextColor ?? this.disabledTextColor,
      errorTextColor: errorTextColor ?? this.errorTextColor,
      requiredColor: requiredColor ?? this.requiredColor,
      optionalColor: optionalColor ?? this.optionalColor,
      textStyle: textStyle ?? this.textStyle,
      fontSize: fontSize ?? this.fontSize,
      fontWeight: fontWeight ?? this.fontWeight,
      letterSpacing: letterSpacing ?? this.letterSpacing,
      height: height ?? this.height,
      margin: margin ?? this.margin,
      padding: padding ?? this.padding,
      indicatorSpacing: indicatorSpacing ?? this.indicatorSpacing,
      requiredStyle: requiredStyle ?? this.requiredStyle,
      optionalStyle: optionalStyle ?? this.optionalStyle,
      requiredText: requiredText ?? this.requiredText,
      optionalText: optionalText ?? this.optionalText,
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
      showFocusIndicator: showFocusIndicator ?? this.showFocusIndicator,
      focusIndicatorColor: focusIndicatorColor ?? this.focusIndicatorColor,
      focusIndicatorWidth: focusIndicatorWidth ?? this.focusIndicatorWidth,
    );
  }

  @override
  ShadcnLabelTheme lerp(ShadcnLabelTheme? other, double t) {
    if (other is! ShadcnLabelTheme) {
      return this;
    }

    return ShadcnLabelTheme(
      textColor: Color.lerp(textColor, other.textColor, t),
      disabledTextColor: Color.lerp(disabledTextColor, other.disabledTextColor, t),
      errorTextColor: Color.lerp(errorTextColor, other.errorTextColor, t),
      requiredColor: Color.lerp(requiredColor, other.requiredColor, t),
      optionalColor: Color.lerp(optionalColor, other.optionalColor, t),
      textStyle: TextStyle.lerp(textStyle, other.textStyle, t),
      fontSize: lerpDouble(fontSize, other.fontSize, t),
      fontWeight: FontWeight.lerp(fontWeight, other.fontWeight, t),
      letterSpacing: lerpDouble(letterSpacing, other.letterSpacing, t),
      height: lerpDouble(height, other.height, t),
      margin: EdgeInsets.lerp(margin, other.margin, t),
      padding: EdgeInsets.lerp(padding, other.padding, t),
      indicatorSpacing: lerpDouble(indicatorSpacing, other.indicatorSpacing, t),
      requiredStyle: TextStyle.lerp(requiredStyle, other.requiredStyle, t),
      optionalStyle: TextStyle.lerp(optionalStyle, other.optionalStyle, t),
      requiredText: t < 0.5 ? requiredText : other.requiredText,
      optionalText: t < 0.5 ? optionalText : other.optionalText,
      animationDuration: t < 0.5 ? animationDuration : other.animationDuration,
      animationCurve: t < 0.5 ? animationCurve : other.animationCurve,
      showFocusIndicator: t < 0.5 ? showFocusIndicator : other.showFocusIndicator,
      focusIndicatorColor: Color.lerp(focusIndicatorColor, other.focusIndicatorColor, t),
      focusIndicatorWidth: lerpDouble(focusIndicatorWidth, other.focusIndicatorWidth, t),
    );
  }

  /// Creates a default [ShadcnLabelTheme] from the given [ColorScheme].
  /// 
  /// This factory method creates a theme that follows shadcn design principles
  /// while integrating with Material Design. The resulting theme will have
  /// consistent styling that works well with both light and dark themes.
  /// 
  /// The default theme uses:
  /// - OnSurface color for text
  /// - Error color for required indicators and error states
  /// - Disabled color for disabled states
  /// - Standard typography scaling
  static ShadcnLabelTheme defaultTheme(ColorScheme colorScheme) {
    final isLight = colorScheme.brightness == Brightness.light;
    
    return ShadcnLabelTheme(
      textColor: colorScheme.onSurface,
      disabledTextColor: colorScheme.onSurface.withOpacity(0.38),
      errorTextColor: colorScheme.error,
      requiredColor: colorScheme.error,
      optionalColor: colorScheme.onSurfaceVariant,
      
      // Typography
      textStyle: null, // Will use Material theme default
      fontSize: ShadcnTokens.fontSizeSm,
      fontWeight: ShadcnTokens.fontWeightMedium,
      letterSpacing: 0.01,
      height: ShadcnTokens.lineHeightNormal,
      
      // Spacing
      margin: EdgeInsets.zero,
      padding: EdgeInsets.zero,
      indicatorSpacing: ShadcnTokens.spacing1,
      
      // Indicator styling
      requiredStyle: TextStyle(
        color: colorScheme.error,
        fontSize: ShadcnTokens.fontSizeSm,
        fontWeight: ShadcnTokens.fontWeightNormal,
      ),
      optionalStyle: TextStyle(
        color: colorScheme.onSurfaceVariant,
        fontSize: ShadcnTokens.fontSizeXs,
        fontWeight: ShadcnTokens.fontWeightNormal,
        fontStyle: FontStyle.italic,
      ),
      requiredText: '*',
      optionalText: '(optional)',
      
      // Animation
      animationDuration: ShadcnTokens.durationFast,
      animationCurve: Curves.easeInOut,
      
      // Focus indicator
      showFocusIndicator: false, // Disabled by default
      focusIndicatorColor: colorScheme.primary,
      focusIndicatorWidth: 2.0,
    );
  }

  /// Creates a theme optimized for form fields.
  /// 
  /// This variant includes focus indicators and enhanced spacing
  /// for better form field association and user experience.
  static ShadcnLabelTheme formFieldTheme(ColorScheme colorScheme) {
    return defaultTheme(colorScheme).copyWith(
      margin: const EdgeInsets.only(bottom: ShadcnTokens.spacing1),
      showFocusIndicator: true,
      fontSize: ShadcnTokens.fontSizeMd,
    );
  }

  /// Creates a theme optimized for compact layouts.
  /// 
  /// This variant uses smaller font sizes and tighter spacing
  /// for use in compact form layouts or dense information displays.
  static ShadcnLabelTheme compactTheme(ColorScheme colorScheme) {
    return defaultTheme(colorScheme).copyWith(
      fontSize: ShadcnTokens.fontSizeXs,
      indicatorSpacing: 2.0,
      optionalStyle: TextStyle(
        color: colorScheme.onSurfaceVariant,
        fontSize: 10.0,
        fontWeight: ShadcnTokens.fontWeightNormal,
        fontStyle: FontStyle.italic,
      ),
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ShadcnLabelTheme &&
          runtimeType == other.runtimeType &&
          textColor == other.textColor &&
          disabledTextColor == other.disabledTextColor &&
          errorTextColor == other.errorTextColor &&
          requiredColor == other.requiredColor &&
          optionalColor == other.optionalColor &&
          textStyle == other.textStyle &&
          fontSize == other.fontSize &&
          fontWeight == other.fontWeight &&
          letterSpacing == other.letterSpacing &&
          height == other.height &&
          margin == other.margin &&
          padding == other.padding &&
          indicatorSpacing == other.indicatorSpacing &&
          requiredStyle == other.requiredStyle &&
          optionalStyle == other.optionalStyle &&
          requiredText == other.requiredText &&
          optionalText == other.optionalText &&
          animationDuration == other.animationDuration &&
          animationCurve == other.animationCurve &&
          showFocusIndicator == other.showFocusIndicator &&
          focusIndicatorColor == other.focusIndicatorColor &&
          focusIndicatorWidth == other.focusIndicatorWidth;

  @override
  int get hashCode =>
      textColor.hashCode ^
      disabledTextColor.hashCode ^
      errorTextColor.hashCode ^
      requiredColor.hashCode ^
      optionalColor.hashCode ^
      textStyle.hashCode ^
      fontSize.hashCode ^
      fontWeight.hashCode ^
      letterSpacing.hashCode ^
      height.hashCode ^
      margin.hashCode ^
      padding.hashCode ^
      indicatorSpacing.hashCode ^
      requiredStyle.hashCode ^
      optionalStyle.hashCode ^
      requiredText.hashCode ^
      optionalText.hashCode ^
      animationDuration.hashCode ^
      animationCurve.hashCode ^
      showFocusIndicator.hashCode ^
      focusIndicatorColor.hashCode ^
      focusIndicatorWidth.hashCode;

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    
    properties.add(ColorProperty('textColor', textColor));
    properties.add(ColorProperty('disabledTextColor', disabledTextColor));
    properties.add(ColorProperty('errorTextColor', errorTextColor));
    properties.add(ColorProperty('requiredColor', requiredColor));
    properties.add(ColorProperty('optionalColor', optionalColor));
    properties.add(DiagnosticsProperty<TextStyle>('textStyle', textStyle));
    properties.add(DoubleProperty('fontSize', fontSize));
    properties.add(DiagnosticsProperty<FontWeight>('fontWeight', fontWeight));
    properties.add(DoubleProperty('letterSpacing', letterSpacing));
    properties.add(DoubleProperty('height', height));
    properties.add(DiagnosticsProperty<EdgeInsets>('margin', margin));
    properties.add(DiagnosticsProperty<EdgeInsets>('padding', padding));
    properties.add(DoubleProperty('indicatorSpacing', indicatorSpacing));
    properties.add(DiagnosticsProperty<TextStyle>('requiredStyle', requiredStyle));
    properties.add(DiagnosticsProperty<TextStyle>('optionalStyle', optionalStyle));
    properties.add(StringProperty('requiredText', requiredText));
    properties.add(StringProperty('optionalText', optionalText));
    properties.add(DiagnosticsProperty<Duration>('animationDuration', animationDuration));
    properties.add(DiagnosticsProperty<Curve>('animationCurve', animationCurve));
    properties.add(DiagnosticsProperty<bool>('showFocusIndicator', showFocusIndicator));
    properties.add(ColorProperty('focusIndicatorColor', focusIndicatorColor));
    properties.add(DoubleProperty('focusIndicatorWidth', focusIndicatorWidth));
  }
}

/// State types for [ShadcnLabel].
/// 
/// These states determine the visual appearance of the label:
/// - [normal]: Default state with standard styling
/// - [disabled]: Disabled state with reduced opacity
/// - [error]: Error state with error color styling
enum ShadcnLabelState {
  /// Normal label state
  normal,
  
  /// Disabled label state
  disabled,
  
  /// Error label state
  error,
}

/// Requirement indicator types for [ShadcnLabel].
/// 
/// These determine what indicator is shown next to the label text:
/// - [none]: No indicator shown
/// - [required]: Required indicator (typically *)
/// - [optional]: Optional indicator (typically "(optional)")
enum ShadcnLabelRequirement {
  /// No requirement indicator
  none,
  
  /// Required field indicator
  required,
  
  /// Optional field indicator
  optional,
}