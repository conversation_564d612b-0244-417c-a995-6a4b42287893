import 'package:flutter/material.dart';
import '../../constants/shadcn_tokens.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for the ShadcnRadioGroup component.
/// 
/// This theme extension provides all styling properties for radio button variants,
/// sizes, and interactive states. It follows shadcn design principles while
/// integrating with Material Design theming patterns.
/// 
/// All properties are nullable to support fallback to Material theme values
/// and provide flexible customization options for developers.
class ShadcnRadioGroupTheme extends ShadcnThemeExtension<ShadcnRadioGroupTheme> {
  // Selected state colors
  final Color? selectedBackground;
  final Color? selectedForeground;
  final Color? selectedBorder;
  final Color? selectedInnerCircle;
  
  // Unselected state colors
  final Color? unselectedBackground;
  final Color? unselectedForeground;
  final Color? unselectedBorder;
  
  // Interactive state overlays
  final Color? hoverOverlay;
  final Color? pressedOverlay;
  final Color? focusedOverlay;
  
  // Disabled state colors
  final Color? disabledSelectedBackground;
  final Color? disabledSelectedForeground;
  final Color? disabledSelectedBorder;
  final Color? disabledSelectedInnerCircle;
  final Color? disabledUnselectedBackground;
  final Color? disabledUnselectedForeground;
  final Color? disabledUnselectedBorder;
  
  // Sizing properties
  final double? size;
  final double? smallSize;
  final double? largeSize;
  final double? innerCircleSize;
  final double? smallInnerCircleSize;
  final double? largeInnerCircleSize;
  
  // Border properties
  final double? borderWidth;
  
  // Spacing and layout properties
  final EdgeInsets? padding;
  final double? itemSpacing;
  final double? labelSpacing;
  final MainAxisAlignment? mainAxisAlignment;
  final CrossAxisAlignment? crossAxisAlignment;
  final Axis? direction;
  final WrapAlignment? wrapAlignment;
  final WrapCrossAlignment? wrapCrossAlignment;
  final double? runSpacing;
  
  // Text styling
  final TextStyle? labelStyle;
  final TextStyle? helperStyle;
  final TextStyle? errorStyle;
  
  // Animation properties
  final Duration? animationDuration;
  final Curve? animationCurve;
  
  // Focus ring properties
  final double? focusRingWidth;
  final double? focusRingOffset;

  const ShadcnRadioGroupTheme({
    // Selected state colors
    this.selectedBackground,
    this.selectedForeground,
    this.selectedBorder,
    this.selectedInnerCircle,
    
    // Unselected state colors
    this.unselectedBackground,
    this.unselectedForeground,
    this.unselectedBorder,
    
    // Interactive state overlays
    this.hoverOverlay,
    this.pressedOverlay,
    this.focusedOverlay,
    
    // Disabled state colors
    this.disabledSelectedBackground,
    this.disabledSelectedForeground,
    this.disabledSelectedBorder,
    this.disabledSelectedInnerCircle,
    this.disabledUnselectedBackground,
    this.disabledUnselectedForeground,
    this.disabledUnselectedBorder,
    
    // Sizing properties
    this.size,
    this.smallSize,
    this.largeSize,
    this.innerCircleSize,
    this.smallInnerCircleSize,
    this.largeInnerCircleSize,
    
    // Border properties
    this.borderWidth,
    
    // Spacing and layout properties
    this.padding,
    this.itemSpacing,
    this.labelSpacing,
    this.mainAxisAlignment,
    this.crossAxisAlignment,
    this.direction,
    this.wrapAlignment,
    this.wrapCrossAlignment,
    this.runSpacing,
    
    // Text styling
    this.labelStyle,
    this.helperStyle,
    this.errorStyle,
    
    // Animation properties
    this.animationDuration,
    this.animationCurve,
    
    // Focus ring properties
    this.focusRingWidth,
    this.focusRingOffset,
  });

  /// Creates a ShadcnRadioGroupTheme with default values based on the provided ColorScheme.
  /// 
  /// This factory constructor provides sensible defaults that follow shadcn design
  /// principles while integrating with Material Design color schemes.
  static ShadcnRadioGroupTheme defaultTheme(ColorScheme colorScheme) {
    final brightness = colorScheme.brightness;
    final isDark = brightness == Brightness.dark;
    
    return ShadcnRadioGroupTheme(
      // Selected state colors
      selectedBackground: Colors.transparent,
      selectedForeground: colorScheme.primary,
      selectedBorder: colorScheme.primary,
      selectedInnerCircle: colorScheme.primary,
      
      // Unselected state colors
      unselectedBackground: Colors.transparent,
      unselectedForeground: colorScheme.onSurface,
      unselectedBorder: isDark 
        ? colorScheme.outline.withOpacity(0.6)
        : colorScheme.outline,
      
      // Interactive state overlays
      hoverOverlay: colorScheme.primary.withOpacity(0.08),
      pressedOverlay: colorScheme.primary.withOpacity(0.12),
      focusedOverlay: colorScheme.primary.withOpacity(0.12),
      
      // Disabled state colors
      disabledSelectedBackground: Colors.transparent,
      disabledSelectedForeground: colorScheme.onSurface.withOpacity(0.38),
      disabledSelectedBorder: colorScheme.onSurface.withOpacity(0.12),
      disabledSelectedInnerCircle: colorScheme.onSurface.withOpacity(0.38),
      disabledUnselectedBackground: Colors.transparent,
      disabledUnselectedForeground: colorScheme.onSurface.withOpacity(0.38),
      disabledUnselectedBorder: colorScheme.onSurface.withOpacity(0.12),
      
      // Sizing properties
      size: 20.0,
      smallSize: 16.0,
      largeSize: 24.0,
      innerCircleSize: 8.0,
      smallInnerCircleSize: 6.0,
      largeInnerCircleSize: 10.0,
      
      // Border properties
      borderWidth: ShadcnTokens.borderWidth,
      
      // Spacing and layout properties
      padding: EdgeInsets.all(ShadcnTokens.spacing1),
      itemSpacing: ShadcnTokens.spacing4,
      labelSpacing: ShadcnTokens.spacing2,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      direction: Axis.vertical,
      wrapAlignment: WrapAlignment.start,
      wrapCrossAlignment: WrapCrossAlignment.center,
      runSpacing: ShadcnTokens.spacing2,
      
      // Text styling
      labelStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightNormal,
        color: colorScheme.onSurface,
        height: ShadcnTokens.lineHeightNormal,
      ),
      helperStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeSm,
        fontWeight: ShadcnTokens.fontWeightNormal,
        color: colorScheme.onSurface.withOpacity(0.6),
        height: ShadcnTokens.lineHeightNormal,
      ),
      errorStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeSm,
        fontWeight: ShadcnTokens.fontWeightNormal,
        color: colorScheme.error,
        height: ShadcnTokens.lineHeightNormal,
      ),
      
      // Animation properties
      animationDuration: ShadcnTokens.durationFast,
      animationCurve: Curves.easeInOut,
      
      // Focus ring properties
      focusRingWidth: 2.0,
      focusRingOffset: 2.0,
    );
  }

  @override
  ShadcnRadioGroupTheme copyWith({
    // Selected state colors
    Color? selectedBackground,
    Color? selectedForeground,
    Color? selectedBorder,
    Color? selectedInnerCircle,
    
    // Unselected state colors
    Color? unselectedBackground,
    Color? unselectedForeground,
    Color? unselectedBorder,
    
    // Interactive state overlays
    Color? hoverOverlay,
    Color? pressedOverlay,
    Color? focusedOverlay,
    
    // Disabled state colors
    Color? disabledSelectedBackground,
    Color? disabledSelectedForeground,
    Color? disabledSelectedBorder,
    Color? disabledSelectedInnerCircle,
    Color? disabledUnselectedBackground,
    Color? disabledUnselectedForeground,
    Color? disabledUnselectedBorder,
    
    // Sizing properties
    double? size,
    double? smallSize,
    double? largeSize,
    double? innerCircleSize,
    double? smallInnerCircleSize,
    double? largeInnerCircleSize,
    
    // Border properties
    double? borderWidth,
    
    // Spacing and layout properties
    EdgeInsets? padding,
    double? itemSpacing,
    double? labelSpacing,
    MainAxisAlignment? mainAxisAlignment,
    CrossAxisAlignment? crossAxisAlignment,
    Axis? direction,
    WrapAlignment? wrapAlignment,
    WrapCrossAlignment? wrapCrossAlignment,
    double? runSpacing,
    
    // Text styling
    TextStyle? labelStyle,
    TextStyle? helperStyle,
    TextStyle? errorStyle,
    
    // Animation properties
    Duration? animationDuration,
    Curve? animationCurve,
    
    // Focus ring properties
    double? focusRingWidth,
    double? focusRingOffset,
  }) {
    return ShadcnRadioGroupTheme(
      // Selected state colors
      selectedBackground: selectedBackground ?? this.selectedBackground,
      selectedForeground: selectedForeground ?? this.selectedForeground,
      selectedBorder: selectedBorder ?? this.selectedBorder,
      selectedInnerCircle: selectedInnerCircle ?? this.selectedInnerCircle,
      
      // Unselected state colors
      unselectedBackground: unselectedBackground ?? this.unselectedBackground,
      unselectedForeground: unselectedForeground ?? this.unselectedForeground,
      unselectedBorder: unselectedBorder ?? this.unselectedBorder,
      
      // Interactive state overlays
      hoverOverlay: hoverOverlay ?? this.hoverOverlay,
      pressedOverlay: pressedOverlay ?? this.pressedOverlay,
      focusedOverlay: focusedOverlay ?? this.focusedOverlay,
      
      // Disabled state colors
      disabledSelectedBackground: disabledSelectedBackground ?? this.disabledSelectedBackground,
      disabledSelectedForeground: disabledSelectedForeground ?? this.disabledSelectedForeground,
      disabledSelectedBorder: disabledSelectedBorder ?? this.disabledSelectedBorder,
      disabledSelectedInnerCircle: disabledSelectedInnerCircle ?? this.disabledSelectedInnerCircle,
      disabledUnselectedBackground: disabledUnselectedBackground ?? this.disabledUnselectedBackground,
      disabledUnselectedForeground: disabledUnselectedForeground ?? this.disabledUnselectedForeground,
      disabledUnselectedBorder: disabledUnselectedBorder ?? this.disabledUnselectedBorder,
      
      // Sizing properties
      size: size ?? this.size,
      smallSize: smallSize ?? this.smallSize,
      largeSize: largeSize ?? this.largeSize,
      innerCircleSize: innerCircleSize ?? this.innerCircleSize,
      smallInnerCircleSize: smallInnerCircleSize ?? this.smallInnerCircleSize,
      largeInnerCircleSize: largeInnerCircleSize ?? this.largeInnerCircleSize,
      
      // Border properties
      borderWidth: borderWidth ?? this.borderWidth,
      
      // Spacing and layout properties
      padding: padding ?? this.padding,
      itemSpacing: itemSpacing ?? this.itemSpacing,
      labelSpacing: labelSpacing ?? this.labelSpacing,
      mainAxisAlignment: mainAxisAlignment ?? this.mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment ?? this.crossAxisAlignment,
      direction: direction ?? this.direction,
      wrapAlignment: wrapAlignment ?? this.wrapAlignment,
      wrapCrossAlignment: wrapCrossAlignment ?? this.wrapCrossAlignment,
      runSpacing: runSpacing ?? this.runSpacing,
      
      // Text styling
      labelStyle: labelStyle ?? this.labelStyle,
      helperStyle: helperStyle ?? this.helperStyle,
      errorStyle: errorStyle ?? this.errorStyle,
      
      // Animation properties
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
      
      // Focus ring properties
      focusRingWidth: focusRingWidth ?? this.focusRingWidth,
      focusRingOffset: focusRingOffset ?? this.focusRingOffset,
    );
  }

  @override
  ShadcnRadioGroupTheme lerp(ShadcnRadioGroupTheme? other, double t) {
    if (other is! ShadcnRadioGroupTheme) {
      return this;
    }
    
    return ShadcnRadioGroupTheme(
      // Selected state colors
      selectedBackground: Color.lerp(selectedBackground, other.selectedBackground, t),
      selectedForeground: Color.lerp(selectedForeground, other.selectedForeground, t),
      selectedBorder: Color.lerp(selectedBorder, other.selectedBorder, t),
      selectedInnerCircle: Color.lerp(selectedInnerCircle, other.selectedInnerCircle, t),
      
      // Unselected state colors
      unselectedBackground: Color.lerp(unselectedBackground, other.unselectedBackground, t),
      unselectedForeground: Color.lerp(unselectedForeground, other.unselectedForeground, t),
      unselectedBorder: Color.lerp(unselectedBorder, other.unselectedBorder, t),
      
      // Interactive state overlays
      hoverOverlay: Color.lerp(hoverOverlay, other.hoverOverlay, t),
      pressedOverlay: Color.lerp(pressedOverlay, other.pressedOverlay, t),
      focusedOverlay: Color.lerp(focusedOverlay, other.focusedOverlay, t),
      
      // Disabled state colors
      disabledSelectedBackground: Color.lerp(disabledSelectedBackground, other.disabledSelectedBackground, t),
      disabledSelectedForeground: Color.lerp(disabledSelectedForeground, other.disabledSelectedForeground, t),
      disabledSelectedBorder: Color.lerp(disabledSelectedBorder, other.disabledSelectedBorder, t),
      disabledSelectedInnerCircle: Color.lerp(disabledSelectedInnerCircle, other.disabledSelectedInnerCircle, t),
      disabledUnselectedBackground: Color.lerp(disabledUnselectedBackground, other.disabledUnselectedBackground, t),
      disabledUnselectedForeground: Color.lerp(disabledUnselectedForeground, other.disabledUnselectedForeground, t),
      disabledUnselectedBorder: Color.lerp(disabledUnselectedBorder, other.disabledUnselectedBorder, t),
      
      // Sizing properties
      size: lerpDouble(size, other.size, t),
      smallSize: lerpDouble(smallSize, other.smallSize, t),
      largeSize: lerpDouble(largeSize, other.largeSize, t),
      innerCircleSize: lerpDouble(innerCircleSize, other.innerCircleSize, t),
      smallInnerCircleSize: lerpDouble(smallInnerCircleSize, other.smallInnerCircleSize, t),
      largeInnerCircleSize: lerpDouble(largeInnerCircleSize, other.largeInnerCircleSize, t),
      
      // Border properties
      borderWidth: lerpDouble(borderWidth, other.borderWidth, t),
      
      // Spacing and layout properties
      padding: EdgeInsets.lerp(padding, other.padding, t),
      itemSpacing: lerpDouble(itemSpacing, other.itemSpacing, t),
      labelSpacing: lerpDouble(labelSpacing, other.labelSpacing, t),
      mainAxisAlignment: other.mainAxisAlignment ?? mainAxisAlignment,
      crossAxisAlignment: other.crossAxisAlignment ?? crossAxisAlignment,
      direction: other.direction ?? direction,
      wrapAlignment: other.wrapAlignment ?? wrapAlignment,
      wrapCrossAlignment: other.wrapCrossAlignment ?? wrapCrossAlignment,
      runSpacing: lerpDouble(runSpacing, other.runSpacing, t),
      
      // Text styling
      labelStyle: TextStyle.lerp(labelStyle, other.labelStyle, t),
      helperStyle: TextStyle.lerp(helperStyle, other.helperStyle, t),
      errorStyle: TextStyle.lerp(errorStyle, other.errorStyle, t),
      
      // Animation properties
      animationDuration: lerpDuration(animationDuration, other.animationDuration, t),
      animationCurve: other.animationCurve ?? animationCurve,
      
      // Focus ring properties
      focusRingWidth: lerpDouble(focusRingWidth, other.focusRingWidth, t),
      focusRingOffset: lerpDouble(focusRingOffset, other.focusRingOffset, t),
    );
  }

  @override
  bool validate({bool throwOnError = false}) {
    try {
      // Validate that essential colors are not null in a complete theme
      if (selectedBackground != null && selectedForeground == null) {
        if (throwOnError) {
          throw ThemeException('ShadcnRadioGroupTheme: selectedForeground cannot be null when selectedBackground is provided');
        }
        return false;
      }
      
      if (size != null && size! <= 0) {
        if (throwOnError) {
          throw ThemeException('ShadcnRadioGroupTheme: size must be greater than 0');
        }
        return false;
      }
      
      if (borderWidth != null && borderWidth! < 0) {
        if (throwOnError) {
          throw ThemeException('ShadcnRadioGroupTheme: borderWidth cannot be negative');
        }
        return false;
      }
      
      if (itemSpacing != null && itemSpacing! < 0) {
        if (throwOnError) {
          throw ThemeException('ShadcnRadioGroupTheme: itemSpacing cannot be negative');
        }
        return false;
      }
      
      return true;
    } catch (e) {
      if (throwOnError) rethrow;
      return false;
    }
  }

  /// Helper method to resolve size based on the size variant
  double resolveSizeForVariant(ShadcnRadioGroupSize sizeVariant) {
    switch (sizeVariant) {
      case ShadcnRadioGroupSize.small:
        return smallSize ?? 16.0;
      case ShadcnRadioGroupSize.medium:
        return size ?? 20.0;
      case ShadcnRadioGroupSize.large:
        return largeSize ?? 24.0;
    }
  }

  /// Helper method to resolve inner circle size based on the size variant
  double resolveInnerCircleSizeForVariant(ShadcnRadioGroupSize sizeVariant) {
    switch (sizeVariant) {
      case ShadcnRadioGroupSize.small:
        return smallInnerCircleSize ?? 6.0;
      case ShadcnRadioGroupSize.medium:
        return innerCircleSize ?? 8.0;
      case ShadcnRadioGroupSize.large:
        return largeInnerCircleSize ?? 10.0;
    }
  }
}

/// Helper method for lerping durations
Duration? lerpDuration(Duration? a, Duration? b, double t) {
  if (a == null && b == null) return null;
  if (a == null) return b;
  if (b == null) return a;
  return Duration(
    milliseconds: (a.inMilliseconds + ((b.inMilliseconds - a.inMilliseconds) * t)).round(),
  );
}

/// Helper method for lerping double values
double? lerpDouble(double? a, double? b, double t) {
  if (a == null && b == null) return null;
  if (a == null) return b;
  if (b == null) return a;
  return a + (b - a) * t;
}

/// Exception thrown when theme validation fails
class ThemeException implements Exception {
  final String message;
  
  const ThemeException(this.message);
  
  @override
  String toString() => message;
}

/// Enum for radio group size variants
enum ShadcnRadioGroupSize {
  small,
  medium,
  large,
}

/// Enum for radio group layout direction
enum ShadcnRadioGroupDirection {
  vertical,
  horizontal,
  wrap,
}