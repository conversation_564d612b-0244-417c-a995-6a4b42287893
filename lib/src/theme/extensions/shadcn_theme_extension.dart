import 'package:flutter/material.dart';
import '../../utils/theme_resolver.dart';

/// Abstract base class for all shadcn component theme extensions.
/// 
/// This class provides a consistent interface for theme resolution and
/// fallback handling across all shadcn components. All component-specific
/// theme extensions must extend this class.
abstract class ShadcnThemeExtension<T extends ShadcnThemeExtension<T>> 
    extends ThemeExtension<T> {
  
  const ShadcnThemeExtension();
  
  /// Resolves a color property with robust fallback chain using ShadcnThemeResolver.
  /// 
  /// Resolution order:
  /// 1. Custom color (if provided)
  /// 2. Material theme color via getter
  /// 3. Hardcoded fallback color
  Color resolveColor(
    BuildContext context, 
    Color? customColor, 
    Color Function(ThemeData) materialColorGetter, [
    Color fallbackColor = Colors.grey,
  ]) {
    return ShadcnThemeResolver.resolveColor(
      context,
      customColor,
      materialColorGetter,
      fallbackColor,
    );
  }
  
  /// Resolves a text style with theme inheritance using ShadcnThemeResolver.
  /// 
  /// Merges custom style properties with Material theme base style,
  /// providing robust error handling and fallback support.
  TextStyle resolveTextStyle(
    BuildContext context, 
    TextStyle? customStyle,
    TextStyle Function(TextTheme) materialStyleGetter, [
    TextStyle? fallbackStyle,
  ]) {
    return ShadcnThemeResolver.resolveTextStyle(
      context,
      customStyle,
      materialStyleGetter,
      fallbackStyle,
    );
  }
  
  /// Resolves spacing using consistent token system with visual density support.
  /// 
  /// Uses ShadcnThemeResolver for proper visual density adjustments and
  /// error handling in edge cases.
  EdgeInsets resolveSpacing(
    BuildContext context, 
    EdgeInsets? customSpacing,
    EdgeInsets defaultSpacing,
  ) {
    return ShadcnThemeResolver.resolveSpacing(
      context,
      customSpacing,
      defaultSpacing,
    );
  }
  
  /// Resolves border radius with theme-aware fallback using ShadcnThemeResolver.
  /// 
  /// Provides consistent border radius resolution with proper error handling.
  BorderRadius resolveBorderRadius(
    BuildContext context,
    BorderRadius? customRadius,
    BorderRadius defaultRadius,
  ) {
    return ShadcnThemeResolver.resolveBorderRadius(
      context,
      customRadius,
      defaultRadius,
    );
  }
  
  /// Resolves double values with visual density consideration using ShadcnThemeResolver.
  /// 
  /// Applies visual density adjustments and clamps values appropriately,
  /// with robust error handling for edge cases.
  double resolveDouble(
    BuildContext context,
    double? customValue,
    double defaultValue, {
    bool applyVerticalDensity = true,
  }) {
    return ShadcnThemeResolver.resolveDouble(
      context,
      customValue,
      defaultValue,
      applyVerticalDensity: applyVerticalDensity,
    );
  }
  
  /// Validates that this theme extension has all required properties configured.
  /// 
  /// Subclasses can override this method to provide specific validation logic
  /// for their theme properties. The default implementation always returns true.
  /// 
  /// Returns true if the theme extension is valid, false otherwise.
  /// If [throwOnError] is true, throws a [ThemeException] on validation failure.
  bool validate({
    bool throwOnError = false,
  }) {
    // Base implementation doesn't validate specific properties
    // Subclasses should override this method for specific validation
    return true;
  }
  
  /// Gets the theme extension of type [T] from the current context.
  /// 
  /// Returns null if the extension is not found in the theme.
  /// Uses ShadcnThemeResolver for safe theme access.
  static T? of<T extends ThemeExtension<T>>(BuildContext context) {
    try {
      return Theme.of(context).extension<T>();
    } catch (e) {
      debugPrint('Failed to get theme extension $T: $e');
      return null;
    }
  }
  
  /// Safely resolves a theme extension with fallback to default factory.
  /// 
  /// This is a convenience method that delegates to ShadcnThemeResolver
  /// for consistent theme resolution across all components.
  static T resolve<T extends ThemeExtension<T>>(
    BuildContext context,
    T Function(ColorScheme) defaultFactory,
  ) {
    return ShadcnThemeResolver.resolveThemeExtension<T>(
      context,
      defaultFactory,
    );
  }
}