import 'package:flutter/material.dart';
import '../../../shadcn.dart';

/// Theme extension for Tooltip components.
/// 
/// This extension provides comprehensive theming for tooltip overlays including
/// positioning, arrow styling, colors, and animations. Designed to integrate
/// with Material Design while providing shadcn-specific styling capabilities.
class ShadcnTooltipTheme extends ShadcnThemeExtension<ShadcnTooltipTheme> {
  /// Background color of the tooltip.
  final Color? backgroundColor;
  
  /// Foreground color for text content.
  final Color? foregroundColor;
  
  /// Border radius of the tooltip.
  final BorderRadius? borderRadius;
  
  /// Padding inside the tooltip content.
  final EdgeInsets? contentPadding;
  
  /// Maximum width of the tooltip.
  final double? maxWidth;
  
  /// Size of the arrow (width and height).
  final double? arrowSize;
  
  /// Color of the arrow.
  final Color? arrowColor;
  
  /// Offset of the tooltip from the target widget.
  final double? offset;
  
  /// Delay before showing the tooltip on hover.
  final Duration? showDelay;
  
  /// Delay before hiding the tooltip after mouse exit.
  final Duration? hideDelay;
  
  /// Duration for tooltip fade in/out animations.
  final Duration? animationDuration;
  
  /// Animation curve for tooltip transitions.
  final Curve? animationCurve;
  
  /// Text style for the tooltip content.
  final TextStyle? textStyle;
  
  /// Elevation of the tooltip.
  final double? elevation;
  
  /// Shadow color for elevation effect.
  final Color? shadowColor;
  
  /// Whether tooltip should show on long press (mobile).
  final bool? showOnLongPress;
  
  /// Whether tooltip should show on tap (accessibility).
  final bool? showOnTap;
  
  /// Whether tooltip should show on focus.
  final bool? showOnFocus;
  
  /// Whether tooltip should show on hover.
  final bool? showOnHover;

  const ShadcnTooltipTheme({
    this.backgroundColor,
    this.foregroundColor,
    this.borderRadius,
    this.contentPadding,
    this.maxWidth,
    this.arrowSize,
    this.arrowColor,
    this.offset,
    this.showDelay,
    this.hideDelay,
    this.animationDuration,
    this.animationCurve,
    this.textStyle,
    this.elevation,
    this.shadowColor,
    this.showOnLongPress,
    this.showOnTap,
    this.showOnFocus,
    this.showOnHover,
  });

  @override
  ShadcnTooltipTheme copyWith({
    Color? backgroundColor,
    Color? foregroundColor,
    BorderRadius? borderRadius,
    EdgeInsets? contentPadding,
    double? maxWidth,
    double? arrowSize,
    Color? arrowColor,
    double? offset,
    Duration? showDelay,
    Duration? hideDelay,
    Duration? animationDuration,
    Curve? animationCurve,
    TextStyle? textStyle,
    double? elevation,
    Color? shadowColor,
    bool? showOnLongPress,
    bool? showOnTap,
    bool? showOnFocus,
    bool? showOnHover,
  }) {
    return ShadcnTooltipTheme(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      foregroundColor: foregroundColor ?? this.foregroundColor,
      borderRadius: borderRadius ?? this.borderRadius,
      contentPadding: contentPadding ?? this.contentPadding,
      maxWidth: maxWidth ?? this.maxWidth,
      arrowSize: arrowSize ?? this.arrowSize,
      arrowColor: arrowColor ?? this.arrowColor,
      offset: offset ?? this.offset,
      showDelay: showDelay ?? this.showDelay,
      hideDelay: hideDelay ?? this.hideDelay,
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
      textStyle: textStyle ?? this.textStyle,
      elevation: elevation ?? this.elevation,
      shadowColor: shadowColor ?? this.shadowColor,
      showOnLongPress: showOnLongPress ?? this.showOnLongPress,
      showOnTap: showOnTap ?? this.showOnTap,
      showOnFocus: showOnFocus ?? this.showOnFocus,
      showOnHover: showOnHover ?? this.showOnHover,
    );
  }

  @override
  ShadcnTooltipTheme lerp(ShadcnTooltipTheme? other, double t) {
    if (other == null) return this;
    
    return ShadcnTooltipTheme(
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t),
      foregroundColor: Color.lerp(foregroundColor, other.foregroundColor, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      contentPadding: EdgeInsets.lerp(contentPadding, other.contentPadding, t),
      maxWidth: t < 0.5 ? maxWidth : other.maxWidth,
      arrowSize: t < 0.5 ? arrowSize : other.arrowSize,
      arrowColor: Color.lerp(arrowColor, other.arrowColor, t),
      offset: t < 0.5 ? offset : other.offset,
      showDelay: t < 0.5 ? showDelay : other.showDelay,
      hideDelay: t < 0.5 ? hideDelay : other.hideDelay,
      animationDuration: t < 0.5 ? animationDuration : other.animationDuration,
      animationCurve: t < 0.5 ? animationCurve : other.animationCurve,
      textStyle: TextStyle.lerp(textStyle, other.textStyle, t),
      elevation: t < 0.5 ? elevation : other.elevation,
      shadowColor: Color.lerp(shadowColor, other.shadowColor, t),
      showOnLongPress: t < 0.5 ? showOnLongPress : other.showOnLongPress,
      showOnTap: t < 0.5 ? showOnTap : other.showOnTap,
      showOnFocus: t < 0.5 ? showOnFocus : other.showOnFocus,
      showOnHover: t < 0.5 ? showOnHover : other.showOnHover,
    );
  }

  /// Creates a default theme based on the provided color scheme.
  /// 
  /// This factory ensures that all tooltip components have sensible defaults
  /// that integrate well with Material Design while maintaining shadcn aesthetics.
  static ShadcnTooltipTheme defaultTheme(ColorScheme colorScheme) {
    final isDark = colorScheme.brightness == Brightness.dark;
    
    return ShadcnTooltipTheme(
      backgroundColor: isDark 
          ? colorScheme.onSurface.withOpacity(0.9)
          : colorScheme.onSurface.withOpacity(0.9),
      foregroundColor: isDark 
          ? colorScheme.surface
          : colorScheme.surface,
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusSm),
      contentPadding: EdgeInsets.symmetric(
        horizontal: ShadcnTokens.spacing2,
        vertical: ShadcnTokens.spacing1,
      ),
      maxWidth: 200.0,
      arrowSize: 6.0,
      arrowColor: isDark 
          ? colorScheme.onSurface.withOpacity(0.9)
          : colorScheme.onSurface.withOpacity(0.9),
      offset: 8.0,
      showDelay: const Duration(milliseconds: 500),
      hideDelay: const Duration(milliseconds: 150),
      animationDuration: ShadcnTokens.durationFast,
      animationCurve: Curves.easeOut,
      textStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeSm,
        fontWeight: ShadcnTokens.fontWeightNormal,
        color: isDark 
            ? colorScheme.surface
            : colorScheme.surface,
        height: ShadcnTokens.lineHeightTight,
      ),
      elevation: ShadcnTokens.elevationSm,
      shadowColor: colorScheme.shadow,
      showOnLongPress: true,
      showOnTap: false,
      showOnFocus: true,
      showOnHover: true,
    );
  }

  /// Creates a theme with custom colors for specialized tooltips.
  static ShadcnTooltipTheme customColors({
    required ColorScheme colorScheme,
    Color? backgroundColor,
    Color? foregroundColor,
    bool highContrast = false,
  }) {
    final defaultTheme = ShadcnTooltipTheme.defaultTheme(colorScheme);
    
    Color effectiveBackground = backgroundColor ?? defaultTheme.backgroundColor!;
    Color effectiveForeground = foregroundColor ?? defaultTheme.foregroundColor!;
    
    // Ensure high contrast if requested
    if (highContrast) {
      final backgroundLuminance = effectiveBackground.computeLuminance();
      if (backgroundLuminance > 0.5) {
        // Light background, use dark text
        effectiveForeground = Colors.black87;
      } else {
        // Dark background, use light text
        effectiveForeground = Colors.white;
      }
    }
    
    return defaultTheme.copyWith(
      backgroundColor: effectiveBackground,
      foregroundColor: effectiveForeground,
      arrowColor: effectiveBackground,
      textStyle: defaultTheme.textStyle?.copyWith(
        color: effectiveForeground,
      ),
    );
  }

  /// Creates a theme for error tooltips.
  static ShadcnTooltipTheme errorTheme(ColorScheme colorScheme) {
    return customColors(
      colorScheme: colorScheme,
      backgroundColor: colorScheme.error,
      foregroundColor: colorScheme.onError,
      highContrast: true,
    );
  }

  /// Creates a theme for warning tooltips.
  static ShadcnTooltipTheme warningTheme(ColorScheme colorScheme) {
    return customColors(
      colorScheme: colorScheme,
      backgroundColor: Colors.orange.shade700,
      foregroundColor: Colors.white,
      highContrast: true,
    );
  }

  /// Creates a theme for success tooltips.
  static ShadcnTooltipTheme successTheme(ColorScheme colorScheme) {
    return customColors(
      colorScheme: colorScheme,
      backgroundColor: Colors.green.shade700,
      foregroundColor: Colors.white,
      highContrast: true,
    );
  }

  /// Resolves the background color using the theme-aware fallback chain.
  Color resolveBackgroundColor(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return resolveColor(
      context,
      backgroundColor,
      (theme) => isDark 
          ? theme.colorScheme.onSurface.withOpacity(0.9)
          : theme.colorScheme.onSurface.withOpacity(0.9),
    );
  }

  /// Resolves the foreground color using the theme-aware fallback chain.
  Color resolveForegroundColor(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return resolveColor(
      context,
      foregroundColor,
      (theme) => isDark 
          ? theme.colorScheme.surface
          : theme.colorScheme.surface,
    );
  }

  /// Resolves the arrow color, defaulting to background color.
  Color resolveArrowColor(BuildContext context) {
    return arrowColor ?? resolveBackgroundColor(context);
  }

  /// Resolves the content padding with visual density adjustments.
  EdgeInsets resolveContentPadding(BuildContext context) {
    return resolveSpacing(
      context,
      contentPadding,
      EdgeInsets.symmetric(
        horizontal: ShadcnTokens.spacing2,
        vertical: ShadcnTokens.spacing1,
      ),
    );
  }

  /// Resolves the border radius with theme consistency.
  BorderRadius resolveTooltipBorderRadius(BuildContext context) {
    return super.resolveBorderRadius(
      context,
      borderRadius,
      BorderRadius.circular(ShadcnTokens.radiusSm),
    );
  }

  /// Resolves the text style with theme inheritance.
  TextStyle resolveTooltipTextStyle(BuildContext context) {
    return super.resolveTextStyle(
      context,
      textStyle,
      (textTheme) => textTheme.bodySmall ?? const TextStyle(),
    );
  }

  @override
  bool validate({bool throwOnError = false}) {
    try {
      // Validate size constraints
      if (maxWidth != null && maxWidth! <= 0) {
        throw StateError('maxWidth must be positive');
      }
      
      // Validate arrow size
      if (arrowSize != null && arrowSize! <= 0) {
        throw StateError('arrowSize must be positive');
      }
      
      // Validate offset
      if (offset != null && offset! < 0) {
        throw StateError('offset cannot be negative');
      }
      
      // Validate durations
      if (showDelay != null && showDelay!.isNegative) {
        throw StateError('showDelay cannot be negative');
      }
      if (hideDelay != null && hideDelay!.isNegative) {
        throw StateError('hideDelay cannot be negative');
      }
      if (animationDuration != null && animationDuration!.isNegative) {
        throw StateError('animationDuration cannot be negative');
      }
      
      // Validate elevation
      if (elevation != null && elevation! < 0) {
        throw StateError('elevation cannot be negative');
      }
      
      return true;
    } catch (e) {
      if (throwOnError) {
        rethrow;
      }
      debugPrint('ShadcnTooltipTheme validation failed: $e');
      return false;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is ShadcnTooltipTheme &&
        backgroundColor == other.backgroundColor &&
        foregroundColor == other.foregroundColor &&
        borderRadius == other.borderRadius &&
        contentPadding == other.contentPadding &&
        maxWidth == other.maxWidth &&
        arrowSize == other.arrowSize &&
        arrowColor == other.arrowColor &&
        offset == other.offset &&
        showDelay == other.showDelay &&
        hideDelay == other.hideDelay &&
        animationDuration == other.animationDuration &&
        animationCurve == other.animationCurve &&
        textStyle == other.textStyle &&
        elevation == other.elevation &&
        shadowColor == other.shadowColor &&
        showOnLongPress == other.showOnLongPress &&
        showOnTap == other.showOnTap &&
        showOnFocus == other.showOnFocus &&
        showOnHover == other.showOnHover;
  }

  @override
  int get hashCode {
    return Object.hash(
      backgroundColor,
      foregroundColor,
      borderRadius,
      contentPadding,
      maxWidth,
      arrowSize,
      arrowColor,
      offset,
      showDelay,
      hideDelay,
      animationDuration,
      animationCurve,
      textStyle,
      elevation,
      shadowColor,
      showOnLongPress,
      showOnTap,
      showOnFocus,
      showOnHover,
    );
  }
}