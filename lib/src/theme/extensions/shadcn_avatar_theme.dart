import 'dart:ui' show lerpDouble;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'shadcn_theme_extension.dart';
import '../../constants/shadcn_tokens.dart';

/// Theme extension for [ShadcnAvatar] component.
/// 
/// This theme extension provides comprehensive theming support for avatar components,
/// including sizing variants, background colors, border properties, and text styling
/// for initials display. All properties can be customized while maintaining
/// shadcn design system consistency.
/// 
/// The avatar theme supports three size variants (small, medium, large) with
/// shadcn-standard dimensions and proper Material Design integration.
/// 
/// Example usage:
/// ```dart
/// Theme(
///   data: ThemeData(
///     extensions: [
///       ShadcnAvatarTheme(
///         backgroundColor: Colors.blue,
///         foregroundColor: Colors.white,
///         borderColor: Colors.grey,
///       ),
///     ],
///   ),
///   child: ShadcnAvatar(initials: 'J<PERSON>'),
/// )
/// ```
class ShadcnAvatarTheme extends ShadcnThemeExtension<ShadcnAvatarTheme> {
  /// Background color for the avatar.
  final Color? backgroundColor;
  
  /// Foreground color for text and icons in the avatar.
  final Color? foregroundColor;
  
  /// Border color for the avatar.
  final Color? borderColor;
  
  /// Border width for the avatar.
  final double? borderWidth;
  
  /// Border radius for the avatar. If null, will be circular.
  final BorderRadius? borderRadius;
  
  // Size variant properties
  
  /// Small avatar size (32px).
  final double? smallSize;
  
  /// Medium avatar size (40px) - default.
  final double? mediumSize;
  
  /// Large avatar size (48px).
  final double? largeSize;
  
  // Text styling for initials
  
  /// Text style for small avatar initials.
  final TextStyle? smallTextStyle;
  
  /// Text style for medium avatar initials.
  final TextStyle? mediumTextStyle;
  
  /// Text style for large avatar initials.
  final TextStyle? largeTextStyle;
  
  // Fallback icon properties
  
  /// Icon to display when no image or initials are provided.
  final IconData? fallbackIcon;
  
  /// Size of the fallback icon for small avatars.
  final double? smallIconSize;
  
  /// Size of the fallback icon for medium avatars.
  final double? mediumIconSize;
  
  /// Size of the fallback icon for large avatars.
  final double? largeIconSize;
  
  // Animation properties
  
  /// Duration for size and state transitions.
  final Duration? animationDuration;
  
  /// Curve for animations.
  final Curve? animationCurve;

  const ShadcnAvatarTheme({
    this.backgroundColor,
    this.foregroundColor,
    this.borderColor,
    this.borderWidth,
    this.borderRadius,
    this.smallSize,
    this.mediumSize,
    this.largeSize,
    this.smallTextStyle,
    this.mediumTextStyle,
    this.largeTextStyle,
    this.fallbackIcon,
    this.smallIconSize,
    this.mediumIconSize,
    this.largeIconSize,
    this.animationDuration,
    this.animationCurve,
  });

  @override
  ShadcnAvatarTheme copyWith({
    Color? backgroundColor,
    Color? foregroundColor,
    Color? borderColor,
    double? borderWidth,
    BorderRadius? borderRadius,
    double? smallSize,
    double? mediumSize,
    double? largeSize,
    TextStyle? smallTextStyle,
    TextStyle? mediumTextStyle,
    TextStyle? largeTextStyle,
    IconData? fallbackIcon,
    double? smallIconSize,
    double? mediumIconSize,
    double? largeIconSize,
    Duration? animationDuration,
    Curve? animationCurve,
  }) {
    return ShadcnAvatarTheme(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      foregroundColor: foregroundColor ?? this.foregroundColor,
      borderColor: borderColor ?? this.borderColor,
      borderWidth: borderWidth ?? this.borderWidth,
      borderRadius: borderRadius ?? this.borderRadius,
      smallSize: smallSize ?? this.smallSize,
      mediumSize: mediumSize ?? this.mediumSize,
      largeSize: largeSize ?? this.largeSize,
      smallTextStyle: smallTextStyle ?? this.smallTextStyle,
      mediumTextStyle: mediumTextStyle ?? this.mediumTextStyle,
      largeTextStyle: largeTextStyle ?? this.largeTextStyle,
      fallbackIcon: fallbackIcon ?? this.fallbackIcon,
      smallIconSize: smallIconSize ?? this.smallIconSize,
      mediumIconSize: mediumIconSize ?? this.mediumIconSize,
      largeIconSize: largeIconSize ?? this.largeIconSize,
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
    );
  }

  @override
  ShadcnAvatarTheme lerp(ShadcnAvatarTheme? other, double t) {
    if (other is! ShadcnAvatarTheme) {
      return this;
    }

    return ShadcnAvatarTheme(
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t),
      foregroundColor: Color.lerp(foregroundColor, other.foregroundColor, t),
      borderColor: Color.lerp(borderColor, other.borderColor, t),
      borderWidth: lerpDouble(borderWidth, other.borderWidth, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      smallSize: lerpDouble(smallSize, other.smallSize, t),
      mediumSize: lerpDouble(mediumSize, other.mediumSize, t),
      largeSize: lerpDouble(largeSize, other.largeSize, t),
      smallTextStyle: TextStyle.lerp(smallTextStyle, other.smallTextStyle, t),
      mediumTextStyle: TextStyle.lerp(mediumTextStyle, other.mediumTextStyle, t),
      largeTextStyle: TextStyle.lerp(largeTextStyle, other.largeTextStyle, t),
      fallbackIcon: t < 0.5 ? fallbackIcon : other.fallbackIcon,
      smallIconSize: lerpDouble(smallIconSize, other.smallIconSize, t),
      mediumIconSize: lerpDouble(mediumIconSize, other.mediumIconSize, t),
      largeIconSize: lerpDouble(largeIconSize, other.largeIconSize, t),
      animationDuration: t < 0.5 ? animationDuration : other.animationDuration,
      animationCurve: t < 0.5 ? animationCurve : other.animationCurve,
    );
  }

  /// Creates a default [ShadcnAvatarTheme] from the given [ColorScheme].
  /// 
  /// This factory method creates a theme that follows shadcn design principles
  /// while integrating with Material Design. The resulting theme will have
  /// consistent styling that works well with both light and dark themes.
  /// 
  /// The default theme uses:
  /// - Primary color for background
  /// - On-primary color for foreground/text
  /// - Outline color for border
  /// - Shadcn-standard sizes (32px, 40px, 48px)
  /// - Proper text scaling for initials
  static ShadcnAvatarTheme defaultTheme(ColorScheme colorScheme) {
    final isLight = colorScheme.brightness == Brightness.light;
    
    return ShadcnAvatarTheme(
      backgroundColor: colorScheme.surface,
      foregroundColor: colorScheme.onSurface,
      borderColor: colorScheme.outline.withOpacity(0.12),
      borderWidth: ShadcnTokens.borderWidth,
      borderRadius: null, // Will be circular by default
      
      // Size variants matching shadcn standards
      smallSize: ShadcnTokens.avatarSizeSm,
      mediumSize: ShadcnTokens.avatarSizeMd,
      largeSize: ShadcnTokens.avatarSizeLg,
      
      // Text styles for initials with proper scaling
      smallTextStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeSm,
        fontWeight: ShadcnTokens.fontWeightMedium,
        color: colorScheme.onSurface,
        letterSpacing: 0.1,
      ),
      mediumTextStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightMedium,
        color: colorScheme.onSurface,
        letterSpacing: 0.1,
      ),
      largeTextStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeLg,
        fontWeight: ShadcnTokens.fontWeightMedium,
        color: colorScheme.onSurface,
        letterSpacing: 0.1,
      ),
      
      // Fallback icon
      fallbackIcon: Icons.person,
      smallIconSize: ShadcnTokens.iconSizeSm,
      mediumIconSize: ShadcnTokens.iconSizeMd,
      largeIconSize: ShadcnTokens.iconSizeLg,
      
      // Animation properties
      animationDuration: ShadcnTokens.durationFast,
      animationCurve: Curves.easeInOut,
    );
  }

  /// Creates a theme optimized for user profile pictures.
  /// 
  /// This variant uses a subtle border and background that works well
  /// for profile picture display in user interfaces.
  static ShadcnAvatarTheme profileTheme(ColorScheme colorScheme) {
    return defaultTheme(colorScheme).copyWith(
      backgroundColor: colorScheme.surfaceVariant,
      foregroundColor: colorScheme.onSurfaceVariant,
      borderColor: colorScheme.outline.withOpacity(0.2),
    );
  }

  /// Creates a theme optimized for status indicators.
  /// 
  /// This variant uses accent colors to make avatars stand out
  /// when used as status indicators or in notification contexts.
  static ShadcnAvatarTheme statusTheme(ColorScheme colorScheme) {
    return defaultTheme(colorScheme).copyWith(
      backgroundColor: colorScheme.secondary,
      foregroundColor: colorScheme.onSecondary,
      borderColor: colorScheme.secondary,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ShadcnAvatarTheme &&
          runtimeType == other.runtimeType &&
          backgroundColor == other.backgroundColor &&
          foregroundColor == other.foregroundColor &&
          borderColor == other.borderColor &&
          borderWidth == other.borderWidth &&
          borderRadius == other.borderRadius &&
          smallSize == other.smallSize &&
          mediumSize == other.mediumSize &&
          largeSize == other.largeSize &&
          smallTextStyle == other.smallTextStyle &&
          mediumTextStyle == other.mediumTextStyle &&
          largeTextStyle == other.largeTextStyle &&
          fallbackIcon == other.fallbackIcon &&
          smallIconSize == other.smallIconSize &&
          mediumIconSize == other.mediumIconSize &&
          largeIconSize == other.largeIconSize &&
          animationDuration == other.animationDuration &&
          animationCurve == other.animationCurve;

  @override
  int get hashCode =>
      backgroundColor.hashCode ^
      foregroundColor.hashCode ^
      borderColor.hashCode ^
      borderWidth.hashCode ^
      borderRadius.hashCode ^
      smallSize.hashCode ^
      mediumSize.hashCode ^
      largeSize.hashCode ^
      smallTextStyle.hashCode ^
      mediumTextStyle.hashCode ^
      largeTextStyle.hashCode ^
      fallbackIcon.hashCode ^
      smallIconSize.hashCode ^
      mediumIconSize.hashCode ^
      largeIconSize.hashCode ^
      animationDuration.hashCode ^
      animationCurve.hashCode;

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    
    properties.add(ColorProperty('backgroundColor', backgroundColor));
    properties.add(ColorProperty('foregroundColor', foregroundColor));
    properties.add(ColorProperty('borderColor', borderColor));
    properties.add(DoubleProperty('borderWidth', borderWidth));
    properties.add(DiagnosticsProperty<BorderRadius>('borderRadius', borderRadius));
    properties.add(DoubleProperty('smallSize', smallSize));
    properties.add(DoubleProperty('mediumSize', mediumSize));
    properties.add(DoubleProperty('largeSize', largeSize));
    properties.add(DiagnosticsProperty<TextStyle>('smallTextStyle', smallTextStyle));
    properties.add(DiagnosticsProperty<TextStyle>('mediumTextStyle', mediumTextStyle));
    properties.add(DiagnosticsProperty<TextStyle>('largeTextStyle', largeTextStyle));
    properties.add(DiagnosticsProperty<IconData>('fallbackIcon', fallbackIcon));
    properties.add(DoubleProperty('smallIconSize', smallIconSize));
    properties.add(DoubleProperty('mediumIconSize', mediumIconSize));
    properties.add(DoubleProperty('largeIconSize', largeIconSize));
    properties.add(DiagnosticsProperty<Duration>('animationDuration', animationDuration));
    properties.add(DiagnosticsProperty<Curve>('animationCurve', animationCurve));
  }
}

/// Size variants for [ShadcnAvatar].
/// 
/// These sizes follow shadcn design system standards:
/// - [small]: 32px diameter
/// - [medium]: 40px diameter (default)
/// - [large]: 48px diameter
enum ShadcnAvatarSize {
  /// Small avatar (32px diameter)
  small,
  
  /// Medium avatar (40px diameter) - default size
  medium,
  
  /// Large avatar (48px diameter)
  large,
}