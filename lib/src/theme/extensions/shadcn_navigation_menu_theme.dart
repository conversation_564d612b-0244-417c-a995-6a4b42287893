import 'package:flutter/material.dart';
import '../../constants/shadcn_tokens.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for the ShadcnNavigationMenu component.
/// 
/// This theme extension provides all styling properties for navigation menus,
/// including item styling, dropdown support, nested menu handling, and interactive states.
/// It follows shadcn design principles while integrating with Material Design theming patterns.
/// 
/// All properties are nullable to support fallback to Material theme values
/// and provide flexible customization options for developers.
class ShadcnNavigationMenuTheme extends ShadcnThemeExtension<ShadcnNavigationMenuTheme> {
  // Container properties
  final Color? backgroundColor;
  final Color? surfaceColor;
  final EdgeInsets? containerPadding;
  final BorderRadius? borderRadius;
  final double? minHeight;
  final BoxBorder? border;
  final List<BoxShadow>? shadows;
  
  // Item styling
  final Color? itemColor;
  final Color? itemActiveColor;
  final Color? itemHoverColor;
  final Color? itemFocusColor;
  final Color? itemPressedColor;
  final Color? itemDisabledColor;
  
  // Item background colors
  final Color? itemBackgroundColor;
  final Color? itemActiveBackgroundColor;
  final Color? itemHoverBackgroundColor;
  final Color? itemFocusBackgroundColor;
  final Color? itemPressedBackgroundColor;
  final Color? itemDisabledBackgroundColor;
  
  // Item properties
  final EdgeInsets? itemPadding;
  final double? itemHeight;
  final double? itemSpacing;
  final BorderRadius? itemBorderRadius;
  final double? itemMinWidth;
  
  // Text styling
  final TextStyle? itemTextStyle;
  final TextStyle? activeItemTextStyle;
  final FontWeight? itemFontWeight;
  final double? itemFontSize;
  
  // Icon styling
  final double? iconSize;
  final double? iconSpacing;
  final Color? iconColor;
  final Color? activeIconColor;
  
  // Dropdown properties
  final Color? dropdownBackgroundColor;
  final Color? dropdownSurfaceColor;
  final EdgeInsets? dropdownPadding;
  final BorderRadius? dropdownBorderRadius;
  final double? dropdownElevation;
  final List<BoxShadow>? dropdownShadows;
  final BoxBorder? dropdownBorder;
  final double? dropdownMinWidth;
  final double? dropdownMaxWidth;
  final double? dropdownOffset;
  
  // Dropdown item styling
  final EdgeInsets? dropdownItemPadding;
  final double? dropdownItemHeight;
  final double? dropdownItemSpacing;
  final BorderRadius? dropdownItemBorderRadius;
  
  // Divider styling
  final Color? dividerColor;
  final double? dividerThickness;
  final double? dividerHeight;
  final EdgeInsets? dividerMargin;
  
  // Animation properties
  final Duration? animationDuration;
  final Curve? animationCurve;
  final Duration? hoverAnimationDuration;
  final Duration? dropdownAnimationDuration;
  
  // Focus properties
  final Color? focusColor;
  final double? focusWidth;
  final double? focusOffset;
  
  // Indicator properties
  final Color? indicatorColor;
  final double? indicatorWidth;
  final double? indicatorHeight;
  final BorderRadius? indicatorBorderRadius;
  
  const ShadcnNavigationMenuTheme({
    // Container
    this.backgroundColor,
    this.surfaceColor,
    this.containerPadding,
    this.borderRadius,
    this.minHeight,
    this.border,
    this.shadows,
    
    // Item colors
    this.itemColor,
    this.itemActiveColor,
    this.itemHoverColor,
    this.itemFocusColor,
    this.itemPressedColor,
    this.itemDisabledColor,
    
    // Item background colors
    this.itemBackgroundColor,
    this.itemActiveBackgroundColor,
    this.itemHoverBackgroundColor,
    this.itemFocusBackgroundColor,
    this.itemPressedBackgroundColor,
    this.itemDisabledBackgroundColor,
    
    // Item properties
    this.itemPadding,
    this.itemHeight,
    this.itemSpacing,
    this.itemBorderRadius,
    this.itemMinWidth,
    
    // Text styling
    this.itemTextStyle,
    this.activeItemTextStyle,
    this.itemFontWeight,
    this.itemFontSize,
    
    // Icon styling
    this.iconSize,
    this.iconSpacing,
    this.iconColor,
    this.activeIconColor,
    
    // Dropdown
    this.dropdownBackgroundColor,
    this.dropdownSurfaceColor,
    this.dropdownPadding,
    this.dropdownBorderRadius,
    this.dropdownElevation,
    this.dropdownShadows,
    this.dropdownBorder,
    this.dropdownMinWidth,
    this.dropdownMaxWidth,
    this.dropdownOffset,
    
    // Dropdown items
    this.dropdownItemPadding,
    this.dropdownItemHeight,
    this.dropdownItemSpacing,
    this.dropdownItemBorderRadius,
    
    // Divider
    this.dividerColor,
    this.dividerThickness,
    this.dividerHeight,
    this.dividerMargin,
    
    // Animation
    this.animationDuration,
    this.animationCurve,
    this.hoverAnimationDuration,
    this.dropdownAnimationDuration,
    
    // Focus
    this.focusColor,
    this.focusWidth,
    this.focusOffset,
    
    // Indicator
    this.indicatorColor,
    this.indicatorWidth,
    this.indicatorHeight,
    this.indicatorBorderRadius,
  });

  /// Creates a default navigation menu theme based on the provided ColorScheme.
  /// 
  /// This factory method generates a complete navigation menu theme that follows
  /// shadcn design principles while integrating with Material Design colors.
  /// All properties are set to shadcn-standard values with proper fallbacks.
  static ShadcnNavigationMenuTheme defaultTheme(ColorScheme colorScheme) {
    final isDark = colorScheme.brightness == Brightness.dark;
    
    return ShadcnNavigationMenuTheme(
      // Container
      backgroundColor: Colors.transparent,
      surfaceColor: colorScheme.surface,
      containerPadding: const EdgeInsets.symmetric(
        horizontal: ShadcnTokens.spacing1,
        vertical: ShadcnTokens.spacing1,
      ),
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusMd),
      minHeight: ShadcnTokens.buttonHeightMd,
      border: null,
      shadows: null,
      
      // Item colors
      itemColor: colorScheme.onSurface.withOpacity(0.7),
      itemActiveColor: colorScheme.onSurface,
      itemHoverColor: colorScheme.onSurface,
      itemFocusColor: colorScheme.onSurface,
      itemPressedColor: colorScheme.onSurface.withOpacity(0.9),
      itemDisabledColor: colorScheme.onSurface.withOpacity(0.38),
      
      // Item background colors
      itemBackgroundColor: Colors.transparent,
      itemActiveBackgroundColor: colorScheme.primary.withOpacity(0.1),
      itemHoverBackgroundColor: colorScheme.onSurface.withOpacity(0.04),
      itemFocusBackgroundColor: colorScheme.primary.withOpacity(0.12),
      itemPressedBackgroundColor: colorScheme.onSurface.withOpacity(0.08),
      itemDisabledBackgroundColor: Colors.transparent,
      
      // Item properties
      itemPadding: const EdgeInsets.symmetric(
        horizontal: ShadcnTokens.spacing3,
        vertical: ShadcnTokens.spacing2,
      ),
      itemHeight: ShadcnTokens.buttonHeightSm,
      itemSpacing: ShadcnTokens.spacing1,
      itemBorderRadius: BorderRadius.circular(ShadcnTokens.radiusSm),
      itemMinWidth: 64.0,
      
      // Text styling
      itemTextStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightMedium,
        height: ShadcnTokens.lineHeightNormal,
      ),
      activeItemTextStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightSemibold,
        height: ShadcnTokens.lineHeightNormal,
      ),
      itemFontWeight: ShadcnTokens.fontWeightMedium,
      itemFontSize: ShadcnTokens.fontSizeMd,
      
      // Icon styling
      iconSize: ShadcnTokens.iconSizeSm,
      iconSpacing: ShadcnTokens.spacing2,
      iconColor: colorScheme.onSurface.withOpacity(0.7),
      activeIconColor: colorScheme.onSurface,
      
      // Dropdown
      dropdownBackgroundColor: colorScheme.surface,
      dropdownSurfaceColor: colorScheme.surface,
      dropdownPadding: const EdgeInsets.all(ShadcnTokens.spacing1),
      dropdownBorderRadius: BorderRadius.circular(ShadcnTokens.radiusMd),
      dropdownElevation: ShadcnTokens.elevationMd,
      dropdownShadows: [
        BoxShadow(
          color: colorScheme.shadow.withOpacity(0.1),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
      dropdownBorder: Border.all(
        color: colorScheme.outline.withOpacity(0.2),
        width: ShadcnTokens.borderWidth,
      ),
      dropdownMinWidth: 200.0,
      dropdownMaxWidth: 400.0,
      dropdownOffset: 4.0,
      
      // Dropdown items
      dropdownItemPadding: const EdgeInsets.symmetric(
        horizontal: ShadcnTokens.spacing3,
        vertical: ShadcnTokens.spacing2,
      ),
      dropdownItemHeight: ShadcnTokens.buttonHeightSm,
      dropdownItemSpacing: ShadcnTokens.spacing0,
      dropdownItemBorderRadius: BorderRadius.circular(ShadcnTokens.radiusSm),
      
      // Divider
      dividerColor: colorScheme.outline.withOpacity(0.2),
      dividerThickness: ShadcnTokens.borderWidth,
      dividerHeight: 1.0,
      dividerMargin: const EdgeInsets.symmetric(vertical: ShadcnTokens.spacing1),
      
      // Animation
      animationDuration: ShadcnTokens.durationNormal,
      animationCurve: Curves.easeInOut,
      hoverAnimationDuration: ShadcnTokens.durationFast,
      dropdownAnimationDuration: ShadcnTokens.durationNormal,
      
      // Focus
      focusColor: colorScheme.primary.withOpacity(0.12),
      focusWidth: 2.0,
      focusOffset: 2.0,
      
      // Indicator
      indicatorColor: colorScheme.primary,
      indicatorWidth: 24.0,
      indicatorHeight: 2.0,
      indicatorBorderRadius: BorderRadius.circular(1.0),
    );
  }

  @override
  ShadcnNavigationMenuTheme copyWith({
    // Container
    Color? backgroundColor,
    Color? surfaceColor,
    EdgeInsets? containerPadding,
    BorderRadius? borderRadius,
    double? minHeight,
    BoxBorder? border,
    List<BoxShadow>? shadows,
    
    // Item colors
    Color? itemColor,
    Color? itemActiveColor,
    Color? itemHoverColor,
    Color? itemFocusColor,
    Color? itemPressedColor,
    Color? itemDisabledColor,
    
    // Item background colors
    Color? itemBackgroundColor,
    Color? itemActiveBackgroundColor,
    Color? itemHoverBackgroundColor,
    Color? itemFocusBackgroundColor,
    Color? itemPressedBackgroundColor,
    Color? itemDisabledBackgroundColor,
    
    // Item properties
    EdgeInsets? itemPadding,
    double? itemHeight,
    double? itemSpacing,
    BorderRadius? itemBorderRadius,
    double? itemMinWidth,
    
    // Text styling
    TextStyle? itemTextStyle,
    TextStyle? activeItemTextStyle,
    FontWeight? itemFontWeight,
    double? itemFontSize,
    
    // Icon styling
    double? iconSize,
    double? iconSpacing,
    Color? iconColor,
    Color? activeIconColor,
    
    // Dropdown
    Color? dropdownBackgroundColor,
    Color? dropdownSurfaceColor,
    EdgeInsets? dropdownPadding,
    BorderRadius? dropdownBorderRadius,
    double? dropdownElevation,
    List<BoxShadow>? dropdownShadows,
    BoxBorder? dropdownBorder,
    double? dropdownMinWidth,
    double? dropdownMaxWidth,
    double? dropdownOffset,
    
    // Dropdown items
    EdgeInsets? dropdownItemPadding,
    double? dropdownItemHeight,
    double? dropdownItemSpacing,
    BorderRadius? dropdownItemBorderRadius,
    
    // Divider
    Color? dividerColor,
    double? dividerThickness,
    double? dividerHeight,
    EdgeInsets? dividerMargin,
    
    // Animation
    Duration? animationDuration,
    Curve? animationCurve,
    Duration? hoverAnimationDuration,
    Duration? dropdownAnimationDuration,
    
    // Focus
    Color? focusColor,
    double? focusWidth,
    double? focusOffset,
    
    // Indicator
    Color? indicatorColor,
    double? indicatorWidth,
    double? indicatorHeight,
    BorderRadius? indicatorBorderRadius,
  }) {
    return ShadcnNavigationMenuTheme(
      // Container
      backgroundColor: backgroundColor ?? this.backgroundColor,
      surfaceColor: surfaceColor ?? this.surfaceColor,
      containerPadding: containerPadding ?? this.containerPadding,
      borderRadius: borderRadius ?? this.borderRadius,
      minHeight: minHeight ?? this.minHeight,
      border: border ?? this.border,
      shadows: shadows ?? this.shadows,
      
      // Item colors
      itemColor: itemColor ?? this.itemColor,
      itemActiveColor: itemActiveColor ?? this.itemActiveColor,
      itemHoverColor: itemHoverColor ?? this.itemHoverColor,
      itemFocusColor: itemFocusColor ?? this.itemFocusColor,
      itemPressedColor: itemPressedColor ?? this.itemPressedColor,
      itemDisabledColor: itemDisabledColor ?? this.itemDisabledColor,
      
      // Item background colors
      itemBackgroundColor: itemBackgroundColor ?? this.itemBackgroundColor,
      itemActiveBackgroundColor: itemActiveBackgroundColor ?? this.itemActiveBackgroundColor,
      itemHoverBackgroundColor: itemHoverBackgroundColor ?? this.itemHoverBackgroundColor,
      itemFocusBackgroundColor: itemFocusBackgroundColor ?? this.itemFocusBackgroundColor,
      itemPressedBackgroundColor: itemPressedBackgroundColor ?? this.itemPressedBackgroundColor,
      itemDisabledBackgroundColor: itemDisabledBackgroundColor ?? this.itemDisabledBackgroundColor,
      
      // Item properties
      itemPadding: itemPadding ?? this.itemPadding,
      itemHeight: itemHeight ?? this.itemHeight,
      itemSpacing: itemSpacing ?? this.itemSpacing,
      itemBorderRadius: itemBorderRadius ?? this.itemBorderRadius,
      itemMinWidth: itemMinWidth ?? this.itemMinWidth,
      
      // Text styling
      itemTextStyle: itemTextStyle ?? this.itemTextStyle,
      activeItemTextStyle: activeItemTextStyle ?? this.activeItemTextStyle,
      itemFontWeight: itemFontWeight ?? this.itemFontWeight,
      itemFontSize: itemFontSize ?? this.itemFontSize,
      
      // Icon styling
      iconSize: iconSize ?? this.iconSize,
      iconSpacing: iconSpacing ?? this.iconSpacing,
      iconColor: iconColor ?? this.iconColor,
      activeIconColor: activeIconColor ?? this.activeIconColor,
      
      // Dropdown
      dropdownBackgroundColor: dropdownBackgroundColor ?? this.dropdownBackgroundColor,
      dropdownSurfaceColor: dropdownSurfaceColor ?? this.dropdownSurfaceColor,
      dropdownPadding: dropdownPadding ?? this.dropdownPadding,
      dropdownBorderRadius: dropdownBorderRadius ?? this.dropdownBorderRadius,
      dropdownElevation: dropdownElevation ?? this.dropdownElevation,
      dropdownShadows: dropdownShadows ?? this.dropdownShadows,
      dropdownBorder: dropdownBorder ?? this.dropdownBorder,
      dropdownMinWidth: dropdownMinWidth ?? this.dropdownMinWidth,
      dropdownMaxWidth: dropdownMaxWidth ?? this.dropdownMaxWidth,
      dropdownOffset: dropdownOffset ?? this.dropdownOffset,
      
      // Dropdown items
      dropdownItemPadding: dropdownItemPadding ?? this.dropdownItemPadding,
      dropdownItemHeight: dropdownItemHeight ?? this.dropdownItemHeight,
      dropdownItemSpacing: dropdownItemSpacing ?? this.dropdownItemSpacing,
      dropdownItemBorderRadius: dropdownItemBorderRadius ?? this.dropdownItemBorderRadius,
      
      // Divider
      dividerColor: dividerColor ?? this.dividerColor,
      dividerThickness: dividerThickness ?? this.dividerThickness,
      dividerHeight: dividerHeight ?? this.dividerHeight,
      dividerMargin: dividerMargin ?? this.dividerMargin,
      
      // Animation
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
      hoverAnimationDuration: hoverAnimationDuration ?? this.hoverAnimationDuration,
      dropdownAnimationDuration: dropdownAnimationDuration ?? this.dropdownAnimationDuration,
      
      // Focus
      focusColor: focusColor ?? this.focusColor,
      focusWidth: focusWidth ?? this.focusWidth,
      focusOffset: focusOffset ?? this.focusOffset,
      
      // Indicator
      indicatorColor: indicatorColor ?? this.indicatorColor,
      indicatorWidth: indicatorWidth ?? this.indicatorWidth,
      indicatorHeight: indicatorHeight ?? this.indicatorHeight,
      indicatorBorderRadius: indicatorBorderRadius ?? this.indicatorBorderRadius,
    );
  }

  @override
  ShadcnNavigationMenuTheme lerp(ThemeExtension<ShadcnNavigationMenuTheme>? other, double t) {
    if (other is! ShadcnNavigationMenuTheme) {
      return this;
    }
    
    return ShadcnNavigationMenuTheme(
      // Container
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t),
      surfaceColor: Color.lerp(surfaceColor, other.surfaceColor, t),
      containerPadding: EdgeInsets.lerp(containerPadding, other.containerPadding, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      minHeight: t < 0.5 ? minHeight : other.minHeight,
      border: t < 0.5 ? border : other.border,
      shadows: t < 0.5 ? shadows : other.shadows,
      
      // Item colors
      itemColor: Color.lerp(itemColor, other.itemColor, t),
      itemActiveColor: Color.lerp(itemActiveColor, other.itemActiveColor, t),
      itemHoverColor: Color.lerp(itemHoverColor, other.itemHoverColor, t),
      itemFocusColor: Color.lerp(itemFocusColor, other.itemFocusColor, t),
      itemPressedColor: Color.lerp(itemPressedColor, other.itemPressedColor, t),
      itemDisabledColor: Color.lerp(itemDisabledColor, other.itemDisabledColor, t),
      
      // Item background colors
      itemBackgroundColor: Color.lerp(itemBackgroundColor, other.itemBackgroundColor, t),
      itemActiveBackgroundColor: Color.lerp(itemActiveBackgroundColor, other.itemActiveBackgroundColor, t),
      itemHoverBackgroundColor: Color.lerp(itemHoverBackgroundColor, other.itemHoverBackgroundColor, t),
      itemFocusBackgroundColor: Color.lerp(itemFocusBackgroundColor, other.itemFocusBackgroundColor, t),
      itemPressedBackgroundColor: Color.lerp(itemPressedBackgroundColor, other.itemPressedBackgroundColor, t),
      itemDisabledBackgroundColor: Color.lerp(itemDisabledBackgroundColor, other.itemDisabledBackgroundColor, t),
      
      // Item properties
      itemPadding: EdgeInsets.lerp(itemPadding, other.itemPadding, t),
      itemHeight: t < 0.5 ? itemHeight : other.itemHeight,
      itemSpacing: t < 0.5 ? itemSpacing : other.itemSpacing,
      itemBorderRadius: BorderRadius.lerp(itemBorderRadius, other.itemBorderRadius, t),
      itemMinWidth: t < 0.5 ? itemMinWidth : other.itemMinWidth,
      
      // Text styling
      itemTextStyle: TextStyle.lerp(itemTextStyle, other.itemTextStyle, t),
      activeItemTextStyle: TextStyle.lerp(activeItemTextStyle, other.activeItemTextStyle, t),
      itemFontWeight: t < 0.5 ? itemFontWeight : other.itemFontWeight,
      itemFontSize: t < 0.5 ? itemFontSize : other.itemFontSize,
      
      // Icon styling
      iconSize: t < 0.5 ? iconSize : other.iconSize,
      iconSpacing: t < 0.5 ? iconSpacing : other.iconSpacing,
      iconColor: Color.lerp(iconColor, other.iconColor, t),
      activeIconColor: Color.lerp(activeIconColor, other.activeIconColor, t),
      
      // Dropdown
      dropdownBackgroundColor: Color.lerp(dropdownBackgroundColor, other.dropdownBackgroundColor, t),
      dropdownSurfaceColor: Color.lerp(dropdownSurfaceColor, other.dropdownSurfaceColor, t),
      dropdownPadding: EdgeInsets.lerp(dropdownPadding, other.dropdownPadding, t),
      dropdownBorderRadius: BorderRadius.lerp(dropdownBorderRadius, other.dropdownBorderRadius, t),
      dropdownElevation: t < 0.5 ? dropdownElevation : other.dropdownElevation,
      dropdownShadows: t < 0.5 ? dropdownShadows : other.dropdownShadows,
      dropdownBorder: t < 0.5 ? dropdownBorder : other.dropdownBorder,
      dropdownMinWidth: t < 0.5 ? dropdownMinWidth : other.dropdownMinWidth,
      dropdownMaxWidth: t < 0.5 ? dropdownMaxWidth : other.dropdownMaxWidth,
      dropdownOffset: t < 0.5 ? dropdownOffset : other.dropdownOffset,
      
      // Dropdown items
      dropdownItemPadding: EdgeInsets.lerp(dropdownItemPadding, other.dropdownItemPadding, t),
      dropdownItemHeight: t < 0.5 ? dropdownItemHeight : other.dropdownItemHeight,
      dropdownItemSpacing: t < 0.5 ? dropdownItemSpacing : other.dropdownItemSpacing,
      dropdownItemBorderRadius: BorderRadius.lerp(dropdownItemBorderRadius, other.dropdownItemBorderRadius, t),
      
      // Divider
      dividerColor: Color.lerp(dividerColor, other.dividerColor, t),
      dividerThickness: t < 0.5 ? dividerThickness : other.dividerThickness,
      dividerHeight: t < 0.5 ? dividerHeight : other.dividerHeight,
      dividerMargin: EdgeInsets.lerp(dividerMargin, other.dividerMargin, t),
      
      // Animation
      animationDuration: t < 0.5 ? animationDuration : other.animationDuration,
      animationCurve: t < 0.5 ? animationCurve : other.animationCurve,
      hoverAnimationDuration: t < 0.5 ? hoverAnimationDuration : other.hoverAnimationDuration,
      dropdownAnimationDuration: t < 0.5 ? dropdownAnimationDuration : other.dropdownAnimationDuration,
      
      // Focus
      focusColor: Color.lerp(focusColor, other.focusColor, t),
      focusWidth: t < 0.5 ? focusWidth : other.focusWidth,
      focusOffset: t < 0.5 ? focusOffset : other.focusOffset,
      
      // Indicator
      indicatorColor: Color.lerp(indicatorColor, other.indicatorColor, t),
      indicatorWidth: t < 0.5 ? indicatorWidth : other.indicatorWidth,
      indicatorHeight: t < 0.5 ? indicatorHeight : other.indicatorHeight,
      indicatorBorderRadius: BorderRadius.lerp(indicatorBorderRadius, other.indicatorBorderRadius, t),
    );
  }

  @override
  bool validate({bool throwOnError = false}) {
    try {
      // Validate that required color properties have non-null values
      final requiredColors = {
        'itemColor': itemColor,
        'itemActiveColor': itemActiveColor,
        'dropdownBackgroundColor': dropdownBackgroundColor,
      };
      
      for (final entry in requiredColors.entries) {
        if (entry.value == null) {
          final error = 'ShadcnNavigationMenuTheme: ${entry.key} cannot be null';
          if (throwOnError) {
            throw FlutterError(error);
          }
          debugPrint('Warning: $error');
          return false;
        }
      }
      
      // Validate size properties
      final sizes = [minHeight, itemHeight, dropdownElevation];
      for (final size in sizes) {
        if (size != null && size < 0) {
          final error = 'ShadcnNavigationMenuTheme: Size values cannot be negative';
          if (throwOnError) {
            throw FlutterError(error);
          }
          debugPrint('Warning: $error');
          return false;
        }
      }
      
      return true;
    } catch (e) {
      if (throwOnError) {
        rethrow;
      }
      debugPrint('ShadcnNavigationMenuTheme validation error: $e');
      return false;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other.runtimeType != runtimeType) return false;
    
    return other is ShadcnNavigationMenuTheme &&
        // Container
        other.backgroundColor == backgroundColor &&
        other.surfaceColor == surfaceColor &&
        other.containerPadding == containerPadding &&
        other.borderRadius == borderRadius &&
        other.minHeight == minHeight &&
        other.border == border &&
        // Item colors (comparing a subset for brevity)
        other.itemColor == itemColor &&
        other.itemActiveColor == itemActiveColor &&
        other.itemHoverColor == itemHoverColor &&
        // Dropdown properties (comparing a subset)
        other.dropdownBackgroundColor == dropdownBackgroundColor &&
        other.dropdownElevation == dropdownElevation;
  }

  @override
  int get hashCode {
    return Object.hashAll([
      backgroundColor,
      surfaceColor,
      containerPadding,
      borderRadius,
      minHeight,
      itemColor,
      itemActiveColor,
      itemHoverColor,
      dropdownBackgroundColor,
      dropdownElevation,
    ]);
  }

  @override
  String toString() {
    return 'ShadcnNavigationMenuTheme('
        'itemColor: $itemColor, '
        'itemActiveColor: $itemActiveColor, '
        'dropdownBackgroundColor: $dropdownBackgroundColor, '
        'minHeight: $minHeight'
        ')';
  }
}

/// Data class representing a navigation menu item.
/// 
/// Each navigation menu item can contain text, icon, child items for nested menus,
/// and various callbacks for comprehensive navigation support.
class ShadcnNavigationMenuItem {
  /// The text label for this navigation item
  final String text;
  
  /// Optional icon to display alongside the text
  final Widget? icon;
  
  /// Optional leading icon
  final Widget? leading;
  
  /// Optional trailing icon or widget
  final Widget? trailing;
  
  /// Callback executed when the navigation item is tapped
  final VoidCallback? onTap;
  
  /// Optional child items for dropdown/nested menu support
  final List<ShadcnNavigationMenuItem>? children;
  
  /// Whether this navigation item is currently active/selected
  final bool active;
  
  /// Whether this navigation item is disabled
  final bool disabled;
  
  /// Optional semantic label for accessibility
  final String? semanticLabel;
  
  /// Optional tooltip text
  final String? tooltip;
  
  /// Optional divider to show after this item
  final bool showDividerAfter;
  
  const ShadcnNavigationMenuItem({
    required this.text,
    this.icon,
    this.leading,
    this.trailing,
    this.onTap,
    this.children,
    this.active = false,
    this.disabled = false,
    this.semanticLabel,
    this.tooltip,
    this.showDividerAfter = false,
  });
  
  /// Returns true if this item has children (dropdown/submenu)
  bool get hasChildren => children != null && children!.isNotEmpty;
  
  /// Returns true if this item is interactive (has onTap or children)
  bool get isInteractive => onTap != null || hasChildren;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other.runtimeType != runtimeType) return false;
    
    return other is ShadcnNavigationMenuItem &&
        other.text == text &&
        other.icon == icon &&
        other.leading == leading &&
        other.trailing == trailing &&
        other.onTap == onTap &&
        other.active == active &&
        other.disabled == disabled &&
        other.semanticLabel == semanticLabel &&
        other.tooltip == tooltip &&
        other.showDividerAfter == showDividerAfter;
  }

  @override
  int get hashCode {
    return Object.hash(
      text,
      icon,
      leading,
      trailing,
      onTap,
      active,
      disabled,
      semanticLabel,
      tooltip,
      showDividerAfter,
    );
  }

  @override
  String toString() {
    return 'ShadcnNavigationMenuItem('
        'text: $text, '
        'active: $active, '
        'disabled: $disabled, '
        'hasChildren: $hasChildren, '
        'hasCallback: ${onTap != null}'
        ')';
  }
}