import 'dart:ui' show lerpDouble;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../../src/constants/shadcn_tokens.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for ShadcnAccordion component.
/// 
/// Provides styling properties for accordion headers, content, triggers,
/// and animations while maintaining shadcn design consistency and Material
/// theme integration.
/// 
/// This theme extension covers:
/// - Header styling (background, text, padding)
/// - Content styling (background, padding, borders)
/// - Trigger styling (icons, colors, transitions)
/// - Animation properties (duration, curves)
/// - Interactive states (hover, focus, expanded/collapsed)
class ShadcnAccordionTheme extends ShadcnThemeExtension<ShadcnAccordionTheme> {
  /// Background color for the accordion header
  final Color? headerBackground;
  
  /// Text color for the accordion header
  final Color? headerForeground;
  
  /// Background color for the accordion header when hovered
  final Color? headerHoverBackground;
  
  /// Text color for the accordion header when hovered
  final Color? headerHoverForeground;
  
  /// Background color for the accordion content area
  final Color? contentBackground;
  
  /// Text color for the accordion content
  final Color? contentForeground;
  
  /// Border color for the accordion items
  final Color? borderColor;
  
  /// Color of the trigger icon
  final Color? triggerIconColor;
  
  /// Color of the trigger icon when hovered
  final Color? triggerIconHoverColor;
  
  /// Text style for the accordion header
  final TextStyle? headerTextStyle;
  
  /// Text style for the accordion content
  final TextStyle? contentTextStyle;
  
  /// Padding for the accordion header
  final EdgeInsets? headerPadding;
  
  /// Padding for the accordion content
  final EdgeInsets? contentPadding;
  
  /// Border radius for the accordion container
  final BorderRadius? borderRadius;
  
  /// Border width for the accordion items
  final double? borderWidth;
  
  /// Size of the trigger icon
  final double? triggerIconSize;
  
  /// Duration for expand/collapse animations
  final Duration? animationDuration;
  
  /// Curve for expand/collapse animations
  final Curve? animationCurve;
  
  /// Elevation for the accordion container
  final double? elevation;
  
  /// Shadow color for elevated accordions
  final Color? shadowColor;

  const ShadcnAccordionTheme({
    this.headerBackground,
    this.headerForeground,
    this.headerHoverBackground,
    this.headerHoverForeground,
    this.contentBackground,
    this.contentForeground,
    this.borderColor,
    this.triggerIconColor,
    this.triggerIconHoverColor,
    this.headerTextStyle,
    this.contentTextStyle,
    this.headerPadding,
    this.contentPadding,
    this.borderRadius,
    this.borderWidth,
    this.triggerIconSize,
    this.animationDuration,
    this.animationCurve,
    this.elevation,
    this.shadowColor,
  });

  @override
  ShadcnAccordionTheme copyWith({
    Color? headerBackground,
    Color? headerForeground,
    Color? headerHoverBackground,
    Color? headerHoverForeground,
    Color? contentBackground,
    Color? contentForeground,
    Color? borderColor,
    Color? triggerIconColor,
    Color? triggerIconHoverColor,
    TextStyle? headerTextStyle,
    TextStyle? contentTextStyle,
    EdgeInsets? headerPadding,
    EdgeInsets? contentPadding,
    BorderRadius? borderRadius,
    double? borderWidth,
    double? triggerIconSize,
    Duration? animationDuration,
    Curve? animationCurve,
    double? elevation,
    Color? shadowColor,
  }) {
    return ShadcnAccordionTheme(
      headerBackground: headerBackground ?? this.headerBackground,
      headerForeground: headerForeground ?? this.headerForeground,
      headerHoverBackground: headerHoverBackground ?? this.headerHoverBackground,
      headerHoverForeground: headerHoverForeground ?? this.headerHoverForeground,
      contentBackground: contentBackground ?? this.contentBackground,
      contentForeground: contentForeground ?? this.contentForeground,
      borderColor: borderColor ?? this.borderColor,
      triggerIconColor: triggerIconColor ?? this.triggerIconColor,
      triggerIconHoverColor: triggerIconHoverColor ?? this.triggerIconHoverColor,
      headerTextStyle: headerTextStyle ?? this.headerTextStyle,
      contentTextStyle: contentTextStyle ?? this.contentTextStyle,
      headerPadding: headerPadding ?? this.headerPadding,
      contentPadding: contentPadding ?? this.contentPadding,
      borderRadius: borderRadius ?? this.borderRadius,
      borderWidth: borderWidth ?? this.borderWidth,
      triggerIconSize: triggerIconSize ?? this.triggerIconSize,
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
      elevation: elevation ?? this.elevation,
      shadowColor: shadowColor ?? this.shadowColor,
    );
  }

  @override
  ShadcnAccordionTheme lerp(ShadcnAccordionTheme? other, double t) {
    if (other == null) return this;
    
    return ShadcnAccordionTheme(
      headerBackground: Color.lerp(headerBackground, other.headerBackground, t),
      headerForeground: Color.lerp(headerForeground, other.headerForeground, t),
      headerHoverBackground: Color.lerp(headerHoverBackground, other.headerHoverBackground, t),
      headerHoverForeground: Color.lerp(headerHoverForeground, other.headerHoverForeground, t),
      contentBackground: Color.lerp(contentBackground, other.contentBackground, t),
      contentForeground: Color.lerp(contentForeground, other.contentForeground, t),
      borderColor: Color.lerp(borderColor, other.borderColor, t),
      triggerIconColor: Color.lerp(triggerIconColor, other.triggerIconColor, t),
      triggerIconHoverColor: Color.lerp(triggerIconHoverColor, other.triggerIconHoverColor, t),
      headerTextStyle: TextStyle.lerp(headerTextStyle, other.headerTextStyle, t),
      contentTextStyle: TextStyle.lerp(contentTextStyle, other.contentTextStyle, t),
      headerPadding: EdgeInsets.lerp(headerPadding, other.headerPadding, t),
      contentPadding: EdgeInsets.lerp(contentPadding, other.contentPadding, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      borderWidth: lerpDouble(borderWidth, other.borderWidth, t),
      triggerIconSize: lerpDouble(triggerIconSize, other.triggerIconSize, t),
      animationDuration: t < 0.5 ? animationDuration : other.animationDuration,
      animationCurve: t < 0.5 ? animationCurve : other.animationCurve,
      elevation: lerpDouble(elevation, other.elevation, t),
      shadowColor: Color.lerp(shadowColor, other.shadowColor, t),
    );
  }

  /// Creates a default ShadcnAccordionTheme based on the provided ColorScheme.
  /// 
  /// This factory method generates a theme that follows shadcn design principles
  /// while integrating with Material Design colors. It provides sensible defaults
  /// for all accordion styling properties.
  /// 
  /// The theme is designed to work well in both light and dark modes and
  /// automatically adapts to the provided color scheme.
  static ShadcnAccordionTheme defaultTheme(ColorScheme colorScheme) {
    final bool isDark = colorScheme.brightness == Brightness.dark;
    
    return ShadcnAccordionTheme(
      headerBackground: colorScheme.surface,
      headerForeground: colorScheme.onSurface,
      headerHoverBackground: isDark 
        ? colorScheme.surfaceVariant.withOpacity(0.1)
        : colorScheme.surfaceVariant.withOpacity(0.05),
      headerHoverForeground: colorScheme.onSurface,
      contentBackground: colorScheme.surface,
      contentForeground: colorScheme.onSurface.withOpacity(0.8),
      borderColor: colorScheme.outline,
      triggerIconColor: colorScheme.onSurface.withOpacity(0.6),
      triggerIconHoverColor: colorScheme.onSurface,
      headerTextStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightMedium,
        color: colorScheme.onSurface,
        height: ShadcnTokens.lineHeightNormal,
      ),
      contentTextStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightNormal,
        color: colorScheme.onSurface.withOpacity(0.8),
        height: ShadcnTokens.lineHeightNormal,
      ),
      headerPadding: const EdgeInsets.symmetric(
        horizontal: ShadcnTokens.spacing4,
        vertical: ShadcnTokens.spacing3,
      ),
      contentPadding: const EdgeInsets.only(
        left: ShadcnTokens.spacing4,
        right: ShadcnTokens.spacing4,
        bottom: ShadcnTokens.spacing4,
        top: 0,
      ),
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusMd),
      borderWidth: ShadcnTokens.borderWidth,
      triggerIconSize: ShadcnTokens.iconSizeMd,
      animationDuration: ShadcnTokens.durationNormal,
      animationCurve: Curves.easeInOut,
      elevation: ShadcnTokens.elevationNone,
      shadowColor: colorScheme.shadow,
    );
  }

  /// Creates a light theme variant optimized for light mode displays.
  static ShadcnAccordionTheme lightTheme(ColorScheme colorScheme) {
    return defaultTheme(colorScheme).copyWith(
      headerBackground: colorScheme.surface,
      headerHoverBackground: colorScheme.surfaceVariant.withOpacity(0.05),
      borderColor: colorScheme.outline.withOpacity(0.2),
    );
  }

  /// Creates a dark theme variant optimized for dark mode displays.
  static ShadcnAccordionTheme darkTheme(ColorScheme colorScheme) {
    return defaultTheme(colorScheme).copyWith(
      headerBackground: colorScheme.surface,
      headerHoverBackground: colorScheme.surfaceVariant.withOpacity(0.1),
      borderColor: colorScheme.outline.withOpacity(0.3),
    );
  }

  @override
  bool validate({bool throwOnError = false}) {
    try {
      // Validate animation properties
      if (animationDuration != null && animationDuration!.inMilliseconds < 0) {
        if (throwOnError) {
          throw Exception('Animation duration cannot be negative');
        }
        return false;
      }
      
      // Validate sizing properties
      if (borderWidth != null && borderWidth! < 0) {
        if (throwOnError) {
          throw Exception('Border width cannot be negative');
        }
        return false;
      }
      
      if (triggerIconSize != null && triggerIconSize! <= 0) {
        if (throwOnError) {
          throw Exception('Trigger icon size must be positive');
        }
        return false;
      }
      
      if (elevation != null && elevation! < 0) {
        if (throwOnError) {
          throw Exception('Elevation cannot be negative');
        }
        return false;
      }
      
      return true;
    } catch (e) {
      if (throwOnError) rethrow;
      return false;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is ShadcnAccordionTheme &&
        other.headerBackground == headerBackground &&
        other.headerForeground == headerForeground &&
        other.headerHoverBackground == headerHoverBackground &&
        other.headerHoverForeground == headerHoverForeground &&
        other.contentBackground == contentBackground &&
        other.contentForeground == contentForeground &&
        other.borderColor == borderColor &&
        other.triggerIconColor == triggerIconColor &&
        other.triggerIconHoverColor == triggerIconHoverColor &&
        other.headerTextStyle == headerTextStyle &&
        other.contentTextStyle == contentTextStyle &&
        other.headerPadding == headerPadding &&
        other.contentPadding == contentPadding &&
        other.borderRadius == borderRadius &&
        other.borderWidth == borderWidth &&
        other.triggerIconSize == triggerIconSize &&
        other.animationDuration == animationDuration &&
        other.animationCurve == animationCurve &&
        other.elevation == elevation &&
        other.shadowColor == shadowColor;
  }

  @override
  int get hashCode {
    return Object.hashAll([
      headerBackground,
      headerForeground,
      headerHoverBackground,
      headerHoverForeground,
      contentBackground,
      contentForeground,
      borderColor,
      triggerIconColor,
      triggerIconHoverColor,
      headerTextStyle,
      contentTextStyle,
      headerPadding,
      contentPadding,
      borderRadius,
      borderWidth,
      triggerIconSize,
      animationDuration,
      animationCurve,
      elevation,
      shadowColor,
    ]);
  }

}