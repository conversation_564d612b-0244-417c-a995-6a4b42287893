import 'package:flutter/material.dart';
import '../../constants/shadcn_tokens.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for shadcn Select components.
/// 
/// This extension provides comprehensive theming support for both ShadcnSelect
/// and ShadcnCombobox components, including dropdown styling, search functionality,
/// and keyboard navigation appearance.
/// 
/// The theme follows shadcn design principles while integrating seamlessly
/// with Material Design components and patterns.
class ShadcnSelectTheme extends ShadcnThemeExtension<ShadcnSelectTheme> {
  /// Background color for the select trigger
  final Color? triggerBackground;
  
  /// Text color for the select trigger
  final Color? triggerForeground;
  
  /// Border color for the select trigger
  final Color? triggerBorder;
  
  /// Background color when trigger is hovered
  final Color? triggerHoverBackground;
  
  /// Background color when trigger is focused
  final Color? triggerFocusBackground;
  
  /// Border color when trigger is focused
  final Color? triggerFocusBorder;
  
  /// Background color when trigger is disabled
  final Color? triggerDisabledBackground;
  
  /// Text color when trigger is disabled
  final Color? triggerDisabledForeground;
  
  /// Background color for the dropdown menu
  final Color? dropdownBackground;
  
  /// Shadow color for the dropdown menu
  final Color? dropdownShadow;
  
  /// Border color for the dropdown menu
  final Color? dropdownBorder;
  
  /// Background color for dropdown items
  final Color? itemBackground;
  
  /// Text color for dropdown items
  final Color? itemForeground;
  
  /// Background color for hovered dropdown items
  final Color? itemHoverBackground;
  
  /// Background color for focused dropdown items
  final Color? itemFocusBackground;
  
  /// Background color for selected dropdown items
  final Color? itemSelectedBackground;
  
  /// Text color for selected dropdown items
  final Color? itemSelectedForeground;
  
  /// Background color for disabled dropdown items
  final Color? itemDisabledBackground;
  
  /// Text color for disabled dropdown items
  final Color? itemDisabledForeground;
  
  /// Background color for the search input (Combobox)
  final Color? searchBackground;
  
  /// Text color for the search input (Combobox)
  final Color? searchForeground;
  
  /// Border color for the search input (Combobox)
  final Color? searchBorder;
  
  /// Placeholder text color for the search input (Combobox)
  final Color? searchPlaceholder;
  
  /// Color for the dropdown arrow icon
  final Color? arrowColor;
  
  /// Color for the clear button icon (Combobox)
  final Color? clearButtonColor;
  
  /// Height of the select trigger
  final double? triggerHeight;
  
  /// Width of the select trigger (null for full width)
  final double? triggerWidth;
  
  /// Padding inside the select trigger
  final EdgeInsets? triggerPadding;
  
  /// Border radius for the select trigger
  final BorderRadius? triggerBorderRadius;
  
  /// Border width for the select trigger
  final double? triggerBorderWidth;
  
  /// Maximum height for the dropdown menu
  final double? dropdownMaxHeight;
  
  /// Minimum width for the dropdown menu
  final double? dropdownMinWidth;
  
  /// Padding inside the dropdown menu
  final EdgeInsets? dropdownPadding;
  
  /// Border radius for the dropdown menu
  final BorderRadius? dropdownBorderRadius;
  
  /// Border width for the dropdown menu
  final double? dropdownBorderWidth;
  
  /// Elevation for the dropdown menu
  final double? dropdownElevation;
  
  /// Height for each dropdown item
  final double? itemHeight;
  
  /// Padding for each dropdown item
  final EdgeInsets? itemPadding;
  
  /// Border radius for dropdown items
  final BorderRadius? itemBorderRadius;
  
  /// Height for the search input (Combobox)
  final double? searchHeight;
  
  /// Padding for the search input (Combobox)
  final EdgeInsets? searchPadding;
  
  /// Border radius for the search input (Combobox)
  final BorderRadius? searchBorderRadius;
  
  /// Border width for the search input (Combobox)
  final double? searchBorderWidth;
  
  /// Size of the dropdown arrow icon
  final double? arrowSize;
  
  /// Size of the clear button icon (Combobox)
  final double? clearButtonSize;
  
  /// Text style for the select trigger
  final TextStyle? triggerTextStyle;
  
  /// Text style for dropdown items
  final TextStyle? itemTextStyle;
  
  /// Text style for the search input (Combobox)
  final TextStyle? searchTextStyle;
  
  /// Text style for placeholder text in search input (Combobox)
  final TextStyle? searchPlaceholderStyle;
  
  /// Duration for animations
  final Duration? animationDuration;
  
  /// Animation curve
  final Curve? animationCurve;
  
  /// Whether to show the dropdown arrow
  final bool? showArrow;
  
  /// Whether to show the clear button in Combobox
  final bool? showClearButton;
  
  /// Whether to allow multiple selection
  final bool? allowMultiSelect;
  
  /// Whether search is case sensitive (Combobox)
  final bool? caseSensitiveSearch;
  
  const ShadcnSelectTheme({
    // Trigger colors
    this.triggerBackground,
    this.triggerForeground,
    this.triggerBorder,
    this.triggerHoverBackground,
    this.triggerFocusBackground,
    this.triggerFocusBorder,
    this.triggerDisabledBackground,
    this.triggerDisabledForeground,
    
    // Dropdown colors
    this.dropdownBackground,
    this.dropdownShadow,
    this.dropdownBorder,
    
    // Item colors
    this.itemBackground,
    this.itemForeground,
    this.itemHoverBackground,
    this.itemFocusBackground,
    this.itemSelectedBackground,
    this.itemSelectedForeground,
    this.itemDisabledBackground,
    this.itemDisabledForeground,
    
    // Search colors (Combobox)
    this.searchBackground,
    this.searchForeground,
    this.searchBorder,
    this.searchPlaceholder,
    
    // Icon colors
    this.arrowColor,
    this.clearButtonColor,
    
    // Dimensions
    this.triggerHeight,
    this.triggerWidth,
    this.triggerPadding,
    this.triggerBorderRadius,
    this.triggerBorderWidth,
    
    this.dropdownMaxHeight,
    this.dropdownMinWidth,
    this.dropdownPadding,
    this.dropdownBorderRadius,
    this.dropdownBorderWidth,
    this.dropdownElevation,
    
    this.itemHeight,
    this.itemPadding,
    this.itemBorderRadius,
    
    this.searchHeight,
    this.searchPadding,
    this.searchBorderRadius,
    this.searchBorderWidth,
    
    this.arrowSize,
    this.clearButtonSize,
    
    // Text styles
    this.triggerTextStyle,
    this.itemTextStyle,
    this.searchTextStyle,
    this.searchPlaceholderStyle,
    
    // Animation
    this.animationDuration,
    this.animationCurve,
    
    // Behavior
    this.showArrow,
    this.showClearButton,
    this.allowMultiSelect,
    this.caseSensitiveSearch,
  });
  
  /// Creates a default ShadcnSelectTheme from the provided ColorScheme.
  /// 
  /// This factory uses shadcn design tokens and Material Design principles
  /// to create a theme that works well in both light and dark modes.
  static ShadcnSelectTheme defaultTheme(ColorScheme colorScheme) {
    final isDark = colorScheme.brightness == Brightness.dark;
    
    return ShadcnSelectTheme(
      // Trigger colors
      triggerBackground: colorScheme.surface,
      triggerForeground: colorScheme.onSurface,
      triggerBorder: colorScheme.outline,
      triggerHoverBackground: isDark 
        ? colorScheme.surface.withAlpha(200)
        : colorScheme.surfaceContainerHighest,
      triggerFocusBackground: colorScheme.surface,
      triggerFocusBorder: colorScheme.primary,
      triggerDisabledBackground: colorScheme.surfaceContainerHighest,
      triggerDisabledForeground: colorScheme.onSurface.withAlpha(128),
      
      // Dropdown colors
      dropdownBackground: colorScheme.surfaceContainer,
      dropdownShadow: colorScheme.shadow,
      dropdownBorder: colorScheme.outline,
      
      // Item colors
      itemBackground: Colors.transparent,
      itemForeground: colorScheme.onSurface,
      itemHoverBackground: colorScheme.surfaceContainerHighest,
      itemFocusBackground: colorScheme.surfaceContainerHighest,
      itemSelectedBackground: colorScheme.primary,
      itemSelectedForeground: colorScheme.onPrimary,
      itemDisabledBackground: Colors.transparent,
      itemDisabledForeground: colorScheme.onSurface.withAlpha(128),
      
      // Search colors (Combobox)
      searchBackground: colorScheme.surface,
      searchForeground: colorScheme.onSurface,
      searchBorder: colorScheme.outline,
      searchPlaceholder: colorScheme.onSurface.withAlpha(153),
      
      // Icon colors
      arrowColor: colorScheme.onSurface.withAlpha(179),
      clearButtonColor: colorScheme.onSurface.withAlpha(179),
      
      // Dimensions
      triggerHeight: ShadcnTokens.inputHeightMd,
      triggerWidth: null, // Full width by default
      triggerPadding: ShadcnTokens.paddingHorizontal(ShadcnTokens.spacing3),
      triggerBorderRadius: ShadcnTokens.borderRadius(ShadcnTokens.radiusMd),
      triggerBorderWidth: ShadcnTokens.borderWidth,
      
      dropdownMaxHeight: 300.0,
      dropdownMinWidth: 180.0,
      dropdownPadding: ShadcnTokens.paddingAll(ShadcnTokens.spacing1),
      dropdownBorderRadius: ShadcnTokens.borderRadius(ShadcnTokens.radiusMd),
      dropdownBorderWidth: ShadcnTokens.borderWidth,
      dropdownElevation: ShadcnTokens.elevationMd,
      
      itemHeight: 32.0,
      itemPadding: ShadcnTokens.paddingHorizontal(ShadcnTokens.spacing2),
      itemBorderRadius: ShadcnTokens.borderRadius(ShadcnTokens.radiusSm),
      
      searchHeight: ShadcnTokens.inputHeightSm,
      searchPadding: ShadcnTokens.paddingHorizontal(ShadcnTokens.spacing2),
      searchBorderRadius: ShadcnTokens.borderRadius(ShadcnTokens.radiusSm),
      searchBorderWidth: ShadcnTokens.borderWidth,
      
      arrowSize: ShadcnTokens.iconSizeSm,
      clearButtonSize: ShadcnTokens.iconSizeSm,
      
      // Text styles - will be resolved with Material theme
      triggerTextStyle: null,
      itemTextStyle: null,
      searchTextStyle: null,
      searchPlaceholderStyle: null,
      
      // Animation
      animationDuration: ShadcnTokens.durationFast,
      animationCurve: Curves.easeInOut,
      
      // Behavior
      showArrow: true,
      showClearButton: true,
      allowMultiSelect: false,
      caseSensitiveSearch: false,
    );
  }
  
  @override
  ShadcnSelectTheme copyWith({
    // Trigger colors
    Color? triggerBackground,
    Color? triggerForeground,
    Color? triggerBorder,
    Color? triggerHoverBackground,
    Color? triggerFocusBackground,
    Color? triggerFocusBorder,
    Color? triggerDisabledBackground,
    Color? triggerDisabledForeground,
    
    // Dropdown colors
    Color? dropdownBackground,
    Color? dropdownShadow,
    Color? dropdownBorder,
    
    // Item colors
    Color? itemBackground,
    Color? itemForeground,
    Color? itemHoverBackground,
    Color? itemFocusBackground,
    Color? itemSelectedBackground,
    Color? itemSelectedForeground,
    Color? itemDisabledBackground,
    Color? itemDisabledForeground,
    
    // Search colors (Combobox)
    Color? searchBackground,
    Color? searchForeground,
    Color? searchBorder,
    Color? searchPlaceholder,
    
    // Icon colors
    Color? arrowColor,
    Color? clearButtonColor,
    
    // Dimensions
    double? triggerHeight,
    double? triggerWidth,
    EdgeInsets? triggerPadding,
    BorderRadius? triggerBorderRadius,
    double? triggerBorderWidth,
    
    double? dropdownMaxHeight,
    double? dropdownMinWidth,
    EdgeInsets? dropdownPadding,
    BorderRadius? dropdownBorderRadius,
    double? dropdownBorderWidth,
    double? dropdownElevation,
    
    double? itemHeight,
    EdgeInsets? itemPadding,
    BorderRadius? itemBorderRadius,
    
    double? searchHeight,
    EdgeInsets? searchPadding,
    BorderRadius? searchBorderRadius,
    double? searchBorderWidth,
    
    double? arrowSize,
    double? clearButtonSize,
    
    // Text styles
    TextStyle? triggerTextStyle,
    TextStyle? itemTextStyle,
    TextStyle? searchTextStyle,
    TextStyle? searchPlaceholderStyle,
    
    // Animation
    Duration? animationDuration,
    Curve? animationCurve,
    
    // Behavior
    bool? showArrow,
    bool? showClearButton,
    bool? allowMultiSelect,
    bool? caseSensitiveSearch,
  }) {
    return ShadcnSelectTheme(
      // Trigger colors
      triggerBackground: triggerBackground ?? this.triggerBackground,
      triggerForeground: triggerForeground ?? this.triggerForeground,
      triggerBorder: triggerBorder ?? this.triggerBorder,
      triggerHoverBackground: triggerHoverBackground ?? this.triggerHoverBackground,
      triggerFocusBackground: triggerFocusBackground ?? this.triggerFocusBackground,
      triggerFocusBorder: triggerFocusBorder ?? this.triggerFocusBorder,
      triggerDisabledBackground: triggerDisabledBackground ?? this.triggerDisabledBackground,
      triggerDisabledForeground: triggerDisabledForeground ?? this.triggerDisabledForeground,
      
      // Dropdown colors
      dropdownBackground: dropdownBackground ?? this.dropdownBackground,
      dropdownShadow: dropdownShadow ?? this.dropdownShadow,
      dropdownBorder: dropdownBorder ?? this.dropdownBorder,
      
      // Item colors
      itemBackground: itemBackground ?? this.itemBackground,
      itemForeground: itemForeground ?? this.itemForeground,
      itemHoverBackground: itemHoverBackground ?? this.itemHoverBackground,
      itemFocusBackground: itemFocusBackground ?? this.itemFocusBackground,
      itemSelectedBackground: itemSelectedBackground ?? this.itemSelectedBackground,
      itemSelectedForeground: itemSelectedForeground ?? this.itemSelectedForeground,
      itemDisabledBackground: itemDisabledBackground ?? this.itemDisabledBackground,
      itemDisabledForeground: itemDisabledForeground ?? this.itemDisabledForeground,
      
      // Search colors (Combobox)
      searchBackground: searchBackground ?? this.searchBackground,
      searchForeground: searchForeground ?? this.searchForeground,
      searchBorder: searchBorder ?? this.searchBorder,
      searchPlaceholder: searchPlaceholder ?? this.searchPlaceholder,
      
      // Icon colors
      arrowColor: arrowColor ?? this.arrowColor,
      clearButtonColor: clearButtonColor ?? this.clearButtonColor,
      
      // Dimensions
      triggerHeight: triggerHeight ?? this.triggerHeight,
      triggerWidth: triggerWidth ?? this.triggerWidth,
      triggerPadding: triggerPadding ?? this.triggerPadding,
      triggerBorderRadius: triggerBorderRadius ?? this.triggerBorderRadius,
      triggerBorderWidth: triggerBorderWidth ?? this.triggerBorderWidth,
      
      dropdownMaxHeight: dropdownMaxHeight ?? this.dropdownMaxHeight,
      dropdownMinWidth: dropdownMinWidth ?? this.dropdownMinWidth,
      dropdownPadding: dropdownPadding ?? this.dropdownPadding,
      dropdownBorderRadius: dropdownBorderRadius ?? this.dropdownBorderRadius,
      dropdownBorderWidth: dropdownBorderWidth ?? this.dropdownBorderWidth,
      dropdownElevation: dropdownElevation ?? this.dropdownElevation,
      
      itemHeight: itemHeight ?? this.itemHeight,
      itemPadding: itemPadding ?? this.itemPadding,
      itemBorderRadius: itemBorderRadius ?? this.itemBorderRadius,
      
      searchHeight: searchHeight ?? this.searchHeight,
      searchPadding: searchPadding ?? this.searchPadding,
      searchBorderRadius: searchBorderRadius ?? this.searchBorderRadius,
      searchBorderWidth: searchBorderWidth ?? this.searchBorderWidth,
      
      arrowSize: arrowSize ?? this.arrowSize,
      clearButtonSize: clearButtonSize ?? this.clearButtonSize,
      
      // Text styles
      triggerTextStyle: triggerTextStyle ?? this.triggerTextStyle,
      itemTextStyle: itemTextStyle ?? this.itemTextStyle,
      searchTextStyle: searchTextStyle ?? this.searchTextStyle,
      searchPlaceholderStyle: searchPlaceholderStyle ?? this.searchPlaceholderStyle,
      
      // Animation
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
      
      // Behavior
      showArrow: showArrow ?? this.showArrow,
      showClearButton: showClearButton ?? this.showClearButton,
      allowMultiSelect: allowMultiSelect ?? this.allowMultiSelect,
      caseSensitiveSearch: caseSensitiveSearch ?? this.caseSensitiveSearch,
    );
  }
  
  @override
  ShadcnSelectTheme lerp(ShadcnSelectTheme? other, double t) {
    if (other is! ShadcnSelectTheme) return this;
    
    return ShadcnSelectTheme(
      // Trigger colors
      triggerBackground: Color.lerp(triggerBackground, other.triggerBackground, t),
      triggerForeground: Color.lerp(triggerForeground, other.triggerForeground, t),
      triggerBorder: Color.lerp(triggerBorder, other.triggerBorder, t),
      triggerHoverBackground: Color.lerp(triggerHoverBackground, other.triggerHoverBackground, t),
      triggerFocusBackground: Color.lerp(triggerFocusBackground, other.triggerFocusBackground, t),
      triggerFocusBorder: Color.lerp(triggerFocusBorder, other.triggerFocusBorder, t),
      triggerDisabledBackground: Color.lerp(triggerDisabledBackground, other.triggerDisabledBackground, t),
      triggerDisabledForeground: Color.lerp(triggerDisabledForeground, other.triggerDisabledForeground, t),
      
      // Dropdown colors
      dropdownBackground: Color.lerp(dropdownBackground, other.dropdownBackground, t),
      dropdownShadow: Color.lerp(dropdownShadow, other.dropdownShadow, t),
      dropdownBorder: Color.lerp(dropdownBorder, other.dropdownBorder, t),
      
      // Item colors
      itemBackground: Color.lerp(itemBackground, other.itemBackground, t),
      itemForeground: Color.lerp(itemForeground, other.itemForeground, t),
      itemHoverBackground: Color.lerp(itemHoverBackground, other.itemHoverBackground, t),
      itemFocusBackground: Color.lerp(itemFocusBackground, other.itemFocusBackground, t),
      itemSelectedBackground: Color.lerp(itemSelectedBackground, other.itemSelectedBackground, t),
      itemSelectedForeground: Color.lerp(itemSelectedForeground, other.itemSelectedForeground, t),
      itemDisabledBackground: Color.lerp(itemDisabledBackground, other.itemDisabledBackground, t),
      itemDisabledForeground: Color.lerp(itemDisabledForeground, other.itemDisabledForeground, t),
      
      // Search colors (Combobox)
      searchBackground: Color.lerp(searchBackground, other.searchBackground, t),
      searchForeground: Color.lerp(searchForeground, other.searchForeground, t),
      searchBorder: Color.lerp(searchBorder, other.searchBorder, t),
      searchPlaceholder: Color.lerp(searchPlaceholder, other.searchPlaceholder, t),
      
      // Icon colors
      arrowColor: Color.lerp(arrowColor, other.arrowColor, t),
      clearButtonColor: Color.lerp(clearButtonColor, other.clearButtonColor, t),
      
      // Dimensions - lerp numeric values
      triggerHeight: t < 0.5 ? triggerHeight : other.triggerHeight,
      triggerWidth: t < 0.5 ? triggerWidth : other.triggerWidth,
      triggerPadding: EdgeInsets.lerp(triggerPadding, other.triggerPadding, t),
      triggerBorderRadius: BorderRadius.lerp(triggerBorderRadius, other.triggerBorderRadius, t),
      triggerBorderWidth: t < 0.5 ? triggerBorderWidth : other.triggerBorderWidth,
      
      dropdownMaxHeight: t < 0.5 ? dropdownMaxHeight : other.dropdownMaxHeight,
      dropdownMinWidth: t < 0.5 ? dropdownMinWidth : other.dropdownMinWidth,
      dropdownPadding: EdgeInsets.lerp(dropdownPadding, other.dropdownPadding, t),
      dropdownBorderRadius: BorderRadius.lerp(dropdownBorderRadius, other.dropdownBorderRadius, t),
      dropdownBorderWidth: t < 0.5 ? dropdownBorderWidth : other.dropdownBorderWidth,
      dropdownElevation: t < 0.5 ? dropdownElevation : other.dropdownElevation,
      
      itemHeight: t < 0.5 ? itemHeight : other.itemHeight,
      itemPadding: EdgeInsets.lerp(itemPadding, other.itemPadding, t),
      itemBorderRadius: BorderRadius.lerp(itemBorderRadius, other.itemBorderRadius, t),
      
      searchHeight: t < 0.5 ? searchHeight : other.searchHeight,
      searchPadding: EdgeInsets.lerp(searchPadding, other.searchPadding, t),
      searchBorderRadius: BorderRadius.lerp(searchBorderRadius, other.searchBorderRadius, t),
      searchBorderWidth: t < 0.5 ? searchBorderWidth : other.searchBorderWidth,
      
      arrowSize: t < 0.5 ? arrowSize : other.arrowSize,
      clearButtonSize: t < 0.5 ? clearButtonSize : other.clearButtonSize,
      
      // Text styles
      triggerTextStyle: TextStyle.lerp(triggerTextStyle, other.triggerTextStyle, t),
      itemTextStyle: TextStyle.lerp(itemTextStyle, other.itemTextStyle, t),
      searchTextStyle: TextStyle.lerp(searchTextStyle, other.searchTextStyle, t),
      searchPlaceholderStyle: TextStyle.lerp(searchPlaceholderStyle, other.searchPlaceholderStyle, t),
      
      // Animation
      animationDuration: t < 0.5 ? animationDuration : other.animationDuration,
      animationCurve: t < 0.5 ? animationCurve : other.animationCurve,
      
      // Behavior - use threshold for boolean values
      showArrow: t < 0.5 ? showArrow : other.showArrow,
      showClearButton: t < 0.5 ? showClearButton : other.showClearButton,
      allowMultiSelect: t < 0.5 ? allowMultiSelect : other.allowMultiSelect,
      caseSensitiveSearch: t < 0.5 ? caseSensitiveSearch : other.caseSensitiveSearch,
    );
  }
  
  @override
  bool validate({bool throwOnError = false}) {
    try {
      // Validate that essential properties have reasonable values
      assert(
        triggerHeight == null || (triggerHeight! > 0 && triggerHeight! <= 200),
        'triggerHeight must be positive and reasonable (0-200px)',
      );
      
      assert(
        dropdownMaxHeight == null || (dropdownMaxHeight! > 0 && dropdownMaxHeight! <= 1000),
        'dropdownMaxHeight must be positive and reasonable (0-1000px)',
      );
      
      assert(
        itemHeight == null || (itemHeight! > 0 && itemHeight! <= 100),
        'itemHeight must be positive and reasonable (0-100px)',
      );
      
      assert(
        arrowSize == null || (arrowSize! > 0 && arrowSize! <= 50),
        'arrowSize must be positive and reasonable (0-50px)',
      );
      
      return true;
    } catch (e) {
      if (throwOnError) {
        throw FlutterError('ShadcnSelectTheme validation failed: $e');
      }
      return false;
    }
  }
  
  /// Helper method to resolve trigger text style with theme context
  TextStyle resolveTriggerTextStyle(BuildContext context) {
    return resolveTextStyle(
      context,
      triggerTextStyle,
      (textTheme) => textTheme.bodyMedium!,
    );
  }
  
  /// Helper method to resolve item text style with theme context
  TextStyle resolveItemTextStyle(BuildContext context) {
    return resolveTextStyle(
      context,
      itemTextStyle,
      (textTheme) => textTheme.bodyMedium!,
    );
  }
  
  /// Helper method to resolve search text style with theme context
  TextStyle resolveSearchTextStyle(BuildContext context) {
    return resolveTextStyle(
      context,
      searchTextStyle,
      (textTheme) => textTheme.bodyMedium!,
    );
  }
  
  /// Helper method to resolve search placeholder style with theme context
  TextStyle resolveSearchPlaceholderStyle(BuildContext context) {
    final baseStyle = resolveSearchTextStyle(context);
    final materialPlaceholder = baseStyle.copyWith(
      color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
    );
    
    return searchPlaceholderStyle?.merge(materialPlaceholder) ?? materialPlaceholder;
  }
}