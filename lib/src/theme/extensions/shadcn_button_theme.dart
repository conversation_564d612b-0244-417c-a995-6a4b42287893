import 'package:flutter/material.dart';
import '../../constants/shadcn_tokens.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for the ShadcnButton component.
/// 
/// This theme extension provides all styling properties for button variants,
/// sizes, and interactive states. It follows shadcn design principles while
/// integrating with Material Design theming patterns.
/// 
/// All properties are nullable to support fallback to Material theme values
/// and provide flexible customization options for developers.
class ShadcnButtonTheme extends ShadcnThemeExtension<ShadcnButtonTheme> {
  // Primary variant colors
  final Color? primaryBackground;
  final Color? primaryForeground;
  final Color? primaryBorder;
  final Color? primaryHover;
  final Color? primaryPressed;
  final Color? primaryFocused;
  
  // Secondary variant colors
  final Color? secondaryBackground;
  final Color? secondaryForeground;
  final Color? secondaryBorder;
  final Color? secondaryHover;
  final Color? secondaryPressed;
  final Color? secondaryFocused;
  
  // Destructive variant colors
  final Color? destructiveBackground;
  final Color? destructiveForeground;
  final Color? destructiveBorder;
  final Color? destructiveHover;
  final Color? destructivePressed;
  final Color? destructiveFocused;
  
  // Outline variant colors
  final Color? outlineBackground;
  final Color? outlineForeground;
  final Color? outlineBorder;
  final Color? outlineHover;
  final Color? outlinePressed;
  final Color? outlineFocused;
  
  // Ghost variant colors
  final Color? ghostBackground;
  final Color? ghostForeground;
  final Color? ghostBorder;
  final Color? ghostHover;
  final Color? ghostPressed;
  final Color? ghostFocused;
  
  // Link variant colors
  final Color? linkBackground;
  final Color? linkForeground;
  final Color? linkBorder;
  final Color? linkHover;
  final Color? linkPressed;
  final Color? linkFocused;
  
  // Disabled state colors
  final Color? disabledBackground;
  final Color? disabledForeground;
  final Color? disabledBorder;
  
  // Size properties
  final double? smallHeight;
  final double? mediumHeight;
  final double? largeHeight;
  
  final EdgeInsets? smallPadding;
  final EdgeInsets? mediumPadding;
  final EdgeInsets? largePadding;
  
  final double? smallFontSize;
  final double? mediumFontSize;
  final double? largeFontSize;
  
  final double? smallIconSize;
  final double? mediumIconSize;
  final double? largeIconSize;
  
  // Common properties
  final BorderRadius? borderRadius;
  final double? borderWidth;
  final TextStyle? textStyle;
  final FontWeight? fontWeight;
  final double? iconSpacing;
  final double? minWidth;
  final MainAxisSize? mainAxisSize;
  final MainAxisAlignment? mainAxisAlignment;
  final CrossAxisAlignment? crossAxisAlignment;
  
  // Animation properties
  final Duration? animationDuration;
  final Curve? animationCurve;
  
  // Focus properties
  final Color? focusColor;
  final double? focusWidth;
  final double? focusOffset;
  
  const ShadcnButtonTheme({
    // Primary variant
    this.primaryBackground,
    this.primaryForeground,
    this.primaryBorder,
    this.primaryHover,
    this.primaryPressed,
    this.primaryFocused,
    
    // Secondary variant
    this.secondaryBackground,
    this.secondaryForeground,
    this.secondaryBorder,
    this.secondaryHover,
    this.secondaryPressed,
    this.secondaryFocused,
    
    // Destructive variant
    this.destructiveBackground,
    this.destructiveForeground,
    this.destructiveBorder,
    this.destructiveHover,
    this.destructivePressed,
    this.destructiveFocused,
    
    // Outline variant
    this.outlineBackground,
    this.outlineForeground,
    this.outlineBorder,
    this.outlineHover,
    this.outlinePressed,
    this.outlineFocused,
    
    // Ghost variant
    this.ghostBackground,
    this.ghostForeground,
    this.ghostBorder,
    this.ghostHover,
    this.ghostPressed,
    this.ghostFocused,
    
    // Link variant
    this.linkBackground,
    this.linkForeground,
    this.linkBorder,
    this.linkHover,
    this.linkPressed,
    this.linkFocused,
    
    // Disabled state
    this.disabledBackground,
    this.disabledForeground,
    this.disabledBorder,
    
    // Sizes
    this.smallHeight,
    this.mediumHeight,
    this.largeHeight,
    this.smallPadding,
    this.mediumPadding,
    this.largePadding,
    this.smallFontSize,
    this.mediumFontSize,
    this.largeFontSize,
    this.smallIconSize,
    this.mediumIconSize,
    this.largeIconSize,
    
    // Common properties
    this.borderRadius,
    this.borderWidth,
    this.textStyle,
    this.fontWeight,
    this.iconSpacing,
    this.minWidth,
    this.mainAxisSize,
    this.mainAxisAlignment,
    this.crossAxisAlignment,
    
    // Animation
    this.animationDuration,
    this.animationCurve,
    
    // Focus
    this.focusColor,
    this.focusWidth,
    this.focusOffset,
  });

  /// Creates a default button theme based on the provided ColorScheme.
  /// 
  /// This factory method generates a complete button theme that follows
  /// shadcn design principles while integrating with Material Design colors.
  /// All properties are set to shadcn-standard values with proper fallbacks.
  static ShadcnButtonTheme defaultTheme(ColorScheme colorScheme) {
    final isDark = colorScheme.brightness == Brightness.dark;
    
    return ShadcnButtonTheme(
      // Primary variant - uses Material primary colors
      primaryBackground: colorScheme.primary,
      primaryForeground: colorScheme.onPrimary,
      primaryBorder: colorScheme.primary,
      primaryHover: isDark 
          ? colorScheme.primary.withOpacity(0.9)
          : colorScheme.primary.withOpacity(0.8),
      primaryPressed: isDark
          ? colorScheme.primary.withOpacity(0.8)
          : colorScheme.primary.withOpacity(0.9),
      primaryFocused: colorScheme.primary.withOpacity(0.12),
      
      // Secondary variant - uses Material secondary colors
      secondaryBackground: colorScheme.secondary,
      secondaryForeground: colorScheme.onSecondary,
      secondaryBorder: colorScheme.secondary,
      secondaryHover: isDark
          ? colorScheme.secondary.withOpacity(0.9)
          : colorScheme.secondary.withOpacity(0.8),
      secondaryPressed: isDark
          ? colorScheme.secondary.withOpacity(0.8)
          : colorScheme.secondary.withOpacity(0.9),
      secondaryFocused: colorScheme.secondary.withOpacity(0.12),
      
      // Destructive variant - uses Material error colors
      destructiveBackground: colorScheme.error,
      destructiveForeground: colorScheme.onError,
      destructiveBorder: colorScheme.error,
      destructiveHover: isDark
          ? colorScheme.error.withOpacity(0.9)
          : colorScheme.error.withOpacity(0.8),
      destructivePressed: isDark
          ? colorScheme.error.withOpacity(0.8)
          : colorScheme.error.withOpacity(0.9),
      destructiveFocused: colorScheme.error.withOpacity(0.12),
      
      // Outline variant - transparent background with borders
      outlineBackground: Colors.transparent,
      outlineForeground: colorScheme.onSurface,
      outlineBorder: colorScheme.outline,
      outlineHover: colorScheme.onSurface.withOpacity(0.04),
      outlinePressed: colorScheme.onSurface.withOpacity(0.08),
      outlineFocused: colorScheme.primary.withOpacity(0.12),
      
      // Ghost variant - minimal styling with hover effects
      ghostBackground: Colors.transparent,
      ghostForeground: colorScheme.onSurface,
      ghostBorder: Colors.transparent,
      ghostHover: colorScheme.onSurface.withOpacity(0.04),
      ghostPressed: colorScheme.onSurface.withOpacity(0.08),
      ghostFocused: colorScheme.primary.withOpacity(0.12),
      
      // Link variant - text-like appearance
      linkBackground: Colors.transparent,
      linkForeground: colorScheme.primary,
      linkBorder: Colors.transparent,
      linkHover: colorScheme.primary.withOpacity(0.8),
      linkPressed: colorScheme.primary.withOpacity(0.6),
      linkFocused: colorScheme.primary.withOpacity(0.12),
      
      // Disabled state
      disabledBackground: colorScheme.onSurface.withOpacity(0.12),
      disabledForeground: colorScheme.onSurface.withOpacity(0.38),
      disabledBorder: colorScheme.onSurface.withOpacity(0.12),
      
      // Size configurations - shadcn standard dimensions
      smallHeight: ShadcnTokens.buttonHeightSm,
      mediumHeight: ShadcnTokens.buttonHeightMd,
      largeHeight: ShadcnTokens.buttonHeightLg,
      
      smallPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      mediumPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      largePadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      
      smallFontSize: ShadcnTokens.fontSizeSm,
      mediumFontSize: ShadcnTokens.fontSizeMd,
      largeFontSize: ShadcnTokens.fontSizeLg,
      
      smallIconSize: ShadcnTokens.iconSizeSm,
      mediumIconSize: ShadcnTokens.iconSizeMd,
      largeIconSize: ShadcnTokens.iconSizeLg,
      
      // Common properties
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusMd),
      borderWidth: ShadcnTokens.borderWidth,
      fontWeight: ShadcnTokens.fontWeightMedium,
      iconSpacing: ShadcnTokens.spacing2,
      minWidth: 64,
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      
      // Animation
      animationDuration: ShadcnTokens.durationFast,
      animationCurve: Curves.easeInOut,
      
      // Focus
      focusColor: colorScheme.primary.withOpacity(0.12),
      focusWidth: 2.0,
      focusOffset: 2.0,
    );
  }

  @override
  ShadcnButtonTheme copyWith({
    // Primary variant
    Color? primaryBackground,
    Color? primaryForeground,
    Color? primaryBorder,
    Color? primaryHover,
    Color? primaryPressed,
    Color? primaryFocused,
    
    // Secondary variant
    Color? secondaryBackground,
    Color? secondaryForeground,
    Color? secondaryBorder,
    Color? secondaryHover,
    Color? secondaryPressed,
    Color? secondaryFocused,
    
    // Destructive variant
    Color? destructiveBackground,
    Color? destructiveForeground,
    Color? destructiveBorder,
    Color? destructiveHover,
    Color? destructivePressed,
    Color? destructiveFocused,
    
    // Outline variant
    Color? outlineBackground,
    Color? outlineForeground,
    Color? outlineBorder,
    Color? outlineHover,
    Color? outlinePressed,
    Color? outlineFocused,
    
    // Ghost variant
    Color? ghostBackground,
    Color? ghostForeground,
    Color? ghostBorder,
    Color? ghostHover,
    Color? ghostPressed,
    Color? ghostFocused,
    
    // Link variant
    Color? linkBackground,
    Color? linkForeground,
    Color? linkBorder,
    Color? linkHover,
    Color? linkPressed,
    Color? linkFocused,
    
    // Disabled state
    Color? disabledBackground,
    Color? disabledForeground,
    Color? disabledBorder,
    
    // Sizes
    double? smallHeight,
    double? mediumHeight,
    double? largeHeight,
    EdgeInsets? smallPadding,
    EdgeInsets? mediumPadding,
    EdgeInsets? largePadding,
    double? smallFontSize,
    double? mediumFontSize,
    double? largeFontSize,
    double? smallIconSize,
    double? mediumIconSize,
    double? largeIconSize,
    
    // Common properties
    BorderRadius? borderRadius,
    double? borderWidth,
    TextStyle? textStyle,
    FontWeight? fontWeight,
    double? iconSpacing,
    double? minWidth,
    MainAxisSize? mainAxisSize,
    MainAxisAlignment? mainAxisAlignment,
    CrossAxisAlignment? crossAxisAlignment,
    
    // Animation
    Duration? animationDuration,
    Curve? animationCurve,
    
    // Focus
    Color? focusColor,
    double? focusWidth,
    double? focusOffset,
  }) {
    return ShadcnButtonTheme(
      // Primary variant
      primaryBackground: primaryBackground ?? this.primaryBackground,
      primaryForeground: primaryForeground ?? this.primaryForeground,
      primaryBorder: primaryBorder ?? this.primaryBorder,
      primaryHover: primaryHover ?? this.primaryHover,
      primaryPressed: primaryPressed ?? this.primaryPressed,
      primaryFocused: primaryFocused ?? this.primaryFocused,
      
      // Secondary variant
      secondaryBackground: secondaryBackground ?? this.secondaryBackground,
      secondaryForeground: secondaryForeground ?? this.secondaryForeground,
      secondaryBorder: secondaryBorder ?? this.secondaryBorder,
      secondaryHover: secondaryHover ?? this.secondaryHover,
      secondaryPressed: secondaryPressed ?? this.secondaryPressed,
      secondaryFocused: secondaryFocused ?? this.secondaryFocused,
      
      // Destructive variant
      destructiveBackground: destructiveBackground ?? this.destructiveBackground,
      destructiveForeground: destructiveForeground ?? this.destructiveForeground,
      destructiveBorder: destructiveBorder ?? this.destructiveBorder,
      destructiveHover: destructiveHover ?? this.destructiveHover,
      destructivePressed: destructivePressed ?? this.destructivePressed,
      destructiveFocused: destructiveFocused ?? this.destructiveFocused,
      
      // Outline variant
      outlineBackground: outlineBackground ?? this.outlineBackground,
      outlineForeground: outlineForeground ?? this.outlineForeground,
      outlineBorder: outlineBorder ?? this.outlineBorder,
      outlineHover: outlineHover ?? this.outlineHover,
      outlinePressed: outlinePressed ?? this.outlinePressed,
      outlineFocused: outlineFocused ?? this.outlineFocused,
      
      // Ghost variant
      ghostBackground: ghostBackground ?? this.ghostBackground,
      ghostForeground: ghostForeground ?? this.ghostForeground,
      ghostBorder: ghostBorder ?? this.ghostBorder,
      ghostHover: ghostHover ?? this.ghostHover,
      ghostPressed: ghostPressed ?? this.ghostPressed,
      ghostFocused: ghostFocused ?? this.ghostFocused,
      
      // Link variant
      linkBackground: linkBackground ?? this.linkBackground,
      linkForeground: linkForeground ?? this.linkForeground,
      linkBorder: linkBorder ?? this.linkBorder,
      linkHover: linkHover ?? this.linkHover,
      linkPressed: linkPressed ?? this.linkPressed,
      linkFocused: linkFocused ?? this.linkFocused,
      
      // Disabled state
      disabledBackground: disabledBackground ?? this.disabledBackground,
      disabledForeground: disabledForeground ?? this.disabledForeground,
      disabledBorder: disabledBorder ?? this.disabledBorder,
      
      // Sizes
      smallHeight: smallHeight ?? this.smallHeight,
      mediumHeight: mediumHeight ?? this.mediumHeight,
      largeHeight: largeHeight ?? this.largeHeight,
      smallPadding: smallPadding ?? this.smallPadding,
      mediumPadding: mediumPadding ?? this.mediumPadding,
      largePadding: largePadding ?? this.largePadding,
      smallFontSize: smallFontSize ?? this.smallFontSize,
      mediumFontSize: mediumFontSize ?? this.mediumFontSize,
      largeFontSize: largeFontSize ?? this.largeFontSize,
      smallIconSize: smallIconSize ?? this.smallIconSize,
      mediumIconSize: mediumIconSize ?? this.mediumIconSize,
      largeIconSize: largeIconSize ?? this.largeIconSize,
      
      // Common properties
      borderRadius: borderRadius ?? this.borderRadius,
      borderWidth: borderWidth ?? this.borderWidth,
      textStyle: textStyle ?? this.textStyle,
      fontWeight: fontWeight ?? this.fontWeight,
      iconSpacing: iconSpacing ?? this.iconSpacing,
      minWidth: minWidth ?? this.minWidth,
      mainAxisSize: mainAxisSize ?? this.mainAxisSize,
      mainAxisAlignment: mainAxisAlignment ?? this.mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment ?? this.crossAxisAlignment,
      
      // Animation
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
      
      // Focus
      focusColor: focusColor ?? this.focusColor,
      focusWidth: focusWidth ?? this.focusWidth,
      focusOffset: focusOffset ?? this.focusOffset,
    );
  }

  @override
  ShadcnButtonTheme lerp(ThemeExtension<ShadcnButtonTheme>? other, double t) {
    if (other is! ShadcnButtonTheme) {
      return this;
    }
    
    return ShadcnButtonTheme(
      // Primary variant
      primaryBackground: Color.lerp(primaryBackground, other.primaryBackground, t),
      primaryForeground: Color.lerp(primaryForeground, other.primaryForeground, t),
      primaryBorder: Color.lerp(primaryBorder, other.primaryBorder, t),
      primaryHover: Color.lerp(primaryHover, other.primaryHover, t),
      primaryPressed: Color.lerp(primaryPressed, other.primaryPressed, t),
      primaryFocused: Color.lerp(primaryFocused, other.primaryFocused, t),
      
      // Secondary variant
      secondaryBackground: Color.lerp(secondaryBackground, other.secondaryBackground, t),
      secondaryForeground: Color.lerp(secondaryForeground, other.secondaryForeground, t),
      secondaryBorder: Color.lerp(secondaryBorder, other.secondaryBorder, t),
      secondaryHover: Color.lerp(secondaryHover, other.secondaryHover, t),
      secondaryPressed: Color.lerp(secondaryPressed, other.secondaryPressed, t),
      secondaryFocused: Color.lerp(secondaryFocused, other.secondaryFocused, t),
      
      // Destructive variant
      destructiveBackground: Color.lerp(destructiveBackground, other.destructiveBackground, t),
      destructiveForeground: Color.lerp(destructiveForeground, other.destructiveForeground, t),
      destructiveBorder: Color.lerp(destructiveBorder, other.destructiveBorder, t),
      destructiveHover: Color.lerp(destructiveHover, other.destructiveHover, t),
      destructivePressed: Color.lerp(destructivePressed, other.destructivePressed, t),
      destructiveFocused: Color.lerp(destructiveFocused, other.destructiveFocused, t),
      
      // Outline variant
      outlineBackground: Color.lerp(outlineBackground, other.outlineBackground, t),
      outlineForeground: Color.lerp(outlineForeground, other.outlineForeground, t),
      outlineBorder: Color.lerp(outlineBorder, other.outlineBorder, t),
      outlineHover: Color.lerp(outlineHover, other.outlineHover, t),
      outlinePressed: Color.lerp(outlinePressed, other.outlinePressed, t),
      outlineFocused: Color.lerp(outlineFocused, other.outlineFocused, t),
      
      // Ghost variant
      ghostBackground: Color.lerp(ghostBackground, other.ghostBackground, t),
      ghostForeground: Color.lerp(ghostForeground, other.ghostForeground, t),
      ghostBorder: Color.lerp(ghostBorder, other.ghostBorder, t),
      ghostHover: Color.lerp(ghostHover, other.ghostHover, t),
      ghostPressed: Color.lerp(ghostPressed, other.ghostPressed, t),
      ghostFocused: Color.lerp(ghostFocused, other.ghostFocused, t),
      
      // Link variant
      linkBackground: Color.lerp(linkBackground, other.linkBackground, t),
      linkForeground: Color.lerp(linkForeground, other.linkForeground, t),
      linkBorder: Color.lerp(linkBorder, other.linkBorder, t),
      linkHover: Color.lerp(linkHover, other.linkHover, t),
      linkPressed: Color.lerp(linkPressed, other.linkPressed, t),
      linkFocused: Color.lerp(linkFocused, other.linkFocused, t),
      
      // Disabled state
      disabledBackground: Color.lerp(disabledBackground, other.disabledBackground, t),
      disabledForeground: Color.lerp(disabledForeground, other.disabledForeground, t),
      disabledBorder: Color.lerp(disabledBorder, other.disabledBorder, t),
      
      // Sizes - lerp numeric values
      smallHeight: t < 0.5 ? smallHeight : other.smallHeight,
      mediumHeight: t < 0.5 ? mediumHeight : other.mediumHeight,
      largeHeight: t < 0.5 ? largeHeight : other.largeHeight,
      smallPadding: EdgeInsets.lerp(smallPadding, other.smallPadding, t),
      mediumPadding: EdgeInsets.lerp(mediumPadding, other.mediumPadding, t),
      largePadding: EdgeInsets.lerp(largePadding, other.largePadding, t),
      smallFontSize: t < 0.5 ? smallFontSize : other.smallFontSize,
      mediumFontSize: t < 0.5 ? mediumFontSize : other.mediumFontSize,
      largeFontSize: t < 0.5 ? largeFontSize : other.largeFontSize,
      smallIconSize: t < 0.5 ? smallIconSize : other.smallIconSize,
      mediumIconSize: t < 0.5 ? mediumIconSize : other.mediumIconSize,
      largeIconSize: t < 0.5 ? largeIconSize : other.largeIconSize,
      
      // Common properties
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      borderWidth: t < 0.5 ? borderWidth : other.borderWidth,
      textStyle: TextStyle.lerp(textStyle, other.textStyle, t),
      fontWeight: t < 0.5 ? fontWeight : other.fontWeight,
      iconSpacing: t < 0.5 ? iconSpacing : other.iconSpacing,
      minWidth: t < 0.5 ? minWidth : other.minWidth,
      mainAxisSize: t < 0.5 ? mainAxisSize : other.mainAxisSize,
      mainAxisAlignment: t < 0.5 ? mainAxisAlignment : other.mainAxisAlignment,
      crossAxisAlignment: t < 0.5 ? crossAxisAlignment : other.crossAxisAlignment,
      
      // Animation
      animationDuration: t < 0.5 ? animationDuration : other.animationDuration,
      animationCurve: t < 0.5 ? animationCurve : other.animationCurve,
      
      // Focus
      focusColor: Color.lerp(focusColor, other.focusColor, t),
      focusWidth: t < 0.5 ? focusWidth : other.focusWidth,
      focusOffset: t < 0.5 ? focusOffset : other.focusOffset,
    );
  }

  @override
  bool validate({bool throwOnError = false}) {
    try {
      // Validate that required color properties have non-null values
      final requiredColors = {
        'primaryBackground': primaryBackground,
        'primaryForeground': primaryForeground,
        'disabledBackground': disabledBackground,
        'disabledForeground': disabledForeground,
      };
      
      for (final entry in requiredColors.entries) {
        if (entry.value == null) {
          final error = 'ShadcnButtonTheme: ${entry.key} cannot be null';
          if (throwOnError) {
            throw FlutterError(error);
          }
          debugPrint('Warning: $error');
          return false;
        }
      }
      
      // Validate size properties
      final sizes = [smallHeight, mediumHeight, largeHeight];
      for (final size in sizes) {
        if (size != null && size <= 0) {
          final error = 'ShadcnButtonTheme: Height values must be positive';
          if (throwOnError) {
            throw FlutterError(error);
          }
          debugPrint('Warning: $error');
          return false;
        }
      }
      
      // Validate border width
      if (borderWidth != null && borderWidth! < 0) {
        final error = 'ShadcnButtonTheme: borderWidth cannot be negative';
        if (throwOnError) {
          throw FlutterError(error);
        }
        debugPrint('Warning: $error');
        return false;
      }
      
      return true;
    } catch (e) {
      if (throwOnError) {
        rethrow;
      }
      debugPrint('ShadcnButtonTheme validation error: $e');
      return false;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other.runtimeType != runtimeType) return false;
    
    return other is ShadcnButtonTheme &&
        // Primary variant
        other.primaryBackground == primaryBackground &&
        other.primaryForeground == primaryForeground &&
        other.primaryBorder == primaryBorder &&
        other.primaryHover == primaryHover &&
        other.primaryPressed == primaryPressed &&
        other.primaryFocused == primaryFocused &&
        // Secondary variant
        other.secondaryBackground == secondaryBackground &&
        other.secondaryForeground == secondaryForeground &&
        other.secondaryBorder == secondaryBorder &&
        other.secondaryHover == secondaryHover &&
        other.secondaryPressed == secondaryPressed &&
        other.secondaryFocused == secondaryFocused &&
        // Destructive variant
        other.destructiveBackground == destructiveBackground &&
        other.destructiveForeground == destructiveForeground &&
        other.destructiveBorder == destructiveBorder &&
        other.destructiveHover == destructiveHover &&
        other.destructivePressed == destructivePressed &&
        other.destructiveFocused == destructiveFocused &&
        // Outline variant
        other.outlineBackground == outlineBackground &&
        other.outlineForeground == outlineForeground &&
        other.outlineBorder == outlineBorder &&
        other.outlineHover == outlineHover &&
        other.outlinePressed == outlinePressed &&
        other.outlineFocused == outlineFocused &&
        // Ghost variant
        other.ghostBackground == ghostBackground &&
        other.ghostForeground == ghostForeground &&
        other.ghostBorder == ghostBorder &&
        other.ghostHover == ghostHover &&
        other.ghostPressed == ghostPressed &&
        other.ghostFocused == ghostFocused &&
        // Link variant
        other.linkBackground == linkBackground &&
        other.linkForeground == linkForeground &&
        other.linkBorder == linkBorder &&
        other.linkHover == linkHover &&
        other.linkPressed == linkPressed &&
        other.linkFocused == linkFocused &&
        // Disabled state
        other.disabledBackground == disabledBackground &&
        other.disabledForeground == disabledForeground &&
        other.disabledBorder == disabledBorder &&
        // Size properties
        other.smallHeight == smallHeight &&
        other.mediumHeight == mediumHeight &&
        other.largeHeight == largeHeight &&
        other.smallPadding == smallPadding &&
        other.mediumPadding == mediumPadding &&
        other.largePadding == largePadding &&
        other.smallFontSize == smallFontSize &&
        other.mediumFontSize == mediumFontSize &&
        other.largeFontSize == largeFontSize &&
        other.smallIconSize == smallIconSize &&
        other.mediumIconSize == mediumIconSize &&
        other.largeIconSize == largeIconSize &&
        // Common properties
        other.borderRadius == borderRadius &&
        other.borderWidth == borderWidth &&
        other.textStyle == textStyle &&
        other.fontWeight == fontWeight &&
        other.iconSpacing == iconSpacing &&
        other.minWidth == minWidth &&
        other.mainAxisSize == mainAxisSize &&
        other.mainAxisAlignment == mainAxisAlignment &&
        other.crossAxisAlignment == crossAxisAlignment &&
        // Animation
        other.animationDuration == animationDuration &&
        other.animationCurve == animationCurve &&
        // Focus
        other.focusColor == focusColor &&
        other.focusWidth == focusWidth &&
        other.focusOffset == focusOffset;
  }

  @override
  int get hashCode {
    return Object.hashAll([
      // Primary variant
      primaryBackground,
      primaryForeground,
      primaryBorder,
      primaryHover,
      primaryPressed,
      primaryFocused,
      // Secondary variant
      secondaryBackground,
      secondaryForeground,
      secondaryBorder,
      secondaryHover,
      secondaryPressed,
      secondaryFocused,
      // Destructive variant
      destructiveBackground,
      destructiveForeground,
      destructiveBorder,
      destructiveHover,
      destructivePressed,
      destructiveFocused,
      // Outline variant
      outlineBackground,
      outlineForeground,
      outlineBorder,
      outlineHover,
      outlinePressed,
      outlineFocused,
      // Ghost variant
      ghostBackground,
      ghostForeground,
      ghostBorder,
      ghostHover,
      ghostPressed,
      ghostFocused,
      // Link variant
      linkBackground,
      linkForeground,
      linkBorder,
      linkHover,
      linkPressed,
      linkFocused,
      // Disabled state
      disabledBackground,
      disabledForeground,
      disabledBorder,
      // Size properties
      smallHeight,
      mediumHeight,
      largeHeight,
      smallPadding,
      mediumPadding,
      largePadding,
      smallFontSize,
      mediumFontSize,
      largeFontSize,
      smallIconSize,
      mediumIconSize,
      largeIconSize,
      // Common properties
      borderRadius,
      borderWidth,
      textStyle,
      fontWeight,
      iconSpacing,
      minWidth,
      mainAxisSize,
      mainAxisAlignment,
      crossAxisAlignment,
      // Animation
      animationDuration,
      animationCurve,
      // Focus
      focusColor,
      focusWidth,
      focusOffset,
    ]);
  }

  @override
  String toString() {
    return 'ShadcnButtonTheme('
        'primaryBackground: $primaryBackground, '
        'primaryForeground: $primaryForeground, '
        'mediumHeight: $mediumHeight, '
        'borderRadius: $borderRadius'
        ')';
  }
}

/// Enumeration of available button variants.
/// 
/// Each variant provides a different visual style while maintaining
/// consistent interaction patterns and accessibility features.
enum ShadcnButtonVariant {
  /// Primary button - most prominent action
  primary,
  
  /// Secondary button - secondary actions
  secondary,
  
  /// Destructive button - dangerous or irreversible actions
  destructive,
  
  /// Outline button - secondary actions with borders
  outline,
  
  /// Ghost button - minimal styling, often for tertiary actions
  ghost,
  
  /// Link button - text-like appearance for navigation
  link,
}

/// Enumeration of available button sizes.
/// 
/// Each size provides different dimensions while maintaining
/// proportional spacing and proper accessibility targets.
enum ShadcnButtonSize {
  /// Small button - compact size for dense layouts
  small,
  
  /// Medium button - default size for most use cases
  medium,
  
  /// Large button - prominent actions or improved accessibility
  large,
}