import 'package:flutter/material.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for ShadcnContextMenu component styling
/// 
/// Defines styling properties for context menu components including menu containers,
/// menu items, separators, and keyboard shortcuts.
class ShadcnContextMenuTheme extends ShadcnThemeExtension<ShadcnContextMenuTheme> {
  /// Background color of the context menu container
  final Color? background;
  
  /// Border color of the context menu
  final Color? border;
  
  /// Text color for menu items
  final Color? foreground;
  
  /// Background color of menu items
  final Color? itemBackground;
  
  /// Text color of menu items
  final Color? itemForeground;
  
  /// Background color of hovered menu items
  final Color? itemHoverBackground;
  
  /// Text color of hovered menu items
  final Color? itemHoverForeground;
  
  /// Background color of selected menu items
  final Color? itemSelectedBackground;
  
  /// Text color of selected menu items
  final Color? itemSelectedForeground;
  
  /// Background color of disabled menu items
  final Color? itemDisabledBackground;
  
  /// Text color of disabled menu items
  final Color? itemDisabledForeground;
  
  /// Background color of destructive menu items
  final Color? itemDestructiveBackground;
  
  /// Text color of destructive menu items
  final Color? itemDestructiveForeground;
  
  /// Background color of hovered destructive menu items
  final Color? itemDestructiveHoverBackground;
  
  /// Text color of hovered destructive menu items
  final Color? itemDestructiveHoverForeground;
  
  /// Color of menu separators
  final Color? separatorColor;
  
  /// Color of keyboard shortcut indicators
  final Color? shortcutForeground;
  
  /// Background color of keyboard shortcut indicators
  final Color? shortcutBackground;
  
  /// Border radius of the context menu
  final BorderRadius? borderRadius;
  
  /// Minimum width of the context menu
  final double? minWidth;
  
  /// Maximum width of the context menu
  final double? maxWidth;
  
  /// Height of individual menu items
  final double? itemHeight;
  
  /// Height of menu separators
  final double? separatorHeight;
  
  /// Padding for menu items
  final EdgeInsets? itemPadding;
  
  /// Margin for the context menu
  final EdgeInsets? menuMargin;
  
  /// Padding for the context menu
  final EdgeInsets? menuPadding;
  
  /// Text style for menu items
  final TextStyle? itemTextStyle;
  
  /// Text style for keyboard shortcuts
  final TextStyle? shortcutTextStyle;
  
  /// Shadow for the context menu
  final List<BoxShadow>? shadow;
  
  /// Elevation of the context menu
  final double? elevation;

  const ShadcnContextMenuTheme({
    this.background,
    this.border,
    this.foreground,
    this.itemBackground,
    this.itemForeground,
    this.itemHoverBackground,
    this.itemHoverForeground,
    this.itemSelectedBackground,
    this.itemSelectedForeground,
    this.itemDisabledBackground,
    this.itemDisabledForeground,
    this.itemDestructiveBackground,
    this.itemDestructiveForeground,
    this.itemDestructiveHoverBackground,
    this.itemDestructiveHoverForeground,
    this.separatorColor,
    this.shortcutForeground,
    this.shortcutBackground,
    this.borderRadius,
    this.minWidth,
    this.maxWidth,
    this.itemHeight,
    this.separatorHeight,
    this.itemPadding,
    this.menuMargin,
    this.menuPadding,
    this.itemTextStyle,
    this.shortcutTextStyle,
    this.shadow,
    this.elevation,
  });

  @override
  ShadcnContextMenuTheme copyWith({
    Color? background,
    Color? border,
    Color? foreground,
    Color? itemBackground,
    Color? itemForeground,
    Color? itemHoverBackground,
    Color? itemHoverForeground,
    Color? itemSelectedBackground,
    Color? itemSelectedForeground,
    Color? itemDisabledBackground,
    Color? itemDisabledForeground,
    Color? itemDestructiveBackground,
    Color? itemDestructiveForeground,
    Color? itemDestructiveHoverBackground,
    Color? itemDestructiveHoverForeground,
    Color? separatorColor,
    Color? shortcutForeground,
    Color? shortcutBackground,
    BorderRadius? borderRadius,
    double? minWidth,
    double? maxWidth,
    double? itemHeight,
    double? separatorHeight,
    EdgeInsets? itemPadding,
    EdgeInsets? menuMargin,
    EdgeInsets? menuPadding,
    TextStyle? itemTextStyle,
    TextStyle? shortcutTextStyle,
    List<BoxShadow>? shadow,
    double? elevation,
  }) {
    return ShadcnContextMenuTheme(
      background: background ?? this.background,
      border: border ?? this.border,
      foreground: foreground ?? this.foreground,
      itemBackground: itemBackground ?? this.itemBackground,
      itemForeground: itemForeground ?? this.itemForeground,
      itemHoverBackground: itemHoverBackground ?? this.itemHoverBackground,
      itemHoverForeground: itemHoverForeground ?? this.itemHoverForeground,
      itemSelectedBackground: itemSelectedBackground ?? this.itemSelectedBackground,
      itemSelectedForeground: itemSelectedForeground ?? this.itemSelectedForeground,
      itemDisabledBackground: itemDisabledBackground ?? this.itemDisabledBackground,
      itemDisabledForeground: itemDisabledForeground ?? this.itemDisabledForeground,
      itemDestructiveBackground: itemDestructiveBackground ?? this.itemDestructiveBackground,
      itemDestructiveForeground: itemDestructiveForeground ?? this.itemDestructiveForeground,
      itemDestructiveHoverBackground: itemDestructiveHoverBackground ?? this.itemDestructiveHoverBackground,
      itemDestructiveHoverForeground: itemDestructiveHoverForeground ?? this.itemDestructiveHoverForeground,
      separatorColor: separatorColor ?? this.separatorColor,
      shortcutForeground: shortcutForeground ?? this.shortcutForeground,
      shortcutBackground: shortcutBackground ?? this.shortcutBackground,
      borderRadius: borderRadius ?? this.borderRadius,
      minWidth: minWidth ?? this.minWidth,
      maxWidth: maxWidth ?? this.maxWidth,
      itemHeight: itemHeight ?? this.itemHeight,
      separatorHeight: separatorHeight ?? this.separatorHeight,
      itemPadding: itemPadding ?? this.itemPadding,
      menuMargin: menuMargin ?? this.menuMargin,
      menuPadding: menuPadding ?? this.menuPadding,
      itemTextStyle: itemTextStyle ?? this.itemTextStyle,
      shortcutTextStyle: shortcutTextStyle ?? this.shortcutTextStyle,
      shadow: shadow ?? this.shadow,
      elevation: elevation ?? this.elevation,
    );
  }

  @override
  ShadcnContextMenuTheme lerp(ThemeExtension<ShadcnContextMenuTheme>? other, double t) {
    if (other is! ShadcnContextMenuTheme) return this;

    return ShadcnContextMenuTheme(
      background: Color.lerp(background, other.background, t),
      border: Color.lerp(border, other.border, t),
      foreground: Color.lerp(foreground, other.foreground, t),
      itemBackground: Color.lerp(itemBackground, other.itemBackground, t),
      itemForeground: Color.lerp(itemForeground, other.itemForeground, t),
      itemHoverBackground: Color.lerp(itemHoverBackground, other.itemHoverBackground, t),
      itemHoverForeground: Color.lerp(itemHoverForeground, other.itemHoverForeground, t),
      itemSelectedBackground: Color.lerp(itemSelectedBackground, other.itemSelectedBackground, t),
      itemSelectedForeground: Color.lerp(itemSelectedForeground, other.itemSelectedForeground, t),
      itemDisabledBackground: Color.lerp(itemDisabledBackground, other.itemDisabledBackground, t),
      itemDisabledForeground: Color.lerp(itemDisabledForeground, other.itemDisabledForeground, t),
      itemDestructiveBackground: Color.lerp(itemDestructiveBackground, other.itemDestructiveBackground, t),
      itemDestructiveForeground: Color.lerp(itemDestructiveForeground, other.itemDestructiveForeground, t),
      itemDestructiveHoverBackground: Color.lerp(itemDestructiveHoverBackground, other.itemDestructiveHoverBackground, t),
      itemDestructiveHoverForeground: Color.lerp(itemDestructiveHoverForeground, other.itemDestructiveHoverForeground, t),
      separatorColor: Color.lerp(separatorColor, other.separatorColor, t),
      shortcutForeground: Color.lerp(shortcutForeground, other.shortcutForeground, t),
      shortcutBackground: Color.lerp(shortcutBackground, other.shortcutBackground, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      minWidth: lerpDouble(minWidth, other.minWidth, t),
      maxWidth: lerpDouble(maxWidth, other.maxWidth, t),
      itemHeight: lerpDouble(itemHeight, other.itemHeight, t),
      separatorHeight: lerpDouble(separatorHeight, other.separatorHeight, t),
      itemPadding: EdgeInsets.lerp(itemPadding, other.itemPadding, t),
      menuMargin: EdgeInsets.lerp(menuMargin, other.menuMargin, t),
      menuPadding: EdgeInsets.lerp(menuPadding, other.menuPadding, t),
      itemTextStyle: TextStyle.lerp(itemTextStyle, other.itemTextStyle, t),
      shortcutTextStyle: TextStyle.lerp(shortcutTextStyle, other.shortcutTextStyle, t),
      shadow: BoxShadow.lerpList(shadow, other.shadow, t),
      elevation: lerpDouble(elevation, other.elevation, t),
    );
  }

  /// Default theme based on shadcn design tokens
  static ShadcnContextMenuTheme defaultTheme(ColorScheme colorScheme) {
    final isDark = colorScheme.brightness == Brightness.dark;
    
    return ShadcnContextMenuTheme(
      background: isDark ? const Color(0xFF09090B) : const Color(0xFFFFFFFF),
      border: isDark ? const Color(0xFF27272A) : const Color(0xFFE4E4E7),
      foreground: isDark ? const Color(0xFFFAFAFA) : const Color(0xFF09090B),
      itemBackground: Colors.transparent,
      itemForeground: isDark ? const Color(0xFFFAFAFA) : const Color(0xFF09090B),
      itemHoverBackground: isDark ? const Color(0xFF18181B) : const Color(0xFFF4F4F5),
      itemHoverForeground: isDark ? const Color(0xFFFAFAFA) : const Color(0xFF09090B),
      itemSelectedBackground: isDark ? const Color(0xFF3F3F46) : const Color(0xFFE4E4E7),
      itemSelectedForeground: isDark ? const Color(0xFFFAFAFA) : const Color(0xFF09090B),
      itemDisabledBackground: Colors.transparent,
      itemDisabledForeground: isDark ? const Color(0xFF52525B) : const Color(0xFFA1A1AA),
      itemDestructiveBackground: Colors.transparent,
      itemDestructiveForeground: isDark ? const Color(0xFFFEF2F2) : const Color(0xFFDC2626),
      itemDestructiveHoverBackground: isDark ? const Color(0xFFDC2626) : const Color(0xFFDC2626),
      itemDestructiveHoverForeground: isDark ? const Color(0xFFFEF2F2) : const Color(0xFFFEF2F2),
      separatorColor: isDark ? const Color(0xFF27272A) : const Color(0xFFE4E4E7),
      shortcutForeground: isDark ? const Color(0xFF71717A) : const Color(0xFF71717A),
      shortcutBackground: isDark ? const Color(0xFF27272A) : const Color(0xFFF4F4F5),
      borderRadius: BorderRadius.circular(6),
      minWidth: 220,
      maxWidth: 320,
      itemHeight: 32,
      separatorHeight: 1,
      itemPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      menuMargin: const EdgeInsets.all(8),
      menuPadding: const EdgeInsets.all(4),
      itemTextStyle: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: isDark ? const Color(0xFFFAFAFA) : const Color(0xFF09090B),
      ),
      shortcutTextStyle: TextStyle(
        fontSize: 11,
        fontWeight: FontWeight.w400,
        color: isDark ? const Color(0xFF71717A) : const Color(0xFF71717A),
      ),
      shadow: [
        BoxShadow(
          color: isDark ? const Color(0x40000000) : const Color(0x0F000000),
          offset: const Offset(0, 4),
          blurRadius: 16,
          spreadRadius: -2,
        ),
      ],
      elevation: 8,
    );
  }

  double lerpDouble(double? a, double? b, double t) {
    if (a == null && b == null) return 0.0;
    if (a == null) return b! * t;
    if (b == null) return a * (1.0 - t);
    return a + (b - a) * t;
  }
}