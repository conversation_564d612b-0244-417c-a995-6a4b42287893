import 'package:flutter/material.dart';
import '../../constants/shadcn_tokens.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for the ShadcnSwitch component.
/// 
/// This theme extension provides all styling properties for switch variants,
/// sizes, and interactive states. It follows shadcn design principles while
/// integrating with Material Design theming patterns.
/// 
/// All properties are nullable to support fallback to Material theme values
/// and provide flexible customization options for developers.
class ShadcnSwitchTheme extends ShadcnThemeExtension<ShadcnSwitchTheme> {
  // On/enabled state colors
  final Color? onTrackColor;
  final Color? onThumbColor;
  final Color? onBorderColor;
  
  // Off/disabled state colors
  final Color? offTrackColor;
  final Color? offThumbColor;
  final Color? offBorderColor;
  
  // Interactive state overlays
  final Color? hoverOverlay;
  final Color? pressedOverlay;
  final Color? focusedOverlay;
  
  // Disabled state colors
  final Color? disabledOnTrackColor;
  final Color? disabledOnThumbColor;
  final Color? disabledOnBorderColor;
  final Color? disabledOffTrackColor;
  final Color? disabledOffThumbColor;
  final Color? disabledOffBorderColor;
  
  // Sizing properties
  final double? trackWidth;
  final double? trackHeight;
  final double? smallTrackWidth;
  final double? smallTrackHeight;
  final double? largeTrackWidth;
  final double? largeTrackHeight;
  
  final double? thumbSize;
  final double? smallThumbSize;
  final double? largeThumbSize;
  
  // Border properties
  final double? borderWidth;
  final BorderRadius? trackBorderRadius;
  
  // Spacing and layout properties
  final EdgeInsets? padding;
  final double? labelSpacing;
  
  // Text styling
  final TextStyle? labelStyle;
  final TextStyle? helperStyle;
  final TextStyle? errorStyle;
  
  // Animation properties
  final Duration? animationDuration;
  final Curve? animationCurve;
  
  // Focus ring properties
  final double? focusRingWidth;
  final double? focusRingOffset;
  
  // Elevation and shadow
  final double? thumbElevation;
  final List<BoxShadow>? thumbShadow;
  final List<BoxShadow>? trackShadow;

  const ShadcnSwitchTheme({
    // On/enabled state colors
    this.onTrackColor,
    this.onThumbColor,
    this.onBorderColor,
    
    // Off/disabled state colors
    this.offTrackColor,
    this.offThumbColor,
    this.offBorderColor,
    
    // Interactive state overlays
    this.hoverOverlay,
    this.pressedOverlay,
    this.focusedOverlay,
    
    // Disabled state colors
    this.disabledOnTrackColor,
    this.disabledOnThumbColor,
    this.disabledOnBorderColor,
    this.disabledOffTrackColor,
    this.disabledOffThumbColor,
    this.disabledOffBorderColor,
    
    // Sizing properties
    this.trackWidth,
    this.trackHeight,
    this.smallTrackWidth,
    this.smallTrackHeight,
    this.largeTrackWidth,
    this.largeTrackHeight,
    this.thumbSize,
    this.smallThumbSize,
    this.largeThumbSize,
    
    // Border properties
    this.borderWidth,
    this.trackBorderRadius,
    
    // Spacing and layout properties
    this.padding,
    this.labelSpacing,
    
    // Text styling
    this.labelStyle,
    this.helperStyle,
    this.errorStyle,
    
    // Animation properties
    this.animationDuration,
    this.animationCurve,
    
    // Focus ring properties
    this.focusRingWidth,
    this.focusRingOffset,
    
    // Elevation and shadow
    this.thumbElevation,
    this.thumbShadow,
    this.trackShadow,
  });

  /// Creates a ShadcnSwitchTheme with default values based on the provided ColorScheme.
  /// 
  /// This factory constructor provides sensible defaults that follow shadcn design
  /// principles while integrating with Material Design color schemes.
  static ShadcnSwitchTheme defaultTheme(ColorScheme colorScheme) {
    final brightness = colorScheme.brightness;
    final isDark = brightness == Brightness.dark;
    
    return ShadcnSwitchTheme(
      // On/enabled state colors
      onTrackColor: colorScheme.primary,
      onThumbColor: colorScheme.onPrimary,
      onBorderColor: colorScheme.primary,
      
      // Off/disabled state colors
      offTrackColor: isDark 
        ? colorScheme.surface.withOpacity(0.2)
        : colorScheme.onSurface.withOpacity(0.12),
      offThumbColor: isDark 
        ? colorScheme.onSurface.withOpacity(0.8)
        : colorScheme.surface,
      offBorderColor: isDark 
        ? colorScheme.outline.withOpacity(0.6)
        : colorScheme.outline,
      
      // Interactive state overlays
      hoverOverlay: colorScheme.primary.withOpacity(0.08),
      pressedOverlay: colorScheme.primary.withOpacity(0.12),
      focusedOverlay: colorScheme.primary.withOpacity(0.12),
      
      // Disabled state colors
      disabledOnTrackColor: colorScheme.onSurface.withOpacity(0.12),
      disabledOnThumbColor: colorScheme.surface,
      disabledOnBorderColor: colorScheme.onSurface.withOpacity(0.12),
      disabledOffTrackColor: colorScheme.onSurface.withOpacity(0.12),
      disabledOffThumbColor: colorScheme.surface,
      disabledOffBorderColor: colorScheme.onSurface.withOpacity(0.12),
      
      // Sizing properties
      trackWidth: 44.0,
      trackHeight: 24.0,
      smallTrackWidth: 36.0,
      smallTrackHeight: 20.0,
      largeTrackWidth: 52.0,
      largeTrackHeight: 28.0,
      thumbSize: 20.0,
      smallThumbSize: 16.0,
      largeThumbSize: 24.0,
      
      // Border properties
      borderWidth: ShadcnTokens.borderWidth,
      trackBorderRadius: BorderRadius.circular(100),
      
      // Spacing and layout properties
      padding: EdgeInsets.all(ShadcnTokens.spacing1),
      labelSpacing: ShadcnTokens.spacing2,
      
      // Text styling
      labelStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightNormal,
        color: colorScheme.onSurface,
        height: ShadcnTokens.lineHeightNormal,
      ),
      helperStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeSm,
        fontWeight: ShadcnTokens.fontWeightNormal,
        color: colorScheme.onSurface.withOpacity(0.6),
        height: ShadcnTokens.lineHeightNormal,
      ),
      errorStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeSm,
        fontWeight: ShadcnTokens.fontWeightNormal,
        color: colorScheme.error,
        height: ShadcnTokens.lineHeightNormal,
      ),
      
      // Animation properties
      animationDuration: ShadcnTokens.durationFast,
      animationCurve: Curves.easeInOut,
      
      // Focus ring properties
      focusRingWidth: 2.0,
      focusRingOffset: 2.0,
      
      // Elevation and shadow
      thumbElevation: isDark ? 0 : 1,
      thumbShadow: isDark ? null : [
        BoxShadow(
          color: Colors.black.withOpacity(0.15),
          blurRadius: 2,
          offset: const Offset(0, 1),
        ),
      ],
      trackShadow: null,
    );
  }

  @override
  ShadcnSwitchTheme copyWith({
    // On/enabled state colors
    Color? onTrackColor,
    Color? onThumbColor,
    Color? onBorderColor,
    
    // Off/disabled state colors
    Color? offTrackColor,
    Color? offThumbColor,
    Color? offBorderColor,
    
    // Interactive state overlays
    Color? hoverOverlay,
    Color? pressedOverlay,
    Color? focusedOverlay,
    
    // Disabled state colors
    Color? disabledOnTrackColor,
    Color? disabledOnThumbColor,
    Color? disabledOnBorderColor,
    Color? disabledOffTrackColor,
    Color? disabledOffThumbColor,
    Color? disabledOffBorderColor,
    
    // Sizing properties
    double? trackWidth,
    double? trackHeight,
    double? smallTrackWidth,
    double? smallTrackHeight,
    double? largeTrackWidth,
    double? largeTrackHeight,
    double? thumbSize,
    double? smallThumbSize,
    double? largeThumbSize,
    
    // Border properties
    double? borderWidth,
    BorderRadius? trackBorderRadius,
    
    // Spacing and layout properties
    EdgeInsets? padding,
    double? labelSpacing,
    
    // Text styling
    TextStyle? labelStyle,
    TextStyle? helperStyle,
    TextStyle? errorStyle,
    
    // Animation properties
    Duration? animationDuration,
    Curve? animationCurve,
    
    // Focus ring properties
    double? focusRingWidth,
    double? focusRingOffset,
    
    // Elevation and shadow
    double? thumbElevation,
    List<BoxShadow>? thumbShadow,
    List<BoxShadow>? trackShadow,
  }) {
    return ShadcnSwitchTheme(
      // On/enabled state colors
      onTrackColor: onTrackColor ?? this.onTrackColor,
      onThumbColor: onThumbColor ?? this.onThumbColor,
      onBorderColor: onBorderColor ?? this.onBorderColor,
      
      // Off/disabled state colors
      offTrackColor: offTrackColor ?? this.offTrackColor,
      offThumbColor: offThumbColor ?? this.offThumbColor,
      offBorderColor: offBorderColor ?? this.offBorderColor,
      
      // Interactive state overlays
      hoverOverlay: hoverOverlay ?? this.hoverOverlay,
      pressedOverlay: pressedOverlay ?? this.pressedOverlay,
      focusedOverlay: focusedOverlay ?? this.focusedOverlay,
      
      // Disabled state colors
      disabledOnTrackColor: disabledOnTrackColor ?? this.disabledOnTrackColor,
      disabledOnThumbColor: disabledOnThumbColor ?? this.disabledOnThumbColor,
      disabledOnBorderColor: disabledOnBorderColor ?? this.disabledOnBorderColor,
      disabledOffTrackColor: disabledOffTrackColor ?? this.disabledOffTrackColor,
      disabledOffThumbColor: disabledOffThumbColor ?? this.disabledOffThumbColor,
      disabledOffBorderColor: disabledOffBorderColor ?? this.disabledOffBorderColor,
      
      // Sizing properties
      trackWidth: trackWidth ?? this.trackWidth,
      trackHeight: trackHeight ?? this.trackHeight,
      smallTrackWidth: smallTrackWidth ?? this.smallTrackWidth,
      smallTrackHeight: smallTrackHeight ?? this.smallTrackHeight,
      largeTrackWidth: largeTrackWidth ?? this.largeTrackWidth,
      largeTrackHeight: largeTrackHeight ?? this.largeTrackHeight,
      thumbSize: thumbSize ?? this.thumbSize,
      smallThumbSize: smallThumbSize ?? this.smallThumbSize,
      largeThumbSize: largeThumbSize ?? this.largeThumbSize,
      
      // Border properties
      borderWidth: borderWidth ?? this.borderWidth,
      trackBorderRadius: trackBorderRadius ?? this.trackBorderRadius,
      
      // Spacing and layout properties
      padding: padding ?? this.padding,
      labelSpacing: labelSpacing ?? this.labelSpacing,
      
      // Text styling
      labelStyle: labelStyle ?? this.labelStyle,
      helperStyle: helperStyle ?? this.helperStyle,
      errorStyle: errorStyle ?? this.errorStyle,
      
      // Animation properties
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
      
      // Focus ring properties
      focusRingWidth: focusRingWidth ?? this.focusRingWidth,
      focusRingOffset: focusRingOffset ?? this.focusRingOffset,
      
      // Elevation and shadow
      thumbElevation: thumbElevation ?? this.thumbElevation,
      thumbShadow: thumbShadow ?? this.thumbShadow,
      trackShadow: trackShadow ?? this.trackShadow,
    );
  }

  @override
  ShadcnSwitchTheme lerp(ShadcnSwitchTheme? other, double t) {
    if (other is! ShadcnSwitchTheme) {
      return this;
    }
    
    return ShadcnSwitchTheme(
      // On/enabled state colors
      onTrackColor: Color.lerp(onTrackColor, other.onTrackColor, t),
      onThumbColor: Color.lerp(onThumbColor, other.onThumbColor, t),
      onBorderColor: Color.lerp(onBorderColor, other.onBorderColor, t),
      
      // Off/disabled state colors
      offTrackColor: Color.lerp(offTrackColor, other.offTrackColor, t),
      offThumbColor: Color.lerp(offThumbColor, other.offThumbColor, t),
      offBorderColor: Color.lerp(offBorderColor, other.offBorderColor, t),
      
      // Interactive state overlays
      hoverOverlay: Color.lerp(hoverOverlay, other.hoverOverlay, t),
      pressedOverlay: Color.lerp(pressedOverlay, other.pressedOverlay, t),
      focusedOverlay: Color.lerp(focusedOverlay, other.focusedOverlay, t),
      
      // Disabled state colors
      disabledOnTrackColor: Color.lerp(disabledOnTrackColor, other.disabledOnTrackColor, t),
      disabledOnThumbColor: Color.lerp(disabledOnThumbColor, other.disabledOnThumbColor, t),
      disabledOnBorderColor: Color.lerp(disabledOnBorderColor, other.disabledOnBorderColor, t),
      disabledOffTrackColor: Color.lerp(disabledOffTrackColor, other.disabledOffTrackColor, t),
      disabledOffThumbColor: Color.lerp(disabledOffThumbColor, other.disabledOffThumbColor, t),
      disabledOffBorderColor: Color.lerp(disabledOffBorderColor, other.disabledOffBorderColor, t),
      
      // Sizing properties
      trackWidth: lerpDouble(trackWidth, other.trackWidth, t),
      trackHeight: lerpDouble(trackHeight, other.trackHeight, t),
      smallTrackWidth: lerpDouble(smallTrackWidth, other.smallTrackWidth, t),
      smallTrackHeight: lerpDouble(smallTrackHeight, other.smallTrackHeight, t),
      largeTrackWidth: lerpDouble(largeTrackWidth, other.largeTrackWidth, t),
      largeTrackHeight: lerpDouble(largeTrackHeight, other.largeTrackHeight, t),
      thumbSize: lerpDouble(thumbSize, other.thumbSize, t),
      smallThumbSize: lerpDouble(smallThumbSize, other.smallThumbSize, t),
      largeThumbSize: lerpDouble(largeThumbSize, other.largeThumbSize, t),
      
      // Border properties
      borderWidth: lerpDouble(borderWidth, other.borderWidth, t),
      trackBorderRadius: BorderRadius.lerp(trackBorderRadius, other.trackBorderRadius, t),
      
      // Spacing and layout properties
      padding: EdgeInsets.lerp(padding, other.padding, t),
      labelSpacing: lerpDouble(labelSpacing, other.labelSpacing, t),
      
      // Text styling
      labelStyle: TextStyle.lerp(labelStyle, other.labelStyle, t),
      helperStyle: TextStyle.lerp(helperStyle, other.helperStyle, t),
      errorStyle: TextStyle.lerp(errorStyle, other.errorStyle, t),
      
      // Animation properties
      animationDuration: lerpDuration(animationDuration, other.animationDuration, t),
      animationCurve: other.animationCurve ?? animationCurve,
      
      // Focus ring properties
      focusRingWidth: lerpDouble(focusRingWidth, other.focusRingWidth, t),
      focusRingOffset: lerpDouble(focusRingOffset, other.focusRingOffset, t),
      
      // Elevation and shadow
      thumbElevation: lerpDouble(thumbElevation, other.thumbElevation, t),
      thumbShadow: BoxShadow.lerpList(thumbShadow, other.thumbShadow, t),
      trackShadow: BoxShadow.lerpList(trackShadow, other.trackShadow, t),
    );
  }

  @override
  bool validate({bool throwOnError = false}) {
    try {
      // Validate that essential colors are not null in a complete theme
      if (onTrackColor != null && onThumbColor == null) {
        if (throwOnError) {
          throw ThemeException('ShadcnSwitchTheme: onThumbColor cannot be null when onTrackColor is provided');
        }
        return false;
      }
      
      if (trackWidth != null && trackWidth! <= 0) {
        if (throwOnError) {
          throw ThemeException('ShadcnSwitchTheme: trackWidth must be greater than 0');
        }
        return false;
      }
      
      if (trackHeight != null && trackHeight! <= 0) {
        if (throwOnError) {
          throw ThemeException('ShadcnSwitchTheme: trackHeight must be greater than 0');
        }
        return false;
      }
      
      if (thumbSize != null && thumbSize! <= 0) {
        if (throwOnError) {
          throw ThemeException('ShadcnSwitchTheme: thumbSize must be greater than 0');
        }
        return false;
      }
      
      if (borderWidth != null && borderWidth! < 0) {
        if (throwOnError) {
          throw ThemeException('ShadcnSwitchTheme: borderWidth cannot be negative');
        }
        return false;
      }
      
      return true;
    } catch (e) {
      if (throwOnError) rethrow;
      return false;
    }
  }

  /// Helper method to resolve track width based on the size variant
  double resolveTrackWidthForVariant(ShadcnSwitchSize sizeVariant) {
    switch (sizeVariant) {
      case ShadcnSwitchSize.small:
        return smallTrackWidth ?? 36.0;
      case ShadcnSwitchSize.medium:
        return trackWidth ?? 44.0;
      case ShadcnSwitchSize.large:
        return largeTrackWidth ?? 52.0;
    }
  }

  /// Helper method to resolve track height based on the size variant
  double resolveTrackHeightForVariant(ShadcnSwitchSize sizeVariant) {
    switch (sizeVariant) {
      case ShadcnSwitchSize.small:
        return smallTrackHeight ?? 20.0;
      case ShadcnSwitchSize.medium:
        return trackHeight ?? 24.0;
      case ShadcnSwitchSize.large:
        return largeTrackHeight ?? 28.0;
    }
  }

  /// Helper method to resolve thumb size based on the size variant
  double resolveThumbSizeForVariant(ShadcnSwitchSize sizeVariant) {
    switch (sizeVariant) {
      case ShadcnSwitchSize.small:
        return smallThumbSize ?? 16.0;
      case ShadcnSwitchSize.medium:
        return thumbSize ?? 20.0;
      case ShadcnSwitchSize.large:
        return largeThumbSize ?? 24.0;
    }
  }
}

/// Helper method for lerping durations
Duration? lerpDuration(Duration? a, Duration? b, double t) {
  if (a == null && b == null) return null;
  if (a == null) return b;
  if (b == null) return a;
  return Duration(
    milliseconds: (a.inMilliseconds + ((b.inMilliseconds - a.inMilliseconds) * t)).round(),
  );
}

/// Helper method for lerping double values
double? lerpDouble(double? a, double? b, double t) {
  if (a == null && b == null) return null;
  if (a == null) return b;
  if (b == null) return a;
  return a + (b - a) * t;
}

/// Exception thrown when theme validation fails
class ThemeException implements Exception {
  final String message;
  
  const ThemeException(this.message);
  
  @override
  String toString() => message;
}

/// Enum for switch size variants
enum ShadcnSwitchSize {
  small,
  medium,
  large,
}