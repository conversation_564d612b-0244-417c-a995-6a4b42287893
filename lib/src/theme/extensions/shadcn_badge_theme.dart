import 'dart:ui' show lerpDouble;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'shadcn_theme_extension.dart';
import '../../constants/shadcn_tokens.dart';

/// Theme extension for [ShadcnBadge] component.
/// 
/// This theme extension provides comprehensive theming support for badge components,
/// including all shadcn variants (default, secondary, destructive, outline), 
/// sizing options, positioning properties, and styling customization.
/// 
/// The badge theme supports various visual styles from subtle indicators to 
/// prominent notification badges with consistent Material Design integration.
/// 
/// Example usage:
/// ```dart
/// Theme(
///   data: ThemeData(
///     extensions: [
///       ShadcnBadgeTheme(
///         defaultBackground: Colors.blue,
///         defaultForeground: Colors.white,
///         borderRadius: BorderRadius.circular(12),
///       ),
///     ],
///   ),
///   child: ShadcnBadge(text: 'New'),
/// )
/// ```
class ShadcnBadgeTheme extends ShadcnThemeExtension<ShadcnBadgeTheme> {
  // Default variant colors
  
  /// Background color for default variant.
  final Color? defaultBackground;
  
  /// Foreground color for default variant.
  final Color? defaultForeground;
  
  /// Border color for default variant.
  final Color? defaultBorder;
  
  // Secondary variant colors
  
  /// Background color for secondary variant.
  final Color? secondaryBackground;
  
  /// Foreground color for secondary variant.
  final Color? secondaryForeground;
  
  /// Border color for secondary variant.
  final Color? secondaryBorder;
  
  // Destructive variant colors
  
  /// Background color for destructive variant.
  final Color? destructiveBackground;
  
  /// Foreground color for destructive variant.
  final Color? destructiveForeground;
  
  /// Border color for destructive variant.
  final Color? destructiveBorder;
  
  // Outline variant colors
  
  /// Background color for outline variant.
  final Color? outlineBackground;
  
  /// Foreground color for outline variant.
  final Color? outlineForeground;
  
  /// Border color for outline variant.
  final Color? outlineBorder;
  
  // Size and spacing properties
  
  /// Minimum width for the badge.
  final double? minWidth;
  
  /// Minimum height for the badge.
  final double? minHeight;
  
  /// Padding for badge content.
  final EdgeInsets? padding;
  
  /// Border radius for the badge.
  final BorderRadius? borderRadius;
  
  /// Border width for outlined badges.
  final double? borderWidth;
  
  // Typography
  
  /// Text style for badge content.
  final TextStyle? textStyle;
  
  /// Font size for badge text.
  final double? fontSize;
  
  /// Font weight for badge text.
  final FontWeight? fontWeight;
  
  /// Letter spacing for badge text.
  final double? letterSpacing;
  
  // Animation properties
  
  /// Duration for animations and transitions.
  final Duration? animationDuration;
  
  /// Animation curve for transitions.
  final Curve? animationCurve;
  
  // Positioning properties (for positioned badges)
  
  /// Default offset for positioned badges.
  final Offset? positionOffset;
  
  /// Alignment for positioned badges.
  final Alignment? positionAlignment;

  const ShadcnBadgeTheme({
    this.defaultBackground,
    this.defaultForeground,
    this.defaultBorder,
    this.secondaryBackground,
    this.secondaryForeground,
    this.secondaryBorder,
    this.destructiveBackground,
    this.destructiveForeground,
    this.destructiveBorder,
    this.outlineBackground,
    this.outlineForeground,
    this.outlineBorder,
    this.minWidth,
    this.minHeight,
    this.padding,
    this.borderRadius,
    this.borderWidth,
    this.textStyle,
    this.fontSize,
    this.fontWeight,
    this.letterSpacing,
    this.animationDuration,
    this.animationCurve,
    this.positionOffset,
    this.positionAlignment,
  });

  @override
  ShadcnBadgeTheme copyWith({
    Color? defaultBackground,
    Color? defaultForeground,
    Color? defaultBorder,
    Color? secondaryBackground,
    Color? secondaryForeground,
    Color? secondaryBorder,
    Color? destructiveBackground,
    Color? destructiveForeground,
    Color? destructiveBorder,
    Color? outlineBackground,
    Color? outlineForeground,
    Color? outlineBorder,
    double? minWidth,
    double? minHeight,
    EdgeInsets? padding,
    BorderRadius? borderRadius,
    double? borderWidth,
    TextStyle? textStyle,
    double? fontSize,
    FontWeight? fontWeight,
    double? letterSpacing,
    Duration? animationDuration,
    Curve? animationCurve,
    Offset? positionOffset,
    Alignment? positionAlignment,
  }) {
    return ShadcnBadgeTheme(
      defaultBackground: defaultBackground ?? this.defaultBackground,
      defaultForeground: defaultForeground ?? this.defaultForeground,
      defaultBorder: defaultBorder ?? this.defaultBorder,
      secondaryBackground: secondaryBackground ?? this.secondaryBackground,
      secondaryForeground: secondaryForeground ?? this.secondaryForeground,
      secondaryBorder: secondaryBorder ?? this.secondaryBorder,
      destructiveBackground: destructiveBackground ?? this.destructiveBackground,
      destructiveForeground: destructiveForeground ?? this.destructiveForeground,
      destructiveBorder: destructiveBorder ?? this.destructiveBorder,
      outlineBackground: outlineBackground ?? this.outlineBackground,
      outlineForeground: outlineForeground ?? this.outlineForeground,
      outlineBorder: outlineBorder ?? this.outlineBorder,
      minWidth: minWidth ?? this.minWidth,
      minHeight: minHeight ?? this.minHeight,
      padding: padding ?? this.padding,
      borderRadius: borderRadius ?? this.borderRadius,
      borderWidth: borderWidth ?? this.borderWidth,
      textStyle: textStyle ?? this.textStyle,
      fontSize: fontSize ?? this.fontSize,
      fontWeight: fontWeight ?? this.fontWeight,
      letterSpacing: letterSpacing ?? this.letterSpacing,
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
      positionOffset: positionOffset ?? this.positionOffset,
      positionAlignment: positionAlignment ?? this.positionAlignment,
    );
  }

  @override
  ShadcnBadgeTheme lerp(ShadcnBadgeTheme? other, double t) {
    if (other is! ShadcnBadgeTheme) {
      return this;
    }

    return ShadcnBadgeTheme(
      defaultBackground: Color.lerp(defaultBackground, other.defaultBackground, t),
      defaultForeground: Color.lerp(defaultForeground, other.defaultForeground, t),
      defaultBorder: Color.lerp(defaultBorder, other.defaultBorder, t),
      secondaryBackground: Color.lerp(secondaryBackground, other.secondaryBackground, t),
      secondaryForeground: Color.lerp(secondaryForeground, other.secondaryForeground, t),
      secondaryBorder: Color.lerp(secondaryBorder, other.secondaryBorder, t),
      destructiveBackground: Color.lerp(destructiveBackground, other.destructiveBackground, t),
      destructiveForeground: Color.lerp(destructiveForeground, other.destructiveForeground, t),
      destructiveBorder: Color.lerp(destructiveBorder, other.destructiveBorder, t),
      outlineBackground: Color.lerp(outlineBackground, other.outlineBackground, t),
      outlineForeground: Color.lerp(outlineForeground, other.outlineForeground, t),
      outlineBorder: Color.lerp(outlineBorder, other.outlineBorder, t),
      minWidth: lerpDouble(minWidth, other.minWidth, t),
      minHeight: lerpDouble(minHeight, other.minHeight, t),
      padding: EdgeInsets.lerp(padding, other.padding, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      borderWidth: lerpDouble(borderWidth, other.borderWidth, t),
      textStyle: TextStyle.lerp(textStyle, other.textStyle, t),
      fontSize: lerpDouble(fontSize, other.fontSize, t),
      fontWeight: FontWeight.lerp(fontWeight, other.fontWeight, t),
      letterSpacing: lerpDouble(letterSpacing, other.letterSpacing, t),
      animationDuration: t < 0.5 ? animationDuration : other.animationDuration,
      animationCurve: t < 0.5 ? animationCurve : other.animationCurve,
      positionOffset: Offset.lerp(positionOffset, other.positionOffset, t),
      positionAlignment: Alignment.lerp(positionAlignment, other.positionAlignment, t),
    );
  }

  /// Creates a default [ShadcnBadgeTheme] from the given [ColorScheme].
  /// 
  /// This factory method creates a theme that follows shadcn design principles
  /// while integrating with Material Design. The resulting theme will have
  /// consistent styling that works well with both light and dark themes.
  /// 
  /// The default theme uses:
  /// - Primary color for default variant
  /// - Secondary color for secondary variant
  /// - Error color for destructive variant
  /// - Outline style for outline variant
  static ShadcnBadgeTheme defaultTheme(ColorScheme colorScheme) {
    final isLight = colorScheme.brightness == Brightness.light;
    
    return ShadcnBadgeTheme(
      // Default variant (primary)
      defaultBackground: colorScheme.primary,
      defaultForeground: colorScheme.onPrimary,
      defaultBorder: colorScheme.primary,
      
      // Secondary variant
      secondaryBackground: colorScheme.secondary,
      secondaryForeground: colorScheme.onSecondary,
      secondaryBorder: colorScheme.secondary,
      
      // Destructive variant
      destructiveBackground: colorScheme.error,
      destructiveForeground: colorScheme.onError,
      destructiveBorder: colorScheme.error,
      
      // Outline variant
      outlineBackground: Colors.transparent,
      outlineForeground: colorScheme.onSurface,
      outlineBorder: colorScheme.outline,
      
      // Size properties
      minWidth: 20.0,
      minHeight: 20.0,
      padding: const EdgeInsets.symmetric(
        horizontal: ShadcnTokens.spacing2,
        vertical: ShadcnTokens.spacing1,
      ),
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusFull),
      borderWidth: ShadcnTokens.borderWidth,
      
      // Typography
      textStyle: null, // Will use theme default
      fontSize: ShadcnTokens.fontSizeXs,
      fontWeight: ShadcnTokens.fontWeightMedium,
      letterSpacing: 0.025,
      
      // Animation
      animationDuration: ShadcnTokens.durationFast,
      animationCurve: Curves.easeInOut,
      
      // Positioning
      positionOffset: const Offset(-4, -4),
      positionAlignment: Alignment.topRight,
    );
  }

  /// Creates a theme optimized for notification badges.
  /// 
  /// This variant uses smaller sizing and bolder colors to make
  /// badges stand out as notification indicators.
  static ShadcnBadgeTheme notificationTheme(ColorScheme colorScheme) {
    return defaultTheme(colorScheme).copyWith(
      defaultBackground: colorScheme.error,
      defaultForeground: colorScheme.onError,
      minWidth: 18.0,
      minHeight: 18.0,
      fontSize: 10.0,
      padding: const EdgeInsets.symmetric(
        horizontal: ShadcnTokens.spacing1,
        vertical: ShadcnTokens.spacing1,
      ),
    );
  }

  /// Creates a theme optimized for status badges.
  /// 
  /// This variant uses outline styling and subtle colors
  /// for status indicators that shouldn't be too prominent.
  static ShadcnBadgeTheme statusTheme(ColorScheme colorScheme) {
    return defaultTheme(colorScheme).copyWith(
      defaultBackground: Colors.transparent,
      defaultForeground: colorScheme.onSurfaceVariant,
      defaultBorder: colorScheme.outline,
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusSm),
      fontSize: ShadcnTokens.fontSizeSm,
      padding: const EdgeInsets.symmetric(
        horizontal: ShadcnTokens.spacing2,
        vertical: ShadcnTokens.spacing1,
      ),
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ShadcnBadgeTheme &&
          runtimeType == other.runtimeType &&
          defaultBackground == other.defaultBackground &&
          defaultForeground == other.defaultForeground &&
          defaultBorder == other.defaultBorder &&
          secondaryBackground == other.secondaryBackground &&
          secondaryForeground == other.secondaryForeground &&
          secondaryBorder == other.secondaryBorder &&
          destructiveBackground == other.destructiveBackground &&
          destructiveForeground == other.destructiveForeground &&
          destructiveBorder == other.destructiveBorder &&
          outlineBackground == other.outlineBackground &&
          outlineForeground == other.outlineForeground &&
          outlineBorder == other.outlineBorder &&
          minWidth == other.minWidth &&
          minHeight == other.minHeight &&
          padding == other.padding &&
          borderRadius == other.borderRadius &&
          borderWidth == other.borderWidth &&
          textStyle == other.textStyle &&
          fontSize == other.fontSize &&
          fontWeight == other.fontWeight &&
          letterSpacing == other.letterSpacing &&
          animationDuration == other.animationDuration &&
          animationCurve == other.animationCurve &&
          positionOffset == other.positionOffset &&
          positionAlignment == other.positionAlignment;

  @override
  int get hashCode =>
      defaultBackground.hashCode ^
      defaultForeground.hashCode ^
      defaultBorder.hashCode ^
      secondaryBackground.hashCode ^
      secondaryForeground.hashCode ^
      secondaryBorder.hashCode ^
      destructiveBackground.hashCode ^
      destructiveForeground.hashCode ^
      destructiveBorder.hashCode ^
      outlineBackground.hashCode ^
      outlineForeground.hashCode ^
      outlineBorder.hashCode ^
      minWidth.hashCode ^
      minHeight.hashCode ^
      padding.hashCode ^
      borderRadius.hashCode ^
      borderWidth.hashCode ^
      textStyle.hashCode ^
      fontSize.hashCode ^
      fontWeight.hashCode ^
      letterSpacing.hashCode ^
      animationDuration.hashCode ^
      animationCurve.hashCode ^
      positionOffset.hashCode ^
      positionAlignment.hashCode;

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    
    properties.add(ColorProperty('defaultBackground', defaultBackground));
    properties.add(ColorProperty('defaultForeground', defaultForeground));
    properties.add(ColorProperty('defaultBorder', defaultBorder));
    properties.add(ColorProperty('secondaryBackground', secondaryBackground));
    properties.add(ColorProperty('secondaryForeground', secondaryForeground));
    properties.add(ColorProperty('secondaryBorder', secondaryBorder));
    properties.add(ColorProperty('destructiveBackground', destructiveBackground));
    properties.add(ColorProperty('destructiveForeground', destructiveForeground));
    properties.add(ColorProperty('destructiveBorder', destructiveBorder));
    properties.add(ColorProperty('outlineBackground', outlineBackground));
    properties.add(ColorProperty('outlineForeground', outlineForeground));
    properties.add(ColorProperty('outlineBorder', outlineBorder));
    properties.add(DoubleProperty('minWidth', minWidth));
    properties.add(DoubleProperty('minHeight', minHeight));
    properties.add(DiagnosticsProperty<EdgeInsets>('padding', padding));
    properties.add(DiagnosticsProperty<BorderRadius>('borderRadius', borderRadius));
    properties.add(DoubleProperty('borderWidth', borderWidth));
    properties.add(DiagnosticsProperty<TextStyle>('textStyle', textStyle));
    properties.add(DoubleProperty('fontSize', fontSize));
    properties.add(DiagnosticsProperty<FontWeight>('fontWeight', fontWeight));
    properties.add(DoubleProperty('letterSpacing', letterSpacing));
    properties.add(DiagnosticsProperty<Duration>('animationDuration', animationDuration));
    properties.add(DiagnosticsProperty<Curve>('animationCurve', animationCurve));
    properties.add(DiagnosticsProperty<Offset>('positionOffset', positionOffset));
    properties.add(DiagnosticsProperty<Alignment>('positionAlignment', positionAlignment));
  }
}

/// Variant types for [ShadcnBadge].
/// 
/// These variants follow shadcn design system patterns:
/// - [defaultVariant]: Primary color styling (default)
/// - [secondary]: Secondary color styling
/// - [destructive]: Error/destructive color styling
/// - [outline]: Outlined styling with transparent background
enum ShadcnBadgeVariant {
  /// Default variant with primary color styling
  defaultVariant,
  
  /// Secondary variant with secondary color styling
  secondary,
  
  /// Destructive variant with error color styling
  destructive,
  
  /// Outline variant with transparent background and border
  outline,
}