import 'package:flutter/material.dart';
import '../../constants/shadcn_tokens.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for the ShadcnDrawer component.
/// 
/// This theme extension provides styling properties for drawer components,
/// including background colors, header styling, content areas, and animations.
/// It follows shadcn design principles while integrating with Material Design theming.
class ShadcnDrawerTheme extends ShadcnThemeExtension<ShadcnDrawerTheme> {
  // Background colors
  final Color? backgroundColor;
  final Color? overlayColor;
  final Color? headerBackground;
  final Color? contentBackground;
  
  // Text colors
  final Color? titleColor;
  final Color? descriptionColor;
  final Color? contentColor;
  
  // Border and shape
  final Color? borderColor;
  final double? borderWidth;
  final BorderRadius? borderRadius;
  
  // Typography
  final TextStyle? titleTextStyle;
  final TextStyle? descriptionTextStyle;
  final TextStyle? contentTextStyle;
  final FontWeight? titleFontWeight;
  
  // Layout and spacing
  final EdgeInsets? contentPadding;
  final EdgeInsets? headerPadding;
  final double? width;
  final double? headerHeight;
  final double? titleSpacing;
  final double? actionSpacing;
  
  // Animation properties
  final Duration? animationDuration;
  final Curve? animationCurve;
  
  // Shadow and elevation
  final List<BoxShadow>? shadows;
  final double? elevation;
  
  // Barrier properties
  final bool? barrierDismissible;
  final Color? barrierColor;
  
  // Close button properties
  final Widget? closeIcon;
  final Color? closeIconColor;
  final double? closeIconSize;
  final EdgeInsets? closeIconPadding;
  
  // Drag handle properties (for mobile sheets)
  final bool? showDragHandle;
  final Color? dragHandleColor;
  final double? dragHandleWidth;
  final double? dragHandleHeight;
  final BorderRadius? dragHandleBorderRadius;
  
  const ShadcnDrawerTheme({
    // Background colors
    this.backgroundColor,
    this.overlayColor,
    this.headerBackground,
    this.contentBackground,
    
    // Text colors
    this.titleColor,
    this.descriptionColor,
    this.contentColor,
    
    // Border and shape
    this.borderColor,
    this.borderWidth,
    this.borderRadius,
    
    // Typography
    this.titleTextStyle,
    this.descriptionTextStyle,
    this.contentTextStyle,
    this.titleFontWeight,
    
    // Layout and spacing
    this.contentPadding,
    this.headerPadding,
    this.width,
    this.headerHeight,
    this.titleSpacing,
    this.actionSpacing,
    
    // Animation properties
    this.animationDuration,
    this.animationCurve,
    
    // Shadow and elevation
    this.shadows,
    this.elevation,
    
    // Barrier properties
    this.barrierDismissible,
    this.barrierColor,
    
    // Close button properties
    this.closeIcon,
    this.closeIconColor,
    this.closeIconSize,
    this.closeIconPadding,
    
    // Drag handle properties
    this.showDragHandle,
    this.dragHandleColor,
    this.dragHandleWidth,
    this.dragHandleHeight,
    this.dragHandleBorderRadius,
  });

  @override
  ShadcnDrawerTheme copyWith({
    // Background colors
    Color? backgroundColor,
    Color? overlayColor,
    Color? headerBackground,
    Color? contentBackground,
    
    // Text colors
    Color? titleColor,
    Color? descriptionColor,
    Color? contentColor,
    
    // Border and shape
    Color? borderColor,
    double? borderWidth,
    BorderRadius? borderRadius,
    
    // Typography
    TextStyle? titleTextStyle,
    TextStyle? descriptionTextStyle,
    TextStyle? contentTextStyle,
    FontWeight? titleFontWeight,
    
    // Layout and spacing
    EdgeInsets? contentPadding,
    EdgeInsets? headerPadding,
    double? width,
    double? headerHeight,
    double? titleSpacing,
    double? actionSpacing,
    
    // Animation properties
    Duration? animationDuration,
    Curve? animationCurve,
    
    // Shadow and elevation
    List<BoxShadow>? shadows,
    double? elevation,
    
    // Barrier properties
    bool? barrierDismissible,
    Color? barrierColor,
    
    // Close button properties
    Widget? closeIcon,
    Color? closeIconColor,
    double? closeIconSize,
    EdgeInsets? closeIconPadding,
    
    // Drag handle properties
    bool? showDragHandle,
    Color? dragHandleColor,
    double? dragHandleWidth,
    double? dragHandleHeight,
    BorderRadius? dragHandleBorderRadius,
  }) {
    return ShadcnDrawerTheme(
      // Background colors
      backgroundColor: backgroundColor ?? this.backgroundColor,
      overlayColor: overlayColor ?? this.overlayColor,
      headerBackground: headerBackground ?? this.headerBackground,
      contentBackground: contentBackground ?? this.contentBackground,
      
      // Text colors
      titleColor: titleColor ?? this.titleColor,
      descriptionColor: descriptionColor ?? this.descriptionColor,
      contentColor: contentColor ?? this.contentColor,
      
      // Border and shape
      borderColor: borderColor ?? this.borderColor,
      borderWidth: borderWidth ?? this.borderWidth,
      borderRadius: borderRadius ?? this.borderRadius,
      
      // Typography
      titleTextStyle: titleTextStyle ?? this.titleTextStyle,
      descriptionTextStyle: descriptionTextStyle ?? this.descriptionTextStyle,
      contentTextStyle: contentTextStyle ?? this.contentTextStyle,
      titleFontWeight: titleFontWeight ?? this.titleFontWeight,
      
      // Layout and spacing
      contentPadding: contentPadding ?? this.contentPadding,
      headerPadding: headerPadding ?? this.headerPadding,
      width: width ?? this.width,
      headerHeight: headerHeight ?? this.headerHeight,
      titleSpacing: titleSpacing ?? this.titleSpacing,
      actionSpacing: actionSpacing ?? this.actionSpacing,
      
      // Animation properties
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
      
      // Shadow and elevation
      shadows: shadows ?? this.shadows,
      elevation: elevation ?? this.elevation,
      
      // Barrier properties
      barrierDismissible: barrierDismissible ?? this.barrierDismissible,
      barrierColor: barrierColor ?? this.barrierColor,
      
      // Close button properties
      closeIcon: closeIcon ?? this.closeIcon,
      closeIconColor: closeIconColor ?? this.closeIconColor,
      closeIconSize: closeIconSize ?? this.closeIconSize,
      closeIconPadding: closeIconPadding ?? this.closeIconPadding,
      
      // Drag handle properties
      showDragHandle: showDragHandle ?? this.showDragHandle,
      dragHandleColor: dragHandleColor ?? this.dragHandleColor,
      dragHandleWidth: dragHandleWidth ?? this.dragHandleWidth,
      dragHandleHeight: dragHandleHeight ?? this.dragHandleHeight,
      dragHandleBorderRadius: dragHandleBorderRadius ?? this.dragHandleBorderRadius,
    );
  }

  @override
  ShadcnDrawerTheme lerp(ShadcnDrawerTheme? other, double t) {
    if (other is! ShadcnDrawerTheme) return this;
    
    return ShadcnDrawerTheme(
      // Background colors
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t),
      overlayColor: Color.lerp(overlayColor, other.overlayColor, t),
      headerBackground: Color.lerp(headerBackground, other.headerBackground, t),
      contentBackground: Color.lerp(contentBackground, other.contentBackground, t),
      
      // Text colors
      titleColor: Color.lerp(titleColor, other.titleColor, t),
      descriptionColor: Color.lerp(descriptionColor, other.descriptionColor, t),
      contentColor: Color.lerp(contentColor, other.contentColor, t),
      
      // Border and shape
      borderColor: Color.lerp(borderColor, other.borderColor, t),
      borderWidth: lerpDouble(borderWidth, other.borderWidth, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      
      // Typography - text styles don't lerp well, so we use threshold
      titleTextStyle: t < 0.5 ? titleTextStyle : other.titleTextStyle,
      descriptionTextStyle: t < 0.5 ? descriptionTextStyle : other.descriptionTextStyle,
      contentTextStyle: t < 0.5 ? contentTextStyle : other.contentTextStyle,
      titleFontWeight: t < 0.5 ? titleFontWeight : other.titleFontWeight,
      
      // Layout and spacing
      contentPadding: EdgeInsets.lerp(contentPadding, other.contentPadding, t),
      headerPadding: EdgeInsets.lerp(headerPadding, other.headerPadding, t),
      width: lerpDouble(width, other.width, t),
      headerHeight: lerpDouble(headerHeight, other.headerHeight, t),
      titleSpacing: lerpDouble(titleSpacing, other.titleSpacing, t),
      actionSpacing: lerpDouble(actionSpacing, other.actionSpacing, t),
      
      // Animation properties
      animationDuration: lerpDuration(animationDuration ?? Duration.zero, other.animationDuration ?? Duration.zero, t),
      animationCurve: t < 0.5 ? animationCurve : other.animationCurve,
      
      // Shadow and elevation
      shadows: t < 0.5 ? shadows : other.shadows, // BoxShadow lists don't lerp easily
      elevation: lerpDouble(elevation, other.elevation, t),
      
      // Barrier properties
      barrierDismissible: t < 0.5 ? barrierDismissible : other.barrierDismissible,
      barrierColor: Color.lerp(barrierColor, other.barrierColor, t),
      
      // Close button properties
      closeIcon: t < 0.5 ? closeIcon : other.closeIcon,
      closeIconColor: Color.lerp(closeIconColor, other.closeIconColor, t),
      closeIconSize: lerpDouble(closeIconSize, other.closeIconSize, t),
      closeIconPadding: EdgeInsets.lerp(closeIconPadding, other.closeIconPadding, t),
      
      // Drag handle properties
      showDragHandle: t < 0.5 ? showDragHandle : other.showDragHandle,
      dragHandleColor: Color.lerp(dragHandleColor, other.dragHandleColor, t),
      dragHandleWidth: lerpDouble(dragHandleWidth, other.dragHandleWidth, t),
      dragHandleHeight: lerpDouble(dragHandleHeight, other.dragHandleHeight, t),
      dragHandleBorderRadius: BorderRadius.lerp(dragHandleBorderRadius, other.dragHandleBorderRadius, t),
    );
  }

  /// Creates a default theme based on the provided color scheme.
  /// 
  /// This factory constructor provides sensible defaults that follow
  /// shadcn design principles while integrating with Material Design.
  static ShadcnDrawerTheme defaultTheme(ColorScheme colorScheme) {
    return ShadcnDrawerTheme(
      // Background colors
      backgroundColor: colorScheme.surface,
      overlayColor: Colors.black.withOpacity(0.5),
      headerBackground: colorScheme.surface,
      contentBackground: colorScheme.surface,
      
      // Text colors
      titleColor: colorScheme.onSurface,
      descriptionColor: colorScheme.onSurface.withOpacity(0.7),
      contentColor: colorScheme.onSurface,
      
      // Border and shape
      borderColor: colorScheme.outline,
      borderWidth: ShadcnTokens.borderWidth,
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusLg),
      
      // Typography
      titleTextStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeLg,
        fontWeight: ShadcnTokens.fontWeightSemibold,
      ),
      descriptionTextStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightNormal,
      ),
      contentTextStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightNormal,
      ),
      titleFontWeight: ShadcnTokens.fontWeightSemibold,
      
      // Layout and spacing
      contentPadding: const EdgeInsets.all(ShadcnTokens.spacing6),
      headerPadding: const EdgeInsets.all(ShadcnTokens.spacing4),
      width: 400.0,
      headerHeight: 60.0,
      titleSpacing: ShadcnTokens.spacing2,
      actionSpacing: ShadcnTokens.spacing2,
      
      // Animation properties
      animationDuration: ShadcnTokens.durationNormal,
      animationCurve: Curves.easeInOut,
      
      // Shadow and elevation
      elevation: ShadcnTokens.elevationLg,
      
      // Barrier properties
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.5),
      
      // Close button properties
      closeIcon: const Icon(Icons.close),
      closeIconColor: colorScheme.onSurface,
      closeIconSize: ShadcnTokens.iconSizeMd,
      closeIconPadding: const EdgeInsets.all(ShadcnTokens.spacing2),
      
      // Drag handle properties (for mobile sheets)
      showDragHandle: false,
      dragHandleColor: colorScheme.onSurface.withOpacity(0.4),
      dragHandleWidth: 32.0,
      dragHandleHeight: 4.0,
      dragHandleBorderRadius: BorderRadius.circular(2.0),
    );
  }

  @override
  bool validate({bool throwOnError = false}) {
    // Validate required properties
    if (animationDuration != null && animationDuration!.isNegative) {
      if (throwOnError) {
        throw ArgumentError('animationDuration cannot be negative');
      }
      return false;
    }
    
    if (width != null && width! <= 0) {
      if (throwOnError) {
        throw ArgumentError('width must be positive');
      }
      return false;
    }
    
    if (elevation != null && elevation! < 0) {
      if (throwOnError) {
        throw ArgumentError('elevation cannot be negative');
      }
      return false;
    }
    
    return true;
  }
}

/// Helper function to lerp Duration values
Duration lerpDuration(Duration a, Duration b, double t) {
  return Duration(
    microseconds: (a.inMicroseconds + ((b.inMicroseconds - a.inMicroseconds) * t)).round(),
  );
}

/// Helper function to lerp double values with null safety
double? lerpDouble(double? a, double? b, double t) {
  if (a == null && b == null) return null;
  a ??= 0.0;
  b ??= 0.0;
  return a + (b - a) * t;
}