import 'dart:ui' show lerpDouble;

import 'package:flutter/material.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for [ShadcnResizable] component.
/// 
/// This theme controls the visual appearance of resizable components including
/// resize handles, dividers, and interaction feedback.
class ShadcnResizableTheme extends ShadcnThemeExtension<ShadcnResizableTheme> {
  /// Background color for the resize handle
  final Color? handleBackground;
  
  /// Hover color for the resize handle
  final Color? handleHover;
  
  /// Active/pressed color for the resize handle
  final Color? handleActive;
  
  /// Width of the resize handle
  final double? handleWidth;
  
  /// Height of the resize handle (for horizontal dividers)
  final double? handleHeight;
  
  /// Border radius for the resize handle
  final BorderRadius? handleBorderRadius;
  
  /// Color of the resize divider line
  final Color? dividerColor;
  
  /// Width/thickness of the resize divider
  final double? dividerWidth;
  
  /// Opacity of the divider when not active
  final double? dividerOpacity;
  
  /// Opacity of the divider when hovering
  final double? dividerHoverOpacity;
  
  /// Color of the resize indicator dots
  final Color? indicatorColor;
  
  /// Size of the resize indicator dots
  final double? indicatorSize;
  
  /// Spacing between indicator dots
  final double? indicatorSpacing;
  
  /// Animation duration for handle state changes
  final Duration? animationDuration;
  
  /// Animation curve for transitions
  final Curve? animationCurve;
  
  /// Shadow for the resize handle
  final List<BoxShadow>? handleShadow;

  const ShadcnResizableTheme({
    this.handleBackground,
    this.handleHover,
    this.handleActive,
    this.handleWidth,
    this.handleHeight,
    this.handleBorderRadius,
    this.dividerColor,
    this.dividerWidth,
    this.dividerOpacity,
    this.dividerHoverOpacity,
    this.indicatorColor,
    this.indicatorSize,
    this.indicatorSpacing,
    this.animationDuration,
    this.animationCurve,
    this.handleShadow,
  });

  @override
  ShadcnResizableTheme copyWith({
    Color? handleBackground,
    Color? handleHover,
    Color? handleActive,
    double? handleWidth,
    double? handleHeight,
    BorderRadius? handleBorderRadius,
    Color? dividerColor,
    double? dividerWidth,
    double? dividerOpacity,
    double? dividerHoverOpacity,
    Color? indicatorColor,
    double? indicatorSize,
    double? indicatorSpacing,
    Duration? animationDuration,
    Curve? animationCurve,
    List<BoxShadow>? handleShadow,
  }) {
    return ShadcnResizableTheme(
      handleBackground: handleBackground ?? this.handleBackground,
      handleHover: handleHover ?? this.handleHover,
      handleActive: handleActive ?? this.handleActive,
      handleWidth: handleWidth ?? this.handleWidth,
      handleHeight: handleHeight ?? this.handleHeight,
      handleBorderRadius: handleBorderRadius ?? this.handleBorderRadius,
      dividerColor: dividerColor ?? this.dividerColor,
      dividerWidth: dividerWidth ?? this.dividerWidth,
      dividerOpacity: dividerOpacity ?? this.dividerOpacity,
      dividerHoverOpacity: dividerHoverOpacity ?? this.dividerHoverOpacity,
      indicatorColor: indicatorColor ?? this.indicatorColor,
      indicatorSize: indicatorSize ?? this.indicatorSize,
      indicatorSpacing: indicatorSpacing ?? this.indicatorSpacing,
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
      handleShadow: handleShadow ?? this.handleShadow,
    );
  }

  @override
  ShadcnResizableTheme lerp(ShadcnResizableTheme? other, double t) {
    if (other == null) return this;

    return ShadcnResizableTheme(
      handleBackground: Color.lerp(handleBackground, other.handleBackground, t),
      handleHover: Color.lerp(handleHover, other.handleHover, t),
      handleActive: Color.lerp(handleActive, other.handleActive, t),
      handleWidth: lerpDouble(handleWidth, other.handleWidth, t),
      handleHeight: lerpDouble(handleHeight, other.handleHeight, t),
      handleBorderRadius: BorderRadius.lerp(handleBorderRadius, other.handleBorderRadius, t),
      dividerColor: Color.lerp(dividerColor, other.dividerColor, t),
      dividerWidth: lerpDouble(dividerWidth, other.dividerWidth, t),
      dividerOpacity: lerpDouble(dividerOpacity, other.dividerOpacity, t),
      dividerHoverOpacity: lerpDouble(dividerHoverOpacity, other.dividerHoverOpacity, t),
      indicatorColor: Color.lerp(indicatorColor, other.indicatorColor, t),
      indicatorSize: lerpDouble(indicatorSize, other.indicatorSize, t),
      indicatorSpacing: lerpDouble(indicatorSpacing, other.indicatorSpacing, t),
      animationDuration: t < 0.5 ? animationDuration : other.animationDuration,
      animationCurve: t < 0.5 ? animationCurve : other.animationCurve,
      handleShadow: BoxShadow.lerpList(handleShadow, other.handleShadow, t),
    );
  }

  /// Creates a default resizable theme based on the provided [ColorScheme].
  static ShadcnResizableTheme defaultTheme(ColorScheme colorScheme) {
    return ShadcnResizableTheme(
      handleBackground: colorScheme.surface,
      handleHover: colorScheme.primary.withOpacity(0.1),
      handleActive: colorScheme.primary.withOpacity(0.2),
      handleWidth: 4.0,
      handleHeight: 4.0,
      handleBorderRadius: BorderRadius.circular(2.0),
      dividerColor: colorScheme.outline,
      dividerWidth: 1.0,
      dividerOpacity: 0.3,
      dividerHoverOpacity: 0.6,
      indicatorColor: colorScheme.onSurface.withOpacity(0.4),
      indicatorSize: 2.0,
      indicatorSpacing: 2.0,
      animationDuration: const Duration(milliseconds: 150),
      animationCurve: Curves.easeInOut,
      handleShadow: [
        BoxShadow(
          color: colorScheme.shadow.withOpacity(0.1),
          offset: const Offset(0, 1),
          blurRadius: 2,
        ),
      ],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other.runtimeType != runtimeType) return false;
    return other is ShadcnResizableTheme &&
        other.handleBackground == handleBackground &&
        other.handleHover == handleHover &&
        other.handleActive == handleActive &&
        other.handleWidth == handleWidth &&
        other.handleHeight == handleHeight &&
        other.handleBorderRadius == handleBorderRadius &&
        other.dividerColor == dividerColor &&
        other.dividerWidth == dividerWidth &&
        other.dividerOpacity == dividerOpacity &&
        other.dividerHoverOpacity == dividerHoverOpacity &&
        other.indicatorColor == indicatorColor &&
        other.indicatorSize == indicatorSize &&
        other.indicatorSpacing == indicatorSpacing &&
        other.animationDuration == animationDuration &&
        other.animationCurve == animationCurve &&
        other.handleShadow == handleShadow;
  }

  @override
  int get hashCode {
    return Object.hash(
      handleBackground,
      handleHover,
      handleActive,
      handleWidth,
      handleHeight,
      handleBorderRadius,
      dividerColor,
      dividerWidth,
      dividerOpacity,
      dividerHoverOpacity,
      indicatorColor,
      indicatorSize,
      indicatorSpacing,
      animationDuration,
      animationCurve,
      handleShadow,
    );
  }
}