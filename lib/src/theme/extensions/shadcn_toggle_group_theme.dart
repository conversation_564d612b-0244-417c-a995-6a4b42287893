import 'package:flutter/material.dart';
import '../../constants/shadcn_tokens.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for the ShadcnToggleGroup component.
class ShadcnToggleGroupTheme extends ShadcnThemeExtension<ShadcnToggleGroupTheme> {
  final Color? backgroundColor;
  final Color? foregroundColor;
  final Color? selectedBackgroundColor;
  final Color? selectedForegroundColor;
  final Color? borderColor;
  final double? itemSpacing;
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;
  final double? borderWidth;
  final TextStyle? textStyle;

  const ShadcnToggleGroupTheme({
    this.backgroundColor,
    this.foregroundColor,
    this.selectedBackgroundColor,
    this.selectedForegroundColor,
    this.borderColor,
    this.itemSpacing,
    this.padding,
    this.borderRadius,
    this.borderWidth,
    this.textStyle,
  });

  @override
  ShadcnToggleGroupTheme copyWith({
    Color? backgroundColor,
    Color? foregroundColor,
    Color? selectedBackgroundColor,
    Color? selectedForegroundColor,
    Color? borderColor,
    double? itemSpacing,
    EdgeInsets? padding,
    BorderRadius? borderRadius,
    double? borderWidth,
    TextStyle? textStyle,
  }) {
    return ShadcnToggleGroupTheme(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      foregroundColor: foregroundColor ?? this.foregroundColor,
      selectedBackgroundColor: selectedBackgroundColor ?? this.selectedBackgroundColor,
      selectedForegroundColor: selectedForegroundColor ?? this.selectedForegroundColor,
      borderColor: borderColor ?? this.borderColor,
      itemSpacing: itemSpacing ?? this.itemSpacing,
      padding: padding ?? this.padding,
      borderRadius: borderRadius ?? this.borderRadius,
      borderWidth: borderWidth ?? this.borderWidth,
      textStyle: textStyle ?? this.textStyle,
    );
  }

  @override
  ShadcnToggleGroupTheme lerp(ShadcnToggleGroupTheme? other, double t) {
    if (other is! ShadcnToggleGroupTheme) return this;
    return ShadcnToggleGroupTheme(
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t),
      foregroundColor: Color.lerp(foregroundColor, other.foregroundColor, t),
      selectedBackgroundColor: Color.lerp(selectedBackgroundColor, other.selectedBackgroundColor, t),
      selectedForegroundColor: Color.lerp(selectedForegroundColor, other.selectedForegroundColor, t),
      borderColor: Color.lerp(borderColor, other.borderColor, t),
      itemSpacing: lerpDouble(itemSpacing, other.itemSpacing, t),
      padding: EdgeInsets.lerp(padding, other.padding, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      borderWidth: lerpDouble(borderWidth, other.borderWidth, t),
      textStyle: t < 0.5 ? textStyle : other.textStyle,
    );
  }

  static ShadcnToggleGroupTheme defaultTheme(ColorScheme colorScheme) {
    return ShadcnToggleGroupTheme(
      backgroundColor: colorScheme.surface,
      foregroundColor: colorScheme.onSurface,
      selectedBackgroundColor: colorScheme.primary,
      selectedForegroundColor: colorScheme.onPrimary,
      borderColor: colorScheme.outline,
      itemSpacing: 0.0,
      padding: const EdgeInsets.all(ShadcnTokens.spacing1),
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusMd),
      borderWidth: ShadcnTokens.borderWidth,
      textStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightMedium,
      ),
    );
  }
}

double? lerpDouble(double? a, double? b, double t) {
  if (a == null && b == null) return null;
  a ??= 0.0;
  b ??= 0.0;
  return a + (b - a) * t;
}