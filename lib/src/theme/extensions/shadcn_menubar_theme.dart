import 'package:flutter/material.dart';
import '../../constants/shadcn_tokens.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for the ShadcnMenubar component.
class ShadcnMenubarTheme extends ShadcnThemeExtension<ShadcnMenubarTheme> {
  final Color? backgroundColor;
  final Color? foregroundColor;
  final Color? borderColor;
  final Color? hoverColor;
  final Color? selectedColor;
  final Color? selectedForegroundColor;
  final double? height;
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;
  final double? borderWidth;
  final TextStyle? textStyle;
  final double? itemSpacing;
  final EdgeInsets? itemPadding;
  final Duration? animationDuration;
  final Curve? animationCurve;

  const ShadcnMenubarTheme({
    this.backgroundColor,
    this.foregroundColor,
    this.borderColor,
    this.hoverColor,
    this.selectedColor,
    this.selectedForegroundColor,
    this.height,
    this.padding,
    this.borderRadius,
    this.borderWidth,
    this.textStyle,
    this.itemSpacing,
    this.itemPadding,
    this.animationDuration,
    this.animationCurve,
  });

  @override
  ShadcnMenubarTheme copyWith({
    Color? backgroundColor,
    Color? foregroundColor,
    Color? borderColor,
    Color? hoverColor,
    Color? selectedColor,
    Color? selectedForegroundColor,
    double? height,
    EdgeInsets? padding,
    BorderRadius? borderRadius,
    double? borderWidth,
    TextStyle? textStyle,
    double? itemSpacing,
    EdgeInsets? itemPadding,
    Duration? animationDuration,
    Curve? animationCurve,
  }) {
    return ShadcnMenubarTheme(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      foregroundColor: foregroundColor ?? this.foregroundColor,
      borderColor: borderColor ?? this.borderColor,
      hoverColor: hoverColor ?? this.hoverColor,
      selectedColor: selectedColor ?? this.selectedColor,
      selectedForegroundColor: selectedForegroundColor ?? this.selectedForegroundColor,
      height: height ?? this.height,
      padding: padding ?? this.padding,
      borderRadius: borderRadius ?? this.borderRadius,
      borderWidth: borderWidth ?? this.borderWidth,
      textStyle: textStyle ?? this.textStyle,
      itemSpacing: itemSpacing ?? this.itemSpacing,
      itemPadding: itemPadding ?? this.itemPadding,
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
    );
  }

  @override
  ShadcnMenubarTheme lerp(ShadcnMenubarTheme? other, double t) {
    if (other is! ShadcnMenubarTheme) return this;
    return ShadcnMenubarTheme(
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t),
      foregroundColor: Color.lerp(foregroundColor, other.foregroundColor, t),
      borderColor: Color.lerp(borderColor, other.borderColor, t),
      hoverColor: Color.lerp(hoverColor, other.hoverColor, t),
      selectedColor: Color.lerp(selectedColor, other.selectedColor, t),
      selectedForegroundColor: Color.lerp(selectedForegroundColor, other.selectedForegroundColor, t),
      height: lerpDouble(height, other.height, t),
      padding: EdgeInsets.lerp(padding, other.padding, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      borderWidth: lerpDouble(borderWidth, other.borderWidth, t),
      textStyle: t < 0.5 ? textStyle : other.textStyle,
      itemSpacing: lerpDouble(itemSpacing, other.itemSpacing, t),
      itemPadding: EdgeInsets.lerp(itemPadding, other.itemPadding, t),
      animationDuration: lerpDuration(animationDuration ?? Duration.zero, other.animationDuration ?? Duration.zero, t),
      animationCurve: t < 0.5 ? animationCurve : other.animationCurve,
    );
  }

  static ShadcnMenubarTheme defaultTheme(ColorScheme colorScheme) {
    return ShadcnMenubarTheme(
      backgroundColor: colorScheme.surface,
      foregroundColor: colorScheme.onSurface,
      borderColor: colorScheme.outline,
      hoverColor: colorScheme.onSurface.withOpacity(0.04),
      selectedColor: colorScheme.primary,
      selectedForegroundColor: colorScheme.onPrimary,
      height: 44.0,
      padding: const EdgeInsets.all(ShadcnTokens.spacing2),
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusMd),
      borderWidth: ShadcnTokens.borderWidth,
      textStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightMedium,
      ),
      itemSpacing: ShadcnTokens.spacing1,
      itemPadding: const EdgeInsets.symmetric(
        horizontal: ShadcnTokens.spacing3,
        vertical: ShadcnTokens.spacing2,
      ),
      animationDuration: ShadcnTokens.durationFast,
      animationCurve: Curves.easeInOut,
    );
  }
}

// Helper functions
Duration lerpDuration(Duration a, Duration b, double t) {
  return Duration(
    microseconds: (a.inMicroseconds + ((b.inMicroseconds - a.inMicroseconds) * t)).round(),
  );
}

double? lerpDouble(double? a, double? b, double t) {
  if (a == null && b == null) return null;
  a ??= 0.0;
  b ??= 0.0;
  return a + (b - a) * t;
}