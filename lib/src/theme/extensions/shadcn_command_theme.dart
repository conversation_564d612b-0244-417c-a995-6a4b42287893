import 'package:flutter/material.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for ShadcnCommand component styling
/// 
/// Defines styling properties for command palette components including search input,
/// command groups, command items, and keyboard shortcut indicators.
class ShadcnCommandTheme extends ShadcnThemeExtension<ShadcnCommandTheme> {
  /// Background color of the command palette container
  final Color? background;
  
  /// Foreground text color for command palette
  final Color? foreground;
  
  /// Background color of the search input
  final Color? searchBackground;
  
  /// Border color of the search input
  final Color? searchBorder;
  
  /// Text color of search placeholder
  final Color? searchPlaceholder;
  
  /// Background color of command groups
  final Color? groupBackground;
  
  /// Text color of command group headers
  final Color? groupForeground;
  
  /// Background color of command items
  final Color? itemBackground;
  
  /// Text color of command items
  final Color? itemForeground;
  
  /// Background color of hovered command items
  final Color? itemHoverBackground;
  
  /// Text color of hovered command items
  final Color? itemHoverForeground;
  
  /// Background color of selected command items
  final Color? itemSelectedBackground;
  
  /// Text color of selected command items
  final Color? itemSelectedForeground;
  
  /// Color of keyboard shortcut indicators
  final Color? shortcutForeground;
  
  /// Background color of keyboard shortcut indicators
  final Color? shortcutBackground;
  
  /// Border color of the command palette
  final Color? border;
  
  /// Border radius of the command palette
  final BorderRadius? borderRadius;
  
  /// Maximum height of the command palette
  final double? maxHeight;
  
  /// Maximum width of the command palette
  final double? maxWidth;
  
  /// Height of individual command items
  final double? itemHeight;
  
  /// Padding for command items
  final EdgeInsets? itemPadding;
  
  /// Padding for command groups
  final EdgeInsets? groupPadding;
  
  /// Padding for the search input
  final EdgeInsets? searchPadding;
  
  /// Text style for command items
  final TextStyle? itemTextStyle;
  
  /// Text style for command group headers
  final TextStyle? groupTextStyle;
  
  /// Text style for keyboard shortcuts
  final TextStyle? shortcutTextStyle;
  
  /// Text style for search input
  final TextStyle? searchTextStyle;
  
  /// Shadow for the command palette
  final List<BoxShadow>? shadow;

  const ShadcnCommandTheme({
    this.background,
    this.foreground,
    this.searchBackground,
    this.searchBorder,
    this.searchPlaceholder,
    this.groupBackground,
    this.groupForeground,
    this.itemBackground,
    this.itemForeground,
    this.itemHoverBackground,
    this.itemHoverForeground,
    this.itemSelectedBackground,
    this.itemSelectedForeground,
    this.shortcutForeground,
    this.shortcutBackground,
    this.border,
    this.borderRadius,
    this.maxHeight,
    this.maxWidth,
    this.itemHeight,
    this.itemPadding,
    this.groupPadding,
    this.searchPadding,
    this.itemTextStyle,
    this.groupTextStyle,
    this.shortcutTextStyle,
    this.searchTextStyle,
    this.shadow,
  });

  @override
  ShadcnCommandTheme copyWith({
    Color? background,
    Color? foreground,
    Color? searchBackground,
    Color? searchBorder,
    Color? searchPlaceholder,
    Color? groupBackground,
    Color? groupForeground,
    Color? itemBackground,
    Color? itemForeground,
    Color? itemHoverBackground,
    Color? itemHoverForeground,
    Color? itemSelectedBackground,
    Color? itemSelectedForeground,
    Color? shortcutForeground,
    Color? shortcutBackground,
    Color? border,
    BorderRadius? borderRadius,
    double? maxHeight,
    double? maxWidth,
    double? itemHeight,
    EdgeInsets? itemPadding,
    EdgeInsets? groupPadding,
    EdgeInsets? searchPadding,
    TextStyle? itemTextStyle,
    TextStyle? groupTextStyle,
    TextStyle? shortcutTextStyle,
    TextStyle? searchTextStyle,
    List<BoxShadow>? shadow,
  }) {
    return ShadcnCommandTheme(
      background: background ?? this.background,
      foreground: foreground ?? this.foreground,
      searchBackground: searchBackground ?? this.searchBackground,
      searchBorder: searchBorder ?? this.searchBorder,
      searchPlaceholder: searchPlaceholder ?? this.searchPlaceholder,
      groupBackground: groupBackground ?? this.groupBackground,
      groupForeground: groupForeground ?? this.groupForeground,
      itemBackground: itemBackground ?? this.itemBackground,
      itemForeground: itemForeground ?? this.itemForeground,
      itemHoverBackground: itemHoverBackground ?? this.itemHoverBackground,
      itemHoverForeground: itemHoverForeground ?? this.itemHoverForeground,
      itemSelectedBackground: itemSelectedBackground ?? this.itemSelectedBackground,
      itemSelectedForeground: itemSelectedForeground ?? this.itemSelectedForeground,
      shortcutForeground: shortcutForeground ?? this.shortcutForeground,
      shortcutBackground: shortcutBackground ?? this.shortcutBackground,
      border: border ?? this.border,
      borderRadius: borderRadius ?? this.borderRadius,
      maxHeight: maxHeight ?? this.maxHeight,
      maxWidth: maxWidth ?? this.maxWidth,
      itemHeight: itemHeight ?? this.itemHeight,
      itemPadding: itemPadding ?? this.itemPadding,
      groupPadding: groupPadding ?? this.groupPadding,
      searchPadding: searchPadding ?? this.searchPadding,
      itemTextStyle: itemTextStyle ?? this.itemTextStyle,
      groupTextStyle: groupTextStyle ?? this.groupTextStyle,
      shortcutTextStyle: shortcutTextStyle ?? this.shortcutTextStyle,
      searchTextStyle: searchTextStyle ?? this.searchTextStyle,
      shadow: shadow ?? this.shadow,
    );
  }

  @override
  ShadcnCommandTheme lerp(ThemeExtension<ShadcnCommandTheme>? other, double t) {
    if (other is! ShadcnCommandTheme) return this;

    return ShadcnCommandTheme(
      background: Color.lerp(background, other.background, t),
      foreground: Color.lerp(foreground, other.foreground, t),
      searchBackground: Color.lerp(searchBackground, other.searchBackground, t),
      searchBorder: Color.lerp(searchBorder, other.searchBorder, t),
      searchPlaceholder: Color.lerp(searchPlaceholder, other.searchPlaceholder, t),
      groupBackground: Color.lerp(groupBackground, other.groupBackground, t),
      groupForeground: Color.lerp(groupForeground, other.groupForeground, t),
      itemBackground: Color.lerp(itemBackground, other.itemBackground, t),
      itemForeground: Color.lerp(itemForeground, other.itemForeground, t),
      itemHoverBackground: Color.lerp(itemHoverBackground, other.itemHoverBackground, t),
      itemHoverForeground: Color.lerp(itemHoverForeground, other.itemHoverForeground, t),
      itemSelectedBackground: Color.lerp(itemSelectedBackground, other.itemSelectedBackground, t),
      itemSelectedForeground: Color.lerp(itemSelectedForeground, other.itemSelectedForeground, t),
      shortcutForeground: Color.lerp(shortcutForeground, other.shortcutForeground, t),
      shortcutBackground: Color.lerp(shortcutBackground, other.shortcutBackground, t),
      border: Color.lerp(border, other.border, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      maxHeight: lerpDouble(maxHeight, other.maxHeight, t),
      maxWidth: lerpDouble(maxWidth, other.maxWidth, t),
      itemHeight: lerpDouble(itemHeight, other.itemHeight, t),
      itemPadding: EdgeInsets.lerp(itemPadding, other.itemPadding, t),
      groupPadding: EdgeInsets.lerp(groupPadding, other.groupPadding, t),
      searchPadding: EdgeInsets.lerp(searchPadding, other.searchPadding, t),
      itemTextStyle: TextStyle.lerp(itemTextStyle, other.itemTextStyle, t),
      groupTextStyle: TextStyle.lerp(groupTextStyle, other.groupTextStyle, t),
      shortcutTextStyle: TextStyle.lerp(shortcutTextStyle, other.shortcutTextStyle, t),
      searchTextStyle: TextStyle.lerp(searchTextStyle, other.searchTextStyle, t),
      shadow: BoxShadow.lerpList(shadow, other.shadow, t),
    );
  }

  /// Default theme based on shadcn design tokens
  static ShadcnCommandTheme defaultTheme(ColorScheme colorScheme) {
    final isDark = colorScheme.brightness == Brightness.dark;
    
    return ShadcnCommandTheme(
      background: isDark ? const Color(0xFF09090B) : const Color(0xFFFFFFFF),
      foreground: isDark ? const Color(0xFFFAFAFA) : const Color(0xFF09090B),
      searchBackground: isDark ? const Color(0xFF18181B) : const Color(0xFFF4F4F5),
      searchBorder: isDark ? const Color(0xFF27272A) : const Color(0xFFE4E4E7),
      searchPlaceholder: isDark ? const Color(0xFF71717A) : const Color(0xFF71717A),
      groupBackground: Colors.transparent,
      groupForeground: isDark ? const Color(0xFFA1A1AA) : const Color(0xFF71717A),
      itemBackground: Colors.transparent,
      itemForeground: isDark ? const Color(0xFFFAFAFA) : const Color(0xFF09090B),
      itemHoverBackground: isDark ? const Color(0xFF18181B) : const Color(0xFFF4F4F5),
      itemHoverForeground: isDark ? const Color(0xFFFAFAFA) : const Color(0xFF09090B),
      itemSelectedBackground: isDark ? const Color(0xFF3F3F46) : const Color(0xFFE4E4E7),
      itemSelectedForeground: isDark ? const Color(0xFFFAFAFA) : const Color(0xFF09090B),
      shortcutForeground: isDark ? const Color(0xFF71717A) : const Color(0xFF71717A),
      shortcutBackground: isDark ? const Color(0xFF27272A) : const Color(0xFFF4F4F5),
      border: isDark ? const Color(0xFF27272A) : const Color(0xFFE4E4E7),
      borderRadius: BorderRadius.circular(8),
      maxHeight: 400,
      maxWidth: 640,
      itemHeight: 48,
      itemPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      groupPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      searchPadding: const EdgeInsets.all(12),
      itemTextStyle: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: isDark ? const Color(0xFFFAFAFA) : const Color(0xFF09090B),
      ),
      groupTextStyle: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w600,
        color: isDark ? const Color(0xFFA1A1AA) : const Color(0xFF71717A),
      ),
      shortcutTextStyle: TextStyle(
        fontSize: 11,
        fontWeight: FontWeight.w400,
        color: isDark ? const Color(0xFF71717A) : const Color(0xFF71717A),
      ),
      searchTextStyle: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: isDark ? const Color(0xFFFAFAFA) : const Color(0xFF09090B),
      ),
      shadow: [
        BoxShadow(
          color: isDark ? const Color(0x40000000) : const Color(0x0F000000),
          offset: const Offset(0, 4),
          blurRadius: 16,
          spreadRadius: -2,
        ),
      ],
    );
  }

  double lerpDouble(double? a, double? b, double t) {
    if (a == null && b == null) return 0.0;
    if (a == null) return b! * t;
    if (b == null) return a * (1.0 - t);
    return a + (b - a) * t;
  }
}