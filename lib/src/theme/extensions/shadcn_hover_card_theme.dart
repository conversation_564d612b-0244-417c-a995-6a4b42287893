import 'package:flutter/material.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for ShadcnHoverCard component styling
/// 
/// Defines styling properties for hover card components including card containers,
/// content areas, and positioning behavior.
class ShadcnHoverCardTheme extends ShadcnThemeExtension<ShadcnHoverCardTheme> {
  /// Background color of the hover card container
  final Color? background;
  
  /// Border color of the hover card
  final Color? border;
  
  /// Text color for hover card content
  final Color? foreground;
  
  /// Border radius of the hover card
  final BorderRadius? borderRadius;
  
  /// Maximum width of the hover card
  final double? maxWidth;
  
  /// Maximum height of the hover card
  final double? maxHeight;
  
  /// Minimum width of the hover card
  final double? minWidth;
  
  /// Minimum height of the hover card
  final double? minHeight;
  
  /// Padding for the hover card content
  final EdgeInsets? padding;
  
  /// Margin for the hover card
  final EdgeInsets? margin;
  
  /// Text style for hover card content
  final TextStyle? textStyle;
  
  /// Shadow for the hover card
  final List<BoxShadow>? shadow;
  
  /// Elevation of the hover card
  final double? elevation;
  
  /// Duration for hover delay before showing
  final Duration? hoverDelay;
  
  /// Duration for hover delay before hiding
  final Duration? hideDelay;
  
  /// Duration for show animation
  final Duration? showDuration;
  
  /// Duration for hide animation
  final Duration? hideDuration;
  
  /// Animation curve for showing
  final Curve? showCurve;
  
  /// Animation curve for hiding
  final Curve? hideCurve;
  
  /// Offset from the trigger widget
  final Offset? offset;
  
  /// Whether to show an arrow pointing to the trigger
  final bool? showArrow;
  
  /// Size of the arrow
  final double? arrowSize;
  
  /// Color of the arrow
  final Color? arrowColor;

  const ShadcnHoverCardTheme({
    this.background,
    this.border,
    this.foreground,
    this.borderRadius,
    this.maxWidth,
    this.maxHeight,
    this.minWidth,
    this.minHeight,
    this.padding,
    this.margin,
    this.textStyle,
    this.shadow,
    this.elevation,
    this.hoverDelay,
    this.hideDelay,
    this.showDuration,
    this.hideDuration,
    this.showCurve,
    this.hideCurve,
    this.offset,
    this.showArrow,
    this.arrowSize,
    this.arrowColor,
  });

  @override
  ShadcnHoverCardTheme copyWith({
    Color? background,
    Color? border,
    Color? foreground,
    BorderRadius? borderRadius,
    double? maxWidth,
    double? maxHeight,
    double? minWidth,
    double? minHeight,
    EdgeInsets? padding,
    EdgeInsets? margin,
    TextStyle? textStyle,
    List<BoxShadow>? shadow,
    double? elevation,
    Duration? hoverDelay,
    Duration? hideDelay,
    Duration? showDuration,
    Duration? hideDuration,
    Curve? showCurve,
    Curve? hideCurve,
    Offset? offset,
    bool? showArrow,
    double? arrowSize,
    Color? arrowColor,
  }) {
    return ShadcnHoverCardTheme(
      background: background ?? this.background,
      border: border ?? this.border,
      foreground: foreground ?? this.foreground,
      borderRadius: borderRadius ?? this.borderRadius,
      maxWidth: maxWidth ?? this.maxWidth,
      maxHeight: maxHeight ?? this.maxHeight,
      minWidth: minWidth ?? this.minWidth,
      minHeight: minHeight ?? this.minHeight,
      padding: padding ?? this.padding,
      margin: margin ?? this.margin,
      textStyle: textStyle ?? this.textStyle,
      shadow: shadow ?? this.shadow,
      elevation: elevation ?? this.elevation,
      hoverDelay: hoverDelay ?? this.hoverDelay,
      hideDelay: hideDelay ?? this.hideDelay,
      showDuration: showDuration ?? this.showDuration,
      hideDuration: hideDuration ?? this.hideDuration,
      showCurve: showCurve ?? this.showCurve,
      hideCurve: hideCurve ?? this.hideCurve,
      offset: offset ?? this.offset,
      showArrow: showArrow ?? this.showArrow,
      arrowSize: arrowSize ?? this.arrowSize,
      arrowColor: arrowColor ?? this.arrowColor,
    );
  }

  @override
  ShadcnHoverCardTheme lerp(ThemeExtension<ShadcnHoverCardTheme>? other, double t) {
    if (other is! ShadcnHoverCardTheme) return this;

    return ShadcnHoverCardTheme(
      background: Color.lerp(background, other.background, t),
      border: Color.lerp(border, other.border, t),
      foreground: Color.lerp(foreground, other.foreground, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      maxWidth: lerpDouble(maxWidth, other.maxWidth, t),
      maxHeight: lerpDouble(maxHeight, other.maxHeight, t),
      minWidth: lerpDouble(minWidth, other.minWidth, t),
      minHeight: lerpDouble(minHeight, other.minHeight, t),
      padding: EdgeInsets.lerp(padding, other.padding, t),
      margin: EdgeInsets.lerp(margin, other.margin, t),
      textStyle: TextStyle.lerp(textStyle, other.textStyle, t),
      shadow: BoxShadow.lerpList(shadow, other.shadow, t),
      elevation: lerpDouble(elevation, other.elevation, t),
      hoverDelay: t < 0.5 ? hoverDelay : other.hoverDelay,
      hideDelay: t < 0.5 ? hideDelay : other.hideDelay,
      showDuration: t < 0.5 ? showDuration : other.showDuration,
      hideDuration: t < 0.5 ? hideDuration : other.hideDuration,
      showCurve: t < 0.5 ? showCurve : other.showCurve,
      hideCurve: t < 0.5 ? hideCurve : other.hideCurve,
      offset: Offset.lerp(offset, other.offset, t),
      showArrow: t < 0.5 ? showArrow : other.showArrow,
      arrowSize: lerpDouble(arrowSize, other.arrowSize, t),
      arrowColor: Color.lerp(arrowColor, other.arrowColor, t),
    );
  }

  /// Default theme based on shadcn design tokens
  static ShadcnHoverCardTheme defaultTheme(ColorScheme colorScheme) {
    final isDark = colorScheme.brightness == Brightness.dark;
    
    return ShadcnHoverCardTheme(
      background: isDark ? const Color(0xFF09090B) : const Color(0xFFFFFFFF),
      border: isDark ? const Color(0xFF27272A) : const Color(0xFFE4E4E7),
      foreground: isDark ? const Color(0xFFFAFAFA) : const Color(0xFF09090B),
      borderRadius: BorderRadius.circular(6),
      maxWidth: 320,
      maxHeight: 200,
      minWidth: 200,
      minHeight: 60,
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(8),
      textStyle: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: isDark ? const Color(0xFFFAFAFA) : const Color(0xFF09090B),
      ),
      shadow: [
        BoxShadow(
          color: isDark ? const Color(0x40000000) : const Color(0x0F000000),
          offset: const Offset(0, 4),
          blurRadius: 16,
          spreadRadius: -2,
        ),
      ],
      elevation: 8,
      hoverDelay: const Duration(milliseconds: 700),
      hideDelay: const Duration(milliseconds: 300),
      showDuration: const Duration(milliseconds: 200),
      hideDuration: const Duration(milliseconds: 150),
      showCurve: Curves.easeOut,
      hideCurve: Curves.easeIn,
      offset: const Offset(0, 4),
      showArrow: false,
      arrowSize: 8,
      arrowColor: isDark ? const Color(0xFF09090B) : const Color(0xFFFFFFFF),
    );
  }

  double lerpDouble(double? a, double? b, double t) {
    if (a == null && b == null) return 0.0;
    if (a == null) return b! * t;
    if (b == null) return a * (1.0 - t);
    return a + (b - a) * t;
  }
}