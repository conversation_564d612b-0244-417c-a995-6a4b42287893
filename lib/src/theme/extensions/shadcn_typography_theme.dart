import 'package:flutter/material.dart';
import '../../constants/shadcn_tokens.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for the ShadcnTypography component.
class ShadcnTypographyTheme extends ShadcnThemeExtension<ShadcnTypographyTheme> {
  // Heading styles
  final TextStyle? h1Style;
  final TextStyle? h2Style;
  final TextStyle? h3Style;
  final TextStyle? h4Style;
  final TextStyle? h5Style;
  final TextStyle? h6Style;
  
  // Body styles
  final TextStyle? bodyLargeStyle;
  final TextStyle? bodyMediumStyle;
  final TextStyle? bodySmallStyle;
  
  // Display styles
  final TextStyle? displayLargeStyle;
  final TextStyle? displayMediumStyle;
  final TextStyle? displaySmallStyle;
  
  // Label styles
  final TextStyle? labelLargeStyle;
  final TextStyle? labelMediumStyle;
  final TextStyle? labelSmallStyle;
  
  // Special styles
  final TextStyle? codeStyle;
  final TextStyle? mutedStyle;
  final TextStyle? leadStyle;
  final TextStyle? smallStyle;

  const ShadcnTypographyTheme({
    this.h1Style,
    this.h2Style,
    this.h3Style,
    this.h4Style,
    this.h5Style,
    this.h6Style,
    this.bodyLargeStyle,
    this.bodyMediumStyle,
    this.bodySmallStyle,
    this.displayLargeStyle,
    this.displayMediumStyle,
    this.displaySmallStyle,
    this.labelLargeStyle,
    this.labelMediumStyle,
    this.labelSmallStyle,
    this.codeStyle,
    this.mutedStyle,
    this.leadStyle,
    this.smallStyle,
  });

  @override
  ShadcnTypographyTheme copyWith({
    TextStyle? h1Style,
    TextStyle? h2Style,
    TextStyle? h3Style,
    TextStyle? h4Style,
    TextStyle? h5Style,
    TextStyle? h6Style,
    TextStyle? bodyLargeStyle,
    TextStyle? bodyMediumStyle,
    TextStyle? bodySmallStyle,
    TextStyle? displayLargeStyle,
    TextStyle? displayMediumStyle,
    TextStyle? displaySmallStyle,
    TextStyle? labelLargeStyle,
    TextStyle? labelMediumStyle,
    TextStyle? labelSmallStyle,
    TextStyle? codeStyle,
    TextStyle? mutedStyle,
    TextStyle? leadStyle,
    TextStyle? smallStyle,
  }) {
    return ShadcnTypographyTheme(
      h1Style: h1Style ?? this.h1Style,
      h2Style: h2Style ?? this.h2Style,
      h3Style: h3Style ?? this.h3Style,
      h4Style: h4Style ?? this.h4Style,
      h5Style: h5Style ?? this.h5Style,
      h6Style: h6Style ?? this.h6Style,
      bodyLargeStyle: bodyLargeStyle ?? this.bodyLargeStyle,
      bodyMediumStyle: bodyMediumStyle ?? this.bodyMediumStyle,
      bodySmallStyle: bodySmallStyle ?? this.bodySmallStyle,
      displayLargeStyle: displayLargeStyle ?? this.displayLargeStyle,
      displayMediumStyle: displayMediumStyle ?? this.displayMediumStyle,
      displaySmallStyle: displaySmallStyle ?? this.displaySmallStyle,
      labelLargeStyle: labelLargeStyle ?? this.labelLargeStyle,
      labelMediumStyle: labelMediumStyle ?? this.labelMediumStyle,
      labelSmallStyle: labelSmallStyle ?? this.labelSmallStyle,
      codeStyle: codeStyle ?? this.codeStyle,
      mutedStyle: mutedStyle ?? this.mutedStyle,
      leadStyle: leadStyle ?? this.leadStyle,
      smallStyle: smallStyle ?? this.smallStyle,
    );
  }

  @override
  ShadcnTypographyTheme lerp(ShadcnTypographyTheme? other, double t) {
    if (other is! ShadcnTypographyTheme) return this;
    return ShadcnTypographyTheme(
      h1Style: TextStyle.lerp(h1Style, other.h1Style, t),
      h2Style: TextStyle.lerp(h2Style, other.h2Style, t),
      h3Style: TextStyle.lerp(h3Style, other.h3Style, t),
      h4Style: TextStyle.lerp(h4Style, other.h4Style, t),
      h5Style: TextStyle.lerp(h5Style, other.h5Style, t),
      h6Style: TextStyle.lerp(h6Style, other.h6Style, t),
      bodyLargeStyle: TextStyle.lerp(bodyLargeStyle, other.bodyLargeStyle, t),
      bodyMediumStyle: TextStyle.lerp(bodyMediumStyle, other.bodyMediumStyle, t),
      bodySmallStyle: TextStyle.lerp(bodySmallStyle, other.bodySmallStyle, t),
      displayLargeStyle: TextStyle.lerp(displayLargeStyle, other.displayLargeStyle, t),
      displayMediumStyle: TextStyle.lerp(displayMediumStyle, other.displayMediumStyle, t),
      displaySmallStyle: TextStyle.lerp(displaySmallStyle, other.displaySmallStyle, t),
      labelLargeStyle: TextStyle.lerp(labelLargeStyle, other.labelLargeStyle, t),
      labelMediumStyle: TextStyle.lerp(labelMediumStyle, other.labelMediumStyle, t),
      labelSmallStyle: TextStyle.lerp(labelSmallStyle, other.labelSmallStyle, t),
      codeStyle: TextStyle.lerp(codeStyle, other.codeStyle, t),
      mutedStyle: TextStyle.lerp(mutedStyle, other.mutedStyle, t),
      leadStyle: TextStyle.lerp(leadStyle, other.leadStyle, t),
      smallStyle: TextStyle.lerp(smallStyle, other.smallStyle, t),
    );
  }

  static ShadcnTypographyTheme defaultTheme(ColorScheme colorScheme) {
    return ShadcnTypographyTheme(
      h1Style: TextStyle(
        fontSize: 32,
        fontWeight: ShadcnTokens.fontWeightBold,
        height: ShadcnTokens.lineHeightTight,
        color: colorScheme.onSurface,
      ),
      h2Style: TextStyle(
        fontSize: 24,
        fontWeight: ShadcnTokens.fontWeightSemibold,
        height: ShadcnTokens.lineHeightTight,
        color: colorScheme.onSurface,
      ),
      h3Style: TextStyle(
        fontSize: 20,
        fontWeight: ShadcnTokens.fontWeightSemibold,
        color: colorScheme.onSurface,
      ),
      h4Style: TextStyle(
        fontSize: 18,
        fontWeight: ShadcnTokens.fontWeightSemibold,
        color: colorScheme.onSurface,
      ),
      h5Style: TextStyle(
        fontSize: 16,
        fontWeight: ShadcnTokens.fontWeightMedium,
        color: colorScheme.onSurface,
      ),
      h6Style: TextStyle(
        fontSize: 14,
        fontWeight: ShadcnTokens.fontWeightMedium,
        color: colorScheme.onSurface,
      ),
      bodyLargeStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeLg,
        fontWeight: ShadcnTokens.fontWeightNormal,
        color: colorScheme.onSurface,
      ),
      bodyMediumStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        fontWeight: ShadcnTokens.fontWeightNormal,
        color: colorScheme.onSurface,
      ),
      bodySmallStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeSm,
        fontWeight: ShadcnTokens.fontWeightNormal,
        color: colorScheme.onSurface,
      ),
      codeStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeSm,
        fontFamily: 'monospace',
        backgroundColor: colorScheme.surfaceVariant,
        color: colorScheme.onSurfaceVariant,
      ),
      mutedStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeMd,
        color: colorScheme.onSurface.withOpacity(0.6),
      ),
      leadStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeXl,
        color: colorScheme.onSurface.withOpacity(0.8),
      ),
      smallStyle: TextStyle(
        fontSize: ShadcnTokens.fontSizeSm,
        fontWeight: ShadcnTokens.fontWeightMedium,
        color: colorScheme.onSurface.withOpacity(0.8),
      ),
    );
  }
}