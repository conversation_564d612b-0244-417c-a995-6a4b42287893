import 'dart:ui' show lerpDouble;
import 'package:flutter/material.dart';
import 'shadcn_theme_extension.dart';
import '../../constants/shadcn_tokens.dart';

/// Theme extension for the ShadcnToast component.
/// 
/// Defines styling properties for toast notifications including colors, typography,
/// spacing, and variant-specific theming. This theme extension supports all
/// shadcn toast variants: success, error, warning, and info.
/// 
/// The theme automatically inherits from Material Design colors while providing
/// shadcn-specific styling patterns and token-based spacing.
class ShadcnToastTheme extends ShadcnThemeExtension<ShadcnToastTheme> {
  /// Background color for success toast variant
  final Color? successBackground;
  
  /// Foreground color for success toast variant
  final Color? successForeground;
  
  /// Border color for success toast variant
  final Color? successBorder;
  
  /// Icon color for success toast variant
  final Color? successIconColor;
  
  /// Background color for error toast variant
  final Color? errorBackground;
  
  /// Foreground color for error toast variant
  final Color? errorForeground;
  
  /// Border color for error toast variant
  final Color? errorBorder;
  
  /// Icon color for error toast variant
  final Color? errorIconColor;
  
  /// Background color for warning toast variant
  final Color? warningBackground;
  
  /// Foreground color for warning toast variant
  final Color? warningForeground;
  
  /// Border color for warning toast variant
  final Color? warningBorder;
  
  /// Icon color for warning toast variant
  final Color? warningIconColor;
  
  /// Background color for info toast variant
  final Color? infoBackground;
  
  /// Foreground color for info toast variant
  final Color? infoForeground;
  
  /// Border color for info toast variant
  final Color? infoBorder;
  
  /// Icon color for info toast variant
  final Color? infoIconColor;
  
  /// Text style for the toast title
  final TextStyle? titleTextStyle;
  
  /// Text style for the toast description/content
  final TextStyle? descriptionTextStyle;
  
  /// Border radius for toast container
  final BorderRadius? borderRadius;
  
  /// Border width for toast container
  final double? borderWidth;
  
  /// Padding inside the toast container
  final EdgeInsets? padding;
  
  /// Size of the toast icon
  final double? iconSize;
  
  /// Gap between icon and text content
  final double? iconGap;
  
  /// Gap between title and description
  final double? titleDescriptionGap;
  
  /// Minimum height for toast container
  final double? minHeight;
  
  /// Maximum width for toast container
  final double? maxWidth;
  
  /// Elevation/shadow depth for toast
  final double? elevation;
  
  /// Shadow color for toast elevation
  final Color? shadowColor;
  
  /// Duration for show/hide animations
  final Duration? animationDuration;
  
  /// Curve for show/hide animations
  final Curve? animationCurve;
  
  const ShadcnToastTheme({
    // Success variant colors
    this.successBackground,
    this.successForeground,
    this.successBorder,
    this.successIconColor,
    
    // Error variant colors
    this.errorBackground,
    this.errorForeground,
    this.errorBorder,
    this.errorIconColor,
    
    // Warning variant colors
    this.warningBackground,
    this.warningForeground,
    this.warningBorder,
    this.warningIconColor,
    
    // Info variant colors
    this.infoBackground,
    this.infoForeground,
    this.infoBorder,
    this.infoIconColor,
    
    // Typography
    this.titleTextStyle,
    this.descriptionTextStyle,
    
    // Layout properties
    this.borderRadius,
    this.borderWidth,
    this.padding,
    this.iconSize,
    this.iconGap,
    this.titleDescriptionGap,
    this.minHeight,
    this.maxWidth,
    
    // Elevation and shadows
    this.elevation,
    this.shadowColor,
    
    // Animation properties
    this.animationDuration,
    this.animationCurve,
  });
  
  /// Creates a default toast theme from a Material ColorScheme.
  /// 
  /// This factory method generates sensible defaults that integrate with
  /// Material Design colors while following shadcn design patterns.
  static ShadcnToastTheme defaultTheme(ColorScheme colorScheme) {
    final isLight = colorScheme.brightness == Brightness.light;
    
    // Base colors for different variants
    final successColor = Colors.green;
    final errorColor = colorScheme.error;
    final warningColor = Colors.orange;
    final infoColor = colorScheme.primary;
    
    // Background colors with proper opacity for light/dark themes
    final successBg = isLight 
        ? successColor.withValues(alpha: 0.1) 
        : successColor.withValues(alpha: 0.2);
    final errorBg = isLight 
        ? errorColor.withValues(alpha: 0.1) 
        : errorColor.withValues(alpha: 0.2);
    final warningBg = isLight 
        ? warningColor.withValues(alpha: 0.1) 
        : warningColor.withValues(alpha: 0.2);
    final infoBg = isLight 
        ? infoColor.withValues(alpha: 0.1) 
        : infoColor.withValues(alpha: 0.2);
    
    return ShadcnToastTheme(
      // Success variant
      successBackground: successBg,
      successForeground: isLight ? successColor.shade700 : successColor.shade300,
      successBorder: isLight ? successColor.shade200 : successColor.shade600,
      successIconColor: isLight ? successColor.shade700 : successColor.shade300,
      
      // Error variant
      errorBackground: errorBg,
      errorForeground: isLight ? errorColor : colorScheme.onError,
      errorBorder: isLight ? errorColor.withValues(alpha: 0.3) : errorColor.withValues(alpha: 0.6),
      errorIconColor: isLight ? errorColor : colorScheme.onError,
      
      // Warning variant
      warningBackground: warningBg,
      warningForeground: isLight ? warningColor.shade700 : warningColor.shade300,
      warningBorder: isLight ? warningColor.shade200 : warningColor.shade600,
      warningIconColor: isLight ? warningColor.shade700 : warningColor.shade300,
      
      // Info variant
      infoBackground: infoBg,
      infoForeground: isLight ? infoColor : colorScheme.onPrimary,
      infoBorder: isLight ? infoColor.withValues(alpha: 0.3) : infoColor.withValues(alpha: 0.6),
      infoIconColor: isLight ? infoColor : colorScheme.onPrimary,
      
      // Layout properties using shadcn tokens
      borderRadius: ShadcnTokens.borderRadius(ShadcnTokens.radiusMd),
      borderWidth: ShadcnTokens.borderWidth,
      padding: const EdgeInsets.all(ShadcnTokens.spacing4),
      iconSize: ShadcnTokens.iconSizeMd,
      iconGap: ShadcnTokens.spacing3,
      titleDescriptionGap: ShadcnTokens.spacing1,
      minHeight: ShadcnTokens.buttonHeightMd,
      maxWidth: 420.0, // Reasonable max width for toasts
      
      // Elevation for floating appearance
      elevation: ShadcnTokens.elevationMd,
      shadowColor: colorScheme.shadow,
      
      // Animation properties
      animationDuration: ShadcnTokens.durationNormal,
      animationCurve: Curves.easeInOut,
    );
  }
  
  @override
  ShadcnToastTheme copyWith({
    // Success variant colors
    Color? successBackground,
    Color? successForeground,
    Color? successBorder,
    Color? successIconColor,
    
    // Error variant colors
    Color? errorBackground,
    Color? errorForeground,
    Color? errorBorder,
    Color? errorIconColor,
    
    // Warning variant colors
    Color? warningBackground,
    Color? warningForeground,
    Color? warningBorder,
    Color? warningIconColor,
    
    // Info variant colors
    Color? infoBackground,
    Color? infoForeground,
    Color? infoBorder,
    Color? infoIconColor,
    
    // Typography
    TextStyle? titleTextStyle,
    TextStyle? descriptionTextStyle,
    
    // Layout properties
    BorderRadius? borderRadius,
    double? borderWidth,
    EdgeInsets? padding,
    double? iconSize,
    double? iconGap,
    double? titleDescriptionGap,
    double? minHeight,
    double? maxWidth,
    
    // Elevation and shadows
    double? elevation,
    Color? shadowColor,
    
    // Animation properties
    Duration? animationDuration,
    Curve? animationCurve,
  }) {
    return ShadcnToastTheme(
      successBackground: successBackground ?? this.successBackground,
      successForeground: successForeground ?? this.successForeground,
      successBorder: successBorder ?? this.successBorder,
      successIconColor: successIconColor ?? this.successIconColor,
      
      errorBackground: errorBackground ?? this.errorBackground,
      errorForeground: errorForeground ?? this.errorForeground,
      errorBorder: errorBorder ?? this.errorBorder,
      errorIconColor: errorIconColor ?? this.errorIconColor,
      
      warningBackground: warningBackground ?? this.warningBackground,
      warningForeground: warningForeground ?? this.warningForeground,
      warningBorder: warningBorder ?? this.warningBorder,
      warningIconColor: warningIconColor ?? this.warningIconColor,
      
      infoBackground: infoBackground ?? this.infoBackground,
      infoForeground: infoForeground ?? this.infoForeground,
      infoBorder: infoBorder ?? this.infoBorder,
      infoIconColor: infoIconColor ?? this.infoIconColor,
      
      titleTextStyle: titleTextStyle ?? this.titleTextStyle,
      descriptionTextStyle: descriptionTextStyle ?? this.descriptionTextStyle,
      
      borderRadius: borderRadius ?? this.borderRadius,
      borderWidth: borderWidth ?? this.borderWidth,
      padding: padding ?? this.padding,
      iconSize: iconSize ?? this.iconSize,
      iconGap: iconGap ?? this.iconGap,
      titleDescriptionGap: titleDescriptionGap ?? this.titleDescriptionGap,
      minHeight: minHeight ?? this.minHeight,
      maxWidth: maxWidth ?? this.maxWidth,
      
      elevation: elevation ?? this.elevation,
      shadowColor: shadowColor ?? this.shadowColor,
      
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
    );
  }
  
  @override
  ShadcnToastTheme lerp(ShadcnToastTheme? other, double t) {
    if (other == null) return this;
    
    return ShadcnToastTheme(
      successBackground: Color.lerp(successBackground, other.successBackground, t),
      successForeground: Color.lerp(successForeground, other.successForeground, t),
      successBorder: Color.lerp(successBorder, other.successBorder, t),
      successIconColor: Color.lerp(successIconColor, other.successIconColor, t),
      
      errorBackground: Color.lerp(errorBackground, other.errorBackground, t),
      errorForeground: Color.lerp(errorForeground, other.errorForeground, t),
      errorBorder: Color.lerp(errorBorder, other.errorBorder, t),
      errorIconColor: Color.lerp(errorIconColor, other.errorIconColor, t),
      
      warningBackground: Color.lerp(warningBackground, other.warningBackground, t),
      warningForeground: Color.lerp(warningForeground, other.warningForeground, t),
      warningBorder: Color.lerp(warningBorder, other.warningBorder, t),
      warningIconColor: Color.lerp(warningIconColor, other.warningIconColor, t),
      
      infoBackground: Color.lerp(infoBackground, other.infoBackground, t),
      infoForeground: Color.lerp(infoForeground, other.infoForeground, t),
      infoBorder: Color.lerp(infoBorder, other.infoBorder, t),
      infoIconColor: Color.lerp(infoIconColor, other.infoIconColor, t),
      
      titleTextStyle: TextStyle.lerp(titleTextStyle, other.titleTextStyle, t),
      descriptionTextStyle: TextStyle.lerp(descriptionTextStyle, other.descriptionTextStyle, t),
      
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      borderWidth: lerpDouble(borderWidth, other.borderWidth, t),
      padding: EdgeInsets.lerp(padding, other.padding, t),
      iconSize: lerpDouble(iconSize, other.iconSize, t),
      iconGap: lerpDouble(iconGap, other.iconGap, t),
      titleDescriptionGap: lerpDouble(titleDescriptionGap, other.titleDescriptionGap, t),
      minHeight: lerpDouble(minHeight, other.minHeight, t),
      maxWidth: lerpDouble(maxWidth, other.maxWidth, t),
      
      elevation: lerpDouble(elevation, other.elevation, t),
      shadowColor: Color.lerp(shadowColor, other.shadowColor, t),
      
      animationDuration: t < 0.5 ? animationDuration : other.animationDuration,
      animationCurve: t < 0.5 ? animationCurve : other.animationCurve,
    );
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is ShadcnToastTheme &&
        successBackground == other.successBackground &&
        successForeground == other.successForeground &&
        successBorder == other.successBorder &&
        successIconColor == other.successIconColor &&
        errorBackground == other.errorBackground &&
        errorForeground == other.errorForeground &&
        errorBorder == other.errorBorder &&
        errorIconColor == other.errorIconColor &&
        warningBackground == other.warningBackground &&
        warningForeground == other.warningForeground &&
        warningBorder == other.warningBorder &&
        warningIconColor == other.warningIconColor &&
        infoBackground == other.infoBackground &&
        infoForeground == other.infoForeground &&
        infoBorder == other.infoBorder &&
        infoIconColor == other.infoIconColor &&
        titleTextStyle == other.titleTextStyle &&
        descriptionTextStyle == other.descriptionTextStyle &&
        borderRadius == other.borderRadius &&
        borderWidth == other.borderWidth &&
        padding == other.padding &&
        iconSize == other.iconSize &&
        iconGap == other.iconGap &&
        titleDescriptionGap == other.titleDescriptionGap &&
        minHeight == other.minHeight &&
        maxWidth == other.maxWidth &&
        elevation == other.elevation &&
        shadowColor == other.shadowColor &&
        animationDuration == other.animationDuration &&
        animationCurve == other.animationCurve;
  }
  
  @override
  int get hashCode {
    return Object.hashAll([
      successBackground,
      successForeground,
      successBorder,
      successIconColor,
      errorBackground,
      errorForeground,
      errorBorder,
      errorIconColor,
      warningBackground,
      warningForeground,
      warningBorder,
      warningIconColor,
      infoBackground,
      infoForeground,
      infoBorder,
      infoIconColor,
      titleTextStyle,
      descriptionTextStyle,
      borderRadius,
      borderWidth,
      padding,
      iconSize,
      iconGap,
      titleDescriptionGap,
      minHeight,
      maxWidth,
      elevation,
      shadowColor,
      animationDuration,
      animationCurve,
    ]);
  }
}

/// Toast variant enumeration for different message types.
/// 
/// Each variant has associated styling properties and default icons
/// to communicate the appropriate message context to users.
enum ShadcnToastVariant {
  /// Success variant for positive confirmations and completed actions
  success,
  
  /// Error variant for errors and destructive actions
  error,
  
  /// Warning variant for cautionary messages and potential issues
  warning,
  
  /// Info variant for informational messages and general notifications
  info;
  
  /// Returns the default icon for this toast variant
  IconData get defaultIcon {
    switch (this) {
      case ShadcnToastVariant.success:
        return Icons.check_circle_outline;
      case ShadcnToastVariant.error:
        return Icons.error_outline;
      case ShadcnToastVariant.warning:
        return Icons.warning_amber_outlined;
      case ShadcnToastVariant.info:
        return Icons.info_outline;
    }
  }
  
  /// Returns whether this variant represents a critical/negative state
  bool get isCritical {
    return this == ShadcnToastVariant.error || this == ShadcnToastVariant.warning;
  }
  
  /// Returns whether this variant represents a positive state
  bool get isPositive {
    return this == ShadcnToastVariant.success;
  }
  
  /// Returns the semantic role for accessibility
  String get semanticRole {
    switch (this) {
      case ShadcnToastVariant.success:
        return 'status';
      case ShadcnToastVariant.error:
        return 'alert';
      case ShadcnToastVariant.warning:
        return 'alert';
      case ShadcnToastVariant.info:
        return 'status';
    }
  }
}