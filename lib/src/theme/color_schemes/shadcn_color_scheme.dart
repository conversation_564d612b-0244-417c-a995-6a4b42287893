import 'package:flutter/material.dart';

/// Shadcn color scheme extension that provides all shadcn design tokens
/// while maintaining full compatibility with Material Design ColorScheme.
/// 
/// This extension adds shadcn-specific color tokens to the Material theme
/// system, allowing components to access both Material and shadcn colors
/// through the same theme context.
class ShadcnColorScheme extends ThemeExtension<ShadcnColorScheme> {
  /// Border color for outlines, dividers, and input borders
  final Color border;
  
  /// Input background color
  final Color input;
  
  /// Focus ring color for interactive elements
  final Color ring;
  
  /// Page/app background color
  final Color background;
  
  /// Primary text color on background
  final Color foreground;
  
  /// Card background color
  final Color card;
  
  /// Primary text color on card background
  final Color cardForeground;
  
  /// Popover and dropdown background color
  final Color popover;
  
  /// Primary text color on popover background
  final Color popoverForeground;
  
  /// Muted/secondary background color
  final Color muted;
  
  /// Secondary text color on muted background
  final Color mutedForeground;
  
  /// Accent color for highlights and selections
  final Color accent;
  
  /// Text color on accent background
  final Color accentForeground;
  
  /// Destructive/error action color
  final Color destructive;
  
  /// Text color on destructive background
  final Color destructiveForeground;
  
  /// Secondary action color
  final Color secondary;
  
  /// Text color on secondary background
  final Color secondaryForeground;
  
  const ShadcnColorScheme({
    required this.border,
    required this.input,
    required this.ring,
    required this.background,
    required this.foreground,
    required this.card,
    required this.cardForeground,
    required this.popover,
    required this.popoverForeground,
    required this.muted,
    required this.mutedForeground,
    required this.accent,
    required this.accentForeground,
    required this.destructive,
    required this.destructiveForeground,
    required this.secondary,
    required this.secondaryForeground,
  });
  
  /// Creates a shadcn color scheme from a Material Design ColorScheme.
  /// 
  /// This factory method maps Material Design colors to shadcn color tokens,
  /// ensuring full compatibility while providing shadcn-specific semantics.
  factory ShadcnColorScheme.fromMaterial(ColorScheme colorScheme) {
    
    return ShadcnColorScheme(
      // Border uses outline color with appropriate opacity
      border: colorScheme.outline,
      
      // Input background uses surface color
      input: colorScheme.surface,
      
      // Ring uses primary color for focus indicators
      ring: colorScheme.primary,
      
      // Background maps directly to Material background
      background: colorScheme.surface,
      
      // Foreground uses appropriate on-surface color
      foreground: colorScheme.onSurface,
      
      // Card uses surface variant for subtle differentiation
      card: colorScheme.surfaceContainerLowest,
      
      // Card foreground uses on-surface color
      cardForeground: colorScheme.onSurface,
      
      // Popover uses elevated surface color
      popover: colorScheme.surfaceContainer,
      
      // Popover foreground uses on-surface color
      popoverForeground: colorScheme.onSurface,
      
      // Muted uses surface variant for secondary content
      muted: colorScheme.surfaceContainerHighest,
      
      // Muted foreground uses on-surface variant
      mutedForeground: colorScheme.onSurfaceVariant,
      
      // Accent maps to secondary color
      accent: colorScheme.secondary,
      
      // Accent foreground uses on-secondary color
      accentForeground: colorScheme.onSecondary,
      
      // Destructive maps to error color
      destructive: colorScheme.error,
      
      // Destructive foreground uses on-error color
      destructiveForeground: colorScheme.onError,
      
      // Secondary maps to secondary color
      secondary: colorScheme.secondary,
      
      // Secondary foreground uses on-secondary color
      secondaryForeground: colorScheme.onSecondary,
    );
  }
  
  /// Creates a light theme shadcn color scheme with default values.
  factory ShadcnColorScheme.light() {
    return ShadcnColorScheme.fromMaterial(
      ColorScheme.fromSeed(
        seedColor: const Color(0xFF0F172A), // Slate 900
        brightness: Brightness.light,
      ),
    );
  }
  
  /// Creates a dark theme shadcn color scheme with default values.
  factory ShadcnColorScheme.dark() {
    return ShadcnColorScheme.fromMaterial(
      ColorScheme.fromSeed(
        seedColor: const Color(0xFF0F172A), // Slate 900
        brightness: Brightness.dark,
      ),
    );
  }
  
  @override
  ShadcnColorScheme copyWith({
    Color? border,
    Color? input,
    Color? ring,
    Color? background,
    Color? foreground,
    Color? card,
    Color? cardForeground,
    Color? popover,
    Color? popoverForeground,
    Color? muted,
    Color? mutedForeground,
    Color? accent,
    Color? accentForeground,
    Color? destructive,
    Color? destructiveForeground,
    Color? secondary,
    Color? secondaryForeground,
  }) {
    return ShadcnColorScheme(
      border: border ?? this.border,
      input: input ?? this.input,
      ring: ring ?? this.ring,
      background: background ?? this.background,
      foreground: foreground ?? this.foreground,
      card: card ?? this.card,
      cardForeground: cardForeground ?? this.cardForeground,
      popover: popover ?? this.popover,
      popoverForeground: popoverForeground ?? this.popoverForeground,
      muted: muted ?? this.muted,
      mutedForeground: mutedForeground ?? this.mutedForeground,
      accent: accent ?? this.accent,
      accentForeground: accentForeground ?? this.accentForeground,
      destructive: destructive ?? this.destructive,
      destructiveForeground: destructiveForeground ?? this.destructiveForeground,
      secondary: secondary ?? this.secondary,
      secondaryForeground: secondaryForeground ?? this.secondaryForeground,
    );
  }
  
  @override
  ShadcnColorScheme lerp(ThemeExtension<ShadcnColorScheme>? other, double t) {
    if (other is! ShadcnColorScheme) {
      return this;
    }
    
    return ShadcnColorScheme(
      border: Color.lerp(border, other.border, t)!,
      input: Color.lerp(input, other.input, t)!,
      ring: Color.lerp(ring, other.ring, t)!,
      background: Color.lerp(background, other.background, t)!,
      foreground: Color.lerp(foreground, other.foreground, t)!,
      card: Color.lerp(card, other.card, t)!,
      cardForeground: Color.lerp(cardForeground, other.cardForeground, t)!,
      popover: Color.lerp(popover, other.popover, t)!,
      popoverForeground: Color.lerp(popoverForeground, other.popoverForeground, t)!,
      muted: Color.lerp(muted, other.muted, t)!,
      mutedForeground: Color.lerp(mutedForeground, other.mutedForeground, t)!,
      accent: Color.lerp(accent, other.accent, t)!,
      accentForeground: Color.lerp(accentForeground, other.accentForeground, t)!,
      destructive: Color.lerp(destructive, other.destructive, t)!,
      destructiveForeground: Color.lerp(destructiveForeground, other.destructiveForeground, t)!,
      secondary: Color.lerp(secondary, other.secondary, t)!,
      secondaryForeground: Color.lerp(secondaryForeground, other.secondaryForeground, t)!,
    );
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is ShadcnColorScheme &&
        other.border == border &&
        other.input == input &&
        other.ring == ring &&
        other.background == background &&
        other.foreground == foreground &&
        other.card == card &&
        other.cardForeground == cardForeground &&
        other.popover == popover &&
        other.popoverForeground == popoverForeground &&
        other.muted == muted &&
        other.mutedForeground == mutedForeground &&
        other.accent == accent &&
        other.accentForeground == accentForeground &&
        other.destructive == destructive &&
        other.destructiveForeground == destructiveForeground &&
        other.secondary == secondary &&
        other.secondaryForeground == secondaryForeground;
  }
  
  @override
  int get hashCode {
    return Object.hash(
      border,
      input,
      ring,
      background,
      foreground,
      card,
      cardForeground,
      popover,
      popoverForeground,
      muted,
      mutedForeground,
      accent,
      accentForeground,
      destructive,
      destructiveForeground,
      secondary,
      secondaryForeground,
    );
  }
}