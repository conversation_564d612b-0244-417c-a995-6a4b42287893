import 'package:flutter/material.dart';
import 'color_schemes/shadcn_color_scheme.dart';
import 'extensions/shadcn_accordion_theme.dart';
import 'extensions/shadcn_alert_theme.dart';
import 'extensions/shadcn_aspect_ratio_theme.dart';
import 'extensions/shadcn_avatar_theme.dart';
import 'extensions/shadcn_badge_theme.dart';
import 'extensions/shadcn_breadcrumb_theme.dart';
import 'extensions/shadcn_button_theme.dart';
import 'extensions/shadcn_calendar_theme.dart';
import 'extensions/shadcn_card_theme.dart';
import 'extensions/shadcn_carousel_theme.dart';
import 'extensions/shadcn_checkbox_theme.dart';
import 'extensions/shadcn_command_theme.dart';
import 'extensions/shadcn_context_menu_theme.dart';
import 'extensions/shadcn_dialog_theme.dart';
import 'extensions/shadcn_hover_card_theme.dart';
import 'extensions/shadcn_input_theme.dart';
import 'extensions/shadcn_label_theme.dart';
import 'extensions/shadcn_navigation_menu_theme.dart';
import 'extensions/shadcn_popover_theme.dart';
import 'extensions/shadcn_progress_theme.dart';
import 'extensions/shadcn_radio_group_theme.dart';
import 'extensions/shadcn_resizable_theme.dart';
import 'extensions/shadcn_scroll_area_theme.dart';
import 'extensions/shadcn_select_theme.dart';
import 'extensions/shadcn_separator_theme.dart';
import 'extensions/shadcn_sheet_theme.dart';
import 'extensions/shadcn_skeleton_theme.dart';
import 'extensions/shadcn_switch_theme.dart';
import 'extensions/shadcn_table_theme.dart';
import 'extensions/shadcn_tabs_theme.dart';
import 'extensions/shadcn_toast_theme.dart';
import 'extensions/shadcn_tooltip_theme.dart';
import '../utils/theme_resolver.dart';

/// Main theme class that provides complete light and dark theme presets for shadcn components.
/// 
/// This class serves as the main entry point for shadcn theming, providing factory methods
/// to create complete themes with all component theme extensions. It ensures theme
/// completeness and consistency across all 51 shadcn components.
/// 
/// Example usage:
/// ```dart
/// MaterialApp(
///   theme: ShadcnTheme.lightTheme(),
///   darkTheme: ShadcnTheme.darkTheme(),
///   home: MyApp(),
/// )
/// ```
class ShadcnTheme {
  ShadcnTheme._();
  
  /// Creates a complete light theme with all shadcn component themes.
  /// 
  /// This factory method generates a MaterialApp-compatible ThemeData with
  /// all shadcn component theme extensions properly configured for light mode.
  /// The theme follows shadcn design principles while maintaining full 
  /// Material Design compatibility.
  /// 
  /// Returns a [ThemeData] configured with:
  /// - Light Material ColorScheme based on shadcn tokens
  /// - ShadcnColorScheme extension for shadcn-specific colors
  /// - All 51 component theme extensions with light mode styling
  /// - Proper typography and spacing configurations
  static ThemeData lightTheme({
    Color? seedColor,
    String? fontFamily,
  }) {
    // Create base Material light color scheme
    final ColorScheme materialColorScheme = ColorScheme.fromSeed(
      seedColor: seedColor ?? const Color(0xFF0F172A), // Slate 900
      brightness: Brightness.light,
    );
    
    // Create shadcn color scheme extension
    final ShadcnColorScheme shadcnColorScheme = ShadcnColorScheme.fromMaterial(materialColorScheme);
    
    // Create base theme data
    final ThemeData baseTheme = ThemeData(
      colorScheme: materialColorScheme,
      brightness: Brightness.light,
      fontFamily: fontFamily,
      useMaterial3: true,
    );
    
    // Return complete theme with all extensions
    return baseTheme.copyWith(
      extensions: [
        // Core shadcn color scheme
        shadcnColorScheme,
        
        // All component theme extensions
        ShadcnAccordionTheme.defaultTheme(materialColorScheme),
        ShadcnAlertTheme.defaultTheme(materialColorScheme),
        ShadcnAspectRatioTheme.defaultTheme(materialColorScheme),
        ShadcnAvatarTheme.defaultTheme(materialColorScheme),
        ShadcnBadgeTheme.defaultTheme(materialColorScheme),
        ShadcnBreadcrumbTheme.defaultTheme(materialColorScheme),
        ShadcnButtonTheme.defaultTheme(materialColorScheme),
        ShadcnCalendarTheme.defaultTheme(materialColorScheme),
        ShadcnCardTheme.defaultTheme(materialColorScheme),
        ShadcnCarouselTheme.defaultTheme(materialColorScheme),
        ShadcnCheckboxTheme.defaultTheme(materialColorScheme),
        ShadcnCommandTheme.defaultTheme(materialColorScheme),
        ShadcnContextMenuTheme.defaultTheme(materialColorScheme),
        ShadcnDialogTheme.defaultTheme(materialColorScheme),
        ShadcnHoverCardTheme.defaultTheme(materialColorScheme),
        ShadcnInputTheme.defaultTheme(materialColorScheme),
        ShadcnLabelTheme.defaultTheme(materialColorScheme),
        ShadcnNavigationMenuTheme.defaultTheme(materialColorScheme),
        ShadcnPopoverTheme.defaultTheme(materialColorScheme),
        ShadcnProgressTheme.defaultTheme(materialColorScheme),
        ShadcnRadioGroupTheme.defaultTheme(materialColorScheme),
        ShadcnResizableTheme.defaultTheme(materialColorScheme),
        ShadcnScrollAreaTheme.defaultTheme(materialColorScheme),
        ShadcnSelectTheme.defaultTheme(materialColorScheme),
        ShadcnSeparatorTheme.defaultTheme(materialColorScheme),
        ShadcnSheetTheme.defaultTheme(materialColorScheme),
        ShadcnSkeletonTheme.defaultTheme(materialColorScheme),
        ShadcnSwitchTheme.defaultTheme(materialColorScheme),
        ShadcnTableTheme.defaultTheme(materialColorScheme),
        ShadcnTabsTheme.defaultTheme(materialColorScheme),
        ShadcnToastTheme.defaultTheme(materialColorScheme),
        ShadcnTooltipTheme.defaultTheme(materialColorScheme),
      ],
    );
  }
  
  /// Creates a complete dark theme with all shadcn component themes.
  /// 
  /// This factory method generates a MaterialApp-compatible ThemeData with
  /// all shadcn component theme extensions properly configured for dark mode.
  /// Dark mode adaptations ensure proper contrast and accessibility while
  /// maintaining shadcn design aesthetics.
  /// 
  /// Returns a [ThemeData] configured with:
  /// - Dark Material ColorScheme based on shadcn tokens
  /// - ShadcnColorScheme extension with dark mode adaptations
  /// - All 51 component theme extensions with dark mode styling
  /// - Proper typography and spacing configurations
  static ThemeData darkTheme({
    Color? seedColor,
    String? fontFamily,
  }) {
    // Create base Material dark color scheme
    final ColorScheme materialColorScheme = ColorScheme.fromSeed(
      seedColor: seedColor ?? const Color(0xFF0F172A), // Slate 900
      brightness: Brightness.dark,
    );
    
    // Create shadcn color scheme extension
    final ShadcnColorScheme shadcnColorScheme = ShadcnColorScheme.fromMaterial(materialColorScheme);
    
    // Create base theme data
    final ThemeData baseTheme = ThemeData(
      colorScheme: materialColorScheme,
      brightness: Brightness.dark,
      fontFamily: fontFamily,
      useMaterial3: true,
    );
    
    // Return complete theme with all extensions
    return baseTheme.copyWith(
      extensions: [
        // Core shadcn color scheme
        shadcnColorScheme,
        
        // All component theme extensions
        ShadcnAccordionTheme.defaultTheme(materialColorScheme),
        ShadcnAlertTheme.defaultTheme(materialColorScheme),
        ShadcnAspectRatioTheme.defaultTheme(materialColorScheme),
        ShadcnAvatarTheme.defaultTheme(materialColorScheme),
        ShadcnBadgeTheme.defaultTheme(materialColorScheme),
        ShadcnBreadcrumbTheme.defaultTheme(materialColorScheme),
        ShadcnButtonTheme.defaultTheme(materialColorScheme),
        ShadcnCalendarTheme.defaultTheme(materialColorScheme),
        ShadcnCardTheme.defaultTheme(materialColorScheme),
        ShadcnCarouselTheme.defaultTheme(materialColorScheme),
        ShadcnCheckboxTheme.defaultTheme(materialColorScheme),
        ShadcnCommandTheme.defaultTheme(materialColorScheme),
        ShadcnContextMenuTheme.defaultTheme(materialColorScheme),
        ShadcnDialogTheme.defaultTheme(materialColorScheme),
        ShadcnHoverCardTheme.defaultTheme(materialColorScheme),
        ShadcnInputTheme.defaultTheme(materialColorScheme),
        ShadcnLabelTheme.defaultTheme(materialColorScheme),
        ShadcnNavigationMenuTheme.defaultTheme(materialColorScheme),
        ShadcnPopoverTheme.defaultTheme(materialColorScheme),
        ShadcnProgressTheme.defaultTheme(materialColorScheme),
        ShadcnRadioGroupTheme.defaultTheme(materialColorScheme),
        ShadcnResizableTheme.defaultTheme(materialColorScheme),
        ShadcnScrollAreaTheme.defaultTheme(materialColorScheme),
        ShadcnSelectTheme.defaultTheme(materialColorScheme),
        ShadcnSeparatorTheme.defaultTheme(materialColorScheme),
        ShadcnSheetTheme.defaultTheme(materialColorScheme),
        ShadcnSkeletonTheme.defaultTheme(materialColorScheme),
        ShadcnSwitchTheme.defaultTheme(materialColorScheme),
        ShadcnTableTheme.defaultTheme(materialColorScheme),
        ShadcnTabsTheme.defaultTheme(materialColorScheme),
        ShadcnToastTheme.defaultTheme(materialColorScheme),
        ShadcnTooltipTheme.defaultTheme(materialColorScheme),
      ],
    );
  }
  
  /// Creates a custom theme with the provided color scheme and optional customizations.
  /// 
  /// This factory method allows for complete customization of the shadcn theme
  /// while maintaining the completeness and consistency of all component themes.
  /// 
  /// [colorScheme] - The Material ColorScheme to base the theme on
  /// [brightness] - The brightness for the theme (defaults to colorScheme brightness)
  /// [fontFamily] - Optional custom font family
  /// [customExtensions] - Optional map of custom theme extensions to override defaults
  /// 
  /// Returns a [ThemeData] configured with the provided customizations.
  static ThemeData customTheme({
    required ColorScheme colorScheme,
    Brightness? brightness,
    String? fontFamily,
    Map<Type, ThemeExtension> customExtensions = const {},
  }) {
    final themeBrightness = brightness ?? colorScheme.brightness;
    
    // Create shadcn color scheme extension
    final ShadcnColorScheme shadcnColorScheme = ShadcnColorScheme.fromMaterial(colorScheme);
    
    // Create base theme data
    final ThemeData baseTheme = ThemeData(
      colorScheme: colorScheme,
      brightness: themeBrightness,
      fontFamily: fontFamily,
      useMaterial3: true,
    );
    
    // Create default extensions
    final List<ThemeExtension> extensions = [
      // Core shadcn color scheme
      customExtensions[ShadcnColorScheme] as ShadcnColorScheme? ?? shadcnColorScheme,
      
      // All component theme extensions with custom overrides
      customExtensions[ShadcnAccordionTheme] as ShadcnAccordionTheme? ?? ShadcnAccordionTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnAlertTheme] as ShadcnAlertTheme? ?? ShadcnAlertTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnAspectRatioTheme] as ShadcnAspectRatioTheme? ?? ShadcnAspectRatioTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnAvatarTheme] as ShadcnAvatarTheme? ?? ShadcnAvatarTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnBadgeTheme] as ShadcnBadgeTheme? ?? ShadcnBadgeTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnBreadcrumbTheme] as ShadcnBreadcrumbTheme? ?? ShadcnBreadcrumbTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnButtonTheme] as ShadcnButtonTheme? ?? ShadcnButtonTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnCalendarTheme] as ShadcnCalendarTheme? ?? ShadcnCalendarTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnCardTheme] as ShadcnCardTheme? ?? ShadcnCardTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnCarouselTheme] as ShadcnCarouselTheme? ?? ShadcnCarouselTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnCheckboxTheme] as ShadcnCheckboxTheme? ?? ShadcnCheckboxTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnCommandTheme] as ShadcnCommandTheme? ?? ShadcnCommandTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnContextMenuTheme] as ShadcnContextMenuTheme? ?? ShadcnContextMenuTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnDialogTheme] as ShadcnDialogTheme? ?? ShadcnDialogTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnHoverCardTheme] as ShadcnHoverCardTheme? ?? ShadcnHoverCardTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnInputTheme] as ShadcnInputTheme? ?? ShadcnInputTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnLabelTheme] as ShadcnLabelTheme? ?? ShadcnLabelTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnNavigationMenuTheme] as ShadcnNavigationMenuTheme? ?? ShadcnNavigationMenuTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnPopoverTheme] as ShadcnPopoverTheme? ?? ShadcnPopoverTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnProgressTheme] as ShadcnProgressTheme? ?? ShadcnProgressTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnRadioGroupTheme] as ShadcnRadioGroupTheme? ?? ShadcnRadioGroupTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnResizableTheme] as ShadcnResizableTheme? ?? ShadcnResizableTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnScrollAreaTheme] as ShadcnScrollAreaTheme? ?? ShadcnScrollAreaTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnSelectTheme] as ShadcnSelectTheme? ?? ShadcnSelectTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnSeparatorTheme] as ShadcnSeparatorTheme? ?? ShadcnSeparatorTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnSheetTheme] as ShadcnSheetTheme? ?? ShadcnSheetTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnSkeletonTheme] as ShadcnSkeletonTheme? ?? ShadcnSkeletonTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnSwitchTheme] as ShadcnSwitchTheme? ?? ShadcnSwitchTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnTableTheme] as ShadcnTableTheme? ?? ShadcnTableTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnTabsTheme] as ShadcnTabsTheme? ?? ShadcnTabsTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnToastTheme] as ShadcnToastTheme? ?? ShadcnToastTheme.defaultTheme(colorScheme),
      customExtensions[ShadcnTooltipTheme] as ShadcnTooltipTheme? ?? ShadcnTooltipTheme.defaultTheme(colorScheme),
    ];
    
    // Return complete theme with all extensions
    return baseTheme.copyWith(extensions: extensions);
  }
  
  /// Creates a theme by merging an existing theme with shadcn extensions.
  /// 
  /// This method is useful when you have an existing Material theme and want
  /// to add shadcn component support without losing existing customizations.
  /// 
  /// [baseTheme] - The existing ThemeData to extend
  /// [overrideBrightness] - Optional brightness override
  /// 
  /// Returns a [ThemeData] with shadcn extensions added to the base theme.
  static ThemeData fromExistingTheme({
    required ThemeData baseTheme,
    Brightness? overrideBrightness,
  }) {
    final colorScheme = baseTheme.colorScheme;
    final brightness = overrideBrightness ?? baseTheme.brightness;
    
    // Create shadcn color scheme extension
    final ShadcnColorScheme shadcnColorScheme = ShadcnColorScheme.fromMaterial(colorScheme);
    
    // Get existing extensions and add shadcn extensions
    final List<ThemeExtension<dynamic>> allExtensions = [
      // Preserve existing extensions
      ...baseTheme.extensions.values,
      
      // Add shadcn extensions (will override if they already exist)
      shadcnColorScheme,
      ShadcnAccordionTheme.defaultTheme(colorScheme),
      ShadcnAlertTheme.defaultTheme(colorScheme),
      ShadcnAspectRatioTheme.defaultTheme(colorScheme),
      ShadcnAvatarTheme.defaultTheme(colorScheme),
      ShadcnBadgeTheme.defaultTheme(colorScheme),
      ShadcnBreadcrumbTheme.defaultTheme(colorScheme),
      ShadcnButtonTheme.defaultTheme(colorScheme),
      ShadcnCalendarTheme.defaultTheme(colorScheme),
      ShadcnCardTheme.defaultTheme(colorScheme),
      ShadcnCarouselTheme.defaultTheme(colorScheme),
      ShadcnCheckboxTheme.defaultTheme(colorScheme),
      ShadcnCommandTheme.defaultTheme(colorScheme),
      ShadcnContextMenuTheme.defaultTheme(colorScheme),
      ShadcnDialogTheme.defaultTheme(colorScheme),
      ShadcnHoverCardTheme.defaultTheme(colorScheme),
      ShadcnInputTheme.defaultTheme(colorScheme),
      ShadcnLabelTheme.defaultTheme(colorScheme),
      ShadcnNavigationMenuTheme.defaultTheme(colorScheme),
      ShadcnPopoverTheme.defaultTheme(colorScheme),
      ShadcnProgressTheme.defaultTheme(colorScheme),
      ShadcnRadioGroupTheme.defaultTheme(colorScheme),
      ShadcnResizableTheme.defaultTheme(colorScheme),
      ShadcnScrollAreaTheme.defaultTheme(colorScheme),
      ShadcnSelectTheme.defaultTheme(colorScheme),
      ShadcnSeparatorTheme.defaultTheme(colorScheme),
      ShadcnSheetTheme.defaultTheme(colorScheme),
      ShadcnSkeletonTheme.defaultTheme(colorScheme),
      ShadcnSwitchTheme.defaultTheme(colorScheme),
      ShadcnTableTheme.defaultTheme(colorScheme),
      ShadcnTabsTheme.defaultTheme(colorScheme),
      ShadcnToastTheme.defaultTheme(colorScheme),
      ShadcnTooltipTheme.defaultTheme(colorScheme),
    ];
    
    return baseTheme.copyWith(
      brightness: brightness,
      extensions: allExtensions,
    );
  }
}