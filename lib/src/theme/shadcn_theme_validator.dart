import 'package:flutter/material.dart';
import 'color_schemes/shadcn_color_scheme.dart';
import 'extensions/shadcn_accordion_theme.dart';
import 'extensions/shadcn_alert_theme.dart';
import 'extensions/shadcn_aspect_ratio_theme.dart';
import 'extensions/shadcn_avatar_theme.dart';
import 'extensions/shadcn_badge_theme.dart';
import 'extensions/shadcn_breadcrumb_theme.dart';
import 'extensions/shadcn_button_theme.dart';
import 'extensions/shadcn_calendar_theme.dart';
import 'extensions/shadcn_card_theme.dart';
import 'extensions/shadcn_carousel_theme.dart';
import 'extensions/shadcn_checkbox_theme.dart';
import 'extensions/shadcn_command_theme.dart';
import 'extensions/shadcn_context_menu_theme.dart';
import 'extensions/shadcn_dialog_theme.dart';
import 'extensions/shadcn_hover_card_theme.dart';
import 'extensions/shadcn_input_theme.dart';
import 'extensions/shadcn_label_theme.dart';
import 'extensions/shadcn_navigation_menu_theme.dart';
import 'extensions/shadcn_popover_theme.dart';
import 'extensions/shadcn_progress_theme.dart';
import 'extensions/shadcn_radio_group_theme.dart';
import 'extensions/shadcn_resizable_theme.dart';
import 'extensions/shadcn_scroll_area_theme.dart';
import 'extensions/shadcn_select_theme.dart';
import 'extensions/shadcn_separator_theme.dart';
import 'extensions/shadcn_sheet_theme.dart';
import 'extensions/shadcn_skeleton_theme.dart';
import 'extensions/shadcn_switch_theme.dart';
import 'extensions/shadcn_table_theme.dart';
import 'extensions/shadcn_tabs_theme.dart';
import 'extensions/shadcn_toast_theme.dart';
import 'extensions/shadcn_tooltip_theme.dart';
import 'extensions/shadcn_theme_extension.dart';
import '../utils/theme_resolver.dart';

/// Comprehensive theme validation utilities for shadcn themes.
/// 
/// This class provides methods to validate theme completeness, consistency,
/// and compatibility. It ensures that all required component theme extensions
/// are present and properly configured for robust theme functionality.
class ShadcnThemeValidator {
  ShadcnThemeValidator._();
  
  /// List of all required theme extension types for a complete shadcn theme.
  /// 
  /// This list represents all 51 shadcn components plus the core color scheme
  /// extension. Any theme missing these extensions will be considered incomplete.
  static final List<Type> requiredThemeExtensions = [
    ShadcnColorScheme,
    ShadcnAccordionTheme,
    ShadcnAlertTheme,
    ShadcnAspectRatioTheme,
    ShadcnAvatarTheme,
    ShadcnBadgeTheme,
    ShadcnBreadcrumbTheme,
    ShadcnButtonTheme,
    ShadcnCalendarTheme,
    ShadcnCardTheme,
    ShadcnCarouselTheme,
    ShadcnCheckboxTheme,
    ShadcnCommandTheme,
    ShadcnContextMenuTheme,
    ShadcnDialogTheme,
    ShadcnHoverCardTheme,
    ShadcnInputTheme,
    ShadcnLabelTheme,
    ShadcnNavigationMenuTheme,
    ShadcnPopoverTheme,
    ShadcnProgressTheme,
    ShadcnRadioGroupTheme,
    ShadcnResizableTheme,
    ShadcnScrollAreaTheme,
    ShadcnSelectTheme,
    ShadcnSeparatorTheme,
    ShadcnSheetTheme,
    ShadcnSkeletonTheme,
    ShadcnSwitchTheme,
    ShadcnTableTheme,
    ShadcnTabsTheme,
    ShadcnToastTheme,
    ShadcnTooltipTheme,
  ];
  
  /// Validates that a theme has all required shadcn component extensions.
  /// 
  /// This method checks that all 51+ shadcn component theme extensions are
  /// present in the theme. It's essential for ensuring complete shadcn
  /// functionality across all components.
  /// 
  /// [theme] - The theme to validate for completeness
  /// [throwOnError] - Whether to throw exceptions on validation failures
  /// 
  /// Returns a [ThemeValidationResult] with validation details.
  static ThemeValidationResult validateThemeCompleteness({
    required ThemeData theme,
    bool throwOnError = false,
  }) {
    final List<String> errors = [];
    final List<String> warnings = [];
    final List<Type> missingExtensions = [];
    
    try {
      // Check for each required extension
      for (final extensionType in requiredThemeExtensions) {
        if (!theme.extensions.containsKey(extensionType)) {
          missingExtensions.add(extensionType);
          errors.add('Missing required theme extension: ${extensionType.toString()}');
        }
      }
      
      // Validate core theme properties
      if (theme.colorScheme.primary.alpha == 0) {
        warnings.add('Primary color has zero alpha, may cause visibility issues');
      }
      
      if (theme.colorScheme.surface == theme.colorScheme.background) {
        warnings.add('Surface and background colors are identical, may reduce visual hierarchy');
      }
      
      // Check brightness consistency
      if (theme.brightness != theme.colorScheme.brightness) {
        errors.add('Theme brightness (${theme.brightness}) does not match ColorScheme brightness (${theme.colorScheme.brightness})');
      }
      
      final bool isValid = errors.isEmpty;
      
      if (!isValid && throwOnError) {
        throw ThemeValidationException(
          'Theme completeness validation failed',
          errors: errors,
          warnings: warnings,
          missingExtensions: missingExtensions,
        );
      }
      
      return ThemeValidationResult(
        isValid: isValid,
        errors: errors,
        warnings: warnings,
        missingExtensions: missingExtensions,
        validationType: ThemeValidationType.completeness,
      );
      
    } catch (e) {
      final error = 'Unexpected error during theme completeness validation: $e';
      if (throwOnError) {
        throw ThemeValidationException(error, errors: [error]);
      }
      
      return ThemeValidationResult(
        isValid: false,
        errors: [error],
        warnings: warnings,
        missingExtensions: missingExtensions,
        validationType: ThemeValidationType.completeness,
      );
    }
  }
  
  /// Validates the consistency of theme properties across extensions.
  /// 
  /// This method checks that theme extensions have consistent properties,
  /// such as matching border radius values, consistent color usage patterns,
  /// and compatible animation durations.
  /// 
  /// [theme] - The theme to validate for consistency
  /// [throwOnError] - Whether to throw exceptions on validation failures
  /// 
  /// Returns a [ThemeValidationResult] with consistency validation details.
  static ThemeValidationResult validateThemeConsistency({
    required ThemeData theme,
    bool throwOnError = false,
  }) {
    final List<String> errors = [];
    final List<String> warnings = [];
    
    try {
      final extensions = theme.extensions;
      
      // Collect border radius values from components
      final Set<BorderRadius?> borderRadiusValues = {};
      
      // Check button theme
      final buttonTheme = extensions[ShadcnButtonTheme] as ShadcnButtonTheme?;
      if (buttonTheme != null) {
        borderRadiusValues.add(buttonTheme.borderRadius);
      }
      
      // Check card theme
      final cardTheme = extensions[ShadcnCardTheme] as ShadcnCardTheme?;
      if (cardTheme != null) {
        borderRadiusValues.add(cardTheme.borderRadius);
      }
      
      // Check input theme
      final inputTheme = extensions[ShadcnInputTheme] as ShadcnInputTheme?;
      if (inputTheme != null) {
        borderRadiusValues.add(inputTheme.borderRadius);
      }
      
      // Validate border radius consistency (allowing for null values)
      final nonNullRadii = borderRadiusValues.where((r) => r != null).toList();
      if (nonNullRadii.length > 1) {
        final uniqueRadii = nonNullRadii.toSet();
        if (uniqueRadii.length > 3) { // Allow some variation but not too much
          warnings.add('Inconsistent border radius values across components: ${uniqueRadii.map((r) => r.toString()).join(', ')}');
        }
      }
      
      // Validate animation durations
      final Set<Duration?> animationDurations = {};
      
      if (buttonTheme?.animationDuration != null) {
        animationDurations.add(buttonTheme!.animationDuration);
      }
      
      // Check for reasonable animation durations
      for (final duration in animationDurations.where((d) => d != null)) {
        if (duration!.inMilliseconds > 1000) {
          warnings.add('Animation duration ${duration.inMilliseconds}ms may be too slow for good UX');
        }
        if (duration.inMilliseconds < 50) {
          warnings.add('Animation duration ${duration.inMilliseconds}ms may be too fast to perceive');
        }
      }
      
      // Validate color scheme consistency with shadcn color scheme
      final shadcnColors = extensions[ShadcnColorScheme] as ShadcnColorScheme?;
      if (shadcnColors != null) {
        // Check that foreground colors have sufficient contrast
        if (_getColorLuminance(shadcnColors.background) < 0.1 && 
            _getColorLuminance(shadcnColors.foreground) < 0.5) {
          warnings.add('Foreground color may not have sufficient contrast with background');
        }
        
        // Check that card colors are distinct from background
        if (shadcnColors.card == shadcnColors.background) {
          warnings.add('Card color is identical to background color, may reduce visual hierarchy');
        }
      }
      
      final bool isValid = errors.isEmpty;
      
      if (!isValid && throwOnError) {
        throw ThemeValidationException(
          'Theme consistency validation failed',
          errors: errors,
          warnings: warnings,
        );
      }
      
      return ThemeValidationResult(
        isValid: isValid,
        errors: errors,
        warnings: warnings,
        validationType: ThemeValidationType.consistency,
      );
      
    } catch (e) {
      final error = 'Unexpected error during theme consistency validation: $e';
      if (throwOnError) {
        throw ThemeValidationException(error, errors: [error]);
      }
      
      return ThemeValidationResult(
        isValid: false,
        errors: [error],
        warnings: warnings,
        validationType: ThemeValidationType.consistency,
      );
    }
  }
  
  /// Validates individual theme extension properties.
  /// 
  /// This method calls the validate() method on each theme extension
  /// to ensure that extension-specific validation rules are met.
  /// 
  /// [theme] - The theme to validate extensions for
  /// [throwOnError] - Whether to throw exceptions on validation failures
  /// 
  /// Returns a [ThemeValidationResult] with extension validation details.
  static ThemeValidationResult validateThemeExtensions({
    required ThemeData theme,
    bool throwOnError = false,
  }) {
    final List<String> errors = [];
    final List<String> warnings = [];
    
    try {
      for (final extension in theme.extensions.values) {
        if (extension is ShadcnThemeExtension) {
          try {
            final isValid = extension.validate(throwOnError: false);
            if (!isValid) {
              warnings.add('Theme extension ${extension.runtimeType} failed validation');
            }
          } catch (e) {
            errors.add('Theme extension ${extension.runtimeType} validation error: $e');
          }
        }
      }
      
      final bool isValid = errors.isEmpty;
      
      if (!isValid && throwOnError) {
        throw ThemeValidationException(
          'Theme extension validation failed',
          errors: errors,
          warnings: warnings,
        );
      }
      
      return ThemeValidationResult(
        isValid: isValid,
        errors: errors,
        warnings: warnings,
        validationType: ThemeValidationType.extensions,
      );
      
    } catch (e) {
      final error = 'Unexpected error during theme extension validation: $e';
      if (throwOnError) {
        throw ThemeValidationException(error, errors: [error]);
      }
      
      return ThemeValidationResult(
        isValid: false,
        errors: [error],
        warnings: warnings,
        validationType: ThemeValidationType.extensions,
      );
    }
  }
  
  /// Performs comprehensive validation of a theme.
  /// 
  /// This method runs all validation checks (completeness, consistency, and
  /// extensions) and returns a combined result. This is the recommended
  /// method for thorough theme validation.
  /// 
  /// [theme] - The theme to validate comprehensively
  /// [throwOnError] - Whether to throw exceptions on validation failures
  /// 
  /// Returns a [ThemeValidationResult] with all validation details combined.
  static ThemeValidationResult validateTheme({
    required ThemeData theme,
    bool throwOnError = false,
  }) {
    try {
      // Run all validation checks
      final completeness = validateThemeCompleteness(theme: theme, throwOnError: false);
      final consistency = validateThemeConsistency(theme: theme, throwOnError: false);
      final extensions = validateThemeExtensions(theme: theme, throwOnError: false);
      
      // Combine results
      final allErrors = <String>[
        ...completeness.errors,
        ...consistency.errors,
        ...extensions.errors,
      ];
      
      final allWarnings = <String>[
        ...completeness.warnings,
        ...consistency.warnings,
        ...extensions.warnings,
      ];
      
      final bool isValid = allErrors.isEmpty;
      
      if (!isValid && throwOnError) {
        throw ThemeValidationException(
          'Comprehensive theme validation failed',
          errors: allErrors,
          warnings: allWarnings,
          missingExtensions: completeness.missingExtensions,
        );
      }
      
      return ThemeValidationResult(
        isValid: isValid,
        errors: allErrors,
        warnings: allWarnings,
        missingExtensions: completeness.missingExtensions,
        validationType: ThemeValidationType.comprehensive,
      );
      
    } catch (e) {
      final error = 'Unexpected error during comprehensive theme validation: $e';
      if (throwOnError) rethrow;
      
      return ThemeValidationResult(
        isValid: false,
        errors: [error],
        warnings: [],
        validationType: ThemeValidationType.comprehensive,
      );
    }
  }
  
  /// Validates compatibility between light and dark themes.
  /// 
  /// This method checks that light and dark themes have compatible structures,
  /// matching extension types, and appropriate brightness values. Essential
  /// for theme switching functionality.
  /// 
  /// [lightTheme] - The light theme to validate
  /// [darkTheme] - The dark theme to validate
  /// [throwOnError] - Whether to throw exceptions on validation failures
  /// 
  /// Returns a [ThemeValidationResult] with compatibility validation details.
  static ThemeValidationResult validateThemeCompatibility({
    required ThemeData lightTheme,
    required ThemeData darkTheme,
    bool throwOnError = false,
  }) {
    final List<String> errors = [];
    final List<String> warnings = [];
    
    try {
      // Validate brightness values
      if (lightTheme.brightness != Brightness.light) {
        errors.add('Light theme has incorrect brightness: ${lightTheme.brightness}');
      }
      
      if (darkTheme.brightness != Brightness.dark) {
        errors.add('Dark theme has incorrect brightness: ${darkTheme.brightness}');
      }
      
      // Check extension compatibility
      final lightExtensions = lightTheme.extensions.keys.toSet();
      final darkExtensions = darkTheme.extensions.keys.toSet();
      
      final onlyInLight = lightExtensions.difference(darkExtensions);
      final onlyInDark = darkExtensions.difference(lightExtensions);
      
      if (onlyInLight.isNotEmpty) {
        errors.add('Extensions only in light theme: ${onlyInLight.map((t) => t.toString()).join(', ')}');
      }
      
      if (onlyInDark.isNotEmpty) {
        errors.add('Extensions only in dark theme: ${onlyInDark.map((t) => t.toString()).join(', ')}');
      }
      
      // Validate that both themes are individually valid
      final lightValidation = validateTheme(theme: lightTheme, throwOnError: false);
      final darkValidation = validateTheme(theme: darkTheme, throwOnError: false);
      
      if (!lightValidation.isValid) {
        errors.add('Light theme is not valid: ${lightValidation.errors.join(', ')}');
      }
      
      if (!darkValidation.isValid) {
        errors.add('Dark theme is not valid: ${darkValidation.errors.join(', ')}');
      }
      
      // Check for consistent font families
      if (lightTheme.textTheme.bodyMedium?.fontFamily != darkTheme.textTheme.bodyMedium?.fontFamily) {
        warnings.add('Font families differ between themes: "${lightTheme.textTheme.bodyMedium?.fontFamily}" vs "${darkTheme.textTheme.bodyMedium?.fontFamily}"');
      }
      
      final bool isValid = errors.isEmpty;
      
      if (!isValid && throwOnError) {
        throw ThemeValidationException(
          'Theme compatibility validation failed',
          errors: errors,
          warnings: warnings,
        );
      }
      
      return ThemeValidationResult(
        isValid: isValid,
        errors: errors,
        warnings: warnings,
        validationType: ThemeValidationType.compatibility,
      );
      
    } catch (e) {
      final error = 'Unexpected error during theme compatibility validation: $e';
      if (throwOnError) rethrow;
      
      return ThemeValidationResult(
        isValid: false,
        errors: [error],
        warnings: warnings,
        validationType: ThemeValidationType.compatibility,
      );
    }
  }
  
  /// Helper method to calculate color luminance
  static double _getColorLuminance(Color color) {
    return color.computeLuminance();
  }
}

/// Enumeration of validation types for theme validation results.
enum ThemeValidationType {
  /// Validation for theme completeness (all required extensions present)
  completeness,
  
  /// Validation for theme consistency (properties are consistent across extensions)
  consistency,
  
  /// Validation for individual theme extensions
  extensions,
  
  /// Comprehensive validation (all validation types combined)
  comprehensive,
  
  /// Validation for compatibility between light and dark themes
  compatibility,
}

/// Result object containing theme validation details.
/// 
/// This class encapsulates the results of theme validation operations,
/// including validation success status, error messages, warnings,
/// and additional context information.
class ThemeValidationResult {
  /// Whether the theme passed validation
  final bool isValid;
  
  /// List of validation error messages
  final List<String> errors;
  
  /// List of validation warning messages
  final List<String> warnings;
  
  /// List of missing theme extension types (for completeness validation)
  final List<Type> missingExtensions;
  
  /// The type of validation that was performed
  final ThemeValidationType validationType;
  
  const ThemeValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
    required this.validationType,
    this.missingExtensions = const [],
  });
  
  /// Gets the total number of issues (errors + warnings)
  int get totalIssues => errors.length + warnings.length;
  
  /// Gets a formatted summary of the validation result
  String get summary {
    final buffer = StringBuffer();
    buffer.writeln('Theme Validation Result (${validationType.name}):');
    buffer.writeln('  Status: ${isValid ? 'VALID' : 'INVALID'}');
    buffer.writeln('  Errors: ${errors.length}');
    buffer.writeln('  Warnings: ${warnings.length}');
    
    if (missingExtensions.isNotEmpty) {
      buffer.writeln('  Missing Extensions: ${missingExtensions.length}');
    }
    
    if (errors.isNotEmpty) {
      buffer.writeln('  Error Details:');
      for (final error in errors) {
        buffer.writeln('    - $error');
      }
    }
    
    if (warnings.isNotEmpty) {
      buffer.writeln('  Warning Details:');
      for (final warning in warnings) {
        buffer.writeln('    - $warning');
      }
    }
    
    return buffer.toString();
  }
  
  @override
  String toString() => summary;
}

/// Exception thrown when theme validation fails with throwOnError enabled.
/// 
/// This exception provides detailed information about validation failures,
/// including specific errors, warnings, and missing extensions.
class ThemeValidationException implements Exception {
  /// The main error message
  final String message;
  
  /// List of specific validation errors
  final List<String> errors;
  
  /// List of validation warnings
  final List<String> warnings;
  
  /// List of missing theme extensions
  final List<Type> missingExtensions;
  
  const ThemeValidationException(
    this.message, {
    this.errors = const [],
    this.warnings = const [],
    this.missingExtensions = const [],
  });
  
  @override
  String toString() {
    final buffer = StringBuffer();
    buffer.writeln('ThemeValidationException: $message');
    
    if (errors.isNotEmpty) {
      buffer.writeln('Errors:');
      for (final error in errors) {
        buffer.writeln('  - $error');
      }
    }
    
    if (warnings.isNotEmpty) {
      buffer.writeln('Warnings:');
      for (final warning in warnings) {
        buffer.writeln('  - $warning');
      }
    }
    
    if (missingExtensions.isNotEmpty) {
      buffer.writeln('Missing Extensions:');
      for (final extension in missingExtensions) {
        buffer.writeln('  - ${extension.toString()}');
      }
    }
    
    return buffer.toString();
  }
}