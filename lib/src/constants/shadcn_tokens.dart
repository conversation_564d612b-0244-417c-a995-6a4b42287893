import 'package:flutter/material.dart';

/// Contains all shadcn design tokens including spacing, sizes, colors, and typography.
/// 
/// These tokens are based on the shadcn design system and provide consistent
/// values across all components. Values match the exact shadcn specifications
/// while being adapted for Flutter's density and accessibility requirements.
class ShadcnTokens {
  ShadcnTokens._();
  
  // =============================================================================
  // SPACING TOKENS
  // =============================================================================
  
  /// No spacing (0px)
  static const double spacing0 = 0.0;
  
  /// Extra small spacing (4px)
  static const double spacing1 = 4.0;
  
  /// Small spacing (8px)
  static const double spacing2 = 8.0;
  
  /// Medium small spacing (12px)
  static const double spacing3 = 12.0;
  
  /// Medium spacing (16px)
  static const double spacing4 = 16.0;
  
  /// Medium large spacing (20px)
  static const double spacing5 = 20.0;
  
  /// Large spacing (24px)
  static const double spacing6 = 24.0;
  
  /// Extra large spacing (32px)
  static const double spacing8 = 32.0;
  
  /// Extra extra large spacing (40px)
  static const double spacing10 = 40.0;
  
  /// Maximum spacing (48px)
  static const double spacing12 = 48.0;
  
  // =============================================================================
  // SIZE TOKENS
  // =============================================================================
  
  // Button sizes (matching shadcn specifications)
  /// Small button height (36px)
  static const double buttonHeightSm = 36.0;
  
  /// Default button height (40px) - shadcn standard
  static const double buttonHeightMd = 40.0;
  
  /// Large button height (44px)
  static const double buttonHeightLg = 44.0;
  
  // Input sizes (matching shadcn specifications)
  /// Small input height (32px)
  static const double inputHeightSm = 32.0;
  
  /// Default input height (36px) - shadcn standard
  static const double inputHeightMd = 36.0;
  
  /// Large input height (40px)
  static const double inputHeightLg = 40.0;
  
  // Icon sizes
  /// Small icon size (16px)
  static const double iconSizeSm = 16.0;
  
  /// Default icon size (20px)
  static const double iconSizeMd = 20.0;
  
  /// Large icon size (24px)
  static const double iconSizeLg = 24.0;
  
  // Avatar sizes
  /// Small avatar size (32px)
  static const double avatarSizeSm = 32.0;
  
  /// Default avatar size (40px)
  static const double avatarSizeMd = 40.0;
  
  /// Large avatar size (48px)
  static const double avatarSizeLg = 48.0;
  
  // =============================================================================
  // BORDER RADIUS TOKENS
  // =============================================================================
  
  /// No border radius (0px)
  static const double radiusNone = 0.0;
  
  /// Small border radius (4px)
  static const double radiusSm = 4.0;
  
  /// Default border radius (6px) - shadcn standard
  static const double radiusMd = 6.0;
  
  /// Large border radius (8px)
  static const double radiusLg = 8.0;
  
  /// Extra large border radius (12px)
  static const double radiusXl = 12.0;
  
  /// Maximum border radius (16px)
  static const double radiusXxl = 16.0;
  
  /// Full border radius (9999px equivalent)
  static const double radiusFull = 9999.0;
  
  // =============================================================================
  // BORDER WIDTH TOKENS
  // =============================================================================
  
  /// Default border width (1px)
  static const double borderWidth = 1.0;
  
  /// Thick border width (2px)
  static const double borderWidthThick = 2.0;
  
  // =============================================================================
  // FONT SIZE TOKENS
  // =============================================================================
  
  /// Extra small font size (10px)
  static const double fontSizeXs = 10.0;
  
  /// Small font size (12px)
  static const double fontSizeSm = 12.0;
  
  /// Default font size (14px) - shadcn standard
  static const double fontSizeMd = 14.0;
  
  /// Large font size (16px)
  static const double fontSizeLg = 16.0;
  
  /// Extra large font size (18px)
  static const double fontSizeXl = 18.0;
  
  /// Extra extra large font size (20px)
  static const double fontSizeXxl = 20.0;
  
  /// Title font size (24px)
  static const double fontSizeTitle = 24.0;
  
  // =============================================================================
  // LINE HEIGHT TOKENS
  // =============================================================================
  
  /// Tight line height (1.25)
  static const double lineHeightTight = 1.25;
  
  /// Normal line height (1.5)
  static const double lineHeightNormal = 1.5;
  
  /// Relaxed line height (1.75)
  static const double lineHeightRelaxed = 1.75;
  
  // =============================================================================
  // FONT WEIGHT TOKENS
  // =============================================================================
  
  /// Normal font weight (400)
  static const FontWeight fontWeightNormal = FontWeight.w400;
  
  /// Medium font weight (500)
  static const FontWeight fontWeightMedium = FontWeight.w500;
  
  /// Semibold font weight (600)
  static const FontWeight fontWeightSemibold = FontWeight.w600;
  
  /// Bold font weight (700)
  static const FontWeight fontWeightBold = FontWeight.w700;
  
  // =============================================================================
  // ELEVATION TOKENS
  // =============================================================================
  
  /// No elevation (0dp)
  static const double elevationNone = 0.0;
  
  /// Small elevation (1dp)
  static const double elevationSm = 1.0;
  
  /// Default elevation (4dp)
  static const double elevationMd = 4.0;
  
  /// Large elevation (8dp)
  static const double elevationLg = 8.0;
  
  /// Extra large elevation (16dp)
  static const double elevationXl = 16.0;
  
  // =============================================================================
  // OPACITY TOKENS
  // =============================================================================
  
  /// Disabled opacity (0.5)
  static const double opacityDisabled = 0.5;
  
  /// Hover opacity (0.8)
  static const double opacityHover = 0.8;
  
  /// Pressed opacity (0.9)
  static const double opacityPressed = 0.9;
  
  // =============================================================================
  // DURATION TOKENS
  // =============================================================================
  
  /// Fast animation duration (150ms)
  static const Duration durationFast = Duration(milliseconds: 150);
  
  /// Normal animation duration (250ms)
  static const Duration durationNormal = Duration(milliseconds: 250);
  
  /// Slow animation duration (350ms)
  static const Duration durationSlow = Duration(milliseconds: 350);
  
  // =============================================================================
  // CONVENIENCE METHODS
  // =============================================================================
  
  /// Returns EdgeInsets based on spacing token
  static EdgeInsets paddingAll(double spacing) => EdgeInsets.all(spacing);
  
  /// Returns symmetric horizontal EdgeInsets
  static EdgeInsets paddingHorizontal(double spacing) => 
      EdgeInsets.symmetric(horizontal: spacing);
  
  /// Returns symmetric vertical EdgeInsets
  static EdgeInsets paddingVertical(double spacing) => 
      EdgeInsets.symmetric(vertical: spacing);
  
  /// Returns BorderRadius based on radius token
  static BorderRadius borderRadius(double radius) => 
      BorderRadius.circular(radius);
  
  /// Returns default button padding
  static EdgeInsets get buttonPadding => 
      EdgeInsets.symmetric(horizontal: spacing4, vertical: spacing2);
  
  /// Returns default input padding
  static EdgeInsets get inputPadding => 
      EdgeInsets.symmetric(horizontal: spacing3, vertical: spacing2);
  
  /// Returns default card padding
  static EdgeInsets get cardPadding => paddingAll(spacing6);
}