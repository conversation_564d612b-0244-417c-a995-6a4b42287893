import 'package:flutter/material.dart';
import 'dart:async';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_hover_card_theme.dart';

/// Position preference for the hover card
enum ShadcnHoverCardPosition {
  top,
  bottom,
  left,
  right,
  auto, // Automatically choose the best position
}

/// A hover card component with positioning and delay handling
/// 
/// The ShadcnHoverCard widget provides a hover card that appears when hovering
/// over a trigger widget, with configurable positioning and timing.
class ShadcnHoverCard extends ShadcnComponent {
  /// The child widget that triggers the hover card
  final Widget child;
  
  /// The content to display in the hover card
  final Widget content;
  
  /// Preferred position for the hover card
  final ShadcnHoverCardPosition position;
  
  /// Custom offset from the trigger widget
  final Offset? offset;
  
  /// Delay before showing the hover card
  final Duration? hoverDelay;
  
  /// Delay before hiding the hover card
  final Duration? hideDelay;
  
  /// Whether the hover card is enabled
  final bool enabled;
  
  /// Custom width for the hover card
  final double? width;
  
  /// Custom height for the hover card
  final double? height;
  
  /// Callback when the hover card is shown
  final VoidCallback? onShow;
  
  /// Callback when the hover card is hidden
  final VoidCallback? onHide;
  
  /// Whether to close when the content is clicked
  final bool closeOnContentClick;

  const ShadcnHoverCard({
    Key? key,
    required this.child,
    required this.content,
    this.position = ShadcnHoverCardPosition.auto,
    this.offset,
    this.hoverDelay,
    this.hideDelay,
    this.enabled = true,
    this.width,
    this.height,
    this.onShow,
    this.onHide,
    this.closeOnContentClick = false,
  }) : super(key: key);

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    final hoverCardTheme = theme.extension<ShadcnHoverCardTheme>() ??
        ShadcnHoverCardTheme.defaultTheme(theme.colorScheme);

    if (!enabled) {
      return child;
    }

    return _HoverCardTrigger(
      content: content,
      theme: hoverCardTheme,
      position: position,
      offset: offset,
      hoverDelay: hoverDelay,
      hideDelay: hideDelay,
      width: width,
      height: height,
      onShow: onShow,
      onHide: onHide,
      closeOnContentClick: closeOnContentClick,
      child: child,
    );
  }
}

class _HoverCardTrigger extends StatefulWidget {
  final Widget child;
  final Widget content;
  final ShadcnHoverCardTheme theme;
  final ShadcnHoverCardPosition position;
  final Offset? offset;
  final Duration? hoverDelay;
  final Duration? hideDelay;
  final double? width;
  final double? height;
  final VoidCallback? onShow;
  final VoidCallback? onHide;
  final bool closeOnContentClick;

  const _HoverCardTrigger({
    required this.child,
    required this.content,
    required this.theme,
    required this.position,
    this.offset,
    this.hoverDelay,
    this.hideDelay,
    this.width,
    this.height,
    this.onShow,
    this.onHide,
    required this.closeOnContentClick,
  });

  @override
  State<_HoverCardTrigger> createState() => _HoverCardTriggerState();
}

class _HoverCardTriggerState extends State<_HoverCardTrigger>
    with SingleTickerProviderStateMixin {
  OverlayEntry? _overlayEntry;
  Timer? _showTimer;
  Timer? _hideTimer;
  bool _isHovering = false;
  bool _isContentHovering = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.theme.showDuration,
      reverseDuration: widget.theme.hideDuration,
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.95,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: widget.theme.showCurve ?? Curves.easeOut,
      reverseCurve: widget.theme.hideCurve ?? Curves.easeIn,
    ));
    
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: widget.theme.showCurve ?? Curves.easeOut,
      reverseCurve: widget.theme.hideCurve ?? Curves.easeIn,
    ));
  }

  @override
  void dispose() {
    _showTimer?.cancel();
    _hideTimer?.cancel();
    _removeOverlay();
    _animationController.dispose();
    super.dispose();
  }

  void _onEnter() {
    if (_isHovering) return;
    
    _isHovering = true;
    _hideTimer?.cancel();
    
    final delay = widget.hoverDelay ?? widget.theme.hoverDelay ?? Duration.zero;
    
    _showTimer = Timer(delay, () {
      if (_isHovering && mounted) {
        _showOverlay();
      }
    });
  }

  void _onExit() {
    _isHovering = false;
    _showTimer?.cancel();
    
    final delay = widget.hideDelay ?? widget.theme.hideDelay ?? Duration.zero;
    
    _hideTimer = Timer(delay, () {
      if (!_isHovering && !_isContentHovering && mounted) {
        _hideOverlay();
      }
    });
  }

  void _onContentEnter() {
    _isContentHovering = true;
    _hideTimer?.cancel();
  }

  void _onContentExit() {
    _isContentHovering = false;
    
    final delay = widget.hideDelay ?? widget.theme.hideDelay ?? Duration.zero;
    
    _hideTimer = Timer(delay, () {
      if (!_isHovering && !_isContentHovering && mounted) {
        _hideOverlay();
      }
    });
  }

  void _showOverlay() {
    if (_overlayEntry != null) return;

    final overlay = Overlay.of(context);
    final renderBox = context.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;

    _overlayEntry = OverlayEntry(
      builder: (context) => _HoverCardOverlay(
        triggerPosition: position,
        triggerSize: size,
        content: widget.content,
        theme: widget.theme,
        position: widget.position,
        offset: widget.offset,
        width: widget.width,
        height: widget.height,
        scaleAnimation: _scaleAnimation,
        opacityAnimation: _opacityAnimation,
        onContentEnter: _onContentEnter,
        onContentExit: _onContentExit,
        closeOnContentClick: widget.closeOnContentClick,
        onContentClick: widget.closeOnContentClick ? _hideOverlay : null,
      ),
    );

    overlay.insert(_overlayEntry!);
    _animationController.forward();
    widget.onShow?.call();
  }

  void _hideOverlay() {
    if (_overlayEntry == null) return;

    _animationController.reverse().then((_) {
      _removeOverlay();
    });
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    widget.onHide?.call();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => _onEnter(),
      onExit: (_) => _onExit(),
      child: widget.child,
    );
  }
}

class _HoverCardOverlay extends StatelessWidget {
  final Offset triggerPosition;
  final Size triggerSize;
  final Widget content;
  final ShadcnHoverCardTheme theme;
  final ShadcnHoverCardPosition position;
  final Offset? offset;
  final double? width;
  final double? height;
  final Animation<double> scaleAnimation;
  final Animation<double> opacityAnimation;
  final VoidCallback onContentEnter;
  final VoidCallback onContentExit;
  final bool closeOnContentClick;
  final VoidCallback? onContentClick;

  const _HoverCardOverlay({
    required this.triggerPosition,
    required this.triggerSize,
    required this.content,
    required this.theme,
    required this.position,
    this.offset,
    this.width,
    this.height,
    required this.scaleAnimation,
    required this.opacityAnimation,
    required this.onContentEnter,
    required this.onContentExit,
    required this.closeOnContentClick,
    this.onContentClick,
  });

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final cardSize = Size(
      width ?? theme.maxWidth ?? 320,
      height ?? theme.maxHeight ?? 200,
    );

    final cardPosition = _calculatePosition(screenSize, cardSize);

    return Positioned(
      left: cardPosition.dx,
      top: cardPosition.dy,
      child: AnimatedBuilder(
        animation: scaleAnimation,
        child: _buildCard(),
        builder: (context, child) => Transform.scale(
          scale: scaleAnimation.value,
          alignment: _getTransformAlignment(),
          child: Opacity(
            opacity: opacityAnimation.value,
            child: child,
          ),
        ),
      ),
    );
  }

  Widget _buildCard() {
    return MouseRegion(
      onEnter: (_) => onContentEnter(),
      onExit: (_) => onContentExit(),
      child: GestureDetector(
        onTap: closeOnContentClick ? onContentClick : null,
        child: Material(
          elevation: theme.elevation ?? 8,
          color: theme.background,
          borderRadius: theme.borderRadius,
          child: Container(
            constraints: BoxConstraints(
              minWidth: theme.minWidth ?? 200,
              maxWidth: width ?? theme.maxWidth ?? 320,
              minHeight: theme.minHeight ?? 60,
              maxHeight: height ?? theme.maxHeight ?? 200,
            ),
            padding: theme.padding,
            margin: theme.margin,
            decoration: BoxDecoration(
              border: Border.all(color: theme.border ?? Colors.transparent),
              borderRadius: theme.borderRadius,
              boxShadow: theme.shadow,
            ),
            child: DefaultTextStyle(
              style: theme.textStyle ?? const TextStyle(),
              child: content,
            ),
          ),
        ),
      ),
    );
  }

  Offset _calculatePosition(Size screenSize, Size cardSize) {
    final baseOffset = offset ?? theme.offset ?? const Offset(0, 4);
    
    switch (position) {
      case ShadcnHoverCardPosition.top:
        return Offset(
          triggerPosition.dx + (triggerSize.width - cardSize.width) / 2 + baseOffset.dx,
          triggerPosition.dy - cardSize.height + baseOffset.dy,
        );
      
      case ShadcnHoverCardPosition.bottom:
        return Offset(
          triggerPosition.dx + (triggerSize.width - cardSize.width) / 2 + baseOffset.dx,
          triggerPosition.dy + triggerSize.height + baseOffset.dy,
        );
      
      case ShadcnHoverCardPosition.left:
        return Offset(
          triggerPosition.dx - cardSize.width + baseOffset.dx,
          triggerPosition.dy + (triggerSize.height - cardSize.height) / 2 + baseOffset.dy,
        );
      
      case ShadcnHoverCardPosition.right:
        return Offset(
          triggerPosition.dx + triggerSize.width + baseOffset.dx,
          triggerPosition.dy + (triggerSize.height - cardSize.height) / 2 + baseOffset.dy,
        );
      
      case ShadcnHoverCardPosition.auto:
        return _calculateAutoPosition(screenSize, cardSize, baseOffset);
    }
  }

  Offset _calculateAutoPosition(Size screenSize, Size cardSize, Offset baseOffset) {
    // Try bottom first
    final bottomPosition = Offset(
      triggerPosition.dx + (triggerSize.width - cardSize.width) / 2 + baseOffset.dx,
      triggerPosition.dy + triggerSize.height + baseOffset.dy,
    );
    
    if (bottomPosition.dy + cardSize.height <= screenSize.height) {
      return bottomPosition;
    }
    
    // Try top
    final topPosition = Offset(
      triggerPosition.dx + (triggerSize.width - cardSize.width) / 2 + baseOffset.dx,
      triggerPosition.dy - cardSize.height + baseOffset.dy,
    );
    
    if (topPosition.dy >= 0) {
      return topPosition;
    }
    
    // Try right
    final rightPosition = Offset(
      triggerPosition.dx + triggerSize.width + baseOffset.dx,
      triggerPosition.dy + (triggerSize.height - cardSize.height) / 2 + baseOffset.dy,
    );
    
    if (rightPosition.dx + cardSize.width <= screenSize.width) {
      return rightPosition;
    }
    
    // Try left
    final leftPosition = Offset(
      triggerPosition.dx - cardSize.width + baseOffset.dx,
      triggerPosition.dy + (triggerSize.height - cardSize.height) / 2 + baseOffset.dy,
    );
    
    if (leftPosition.dx >= 0) {
      return leftPosition;
    }
    
    // Default to bottom if nothing fits perfectly
    return bottomPosition;
  }

  Alignment _getTransformAlignment() {
    switch (position) {
      case ShadcnHoverCardPosition.top:
        return Alignment.bottomCenter;
      case ShadcnHoverCardPosition.bottom:
        return Alignment.topCenter;
      case ShadcnHoverCardPosition.left:
        return Alignment.centerRight;
      case ShadcnHoverCardPosition.right:
        return Alignment.centerLeft;
      case ShadcnHoverCardPosition.auto:
        return Alignment.topCenter; // Default alignment for auto
    }
  }
}