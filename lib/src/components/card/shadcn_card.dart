import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../constants/shadcn_tokens.dart';
import '../../theme/extensions/shadcn_card_theme.dart';
import '../shadcn_component.dart';

/// Defines the visual variants available for the ShadcnCard component.
/// 
/// Each variant provides different visual styling while maintaining
/// consistency with shadcn design principles and Material Design integration.
enum ShadcnCardVariant {
  /// Default variant with subtle background and minimal styling
  defaultCard,
  
  /// Outlined variant with visible border and no elevation
  outlined,
  
  /// Elevated variant with shadow and Material elevation system
  elevated,
}

/// A versatile card component that follows shadcn design principles
/// while integrating with Flutter's Material Design elevation system.
/// 
/// The ShadcnCard provides a flexible container with customizable styling
/// options including background colors, borders, shadows, and content padding.
/// It supports three main variants: default, outlined, and elevated.
/// 
/// All styling is derived from the theme system using [ShadcnCardTheme],
/// with automatic fallbacks to Material Design values when custom themes
/// are not provided.
/// 
/// Example usage:
/// ```dart
/// // Basic card with default styling
/// ShadcnCard(
///   child: Text('Card content'),
/// )
/// 
/// // Outlined card with custom padding
/// ShadcnCard(
///   variant: ShadcnCardVariant.outlined,
///   padding: EdgeInsets.all(24),
///   child: Column(
///     children: [
///       Text('Title'),
///       Text('Description'),
///     ],
///   ),
/// )
/// 
/// // Elevated card with custom elevation
/// ShadcnCard(
///   variant: ShadcnCardVariant.elevated,
///   elevation: 8.0,
///   child: ListTile(
///     title: Text('Card Title'),
///     subtitle: Text('Card subtitle'),
///   ),
/// )
/// ```
class ShadcnCard extends ShadcnComponent with ShadcnComponentValidation {
  /// The widget to display inside the card
  final Widget child;
  
  /// The visual variant of the card
  final ShadcnCardVariant variant;
  
  /// Custom background color (overrides theme)
  final Color? backgroundColor;
  
  /// Custom border color (overrides theme)
  final Color? borderColor;
  
  /// Custom foreground color for content (overrides theme)
  final Color? foregroundColor;
  
  /// Custom border width (overrides theme)
  final double? borderWidth;
  
  /// Custom border radius (overrides theme)
  final BorderRadius? borderRadius;
  
  /// Custom content padding (overrides theme)
  final EdgeInsets? padding;
  
  /// Custom margin around the card (overrides theme)
  final EdgeInsets? margin;
  
  /// Custom shadow color (overrides theme)
  final Color? shadowColor;
  
  /// Custom elevation value (overrides theme)
  final double? elevation;
  
  /// Custom box shadow configuration (overrides theme)
  final List<BoxShadow>? boxShadow;
  
  /// Custom constraints for the card
  final BoxConstraints? constraints;
  
  /// Custom width for the card
  final double? width;
  
  /// Custom height for the card
  final double? height;
  
  /// Callback when card is tapped
  final VoidCallback? onTap;
  
  /// Callback when card is long pressed
  final VoidCallback? onLongPress;
  
  /// Whether the card is interactive (affects cursor and hover states)
  final bool interactive;
  
  /// Whether the card is clickable (enables tap feedback)
  final bool clickable;
  
  /// Semantic label for accessibility
  final String? semanticLabel;
  
  /// Tooltip message for the card
  final String? tooltip;
  
  /// Custom mouse cursor when hovering
  final MouseCursor? mouseCursor;
  
  /// Clip behavior for the card content
  final Clip clipBehavior;
  
  /// Creates a ShadcnCard with customizable properties.
  /// 
  /// The [child] parameter is required and contains the card's content.
  /// All other parameters are optional and allow customization of the
  /// card's appearance and behavior.
  const ShadcnCard({
    super.key,
    required this.child,
    this.variant = ShadcnCardVariant.defaultCard,
    this.backgroundColor,
    this.borderColor,
    this.foregroundColor,
    this.borderWidth,
    this.borderRadius,
    this.padding,
    this.margin,
    this.shadowColor,
    this.elevation,
    this.boxShadow,
    this.constraints,
    this.width,
    this.height,
    this.onTap,
    this.onLongPress,
    this.interactive = false,
    this.clickable = false,
    this.semanticLabel,
    this.tooltip,
    this.mouseCursor,
    this.clipBehavior = Clip.none,
  });
  
  /// Creates a default variant ShadcnCard.
  const ShadcnCard.defaultCard({
    super.key,
    required this.child,
    this.backgroundColor,
    this.borderColor,
    this.foregroundColor,
    this.borderWidth,
    this.borderRadius,
    this.padding,
    this.margin,
    this.shadowColor,
    this.elevation,
    this.boxShadow,
    this.constraints,
    this.width,
    this.height,
    this.onTap,
    this.onLongPress,
    this.interactive = false,
    this.clickable = false,
    this.semanticLabel,
    this.tooltip,
    this.mouseCursor,
    this.clipBehavior = Clip.none,
  }) : variant = ShadcnCardVariant.defaultCard;
  
  /// Creates an outlined variant ShadcnCard.
  const ShadcnCard.outlined({
    super.key,
    required this.child,
    this.backgroundColor,
    this.borderColor,
    this.foregroundColor,
    this.borderWidth,
    this.borderRadius,
    this.padding,
    this.margin,
    this.shadowColor,
    this.elevation,
    this.boxShadow,
    this.constraints,
    this.width,
    this.height,
    this.onTap,
    this.onLongPress,
    this.interactive = false,
    this.clickable = false,
    this.semanticLabel,
    this.tooltip,
    this.mouseCursor,
    this.clipBehavior = Clip.none,
  }) : variant = ShadcnCardVariant.outlined;
  
  /// Creates an elevated variant ShadcnCard.
  const ShadcnCard.elevated({
    super.key,
    required this.child,
    this.backgroundColor,
    this.borderColor,
    this.foregroundColor,
    this.borderWidth,
    this.borderRadius,
    this.padding,
    this.margin,
    this.shadowColor,
    this.elevation,
    this.boxShadow,
    this.constraints,
    this.width,
    this.height,
    this.onTap,
    this.onLongPress,
    this.interactive = false,
    this.clickable = false,
    this.semanticLabel,
    this.tooltip,
    this.mouseCursor,
    this.clipBehavior = Clip.none,
  }) : variant = ShadcnCardVariant.elevated;
  
  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    // Validate theme and component properties in debug mode
    assert(() {
      validateThemeProperties(context);
      validateRequiredProperties({'child': child});
      validateVariant(
        variant,
        ShadcnCardVariant.values,
        'ShadcnCard',
      );
      validateSize(
        width: width,
        height: height,
        componentName: 'ShadcnCard',
      );
      validateAccessibility(
        semanticLabel: semanticLabel,
        tooltip: tooltip,
        componentName: 'ShadcnCard',
      );
      return true;
    }());
    
    // Resolve theme with fallback
    final cardTheme = resolveTheme<ShadcnCardTheme>(
      context,
      ShadcnCardTheme.defaultTheme,
    );
    
    // Apply variant-specific overrides
    final resolvedTheme = _applyVariantStyling(cardTheme, theme.colorScheme);
    
    // Resolve all styling properties
    final resolvedBackgroundColor = backgroundColor ?? resolvedTheme.resolveBackgroundColor(context);
    final resolvedBorderColor = borderColor ?? resolvedTheme.resolveBorderColor(context);
    final resolvedForegroundColor = foregroundColor ?? resolvedTheme.resolveForegroundColor(context);
    final resolvedBorderWidth = borderWidth ?? resolvedTheme.resolveBorderWidth(context);
    final resolvedBorderRadius = borderRadius ?? resolvedTheme.resolveCardBorderRadius(context);
    final resolvedPadding = padding ?? resolvedTheme.resolvePadding(context);
    final resolvedMargin = margin ?? resolvedTheme.resolveMargin(context);
    final resolvedElevation = elevation ?? resolvedTheme.resolveElevation(context);
    final resolvedShadowColor = shadowColor ?? resolvedTheme.resolveShadowColor(context);
    final resolvedBoxShadow = boxShadow ?? resolvedTheme.resolveBoxShadow(context);
    
    // Resolve constraints
    final resolvedConstraints = constraints ?? resolvedTheme.resolveConstraints(context);
    final finalConstraints = resolvedConstraints != null
        ? (width != null || height != null
            ? resolvedConstraints.copyWith(
                minWidth: width ?? resolvedConstraints.minWidth,
                maxWidth: width ?? resolvedConstraints.maxWidth,
                minHeight: height ?? resolvedConstraints.minHeight,
                maxHeight: height ?? resolvedConstraints.maxHeight,
              )
            : resolvedConstraints)
        : (width != null || height != null
            ? BoxConstraints.tightFor(width: width, height: height)
            : null);
    
    // Determine if card should be interactive
    final isInteractive = interactive || clickable || onTap != null || onLongPress != null;
    final cursor = mouseCursor ?? (isInteractive ? SystemMouseCursors.click : SystemMouseCursors.basic);
    
    // Build the card widget
    Widget cardContent = _buildCardContent(
      context,
      resolvedBackgroundColor: resolvedBackgroundColor,
      resolvedBorderColor: resolvedBorderColor,
      resolvedForegroundColor: resolvedForegroundColor,
      resolvedBorderWidth: resolvedBorderWidth,
      resolvedBorderRadius: resolvedBorderRadius,
      resolvedPadding: resolvedPadding,
      resolvedElevation: resolvedElevation,
      resolvedShadowColor: resolvedShadowColor,
      resolvedBoxShadow: resolvedBoxShadow,
    );
    
    // Apply constraints if specified
    if (finalConstraints != null) {
      cardContent = ConstrainedBox(
        constraints: finalConstraints,
        child: cardContent,
      );
    }
    
    // Apply margin if specified
    if (resolvedMargin != EdgeInsets.zero) {
      cardContent = Padding(
        padding: resolvedMargin,
        child: cardContent,
      );
    }
    
    // Add interaction handling if needed
    if (isInteractive) {
      cardContent = _buildInteractiveWrapper(
        context,
        cardContent,
        resolvedBorderRadius,
        cursor,
      );
    }
    
    // Add semantic labeling
    if (semanticLabel != null) {
      cardContent = Semantics(
        label: semanticLabel,
        container: true,
        child: cardContent,
      );
    }
    
    // Add tooltip if provided
    if (tooltip != null) {
      cardContent = Tooltip(
        message: tooltip!,
        child: cardContent,
      );
    }
    
    return cardContent;
  }
  
  /// Applies variant-specific styling to the theme.
  ShadcnCardTheme _applyVariantStyling(ShadcnCardTheme baseTheme, ColorScheme colorScheme) {
    switch (variant) {
      case ShadcnCardVariant.defaultCard:
        return baseTheme.copyWith(
          elevation: elevation ?? ShadcnTokens.elevationNone,
          borderWidth: borderWidth ?? 0.0,
        );
      
      case ShadcnCardVariant.outlined:
        return baseTheme.copyWith(
          elevation: elevation ?? ShadcnTokens.elevationNone,
          borderWidth: borderWidth ?? ShadcnTokens.borderWidth,
          borderColor: borderColor ?? colorScheme.outline,
        );
      
      case ShadcnCardVariant.elevated:
        return baseTheme.copyWith(
          elevation: elevation ?? ShadcnTokens.elevationMd,
          borderWidth: borderWidth ?? 0.0,
          shadowColor: shadowColor ?? colorScheme.shadow,
        );
    }
  }
  
  /// Builds the core card content with Material design integration.
  Widget _buildCardContent(
    BuildContext context, {
    required Color resolvedBackgroundColor,
    required Color resolvedBorderColor,
    required Color resolvedForegroundColor,
    required double resolvedBorderWidth,
    required BorderRadius resolvedBorderRadius,
    required EdgeInsets resolvedPadding,
    required double resolvedElevation,
    required Color resolvedShadowColor,
    required List<BoxShadow>? resolvedBoxShadow,
  }) {
    // Use Material widget to leverage elevation system
    if (resolvedElevation > 0) {
      return Material(
        elevation: resolvedElevation,
        color: resolvedBackgroundColor,
        shadowColor: resolvedShadowColor,
        borderRadius: resolvedBorderRadius,
        clipBehavior: clipBehavior,
        child: _buildCardContainer(
          context,
          resolvedBorderColor: resolvedBorderColor,
          resolvedBorderWidth: resolvedBorderWidth,
          resolvedBorderRadius: resolvedBorderRadius,
          resolvedPadding: resolvedPadding,
          resolvedForegroundColor: resolvedForegroundColor,
          useContainer: false, // Material already provides background
        ),
      );
    }
    
    // Use Container for non-elevated cards
    return _buildCardContainer(
      context,
      backgroundColor: resolvedBackgroundColor,
      resolvedBorderColor: resolvedBorderColor,
      resolvedBorderWidth: resolvedBorderWidth,
      resolvedBorderRadius: resolvedBorderRadius,
      resolvedPadding: resolvedPadding,
      resolvedForegroundColor: resolvedForegroundColor,
      boxShadow: resolvedBoxShadow,
      useContainer: true,
    );
  }
  
  /// Builds the card container with decoration and content.
  Widget _buildCardContainer(
    BuildContext context, {
    Color? backgroundColor,
    required Color resolvedBorderColor,
    required double resolvedBorderWidth,
    required BorderRadius resolvedBorderRadius,
    required EdgeInsets resolvedPadding,
    required Color resolvedForegroundColor,
    List<BoxShadow>? boxShadow,
    bool useContainer = true,
  }) {
    Widget content = Padding(
      padding: resolvedPadding,
      child: DefaultTextStyle(
        style: TextStyle(color: resolvedForegroundColor),
        child: IconTheme(
          data: IconThemeData(color: resolvedForegroundColor),
          child: child,
        ),
      ),
    );
    
    if (!useContainer) {
      // Content for Material widget (no background decoration needed)
      if (resolvedBorderWidth > 0) {
        content = DecoratedBox(
          decoration: BoxDecoration(
            border: Border.all(
              color: resolvedBorderColor,
              width: resolvedBorderWidth,
            ),
            borderRadius: resolvedBorderRadius,
          ),
          child: content,
        );
      }
      return content;
    }
    
    // Full container decoration
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        border: resolvedBorderWidth > 0
            ? Border.all(
                color: resolvedBorderColor,
                width: resolvedBorderWidth,
              )
            : null,
        borderRadius: resolvedBorderRadius,
        boxShadow: boxShadow,
      ),
      child: content,
    );
  }
  
  /// Builds an interactive wrapper with proper touch feedback.
  Widget _buildInteractiveWrapper(
    BuildContext context,
    Widget cardContent,
    BorderRadius borderRadius,
    MouseCursor cursor,
  ) {
    return MouseRegion(
      cursor: cursor,
      child: GestureDetector(
        onTap: clickable || onTap != null ? onTap : null,
        onLongPress: onLongPress,
        child: cardContent,
      ),
    );
  }
  
  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(EnumProperty<ShadcnCardVariant>('variant', variant));
    properties.add(ColorProperty('backgroundColor', backgroundColor));
    properties.add(ColorProperty('borderColor', borderColor));
    properties.add(ColorProperty('foregroundColor', foregroundColor));
    properties.add(DoubleProperty('borderWidth', borderWidth));
    properties.add(DiagnosticsProperty<BorderRadius>('borderRadius', borderRadius));
    properties.add(DiagnosticsProperty<EdgeInsets>('padding', padding));
    properties.add(DiagnosticsProperty<EdgeInsets>('margin', margin));
    properties.add(ColorProperty('shadowColor', shadowColor));
    properties.add(DoubleProperty('elevation', elevation));
    properties.add(DiagnosticsProperty<List<BoxShadow>>('boxShadow', boxShadow));
    properties.add(DiagnosticsProperty<BoxConstraints>('constraints', constraints));
    properties.add(DoubleProperty('width', width));
    properties.add(DoubleProperty('height', height));
    properties.add(ObjectFlagProperty<VoidCallback>('onTap', onTap, ifNull: 'disabled'));
    properties.add(ObjectFlagProperty<VoidCallback>('onLongPress', onLongPress, ifNull: 'disabled'));
    properties.add(FlagProperty('interactive', value: interactive, ifTrue: 'interactive'));
    properties.add(FlagProperty('clickable', value: clickable, ifTrue: 'clickable'));
    properties.add(StringProperty('semanticLabel', semanticLabel));
    properties.add(StringProperty('tooltip', tooltip));
    properties.add(DiagnosticsProperty<MouseCursor>('mouseCursor', mouseCursor));
    properties.add(EnumProperty<Clip>('clipBehavior', clipBehavior));
  }
}