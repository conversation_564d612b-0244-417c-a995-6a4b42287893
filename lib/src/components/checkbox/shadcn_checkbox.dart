import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../constants/shadcn_tokens.dart';
import '../../theme/extensions/shadcn_checkbox_theme.dart';
import '../shadcn_component.dart';

/// A shadcn-styled checkbox component with comprehensive theming support.
///
/// This checkbox component provides all checkbox states (checked, unchecked, indeterminate)
/// while maintaining full Material Design integration. It supports all interactive
/// states (hover, pressed, focused, disabled) with theme-aware styling.
///
/// The checkbox automatically derives its styling from the current theme context,
/// with fallback to Material Design values. All styling is customizable through
/// the [ShadcnCheckboxTheme] extension.
///
/// Example usage:
/// ```dart
/// ShadcnCheckbox(
///   value: isChecked,
///   onChanged: (value) => setState(() => isChecked = value ?? false),
///   label: 'Accept terms and conditions',
/// )
/// ```
class ShadcnCheckbox extends ShadcnComponent with ShadcnComponentValidation {
  /// The current value of the checkbox.
  /// true = checked, false = unchecked, null = indeterminate
  final bool? value;

  /// Callback function called when the checkbox state changes.
  final ValueChanged<bool?>? onChanged;

  /// The text label displayed next to the checkbox.
  final String? label;

  /// The child widget to display instead of [label]. Takes precedence over [label].
  final Widget? child;

  /// Helper text displayed below the checkbox.
  final String? helperText;

  /// Error text displayed when validation fails.
  final String? errorText;

  /// The size of the checkbox.
  final ShadcnCheckboxSize size;

  /// Whether this checkbox is a tristate checkbox.
  /// If true, the checkbox can have three states: checked, unchecked, and indeterminate.
  final bool tristate;

  /// Custom padding around the checkbox area.
  final EdgeInsets? padding;

  /// Custom border radius. Overrides theme settings.
  final BorderRadius? borderRadius;

  /// Focus node for keyboard navigation.
  final FocusNode? focusNode;

  /// Whether the checkbox should autofocus when first built.
  final bool autofocus;

  /// Tooltip message displayed on hover.
  final String? tooltip;

  /// Semantic label for accessibility.
  final String? semanticLabel;

  /// Whether to exclude this checkbox from semantics tree.
  final bool excludeFromSemantics;

  /// Custom mouse cursor when hovering over the checkbox.
  final MouseCursor? mouseCursor;

  /// Whether to enable haptic feedback on press.
  final bool enableFeedback;

  /// Custom splash color for press animations.
  final Color? splashColor;

  /// Custom highlight color for press states.
  final Color? highlightColor;

  /// Custom hover color overlay.
  final Color? hoverColor;

  /// Custom focus color overlay.
  final Color? focusColor;

  /// Animation duration for state transitions.
  final Duration? animationDuration;

  /// Whether the label should be clickable to toggle the checkbox.
  final bool labelClickable;

  /// Custom check mark icon. If null, uses default check mark.
  final Widget? checkIcon;

  /// Custom indeterminate icon. If null, uses default dash mark.
  final Widget? indeterminateIcon;

  /// Form field validator function.
  final FormFieldValidator<bool>? validator;

  /// Whether this checkbox is part of a form and should save its value.
  final bool autovalidate;

  /// Initial value for form field.
  final bool? initialValue;

  /// Form field key for accessing the field state.
  final GlobalKey<FormFieldState<bool>>? formFieldKey;

  const ShadcnCheckbox({
    super.key,
    required this.value,
    required this.onChanged,
    this.label,
    this.child,
    this.helperText,
    this.errorText,
    this.size = ShadcnCheckboxSize.medium,
    this.tristate = false,
    this.padding,
    this.borderRadius,
    this.focusNode,
    this.autofocus = false,
    this.tooltip,
    this.semanticLabel,
    this.excludeFromSemantics = false,
    this.mouseCursor,
    this.enableFeedback = true,
    this.splashColor,
    this.highlightColor,
    this.hoverColor,
    this.focusColor,
    this.animationDuration,
    this.labelClickable = true,
    this.checkIcon,
    this.indeterminateIcon,
    this.validator,
    this.autovalidate = false,
    this.initialValue,
    this.formFieldKey,
  });

  /// Factory constructor for form field checkbox with validation support.
  factory ShadcnCheckbox.formField({
    Key? key,
    bool? initialValue,
    required ValueChanged<bool?>? onChanged,
    FormFieldValidator<bool>? validator,
    bool autovalidate = false,
    GlobalKey<FormFieldState<bool>>? formFieldKey,
    String? label,
    Widget? child,
    String? helperText,
    ShadcnCheckboxSize size = ShadcnCheckboxSize.medium,
    bool tristate = false,
    String? tooltip,
  }) {
    return ShadcnCheckbox(
      key: key,
      value: initialValue ?? false,
      onChanged: onChanged,
      validator: validator,
      autovalidate: autovalidate,
      formFieldKey: formFieldKey,
      initialValue: initialValue,
      label: label,
      helperText: helperText,
      size: size,
      tristate: tristate,
      tooltip: tooltip,
      child: child,
    );
  }

  /// Whether the checkbox is enabled (has onChanged callback).
  bool get enabled => onChanged != null;

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    // Validate theme context and properties
    validateThemeProperties(context);
    validateVariant(size, ShadcnCheckboxSize.values, 'ShadcnCheckbox');
    validateCallbacks({'onChanged': onChanged}, 'ShadcnCheckbox');
    validateAccessibility(
      semanticLabel: semanticLabel,
      tooltip: tooltip,
      excludeFromSemantics: excludeFromSemantics,
      componentName: 'ShadcnCheckbox',
    );

    // Resolve checkbox theme
    final checkboxTheme = resolveTheme<ShadcnCheckboxTheme>(
      context,
      ShadcnCheckboxTheme.defaultTheme,
    );

    // If this is a form field, wrap with FormField
    if (validator != null || formFieldKey != null) {
      return _buildFormField(context, theme, checkboxTheme);
    }

    return _buildCheckbox(context, theme, checkboxTheme);
  }

  /// Builds the form field wrapper if validation is needed.
  Widget _buildFormField(BuildContext context, ThemeData theme,
      ShadcnCheckboxTheme checkboxTheme) {
    return FormField<bool>(
      key: formFieldKey,
      initialValue: initialValue ?? value ?? false,
      validator: validator,
      autovalidateMode:
          autovalidate ? AutovalidateMode.always : AutovalidateMode.disabled,
      builder: (FormFieldState<bool> field) {
        final hasError = field.hasError;
        final currentValue = field.value ?? false;

        return _buildCheckbox(
          context,
          theme,
          checkboxTheme,
          formFieldState: field,
          hasError: hasError,
          currentValue: tristate
              ? (currentValue ? true : (value == null ? null : false))
              : currentValue,
        );
      },
    );
  }

  /// Builds the main checkbox widget.
  Widget _buildCheckbox(
    BuildContext context,
    ThemeData theme,
    ShadcnCheckboxTheme checkboxTheme, {
    FormFieldState<bool>? formFieldState,
    bool hasError = false,
    bool? currentValue,
  }) {
    final effectiveValue = currentValue ?? value;
    final effectiveErrorText =
        hasError ? (formFieldState?.errorText ?? errorText) : errorText;

    // Resolve colors based on current state
    final colors = _resolveColors(
        checkboxTheme, theme.colorScheme, effectiveValue, hasError);
    final sizeProperties = _resolveSizeProperties(checkboxTheme, size);
    final interactionProperties =
        _resolveInteractionProperties(checkboxTheme, theme.colorScheme);

    // Build the checkbox widget
    final checkboxWidget = _buildCheckboxWidget(
      context,
      theme,
      checkboxTheme,
      colors,
      sizeProperties,
      interactionProperties,
      effectiveValue,
      formFieldState,
    );

    // Build the complete widget with label and helper text
    return _buildCompleteWidget(
      context,
      theme,
      checkboxTheme,
      checkboxWidget,
      effectiveErrorText,
      hasError,
    );
  }

  /// Builds the main checkbox interactive widget.
  Widget _buildCheckboxWidget(
    BuildContext context,
    ThemeData theme,
    ShadcnCheckboxTheme checkboxTheme,
    _CheckboxColors colors,
    _CheckboxSizeProperties sizeProps,
    _CheckboxInteractionProperties interactionProps,
    bool? currentValue,
    FormFieldState<bool>? formFieldState,
  ) {
    final effectivePadding = padding ??
        checkboxTheme.padding ??
        EdgeInsets.all(ShadcnTokens.spacing1);
    final effectiveBorderRadius = borderRadius ??
        checkboxTheme.borderRadius ??
        BorderRadius.circular(ShadcnTokens.radiusSm);
    final effectiveAnimationDuration = animationDuration ??
        checkboxTheme.animationDuration ??
        ShadcnTokens.durationFast;

    Widget checkbox = GestureDetector(
      onTap: enabled
          ? () => _handleTap(context, currentValue, formFieldState)
          : null,
      child: MouseRegion(
        cursor: mouseCursor ??
            (enabled ? SystemMouseCursors.click : SystemMouseCursors.basic),
        child: AnimatedContainer(
          duration: effectiveAnimationDuration,
          curve: checkboxTheme.animationCurve ?? Curves.easeInOut,
          width: sizeProps.size,
          height: sizeProps.size,
          padding: effectivePadding,
          decoration: BoxDecoration(
            color: colors.background,
            border: Border.all(
              color: colors.border,
              width: checkboxTheme.borderWidth ?? ShadcnTokens.borderWidth,
            ),
            borderRadius: effectiveBorderRadius,
          ),
          child: _buildCheckMark(context, checkboxTheme, colors, currentValue),
        ),
      ),
    );

    // Add focus support
    if (focusNode != null) {
      checkbox = Focus(
        focusNode: focusNode,
        autofocus: autofocus,
        child: checkbox,
      );
    }

    // Add semantics
    if (!excludeFromSemantics) {
      checkbox = Semantics(
        label: semanticLabel ?? label,
        checked: currentValue == true,
        mixed: tristate && currentValue == null,
        enabled: enabled,
        focusable: enabled,
        child: checkbox,
      );
    }

    // Add tooltip
    if (tooltip != null) {
      checkbox = Tooltip(
        message: tooltip!,
        child: checkbox,
      );
    }

    return checkbox;
  }

  /// Builds the check mark or indeterminate mark inside the checkbox.
  Widget _buildCheckMark(
    BuildContext context,
    ShadcnCheckboxTheme theme,
    _CheckboxColors colors,
    bool? value,
  ) {
    if (value == true) {
      // Show check mark
      return checkIcon ??
          Icon(
            Icons.check,
            size: (theme.resolveSizeForVariant(size) * 0.6).clamp(12.0, 24.0),
            color:
                enabled ? colors.checkMarkColor : colors.disabledCheckMarkColor,
          );
    } else if (tristate && value == null) {
      // Show indeterminate mark
      return indeterminateIcon ??
          Container(
            width: theme.resolveSizeForVariant(size) * 0.6,
            height: theme.checkMarkStrokeWidth ?? 2.0,
            decoration: BoxDecoration(
              color: enabled
                  ? colors.checkMarkColor
                  : colors.disabledCheckMarkColor,
              borderRadius: BorderRadius.circular(1.0),
            ),
          );
    }

    // Show nothing for unchecked state
    return const SizedBox.shrink();
  }

  /// Builds the complete widget with label and helper text.
  Widget _buildCompleteWidget(
    BuildContext context,
    ThemeData theme,
    ShadcnCheckboxTheme checkboxTheme,
    Widget checkboxWidget,
    String? errorText,
    bool hasError,
  ) {
    final List<Widget> children = [];

    // Build main row with checkbox and label
    final List<Widget> rowChildren = [checkboxWidget];

    if (child != null || (label != null && label!.isNotEmpty)) {
      rowChildren.add(
          SizedBox(width: checkboxTheme.labelSpacing ?? ShadcnTokens.spacing2));

      Widget labelWidget = child ??
          Text(
            label!,
            style: resolveTextStyle(
              context,
              checkboxTheme.labelStyle,
              (textTheme) => textTheme.bodyMedium ?? const TextStyle(),
            ),
          );

      if (labelClickable && enabled) {
        labelWidget = GestureDetector(
          onTap: () => _handleTap(context, value, null),
          child: labelWidget,
        );
      }

      rowChildren.add(Expanded(child: labelWidget));
    }

    children.add(
      Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: rowChildren,
      ),
    );

    // Add helper text
    if (helperText != null && helperText!.isNotEmpty && !hasError) {
      children.add(const SizedBox(height: ShadcnTokens.spacing1));
      children.add(
        Padding(
          padding: EdgeInsets.only(
            left: (checkboxTheme.resolveSizeForVariant(size) +
                (checkboxTheme.labelSpacing ?? ShadcnTokens.spacing2)),
          ),
          child: Text(
            helperText!,
            style: resolveTextStyle(
              context,
              checkboxTheme.helperStyle,
              (textTheme) => textTheme.bodySmall ?? const TextStyle(),
            ),
          ),
        ),
      );
    }

    // Add error text
    if (errorText != null && errorText!.isNotEmpty) {
      children.add(const SizedBox(height: ShadcnTokens.spacing1));
      children.add(
        Padding(
          padding: EdgeInsets.only(
            left: (checkboxTheme.resolveSizeForVariant(size) +
                (checkboxTheme.labelSpacing ?? ShadcnTokens.spacing2)),
          ),
          child: Text(
            errorText!,
            style: resolveTextStyle(
              context,
              checkboxTheme.errorStyle,
              (textTheme) =>
                  textTheme.bodySmall
                      ?.copyWith(color: theme.colorScheme.error) ??
                  TextStyle(color: theme.colorScheme.error),
            ),
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }

  /// Resolves colors for the current state.
  _CheckboxColors _resolveColors(
    ShadcnCheckboxTheme theme,
    ColorScheme colorScheme,
    bool? value,
    bool hasError,
  ) {
    if (!enabled) {
      // Disabled state
      if (value == true) {
        return _CheckboxColors(
          background: theme.disabledCheckedBackground ??
              colorScheme.onSurface.withOpacity(0.12),
          border: theme.disabledCheckedBorder ??
              colorScheme.onSurface.withOpacity(0.12),
          checkMarkColor: theme.disabledCheckMarkColor ?? colorScheme.surface,
          disabledCheckMarkColor:
              theme.disabledCheckMarkColor ?? colorScheme.surface,
        );
      } else {
        return _CheckboxColors(
          background: theme.disabledUncheckedBackground ?? Colors.transparent,
          border: theme.disabledUncheckedBorder ??
              colorScheme.onSurface.withOpacity(0.12),
          checkMarkColor: theme.checkMarkColor ?? colorScheme.onPrimary,
          disabledCheckMarkColor:
              theme.disabledCheckMarkColor ?? colorScheme.surface,
        );
      }
    } else if (hasError) {
      // Error state
      return _CheckboxColors(
        background: value == true ? colorScheme.error : Colors.transparent,
        border: colorScheme.error,
        checkMarkColor: theme.checkMarkColor ?? colorScheme.onError,
        disabledCheckMarkColor:
            theme.disabledCheckMarkColor ?? colorScheme.surface,
      );
    } else if (value == true) {
      // Checked state
      return _CheckboxColors(
        background: theme.checkedBackground ?? colorScheme.primary,
        border: theme.checkedBorder ?? colorScheme.primary,
        checkMarkColor: theme.checkMarkColor ?? colorScheme.onPrimary,
        disabledCheckMarkColor:
            theme.disabledCheckMarkColor ?? colorScheme.surface,
      );
    } else if (tristate && value == null) {
      // Indeterminate state
      return _CheckboxColors(
        background: theme.intermediateBackground ?? colorScheme.primary,
        border: theme.intermediateBorder ?? colorScheme.primary,
        checkMarkColor: theme.checkMarkColor ?? colorScheme.onPrimary,
        disabledCheckMarkColor:
            theme.disabledCheckMarkColor ?? colorScheme.surface,
      );
    } else {
      // Unchecked state
      return _CheckboxColors(
        background: theme.uncheckedBackground ?? Colors.transparent,
        border: theme.uncheckedBorder ?? colorScheme.outline,
        checkMarkColor: theme.checkMarkColor ?? colorScheme.onPrimary,
        disabledCheckMarkColor:
            theme.disabledCheckMarkColor ?? colorScheme.surface,
      );
    }
  }

  /// Resolves size properties for the current size.
  _CheckboxSizeProperties _resolveSizeProperties(
      ShadcnCheckboxTheme theme, ShadcnCheckboxSize size) {
    return _CheckboxSizeProperties(
      size: theme.resolveSizeForVariant(size),
    );
  }

  /// Resolves interaction properties for hover, press, and focus states.
  _CheckboxInteractionProperties _resolveInteractionProperties(
    ShadcnCheckboxTheme theme,
    ColorScheme colorScheme,
  ) {
    return _CheckboxInteractionProperties(
      splashColor: splashColor ??
          theme.pressedOverlay ??
          colorScheme.primary.withOpacity(0.12),
      highlightColor: highlightColor ??
          theme.pressedOverlay ??
          colorScheme.primary.withOpacity(0.08),
      hoverColor: hoverColor ??
          theme.hoverOverlay ??
          colorScheme.primary.withOpacity(0.04),
      focusColor: focusColor ??
          theme.focusedOverlay ??
          colorScheme.primary.withOpacity(0.12),
    );
  }

  /// Handles checkbox tap with haptic feedback.
  void _handleTap(BuildContext context, bool? currentValue,
      FormFieldState<bool>? formFieldState) {
    if (enableFeedback) {
      HapticFeedback.lightImpact();
    }

    bool? newValue;
    if (tristate) {
      if (currentValue == null) {
        newValue = false;
      } else if (currentValue == false) {
        newValue = true;
      } else {
        newValue = null;
      }
    } else {
      newValue = !(currentValue ?? false);
    }

    // Update form field if present
    formFieldState?.didChange(newValue ?? false);

    // Call the onChanged callback
    onChanged?.call(newValue);
  }
}

/// Helper class to hold resolved checkbox colors for different states.
class _CheckboxColors {
  final Color background;
  final Color border;
  final Color checkMarkColor;
  final Color disabledCheckMarkColor;

  const _CheckboxColors({
    required this.background,
    required this.border,
    required this.checkMarkColor,
    required this.disabledCheckMarkColor,
  });
}

/// Helper class to hold resolved checkbox size properties.
class _CheckboxSizeProperties {
  final double size;

  const _CheckboxSizeProperties({
    required this.size,
  });
}

/// Helper class to hold resolved checkbox interaction properties.
class _CheckboxInteractionProperties {
  final Color splashColor;
  final Color highlightColor;
  final Color hoverColor;
  final Color focusColor;

  const _CheckboxInteractionProperties({
    required this.splashColor,
    required this.highlightColor,
    required this.hoverColor,
    required this.focusColor,
  });
}
