import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_slider_theme.dart';

/// A shadcn-styled slider component.
class ShadcnSlider extends ShadcnComponent with ShadcnComponentValidation {
  final double value;
  final ValueChanged<double>? onChanged;
  final ValueChanged<double>? onChangeStart;
  final ValueChanged<double>? onChangeEnd;
  final double min;
  final double max;
  final int? divisions;
  final String? label;
  final Color? activeColor;
  final Color? inactiveColor;
  final Color? thumbColor;
  final bool enabled;
  final String? semanticLabel;
  final String Function(double)? semanticFormatterCallback;
  final FocusNode? focusNode;
  final bool autofocus;

  const ShadcnSlider({
    super.key,
    required this.value,
    required this.onChanged,
    this.onChangeStart,
    this.onChangeEnd,
    this.min = 0.0,
    this.max = 1.0,
    this.divisions,
    this.label,
    this.activeColor,
    this.inactiveColor,
    this.thumbColor,
    this.enabled = true,
    this.semanticLabel,
    this.semanticFormatterCallback,
    this.focusNode,
    this.autofocus = false,
  }) : assert(min <= max),
       assert(value >= min && value <= max),
       assert(divisions == null || divisions > 0);

  bool get _enabled => enabled && onChanged != null;

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    validateThemeProperties(context);
    validateAccessibility(
      semanticLabel: semanticLabel,
      componentName: 'ShadcnSlider',
    );
    
    final sliderTheme = resolveTheme<ShadcnSliderTheme>(
      context,
      ShadcnSliderTheme.defaultTheme,
    );

    // Create custom SliderThemeData
    final customSliderTheme = theme.sliderTheme.copyWith(
      trackHeight: sliderTheme.trackHeight,
      activeTrackColor: _enabled
          ? (activeColor ?? sliderTheme.activeTrackColor ?? theme.colorScheme.primary)
          : (sliderTheme.disabledTrackColor ?? theme.colorScheme.onSurface.withOpacity(0.12)),
      inactiveTrackColor: _enabled
          ? (inactiveColor ?? sliderTheme.inactiveTrackColor ?? theme.colorScheme.surfaceVariant)
          : (sliderTheme.disabledTrackColor ?? theme.colorScheme.onSurface.withOpacity(0.12)),
      thumbColor: _enabled
          ? (thumbColor ?? sliderTheme.thumbColor ?? theme.colorScheme.primary)
          : (sliderTheme.disabledThumbColor ?? theme.colorScheme.onSurface.withOpacity(0.38)),
      disabledActiveTrackColor: sliderTheme.disabledTrackColor ?? theme.colorScheme.onSurface.withOpacity(0.12),
      disabledInactiveTrackColor: sliderTheme.disabledTrackColor ?? theme.colorScheme.onSurface.withOpacity(0.12),
      disabledThumbColor: sliderTheme.disabledThumbColor ?? theme.colorScheme.onSurface.withOpacity(0.38),
      thumbShape: RoundSliderThumbShape(
        enabledThumbRadius: sliderTheme.thumbRadius ?? 10.0,
        elevation: sliderTheme.thumbElevation ?? 1.0,
      ),
      overlayColor: (activeColor ?? sliderTheme.activeTrackColor ?? theme.colorScheme.primary).withOpacity(0.12),
      valueIndicatorColor: sliderTheme.valueBackgroundColor ?? theme.colorScheme.surface,
      valueIndicatorTextStyle: sliderTheme.valueTextStyle ?? theme.textTheme.bodySmall,
    );

    Widget slider = SliderTheme(
      data: customSliderTheme,
      child: Slider(
        value: value,
        onChanged: _enabled ? (newValue) {
          HapticFeedback.selectionClick();
          onChanged!(newValue);
        } : null,
        onChangeStart: _enabled ? onChangeStart : null,
        onChangeEnd: _enabled ? onChangeEnd : null,
        min: min,
        max: max,
        divisions: divisions,
        label: label,
        semanticFormatterCallback: semanticFormatterCallback,
        focusNode: focusNode,
        autofocus: autofocus,
      ),
    );

    // Add padding if specified in theme
    if (sliderTheme.padding != null) {
      slider = Padding(
        padding: sliderTheme.padding!,
        child: slider,
      );
    }

    // Add semantics
    if (semanticLabel != null) {
      slider = Semantics(
        label: semanticLabel,
        value: semanticFormatterCallback?.call(value) ?? value.toString(),
        enabled: _enabled,
        slider: true,
        child: slider,
      );
    }

    return slider;
  }
}