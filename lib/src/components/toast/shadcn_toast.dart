import 'package:flutter/material.dart';
import '../../theme/extensions/shadcn_toast_theme.dart';
import '../../constants/shadcn_tokens.dart';
import '../shadcn_component.dart';

/// A shadcn-style toast notification component for displaying temporary messages.
/// 
/// The ShadcnToast component provides non-blocking notifications that automatically
/// dismiss after a specified duration. It supports multiple variants (success, error,
/// warning, info) with appropriate styling and icons for each message type.
/// 
/// This component supports:
/// - Multiple variants with semantic colors and icons
/// - Custom or automatic icons based on variant
/// - Title and description text with proper hierarchy
/// - Dismissible action with close button
/// - Automatic dismissal with configurable duration
/// - Proper z-index management for overlay positioning
/// - Full theme customization through ShadcnToastTheme
/// - Accessibility support with semantic roles and labels
/// 
/// Example usage:
/// ```dart
/// // Show a success toast
/// ShadcnToast.showSuccess(
///   context: context,
///   title: 'Success',
///   description: 'Your changes have been saved.',
/// );
/// 
/// // Show an error toast with custom duration
/// ShadcnToast.showError(
///   context: context,
///   title: 'Error',
///   description: 'Failed to save changes. Please try again.',
///   duration: Duration(seconds: 5),
/// );
/// 
/// // Show a custom toast
/// ShadcnToast(
///   variant: ShadcnToastVariant.warning,
///   title: 'Warning',
///   description: 'This action cannot be undone.',
///   icon: Icon(Icons.warning),
///   onDismiss: () => print('Toast dismissed'),
/// );
/// ```
class ShadcnToast extends ShadcnComponent with ShadcnComponentValidation {
  /// The toast variant that determines styling and default icon
  final ShadcnToastVariant variant;
  
  /// Optional custom icon to display. If null, uses variant's default icon
  final Widget? icon;
  
  /// The title of the toast notification
  final String? title;
  
  /// The description/body text of the toast notification
  final String? description;
  
  /// Custom content widget that replaces title and description
  final Widget? contentWidget;
  
  /// Callback when the toast is dismissed (either by user action or timeout)
  final VoidCallback? onDismiss;
  
  /// Whether to show a close/dismiss button
  final bool showCloseButton;
  
  /// Custom close button widget
  final Widget? closeButton;
  
  /// Duration before auto-dismiss (null to disable auto-dismiss)
  final Duration? duration;
  
  /// Custom width constraint for the toast
  final double? width;
  
  /// Custom height constraint for the toast
  final double? height;
  
  /// Custom padding override for the toast content
  final EdgeInsets? contentPadding;
  
  /// Custom border radius override for the toast
  final BorderRadius? borderRadius;
  
  /// Semantic label for accessibility
  final String? semanticLabel;
  
  /// Whether to exclude this toast from semantics tree
  final bool excludeFromSemantics;
  
  /// Custom elevation override for the toast shadow
  final double? elevation;
  
  /// Whether this toast is dismissible by user interaction
  final bool dismissible;
  
  /// Animation controller for custom animations (if null, uses default)
  final AnimationController? animationController;
  
  const ShadcnToast({
    super.key,
    this.variant = ShadcnToastVariant.info,
    this.icon,
    this.title,
    this.description,
    this.contentWidget,
    this.onDismiss,
    this.showCloseButton = true,
    this.closeButton,
    this.duration,
    this.width,
    this.height,
    this.contentPadding,
    this.borderRadius,
    this.semanticLabel,
    this.excludeFromSemantics = false,
    this.elevation,
    this.dismissible = true,
    this.animationController,
  }) : assert(
         title != null || description != null || contentWidget != null,
         'Either title, description, or contentWidget must be provided',
       );
  
  /// Creates a success variant toast with the provided content
  const ShadcnToast.success({
    Key? key,
    Widget? icon,
    String? title,
    String? description,
    Widget? contentWidget,
    VoidCallback? onDismiss,
    bool showCloseButton = true,
    Widget? closeButton,
    Duration? duration,
    double? width,
    double? height,
    EdgeInsets? contentPadding,
    BorderRadius? borderRadius,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? elevation,
    bool dismissible = true,
    AnimationController? animationController,
  }) : this(
    key: key,
    variant: ShadcnToastVariant.success,
    icon: icon,
    title: title,
    description: description,
    contentWidget: contentWidget,
    onDismiss: onDismiss,
    showCloseButton: showCloseButton,
    closeButton: closeButton,
    duration: duration,
    width: width,
    height: height,
    contentPadding: contentPadding,
    borderRadius: borderRadius,
    semanticLabel: semanticLabel,
    excludeFromSemantics: excludeFromSemantics,
    elevation: elevation,
    dismissible: dismissible,
    animationController: animationController,
  );
  
  /// Creates an error variant toast with the provided content
  const ShadcnToast.error({
    Key? key,
    Widget? icon,
    String? title,
    String? description,
    Widget? contentWidget,
    VoidCallback? onDismiss,
    bool showCloseButton = true,
    Widget? closeButton,
    Duration? duration,
    double? width,
    double? height,
    EdgeInsets? contentPadding,
    BorderRadius? borderRadius,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? elevation,
    bool dismissible = true,
    AnimationController? animationController,
  }) : this(
    key: key,
    variant: ShadcnToastVariant.error,
    icon: icon,
    title: title,
    description: description,
    contentWidget: contentWidget,
    onDismiss: onDismiss,
    showCloseButton: showCloseButton,
    closeButton: closeButton,
    duration: duration,
    width: width,
    height: height,
    contentPadding: contentPadding,
    borderRadius: borderRadius,
    semanticLabel: semanticLabel,
    excludeFromSemantics: excludeFromSemantics,
    elevation: elevation,
    dismissible: dismissible,
    animationController: animationController,
  );
  
  /// Creates a warning variant toast with the provided content
  const ShadcnToast.warning({
    Key? key,
    Widget? icon,
    String? title,
    String? description,
    Widget? contentWidget,
    VoidCallback? onDismiss,
    bool showCloseButton = true,
    Widget? closeButton,
    Duration? duration,
    double? width,
    double? height,
    EdgeInsets? contentPadding,
    BorderRadius? borderRadius,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? elevation,
    bool dismissible = true,
    AnimationController? animationController,
  }) : this(
    key: key,
    variant: ShadcnToastVariant.warning,
    icon: icon,
    title: title,
    description: description,
    contentWidget: contentWidget,
    onDismiss: onDismiss,
    showCloseButton: showCloseButton,
    closeButton: closeButton,
    duration: duration,
    width: width,
    height: height,
    contentPadding: contentPadding,
    borderRadius: borderRadius,
    semanticLabel: semanticLabel,
    excludeFromSemantics: excludeFromSemantics,
    elevation: elevation,
    dismissible: dismissible,
    animationController: animationController,
  );
  
  /// Creates an info variant toast with the provided content
  const ShadcnToast.info({
    Key? key,
    Widget? icon,
    String? title,
    String? description,
    Widget? contentWidget,
    VoidCallback? onDismiss,
    bool showCloseButton = true,
    Widget? closeButton,
    Duration? duration,
    double? width,
    double? height,
    EdgeInsets? contentPadding,
    BorderRadius? borderRadius,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? elevation,
    bool dismissible = true,
    AnimationController? animationController,
  }) : this(
    key: key,
    variant: ShadcnToastVariant.info,
    icon: icon,
    title: title,
    description: description,
    contentWidget: contentWidget,
    onDismiss: onDismiss,
    showCloseButton: showCloseButton,
    closeButton: closeButton,
    duration: duration,
    width: width,
    height: height,
    contentPadding: contentPadding,
    borderRadius: borderRadius,
    semanticLabel: semanticLabel,
    excludeFromSemantics: excludeFromSemantics,
    elevation: elevation,
    dismissible: dismissible,
    animationController: animationController,
  );
  
  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    // Validate theme context and component configuration
    assert(() {
      validateThemeProperties(context);
      validateAccessibility(
        semanticLabel: semanticLabel,
        excludeFromSemantics: excludeFromSemantics,
        componentName: 'ShadcnToast',
      );
      
      // Validate required content
      if (title == null && description == null && contentWidget == null) {
        throw FlutterError(
          'ShadcnToast requires either title, description, or contentWidget. '
          'Provide at least one of these properties.',
        );
      }
      
      return true;
    }());
    
    // Resolve the toast theme with fallbacks
    final toastTheme = resolveTheme<ShadcnToastTheme>(
      context,
      ShadcnToastTheme.defaultTheme,
    );
    
    // Resolve variant-specific colors
    final variantColors = _resolveVariantColors(toastTheme, variant);
    
    // Resolve layout properties
    final resolvedBorderRadius = resolveBorderRadius(
      context,
      borderRadius,
      toastTheme.borderRadius ?? ShadcnTokens.borderRadius(ShadcnTokens.radiusMd),
    );
    
    final resolvedPadding = resolveSpacing(
      context,
      contentPadding,
      toastTheme.padding ?? ShadcnTokens.paddingAll(ShadcnTokens.spacing4),
    );
    
    final resolvedElevation = resolveDouble(
      context,
      elevation,
      toastTheme.elevation ?? ShadcnTokens.elevationMd,
      applyVerticalDensity: false,
    );
    
    // Build the toast container
    Widget toastContainer = Container(
      width: width,
      height: height,
      constraints: BoxConstraints(
        minHeight: toastTheme.minHeight ?? ShadcnTokens.buttonHeightMd,
        maxWidth: toastTheme.maxWidth ?? 420.0,
      ),
      child: Material(
        elevation: resolvedElevation,
        color: variantColors.background,
        shadowColor: toastTheme.shadowColor,
        borderRadius: resolvedBorderRadius,
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: variantColors.border,
              width: toastTheme.borderWidth ?? ShadcnTokens.borderWidth,
            ),
            borderRadius: resolvedBorderRadius,
          ),
          padding: resolvedPadding,
          child: _buildContent(context, toastTheme, variantColors),
        ),
      ),
    );
    
    // Add semantic wrapper if needed
    if (!excludeFromSemantics) {
      toastContainer = Semantics(
        label: semanticLabel ?? _buildSemanticLabel(),
        liveRegion: true,
        container: true,
        child: ExcludeSemantics(
          excluding: true,
          child: toastContainer,
        ),
      );
    }
    
    // Add dismissible wrapper if enabled
    if (dismissible && onDismiss != null) {
      toastContainer = Dismissible(
        key: key ?? UniqueKey(),
        onDismissed: (_) => onDismiss?.call(),
        child: toastContainer,
      );
    }
    
    return toastContainer;
  }
  
  /// Resolves colors based on the current variant and theme
  _ToastColors _resolveVariantColors(ShadcnToastTheme theme, ShadcnToastVariant variant) {
    switch (variant) {
      case ShadcnToastVariant.success:
        return _ToastColors(
          background: theme.successBackground ?? Colors.green.withValues(alpha: 0.1),
          foreground: theme.successForeground ?? Colors.green.shade700,
          border: theme.successBorder ?? Colors.green.shade200,
          iconColor: theme.successIconColor ?? Colors.green.shade700,
        );
      case ShadcnToastVariant.error:
        return _ToastColors(
          background: theme.errorBackground ?? Colors.red.withValues(alpha: 0.1),
          foreground: theme.errorForeground ?? Colors.red.shade700,
          border: theme.errorBorder ?? Colors.red.shade200,
          iconColor: theme.errorIconColor ?? Colors.red.shade700,
        );
      case ShadcnToastVariant.warning:
        return _ToastColors(
          background: theme.warningBackground ?? Colors.orange.withValues(alpha: 0.1),
          foreground: theme.warningForeground ?? Colors.orange.shade700,
          border: theme.warningBorder ?? Colors.orange.shade200,
          iconColor: theme.warningIconColor ?? Colors.orange.shade700,
        );
      case ShadcnToastVariant.info:
        return _ToastColors(
          background: theme.infoBackground ?? Colors.blue.withValues(alpha: 0.1),
          foreground: theme.infoForeground ?? Colors.blue.shade700,
          border: theme.infoBorder ?? Colors.blue.shade200,
          iconColor: theme.infoIconColor ?? Colors.blue.shade700,
        );
    }
  }
  
  /// Builds the content section of the toast
  Widget _buildContent(
    BuildContext context,
    ShadcnToastTheme theme,
    _ToastColors colors,
  ) {
    // Use custom content widget if provided
    if (contentWidget != null) {
      return _wrapWithDismissButton(context, theme, colors, contentWidget!);
    }
    
    // Build text content layout
    final children = <Widget>[];
    
    // Add icon if available
    final iconWidget = _buildIcon(context, theme, colors);
    if (iconWidget != null) {
      children.add(iconWidget);
      children.add(SizedBox(width: theme.iconGap ?? ShadcnTokens.spacing3));
    }
    
    // Add text content
    final textContent = _buildTextContent(context, theme, colors);
    children.add(Expanded(child: textContent));
    
    // Add close button if enabled
    if (showCloseButton && dismissible) {
      children.add(SizedBox(width: theme.iconGap ?? ShadcnTokens.spacing3));
      children.add(_buildCloseButton(context, theme, colors));
    }
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: children,
    );
  }
  
  /// Builds the icon widget for the toast
  Widget? _buildIcon(
    BuildContext context,
    ShadcnToastTheme theme,
    _ToastColors colors,
  ) {
    Widget? iconWidget;
    
    if (icon != null) {
      // Use custom icon
      iconWidget = icon!;
    } else {
      // Use default variant icon
      final iconSize = resolveDouble(
        context,
        null,
        theme.iconSize ?? ShadcnTokens.iconSizeMd,
        applyVerticalDensity: false,
      );
      
      iconWidget = Icon(
        variant.defaultIcon,
        size: iconSize,
        color: colors.iconColor,
      );
    }
    
    // Ensure icon uses the correct color if it's an Icon widget
    if (iconWidget is Icon) {
      iconWidget = Icon(
        iconWidget.icon,
        size: iconWidget.size ?? theme.iconSize ?? ShadcnTokens.iconSizeMd,
        color: colors.iconColor,
      );
    }
    
    return iconWidget;
  }
  
  /// Builds the text content (title and description)
  Widget _buildTextContent(
    BuildContext context,
    ShadcnToastTheme theme,
    _ToastColors colors,
  ) {
    final children = <Widget>[];
    
    // Add title if provided
    if (title != null) {
      final titleStyle = resolveTextStyle(
        context,
        theme.titleTextStyle,
        (textTheme) => textTheme.labelLarge ?? const TextStyle(),
      ).copyWith(
        color: colors.foreground,
        fontWeight: ShadcnTokens.fontWeightSemibold,
      );
      
      children.add(
        Text(
          title!,
          style: titleStyle,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      );
      
      // Add spacing between title and description
      if (description != null) {
        children.add(SizedBox(
          height: theme.titleDescriptionGap ?? ShadcnTokens.spacing1,
        ));
      }
    }
    
    // Add description if provided
    if (description != null) {
      final descriptionStyle = resolveTextStyle(
        context,
        theme.descriptionTextStyle,
        (textTheme) => textTheme.bodyMedium ?? const TextStyle(),
      ).copyWith(
        color: colors.foreground.withValues(alpha: 0.8),
      );
      
      children.add(
        Text(
          description!,
          style: descriptionStyle,
          maxLines: 3,
          overflow: TextOverflow.ellipsis,
        ),
      );
    }
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: children,
    );
  }
  
  /// Builds the close button for dismissing the toast
  Widget _buildCloseButton(
    BuildContext context,
    ShadcnToastTheme theme,
    _ToastColors colors,
  ) {
    if (closeButton != null) {
      return closeButton!;
    }
    
    return IconButton(
      icon: Icon(
        Icons.close,
        size: ShadcnTokens.iconSizeSm,
        color: colors.foreground.withValues(alpha: 0.6),
      ),
      onPressed: onDismiss,
      constraints: const BoxConstraints(
        minWidth: 24,
        minHeight: 24,
      ),
      padding: EdgeInsets.zero,
      visualDensity: VisualDensity.compact,
    );
  }
  
  /// Wraps content with dismiss button if needed
  Widget _wrapWithDismissButton(
    BuildContext context,
    ShadcnToastTheme theme,
    _ToastColors colors,
    Widget content,
  ) {
    if (!showCloseButton || !dismissible) {
      return content;
    }
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(child: content),
        SizedBox(width: theme.iconGap ?? ShadcnTokens.spacing3),
        _buildCloseButton(context, theme, colors),
      ],
    );
  }
  
  /// Builds a semantic label for accessibility
  String _buildSemanticLabel() {
    final parts = <String>[];
    
    // Add variant context
    parts.add('${variant.name} notification');
    
    // Add title content
    if (title?.isNotEmpty == true) {
      parts.add(title!);
    }
    
    // Add description text
    if (description?.isNotEmpty == true) {
      parts.add(description!);
    }
    
    return parts.join('. ');
  }
}

/// Helper class to hold resolved colors for a toast variant
class _ToastColors {
  final Color background;
  final Color foreground;
  final Color border;
  final Color iconColor;
  
  const _ToastColors({
    required this.background,
    required this.foreground,
    required this.border,
    required this.iconColor,
  });
}

/// Extension methods to provide convenient static show methods for ShadcnToast
extension ShadcnToastExtension on ShadcnToast {
  /// Shows a success toast notification
  static void showSuccess(
    BuildContext context, {
    String? title,
    String? description,
    Duration? duration,
    VoidCallback? onDismiss,
  }) {
    _showToast(
      context,
      ShadcnToast.success(
        title: title,
        description: description,
        duration: duration,
        onDismiss: onDismiss,
      ),
      duration: duration,
    );
  }
  
  /// Shows an error toast notification
  static void showError(
    BuildContext context, {
    String? title,
    String? description,
    Duration? duration,
    VoidCallback? onDismiss,
  }) {
    _showToast(
      context,
      ShadcnToast.error(
        title: title,
        description: description,
        duration: duration,
        onDismiss: onDismiss,
      ),
      duration: duration,
    );
  }
  
  /// Shows a warning toast notification
  static void showWarning(
    BuildContext context, {
    String? title,
    String? description,
    Duration? duration,
    VoidCallback? onDismiss,
  }) {
    _showToast(
      context,
      ShadcnToast.warning(
        title: title,
        description: description,
        duration: duration,
        onDismiss: onDismiss,
      ),
      duration: duration,
    );
  }
  
  /// Shows an info toast notification
  static void showInfo(
    BuildContext context, {
    String? title,
    String? description,
    Duration? duration,
    VoidCallback? onDismiss,
  }) {
    _showToast(
      context,
      ShadcnToast.info(
        title: title,
        description: description,
        duration: duration,
        onDismiss: onDismiss,
      ),
      duration: duration,
    );
  }
  
  /// Internal method to show toast using ScaffoldMessenger
  static void _showToast(
    BuildContext context,
    ShadcnToast toast, {
    Duration? duration,
  }) {
    // Remove any existing toast
    ScaffoldMessenger.of(context).removeCurrentSnackBar();
    
    // Create and show the toast as a SnackBar for proper overlay management
    final snackBar = SnackBar(
      content: toast,
      duration: duration ?? const Duration(seconds: 4),
      backgroundColor: Colors.transparent,
      elevation: 0,
      padding: EdgeInsets.zero,
      behavior: SnackBarBehavior.floating,
      margin: const EdgeInsets.all(ShadcnTokens.spacing4),
    );
    
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }
}