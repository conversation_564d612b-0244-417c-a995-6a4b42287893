import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../shadcn.dart';

/// Enum defining where the tooltip should be positioned relative to its target.
enum ShadcnTooltipPosition {
  top,
  topStart,
  topEnd,
  bottom,
  bottomStart,
  bottomEnd,
  left,
  leftStart,
  leftEnd,
  right,
  rightStart,
  rightEnd,
}

/// A shadcn-styled tooltip component that displays helpful text on hover, focus, or tap.
/// 
/// The tooltip automatically manages show/hide behavior based on user interactions,
/// provides rich positioning options, and integrates seamlessly with Material Design
/// while maintaining shadcn aesthetics.
class ShadcnTooltip extends ShadcnComponent with ShadcnComponentValidation {
  /// The widget that will trigger the tooltip when interacted with.
  final Widget child;
  
  /// The message to display in the tooltip.
  final String message;
  
  /// The preferred position of the tooltip relative to the target.
  final ShadcnTooltipPosition position;
  
  /// Whether to show an arrow pointing to the target.
  final bool showArrow;
  
  /// Whether the tooltip should show on hover (desktop).
  final bool? showOnHover;
  
  /// Whether the tooltip should show on focus.
  final bool? showOnFocus;
  
  /// Whether the tooltip should show on long press (mobile).
  final bool? showOnLongPress;
  
  /// Whether the tooltip should show on tap (accessibility).
  final bool? showOnTap;
  
  /// Custom delay before showing the tooltip.
  final Duration? showDelay;
  
  /// Custom delay before hiding the tooltip.
  final Duration? hideDelay;
  
  /// Custom offset from the calculated position.
  final Offset? customOffset;
  
  /// Callback when tooltip visibility changes.
  final ValueChanged<bool>? onVisibilityChanged;
  
  /// Custom tooltip theme.
  final ShadcnTooltipTheme? theme;
  
  /// Rich content widget instead of simple text message.
  final Widget? richContent;
  
  /// Semantic label for accessibility (defaults to message).
  final String? semanticLabel;
  
  /// Whether the tooltip should be disabled.
  final bool disabled;

  const ShadcnTooltip({
    super.key,
    required this.child,
    required this.message,
    this.position = ShadcnTooltipPosition.bottom,
    this.showArrow = true,
    this.showOnHover,
    this.showOnFocus,
    this.showOnLongPress,
    this.showOnTap,
    this.showDelay,
    this.hideDelay,
    this.customOffset,
    this.onVisibilityChanged,
    this.theme,
    this.richContent,
    this.semanticLabel,
    this.disabled = false,
  });

  /// Creates a tooltip with rich content instead of simple text.
  const ShadcnTooltip.rich({
    super.key,
    required this.child,
    required this.richContent,
    this.message = '',
    this.position = ShadcnTooltipPosition.bottom,
    this.showArrow = true,
    this.showOnHover,
    this.showOnFocus,
    this.showOnLongPress,
    this.showOnTap,
    this.showDelay,
    this.hideDelay,
    this.customOffset,
    this.onVisibilityChanged,
    this.theme,
    this.semanticLabel,
    this.disabled = false,
  });

  @override
  Widget buildWithTheme(BuildContext context, ThemeData materialTheme) {
    // Skip if disabled or no message/content
    if (disabled || (message.isEmpty && richContent == null)) {
      return child;
    }

    // Resolve theme
    final tooltipTheme = theme ?? resolveTheme<ShadcnTooltipTheme>(
      context,
      ShadcnTooltipTheme.defaultTheme,
    );

    // Validate component configuration
    validateThemeProperties(context);
    validateAccessibility(
      semanticLabel: semanticLabel ?? message,
      componentName: 'ShadcnTooltip',
    );

    // Build the tooltip wrapper
    return TooltipWrapper(
      message: message,
      richContent: richContent,
      position: position,
      showArrow: showArrow,
      tooltipTheme: tooltipTheme,
      showOnHover: showOnHover,
      showOnFocus: showOnFocus,
      showOnLongPress: showOnLongPress,
      showOnTap: showOnTap,
      showDelay: showDelay,
      hideDelay: hideDelay,
      customOffset: customOffset,
      onVisibilityChanged: onVisibilityChanged,
      semanticLabel: semanticLabel,
      child: child,
    );
  }
}

/// Internal widget that manages tooltip behavior and overlay.
class TooltipWrapper extends StatefulWidget {
  final Widget child;
  final String message;
  final Widget? richContent;
  final ShadcnTooltipPosition position;
  final bool showArrow;
  final ShadcnTooltipTheme tooltipTheme;
  final bool? showOnHover;
  final bool? showOnFocus;
  final bool? showOnLongPress;
  final bool? showOnTap;
  final Duration? showDelay;
  final Duration? hideDelay;
  final Offset? customOffset;
  final ValueChanged<bool>? onVisibilityChanged;
  final String? semanticLabel;

  const TooltipWrapper({
    super.key,
    required this.child,
    required this.message,
    required this.richContent,
    required this.position,
    required this.showArrow,
    required this.tooltipTheme,
    required this.showOnHover,
    required this.showOnFocus,
    required this.showOnLongPress,
    required this.showOnTap,
    required this.showDelay,
    required this.hideDelay,
    required this.customOffset,
    required this.onVisibilityChanged,
    required this.semanticLabel,
  });

  @override
  State<TooltipWrapper> createState() => _TooltipWrapperState();
}

class _TooltipWrapperState extends State<TooltipWrapper> 
    with SingleTickerProviderStateMixin {
  
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  Timer? _showTimer;
  Timer? _hideTimer;
  
  bool _isVisible = false;
  bool _isHovered = false;
  bool _isFocused = false;
  
  // Resolved behavior flags
  late bool _shouldShowOnHover;
  late bool _shouldShowOnFocus;
  late bool _shouldShowOnLongPress;
  late bool _shouldShowOnTap;

  @override
  void initState() {
    super.initState();
    
    // Resolve behavior flags from theme defaults
    _shouldShowOnHover = widget.showOnHover ?? 
        widget.tooltipTheme.showOnHover ?? true;
    _shouldShowOnFocus = widget.showOnFocus ?? 
        widget.tooltipTheme.showOnFocus ?? true;
    _shouldShowOnLongPress = widget.showOnLongPress ?? 
        widget.tooltipTheme.showOnLongPress ?? true;
    _shouldShowOnTap = widget.showOnTap ?? 
        widget.tooltipTheme.showOnTap ?? false;
    
    // Initialize animation controller
    _animationController = AnimationController(
      duration: widget.tooltipTheme.animationDuration ?? ShadcnTokens.durationFast,
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: widget.tooltipTheme.animationCurve ?? Curves.easeOut,
    ));
  }

  @override
  void dispose() {
    _hideTooltip(immediate: true);
    _showTimer?.cancel();
    _hideTimer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _scheduleShow() {
    _hideTimer?.cancel();
    
    if (_isVisible) return;
    
    final delay = widget.showDelay ?? 
        widget.tooltipTheme.showDelay ?? 
        const Duration(milliseconds: 500);
    
    if (delay > Duration.zero) {
      _showTimer?.cancel();
      _showTimer = Timer(delay, _showTooltip);
    } else {
      _showTooltip();
    }
  }

  void _scheduleHide() {
    _showTimer?.cancel();
    
    if (!_isVisible) return;
    
    final delay = widget.hideDelay ?? 
        widget.tooltipTheme.hideDelay ?? 
        const Duration(milliseconds: 150);
    
    if (delay > Duration.zero) {
      _hideTimer?.cancel();
      _hideTimer = Timer(delay, _hideTooltip);
    } else {
      _hideTooltip();
    }
  }

  void _showTooltip() {
    if (_isVisible) return;
    
    setState(() {
      _isVisible = true;
    });
    
    _overlayEntry = OverlayEntry(
      builder: (context) => TooltipOverlay(
        layerLink: _layerLink,
        message: widget.message,
        richContent: widget.richContent,
        position: widget.position,
        showArrow: widget.showArrow,
        tooltipTheme: widget.tooltipTheme,
        customOffset: widget.customOffset,
        fadeAnimation: _fadeAnimation,
        semanticLabel: widget.semanticLabel,
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
    _animationController.forward();
    
    widget.onVisibilityChanged?.call(true);
  }

  void _hideTooltip({bool immediate = false}) {
    if (!_isVisible) return;
    
    setState(() {
      _isVisible = false;
    });
    
    if (immediate) {
      _overlayEntry?.remove();
      _overlayEntry = null;
    } else {
      _animationController.reverse().then((_) {
        _overlayEntry?.remove();
        _overlayEntry = null;
      });
    }
    
    widget.onVisibilityChanged?.call(false);
  }

  void _handlePointerEnter(PointerEnterEvent event) {
    if (!_shouldShowOnHover) return;
    
    _isHovered = true;
    _scheduleShow();
  }

  void _handlePointerExit(PointerExitEvent event) {
    if (!_shouldShowOnHover) return;
    
    _isHovered = false;
    
    // Only hide if not focused
    if (!_isFocused) {
      _scheduleHide();
    }
  }

  void _handleFocusChange(bool hasFocus) {
    if (!_shouldShowOnFocus) return;
    
    _isFocused = hasFocus;
    
    if (hasFocus) {
      _scheduleShow();
    } else if (!_isHovered) {
      _scheduleHide();
    }
  }

  void _handleTap() {
    if (!_shouldShowOnTap) return;
    
    if (_isVisible) {
      _hideTooltip();
    } else {
      _showTooltip();
    }
  }

  void _handleLongPress() {
    if (!_shouldShowOnLongPress) return;
    
    // Provide haptic feedback
    HapticFeedback.lightImpact();
    _showTooltip();
  }

  @override
  Widget build(BuildContext context) {
    Widget result = CompositedTransformTarget(
      link: _layerLink,
      child: widget.child,
    );

    // Add mouse region for hover detection
    if (_shouldShowOnHover) {
      result = MouseRegion(
        onEnter: _handlePointerEnter,
        onExit: _handlePointerExit,
        child: result,
      );
    }

    // Add focus handling
    if (_shouldShowOnFocus) {
      result = Focus(
        onFocusChange: _handleFocusChange,
        child: result,
      );
    }

    // Add gesture detection for tap and long press
    if (_shouldShowOnTap || _shouldShowOnLongPress) {
      result = GestureDetector(
        onTap: _shouldShowOnTap ? _handleTap : null,
        onLongPress: _shouldShowOnLongPress ? _handleLongPress : null,
        child: result,
      );
    }

    // Add semantic wrapper
    if (widget.semanticLabel?.isNotEmpty == true || widget.message.isNotEmpty) {
      result = Semantics(
        tooltip: widget.semanticLabel ?? widget.message,
        child: result,
      );
    }

    return result;
  }
}

/// The actual tooltip overlay that positions itself relative to the target.
class TooltipOverlay extends StatelessWidget {
  final LayerLink layerLink;
  final String message;
  final Widget? richContent;
  final ShadcnTooltipPosition position;
  final bool showArrow;
  final ShadcnTooltipTheme tooltipTheme;
  final Offset? customOffset;
  final Animation<double> fadeAnimation;
  final String? semanticLabel;

  const TooltipOverlay({
    super.key,
    required this.layerLink,
    required this.message,
    required this.richContent,
    required this.position,
    required this.showArrow,
    required this.tooltipTheme,
    required this.customOffset,
    required this.fadeAnimation,
    this.semanticLabel,
  });

  @override
  Widget build(BuildContext context) {
    final positionData = _calculatePosition();

    return CompositedTransformFollower(
      link: layerLink,
      targetAnchor: positionData.targetAnchor,
      followerAnchor: positionData.followerAnchor,
      offset: positionData.offset,
      child: AnimatedBuilder(
        animation: fadeAnimation,
        builder: (context, child) {
          return FadeTransition(
            opacity: fadeAnimation,
            child: Material(
              type: MaterialType.transparency,
              child: _buildTooltipContent(context, positionData),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTooltipContent(BuildContext context, _TooltipPositionData positionData) {
    final backgroundColor = tooltipTheme.resolveBackgroundColor(context);
    final borderRadius = tooltipTheme.resolveTooltipBorderRadius(context);
    final contentPadding = tooltipTheme.resolveContentPadding(context);

    Widget content = richContent ?? Text(
      message,
      style: tooltipTheme.resolveTooltipTextStyle(context),
    );

    Widget tooltipContent = Container(
      constraints: BoxConstraints(
        maxWidth: tooltipTheme.maxWidth ?? 200.0,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: borderRadius,
        boxShadow: tooltipTheme.elevation != null && tooltipTheme.elevation! > 0
            ? [
                BoxShadow(
                  color: (tooltipTheme.shadowColor ?? Colors.black).withOpacity(0.1),
                  blurRadius: tooltipTheme.elevation! * 2,
                  offset: Offset(0, tooltipTheme.elevation!),
                ),
              ]
            : null,
      ),
      padding: contentPadding,
      child: DefaultTextStyle(
        style: tooltipTheme.resolveTooltipTextStyle(context),
        child: content,
      ),
    );

    // Add arrow if requested
    if (showArrow) {
      return Stack(
        clipBehavior: Clip.none,
        children: [
          tooltipContent,
          _buildArrow(context, positionData, backgroundColor),
        ],
      );
    }

    return tooltipContent;
  }

  Widget _buildArrow(BuildContext context, _TooltipPositionData positionData, Color arrowColor) {
    final arrowSize = tooltipTheme.arrowSize ?? 6.0;
    
    return Positioned(
      top: positionData.arrowPosition.dy,
      left: positionData.arrowPosition.dx,
      child: CustomPaint(
        size: Size(arrowSize, arrowSize),
        painter: _TooltipArrowPainter(
          color: arrowColor,
          direction: positionData.arrowDirection,
        ),
      ),
    );
  }

  _TooltipPositionData _calculatePosition() {
    final offset = tooltipTheme.offset ?? 8.0;
    final arrowSize = showArrow ? (tooltipTheme.arrowSize ?? 6.0) : 0.0;
    final totalOffset = offset + (arrowSize / 2);

    switch (position) {
      case ShadcnTooltipPosition.top:
        return _TooltipPositionData(
          targetAnchor: Alignment.topCenter,
          followerAnchor: Alignment.bottomCenter,
          offset: Offset(0, -totalOffset) + (customOffset ?? Offset.zero),
          arrowDirection: _TooltipArrowDirection.down,
          arrowPosition: Offset(-arrowSize / 2, -arrowSize),
        );
      
      case ShadcnTooltipPosition.topStart:
        return _TooltipPositionData(
          targetAnchor: Alignment.topLeft,
          followerAnchor: Alignment.bottomLeft,
          offset: Offset(0, -totalOffset) + (customOffset ?? Offset.zero),
          arrowDirection: _TooltipArrowDirection.down,
          arrowPosition: Offset(arrowSize, -arrowSize),
        );
      
      case ShadcnTooltipPosition.topEnd:
        return _TooltipPositionData(
          targetAnchor: Alignment.topRight,
          followerAnchor: Alignment.bottomRight,
          offset: Offset(0, -totalOffset) + (customOffset ?? Offset.zero),
          arrowDirection: _TooltipArrowDirection.down,
          arrowPosition: Offset(-arrowSize * 1.5, -arrowSize),
        );
      
      case ShadcnTooltipPosition.bottom:
        return _TooltipPositionData(
          targetAnchor: Alignment.bottomCenter,
          followerAnchor: Alignment.topCenter,
          offset: Offset(0, totalOffset) + (customOffset ?? Offset.zero),
          arrowDirection: _TooltipArrowDirection.up,
          arrowPosition: Offset(-arrowSize / 2, -arrowSize / 2),
        );
      
      case ShadcnTooltipPosition.bottomStart:
        return _TooltipPositionData(
          targetAnchor: Alignment.bottomLeft,
          followerAnchor: Alignment.topLeft,
          offset: Offset(0, totalOffset) + (customOffset ?? Offset.zero),
          arrowDirection: _TooltipArrowDirection.up,
          arrowPosition: Offset(arrowSize, -arrowSize / 2),
        );
      
      case ShadcnTooltipPosition.bottomEnd:
        return _TooltipPositionData(
          targetAnchor: Alignment.bottomRight,
          followerAnchor: Alignment.topRight,
          offset: Offset(0, totalOffset) + (customOffset ?? Offset.zero),
          arrowDirection: _TooltipArrowDirection.up,
          arrowPosition: Offset(-arrowSize * 1.5, -arrowSize / 2),
        );
      
      case ShadcnTooltipPosition.left:
        return _TooltipPositionData(
          targetAnchor: Alignment.centerLeft,
          followerAnchor: Alignment.centerRight,
          offset: Offset(-totalOffset, 0) + (customOffset ?? Offset.zero),
          arrowDirection: _TooltipArrowDirection.right,
          arrowPosition: Offset(-arrowSize / 2, -arrowSize / 2),
        );
      
      case ShadcnTooltipPosition.leftStart:
        return _TooltipPositionData(
          targetAnchor: Alignment.topLeft,
          followerAnchor: Alignment.topRight,
          offset: Offset(-totalOffset, 0) + (customOffset ?? Offset.zero),
          arrowDirection: _TooltipArrowDirection.right,
          arrowPosition: Offset(-arrowSize / 2, arrowSize),
        );
      
      case ShadcnTooltipPosition.leftEnd:
        return _TooltipPositionData(
          targetAnchor: Alignment.bottomLeft,
          followerAnchor: Alignment.bottomRight,
          offset: Offset(-totalOffset, 0) + (customOffset ?? Offset.zero),
          arrowDirection: _TooltipArrowDirection.right,
          arrowPosition: Offset(-arrowSize / 2, -arrowSize * 1.5),
        );
      
      case ShadcnTooltipPosition.right:
        return _TooltipPositionData(
          targetAnchor: Alignment.centerRight,
          followerAnchor: Alignment.centerLeft,
          offset: Offset(totalOffset, 0) + (customOffset ?? Offset.zero),
          arrowDirection: _TooltipArrowDirection.left,
          arrowPosition: Offset(-arrowSize / 2, -arrowSize / 2),
        );
      
      case ShadcnTooltipPosition.rightStart:
        return _TooltipPositionData(
          targetAnchor: Alignment.topRight,
          followerAnchor: Alignment.topLeft,
          offset: Offset(totalOffset, 0) + (customOffset ?? Offset.zero),
          arrowDirection: _TooltipArrowDirection.left,
          arrowPosition: Offset(-arrowSize / 2, arrowSize),
        );
      
      case ShadcnTooltipPosition.rightEnd:
        return _TooltipPositionData(
          targetAnchor: Alignment.bottomRight,
          followerAnchor: Alignment.bottomLeft,
          offset: Offset(totalOffset, 0) + (customOffset ?? Offset.zero),
          arrowDirection: _TooltipArrowDirection.left,
          arrowPosition: Offset(-arrowSize / 2, -arrowSize * 1.5),
        );
    }
  }
}

/// Data class holding tooltip position calculation results.
class _TooltipPositionData {
  final Alignment targetAnchor;
  final Alignment followerAnchor;
  final Offset offset;
  final _TooltipArrowDirection arrowDirection;
  final Offset arrowPosition;

  const _TooltipPositionData({
    required this.targetAnchor,
    required this.followerAnchor,
    required this.offset,
    required this.arrowDirection,
    required this.arrowPosition,
  });
}

/// Enum for tooltip arrow directions.
enum _TooltipArrowDirection { up, down, left, right }

/// Custom painter for drawing the tooltip arrow.
class _TooltipArrowPainter extends CustomPainter {
  final Color color;
  final _TooltipArrowDirection direction;

  const _TooltipArrowPainter({
    required this.color,
    required this.direction,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    final halfSize = size.width / 2;

    switch (direction) {
      case _TooltipArrowDirection.up:
        path.moveTo(halfSize, 0);
        path.lineTo(0, size.height);
        path.lineTo(size.width, size.height);
        break;
      
      case _TooltipArrowDirection.down:
        path.moveTo(0, 0);
        path.lineTo(size.width, 0);
        path.lineTo(halfSize, size.height);
        break;
      
      case _TooltipArrowDirection.left:
        path.moveTo(0, halfSize);
        path.lineTo(size.width, 0);
        path.lineTo(size.width, size.height);
        break;
      
      case _TooltipArrowDirection.right:
        path.moveTo(0, 0);
        path.lineTo(size.width, halfSize);
        path.lineTo(0, size.height);
        break;
    }

    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(_TooltipArrowPainter oldDelegate) {
    return color != oldDelegate.color || direction != oldDelegate.direction;
  }
}