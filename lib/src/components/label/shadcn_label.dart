import 'package:flutter/material.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_label_theme.dart';
import '../../constants/shadcn_tokens.dart';

/// A shadcn-styled label component with proper form field association.
/// 
/// This label component provides accessible form field labeling with support
/// for required/optional indicators, error states, and proper semantic
/// association with form controls. It integrates seamlessly with Material
/// form fields and other shadcn components.
/// 
/// The label automatically derives its styling from the current theme context,
/// with fallback to Material Design values. All styling is customizable through
/// the [ShadcnLabelTheme] extension.
/// 
/// Example usage:
/// ```dart
/// // Basic label
/// ShadcnLabel(text: 'Email Address')
/// 
/// // Required field label
/// ShadcnLabel(
///   text: 'Password',
///   requirement: ShadcnLabelRequirement.required,
/// )
/// 
/// // Label associated with form field
/// Column(
///   crossAxisAlignment: CrossAxisAlignment.start,
///   children: [
///     ShadcnLabel(
///       text: 'Username',
///       requirement: ShadcnLabelRequirement.required,
///       associatedControl: usernameField,
///     ),
///     ShadcnInput(
///       key: usernameField,
///       placeholder: 'Enter username',
///     ),
///   ],
/// )
/// ```
class ShadcnLabel extends ShadcnComponent with ShadcnComponentValidation {
  /// Text content for the label.
  final String? text;
  
  /// Custom child widget. Takes precedence over [text].
  final Widget? child;
  
  /// Current state of the label (normal, disabled, error).
  final ShadcnLabelState state;
  
  /// Requirement indicator type.
  final ShadcnLabelRequirement requirement;
  
  /// Key of the form control this label is associated with.
  /// Used for proper accessibility and semantic association.
  final GlobalKey? associatedControl;
  
  /// Custom text color. Overrides theme setting.
  final Color? textColor;
  
  /// Custom text style. Overrides theme setting.
  final TextStyle? textStyle;
  
  /// Custom font size. Overrides theme setting.
  final double? fontSize;
  
  /// Custom font weight. Overrides theme setting.
  final FontWeight? fontWeight;
  
  /// Custom margin around the label. Overrides theme setting.
  final EdgeInsets? margin;
  
  /// Custom padding within the label. Overrides theme setting.
  final EdgeInsets? padding;
  
  /// Custom required indicator text. Overrides theme setting.
  final String? requiredText;
  
  /// Custom optional indicator text. Overrides theme setting.
  final String? optionalText;
  
  /// Custom required indicator color. Overrides theme setting.
  final Color? requiredColor;
  
  /// Custom optional indicator color. Overrides theme setting.
  final Color? optionalColor;
  
  /// Custom spacing between label text and indicator. Overrides theme setting.
  final double? indicatorSpacing;
  
  /// Callback when the label is tapped.
  final VoidCallback? onTap;
  
  /// Callback when the label is long pressed.
  final VoidCallback? onLongPress;
  
  /// Focus node for keyboard navigation.
  final FocusNode? focusNode;
  
  /// Whether the label should autofocus when first built.
  final bool autofocus;
  
  /// Tooltip message displayed on hover.
  final String? tooltip;
  
  /// Semantic label for accessibility. If null, uses [text].
  final String? semanticLabel;
  
  /// Whether to exclude this label from semantics tree.
  final bool excludeFromSemantics;
  
  /// Custom mouse cursor when hovering over the label.
  final MouseCursor? mouseCursor;
  
  /// Whether to enable haptic feedback on press.
  final bool enableFeedback;
  
  /// Custom splash color for press animations.
  final Color? splashColor;
  
  /// Custom highlight color for press states.
  final Color? highlightColor;
  
  /// Whether to show focus indicator when associated control is focused.
  final bool? showFocusIndicator;

  const ShadcnLabel({
    super.key,
    this.text,
    this.child,
    this.state = ShadcnLabelState.normal,
    this.requirement = ShadcnLabelRequirement.none,
    this.associatedControl,
    this.textColor,
    this.textStyle,
    this.fontSize,
    this.fontWeight,
    this.margin,
    this.padding,
    this.requiredText,
    this.optionalText,
    this.requiredColor,
    this.optionalColor,
    this.indicatorSpacing,
    this.onTap,
    this.onLongPress,
    this.focusNode,
    this.autofocus = false,
    this.tooltip,
    this.semanticLabel,
    this.excludeFromSemantics = false,
    this.mouseCursor,
    this.enableFeedback = true,
    this.splashColor,
    this.highlightColor,
    this.showFocusIndicator,
  }) : assert(
         text != null || child != null,
         'Either text or child must be provided',
       );

  /// Factory constructor for required field label.
  factory ShadcnLabel.required({
    Key? key,
    String? text,
    Widget? child,
    ShadcnLabelState state = ShadcnLabelState.normal,
    GlobalKey? associatedControl,
    String? requiredText,
    Color? requiredColor,
    VoidCallback? onTap,
    String? tooltip,
  }) {
    return ShadcnLabel(
      key: key,
      text: text,
      child: child,
      state: state,
      requirement: ShadcnLabelRequirement.required,
      associatedControl: associatedControl,
      requiredText: requiredText,
      requiredColor: requiredColor,
      onTap: onTap,
      tooltip: tooltip,
    );
  }

  /// Factory constructor for optional field label.
  factory ShadcnLabel.optional({
    Key? key,
    String? text,
    Widget? child,
    ShadcnLabelState state = ShadcnLabelState.normal,
    GlobalKey? associatedControl,
    String? optionalText,
    Color? optionalColor,
    VoidCallback? onTap,
    String? tooltip,
  }) {
    return ShadcnLabel(
      key: key,
      text: text,
      child: child,
      state: state,
      requirement: ShadcnLabelRequirement.optional,
      associatedControl: associatedControl,
      optionalText: optionalText,
      optionalColor: optionalColor,
      onTap: onTap,
      tooltip: tooltip,
    );
  }

  /// Factory constructor for form field label with proper association.
  factory ShadcnLabel.forField({
    Key? key,
    required String text,
    required GlobalKey associatedControl,
    ShadcnLabelState state = ShadcnLabelState.normal,
    ShadcnLabelRequirement requirement = ShadcnLabelRequirement.none,
    VoidCallback? onTap,
    String? tooltip,
  }) {
    return ShadcnLabel(
      key: key,
      text: text,
      state: state,
      requirement: requirement,
      associatedControl: associatedControl,
      onTap: onTap,
      tooltip: tooltip,
      showFocusIndicator: true,
    );
  }

  /// Whether the label is interactive (has tap callbacks).
  bool get isInteractive => onTap != null || onLongPress != null;

  /// Whether the label is associated with a form control.
  bool get hasAssociatedControl => associatedControl != null;

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    // Validate theme context and properties
    validateThemeProperties(context);
    validateVariant(state, ShadcnLabelState.values, 'ShadcnLabel');
    validateVariant(requirement, ShadcnLabelRequirement.values, 'ShadcnLabel');
    validateCallbacks(
      {'onTap': onTap, 'onLongPress': onLongPress}, 
      'ShadcnLabel',
    );
    validateAccessibility(
      semanticLabel: semanticLabel,
      tooltip: tooltip,
      excludeFromSemantics: excludeFromSemantics,
      componentName: 'ShadcnLabel',
    );

    // Resolve label theme
    final labelTheme = resolveTheme<ShadcnLabelTheme>(
      context,
      ShadcnLabelTheme.defaultTheme,
    );

    // Resolve colors based on state
    final resolvedColors = _resolveStateColors(labelTheme, state, theme.colorScheme);

    // Build label content
    final labelContent = _buildLabelContent(context, labelTheme, resolvedColors, theme);

    // Apply spacing
    final effectiveMargin = margin ?? labelTheme.margin ?? EdgeInsets.zero;
    final effectivePadding = padding ?? labelTheme.padding ?? EdgeInsets.zero;

    // Build the label widget
    Widget label = AnimatedContainer(
      duration: labelTheme.animationDuration ?? ShadcnTokens.durationFast,
      curve: labelTheme.animationCurve ?? Curves.easeInOut,
      margin: effectiveMargin,
      padding: effectivePadding,
      child: labelContent,
    );

    // Add interaction handling if needed
    if (isInteractive) {
      label = Material(
        type: MaterialType.transparency,
        child: InkWell(
          onTap: onTap,
          onLongPress: onLongPress,
          splashColor: splashColor,
          highlightColor: highlightColor,
          mouseCursor: mouseCursor ?? SystemMouseCursors.click,
          enableFeedback: enableFeedback,
          excludeFromSemantics: true, // We handle semantics separately
          focusNode: focusNode,
          autofocus: autofocus,
          child: label,
        ),
      );
    }

    // Add focus handling
    if (focusNode != null) {
      label = Focus(
        focusNode: focusNode,
        child: label,
      );
    }

    // Add semantics for form association
    if (!excludeFromSemantics) {
      label = Semantics(
        label: semanticLabel ?? _generateSemanticLabel(),
        excludeSemantics: false,
        child: label,
      );
    }

    // Add tooltip
    if (tooltip != null) {
      label = Tooltip(
        message: tooltip!,
        child: label,
      );
    }

    // Handle form field association
    if (hasAssociatedControl) {
      label = _buildAssociatedLabel(label, labelTheme);
    }

    return label;
  }

  /// Resolves colors for the current state.
  _LabelColors _resolveStateColors(
    ShadcnLabelTheme theme, 
    ShadcnLabelState state, 
    ColorScheme colorScheme,
  ) {
    Color textColor;
    Color indicatorColor;

    switch (state) {
      case ShadcnLabelState.normal:
        textColor = this.textColor ?? 
            theme.textColor ?? 
            colorScheme.onSurface;
        break;

      case ShadcnLabelState.disabled:
        textColor = this.textColor ?? 
            theme.disabledTextColor ?? 
            colorScheme.onSurface.withOpacity(0.38);
        break;

      case ShadcnLabelState.error:
        textColor = this.textColor ?? 
            theme.errorTextColor ?? 
            colorScheme.error;
        break;
    }

    // Resolve indicator colors
    switch (requirement) {
      case ShadcnLabelRequirement.required:
        indicatorColor = requiredColor ?? 
            theme.requiredColor ?? 
            colorScheme.error;
        break;
      case ShadcnLabelRequirement.optional:
        indicatorColor = optionalColor ?? 
            theme.optionalColor ?? 
            colorScheme.onSurfaceVariant;
        break;
      case ShadcnLabelRequirement.none:
        indicatorColor = textColor;
        break;
    }

    return _LabelColors(
      text: textColor,
      indicator: indicatorColor,
    );
  }

  /// Builds the main content of the label.
  Widget _buildLabelContent(
    BuildContext context,
    ShadcnLabelTheme theme,
    _LabelColors colors,
    ThemeData materialTheme,
  ) {
    // Build effective text style
    final effectiveTextStyle = resolveTextStyle(
      context,
      textStyle?.copyWith(color: colors.text),
      (textTheme) => textTheme.labelMedium ?? const TextStyle(),
    ).copyWith(
      color: colors.text,
      fontSize: fontSize ?? theme.fontSize ?? ShadcnTokens.fontSizeSm,
      fontWeight: fontWeight ?? theme.fontWeight ?? ShadcnTokens.fontWeightMedium,
      letterSpacing: theme.letterSpacing ?? 0.01,
      height: theme.height ?? ShadcnTokens.lineHeightNormal,
    );

    // Build content widgets
    final List<Widget> contentWidgets = [];

    // Main label content
    if (child != null) {
      contentWidgets.add(
        DefaultTextStyle(
          style: effectiveTextStyle,
          child: child!,
        ),
      );
    } else if (text != null) {
      contentWidgets.add(
        Text(
          text!,
          style: effectiveTextStyle,
        ),
      );
    }

    // Add requirement indicator if needed
    if (requirement != ShadcnLabelRequirement.none) {
      contentWidgets.add(
        SizedBox(
          width: indicatorSpacing ?? 
              theme.indicatorSpacing ?? 
              ShadcnTokens.spacing1,
        ),
      );
      contentWidgets.add(_buildRequirementIndicator(theme, colors));
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.baseline,
      textBaseline: TextBaseline.alphabetic,
      children: contentWidgets,
    );
  }

  /// Builds the requirement indicator.
  Widget _buildRequirementIndicator(ShadcnLabelTheme theme, _LabelColors colors) {
    String indicatorText;
    TextStyle indicatorStyle;

    switch (requirement) {
      case ShadcnLabelRequirement.required:
        indicatorText = requiredText ?? theme.requiredText ?? '*';
        indicatorStyle = theme.requiredStyle?.copyWith(color: colors.indicator) ?? 
            TextStyle(
              color: colors.indicator,
              fontSize: theme.fontSize ?? ShadcnTokens.fontSizeSm,
              fontWeight: ShadcnTokens.fontWeightNormal,
            );
        break;

      case ShadcnLabelRequirement.optional:
        indicatorText = optionalText ?? theme.optionalText ?? '(optional)';
        indicatorStyle = theme.optionalStyle?.copyWith(color: colors.indicator) ?? 
            TextStyle(
              color: colors.indicator,
              fontSize: (theme.fontSize ?? ShadcnTokens.fontSizeSm) - 2,
              fontWeight: ShadcnTokens.fontWeightNormal,
              fontStyle: FontStyle.italic,
            );
        break;

      case ShadcnLabelRequirement.none:
        return const SizedBox.shrink();
    }

    return Text(
      indicatorText,
      style: indicatorStyle,
    );
  }

  /// Builds label with form field association.
  Widget _buildAssociatedLabel(Widget label, ShadcnLabelTheme theme) {
    // Add tap handling to focus associated control
    if (!isInteractive && associatedControl != null) {
      label = GestureDetector(
        onTap: () => _focusAssociatedControl(),
        child: label,
      );
    }

    // Add focus indicator if enabled
    final shouldShowFocusIndicator = showFocusIndicator ?? 
        theme.showFocusIndicator ?? 
        false;

    if (shouldShowFocusIndicator && associatedControl != null) {
      // This would require listening to the associated control's focus
      // For now, we'll return the label as-is
      // In a real implementation, you would use a FocusScope listener
      label = Container(
        decoration: const BoxDecoration(
          // Focus indicator decoration would go here
        ),
        child: label,
      );
    }

    return label;
  }

  /// Focuses the associated form control.
  void _focusAssociatedControl() {
    if (associatedControl?.currentState != null) {
      final context = associatedControl!.currentContext;
      if (context != null) {
        FocusScope.of(context).requestFocus();
      }
    }
  }

  /// Generates semantic label for accessibility.
  String _generateSemanticLabel() {
    if (semanticLabel != null) return semanticLabel!;
    
    String baseLabel = '';
    if (text != null && text!.isNotEmpty) {
      baseLabel = text!;
    } else {
      baseLabel = 'Label';
    }

    // Add requirement information
    switch (requirement) {
      case ShadcnLabelRequirement.required:
        baseLabel += ', required field';
        break;
      case ShadcnLabelRequirement.optional:
        baseLabel += ', optional field';
        break;
      case ShadcnLabelRequirement.none:
        break;
    }

    // Add state information
    switch (state) {
      case ShadcnLabelState.disabled:
        baseLabel += ', disabled';
        break;
      case ShadcnLabelState.error:
        baseLabel += ', error';
        break;
      case ShadcnLabelState.normal:
        break;
    }

    return baseLabel;
  }
}

/// Helper class to hold resolved label colors.
class _LabelColors {
  final Color text;
  final Color indicator;

  const _LabelColors({
    required this.text,
    required this.indicator,
  });
}