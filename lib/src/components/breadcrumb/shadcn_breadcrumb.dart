import 'package:flutter/material.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_breadcrumb_theme.dart';
import '../../constants/shadcn_tokens.dart';

/// A shadcn-styled breadcrumb navigation component.
/// 
/// Breadcrumbs provide a trail of links to help users navigate through
/// a hierarchical structure. This component follows shadcn design principles
/// while integrating seamlessly with Material Design theming.
/// 
/// Features:
/// - Customizable separator icons and styling
/// - Active/inactive state management
/// - Theme-aware hover and focus states
/// - Full accessibility support
/// - Material Design integration
/// 
/// Example usage:
/// ```dart
/// ShadcnBreadcrumb(
///   items: [
///     ShadcnBreadcrumbItem(
///       text: 'Home',
///       onTap: () => Navigator.pushNamed(context, '/'),
///     ),
///     ShadcnBreadcrumbItem(
///       text: 'Products',
///       onTap: () => Navigator.pushNamed(context, '/products'),
///     ),
///     ShadcnBreadcrumbItem(
///       text: 'Category',
///       // Current page - no onTap callback
///     ),
///   ],
/// )
/// ```
class ShadcnBreadcrumb extends ShadcnComponent with ShadcnComponentValidation {
  /// List of breadcrumb items to display
  final List<ShadcnBreadcrumbItem> items;
  
  /// Custom separator widget to display between items
  /// If null, uses the theme's default separator
  final Widget? separator;
  
  /// Maximum number of items to show before collapsing
  /// If exceeded, middle items will be replaced with "..."
  final int? maxItems;
  
  /// Whether to show the home icon for the first item
  final bool showHome;
  
  /// Custom home icon widget
  final Widget? homeIcon;
  
  /// Optional semantic label for the entire breadcrumb
  final String? semanticLabel;
  
  /// Callback when an item is tapped
  final ValueChanged<int>? onItemTap;
  
  /// Whether the breadcrumb is disabled
  final bool disabled;
  
  /// Custom theme data override
  final ShadcnBreadcrumbTheme? theme;
  
  const ShadcnBreadcrumb({
    super.key,
    required this.items,
    this.separator,
    this.maxItems,
    this.showHome = false,
    this.homeIcon,
    this.semanticLabel,
    this.onItemTap,
    this.disabled = false,
    this.theme,
  });

  @override
  Widget buildWithTheme(BuildContext context, ThemeData materialTheme) {
    // Validate inputs
    validateRequiredProperties({'items': items});
    validateThemeProperties(context);
    
    if (items.isEmpty) {
      return const SizedBox.shrink();
    }
    
    // Resolve theme
    final breadcrumbTheme = theme ?? 
        resolveTheme<ShadcnBreadcrumbTheme>(
          context,
          ShadcnBreadcrumbTheme.defaultTheme,
        );
    
    // Build breadcrumb items with separators
    final children = <Widget>[];
    final displayItems = _getDisplayItems();
    
    for (int i = 0; i < displayItems.length; i++) {
      final item = displayItems[i];
      final isLast = i == displayItems.length - 1;
      final isFirst = i == 0;
      
      // Add home icon if requested and this is the first item
      if (isFirst && showHome) {
        children.add(_buildHomeIcon(context, breadcrumbTheme));
        
        // Only add separator if there are actual items
        if (displayItems.isNotEmpty) {
          children.add(_buildSeparator(context, breadcrumbTheme));
        }
      }
      
      // Add breadcrumb item
      children.add(_buildBreadcrumbItem(
        context,
        breadcrumbTheme,
        item,
        i,
        isLast,
      ));
      
      // Add separator (except for last item)
      if (!isLast) {
        children.add(_buildSeparator(context, breadcrumbTheme));
      }
    }
    
    // Build final breadcrumb container
    return Semantics(
      label: semanticLabel ?? 'Breadcrumb navigation',
      container: true,
      child: Container(
        constraints: BoxConstraints(
          minHeight: resolveDouble(
            context,
            breadcrumbTheme.minHeight,
            ShadcnTokens.buttonHeightSm,
          ),
        ),
        padding: resolveSpacing(
          context,
          breadcrumbTheme.containerPadding,
          const EdgeInsets.symmetric(
            horizontal: ShadcnTokens.spacing0,
            vertical: ShadcnTokens.spacing1,
          ),
        ),
        decoration: BoxDecoration(
          color: resolveColor(
            context,
            breadcrumbTheme.backgroundColor,
            (theme) => Colors.transparent,
          ),
          borderRadius: resolveBorderRadius(
            context,
            breadcrumbTheme.borderRadius,
            BorderRadius.circular(ShadcnTokens.radiusSm),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: children,
        ),
      ),
    );
  }
  
  /// Gets the items to display, handling maxItems truncation
  List<ShadcnBreadcrumbItem> _getDisplayItems() {
    if (maxItems == null || items.length <= maxItems!) {
      return items;
    }
    
    // If we need to truncate, show first item, ellipsis, and last few items
    if (maxItems! < 3) {
      // Not enough space for ellipsis, just show last items
      return items.take(maxItems!).toList();
    }
    
    final result = <ShadcnBreadcrumbItem>[];
    
    // Always show first item
    result.add(items.first);
    
    // Add ellipsis item if needed
    if (items.length > maxItems!) {
      result.add(const ShadcnBreadcrumbItem(
        text: '...',
        disabled: true,
      ));
    }
    
    // Add last few items
    final remainingSlots = maxItems! - result.length;
    if (remainingSlots > 0) {
      final startIndex = items.length - remainingSlots;
      result.addAll(items.skip(startIndex));
    }
    
    return result;
  }
  
  /// Builds a home icon widget
  Widget _buildHomeIcon(BuildContext context, ShadcnBreadcrumbTheme theme) {
    final icon = homeIcon ?? Icon(
      Icons.home,
      size: resolveDouble(
        context,
        theme.separatorSize,
        ShadcnTokens.iconSizeSm,
      ),
    );
    
    return IconTheme(
      data: IconThemeData(
        color: resolveColor(
          context,
          theme.itemColor,
          (materialTheme) => materialTheme.colorScheme.onSurface.withOpacity(0.6),
        ),
        size: resolveDouble(
          context,
          theme.separatorSize,
          ShadcnTokens.iconSizeSm,
        ),
      ),
      child: icon,
    );
  }
  
  /// Builds a separator widget between breadcrumb items
  Widget _buildSeparator(BuildContext context, ShadcnBreadcrumbTheme theme) {
    final separatorWidget = separator ?? theme.separatorIcon ?? Icon(
      Icons.chevron_right,
      size: resolveDouble(
        context,
        theme.separatorSize,
        ShadcnTokens.iconSizeSm,
      ),
    );
    
    return Padding(
      padding: resolveSpacing(
        context,
        theme.separatorPadding,
        const EdgeInsets.symmetric(horizontal: ShadcnTokens.spacing1),
      ),
      child: IconTheme(
        data: IconThemeData(
          color: resolveColor(
            context,
            theme.separatorColor,
            (materialTheme) => materialTheme.colorScheme.onSurface.withOpacity(0.4),
          ),
          size: resolveDouble(
            context,
            theme.separatorSize,
            ShadcnTokens.iconSizeSm,
          ),
        ),
        child: separatorWidget,
      ),
    );
  }
  
  /// Builds an individual breadcrumb item
  Widget _buildBreadcrumbItem(
    BuildContext context,
    ShadcnBreadcrumbTheme theme,
    ShadcnBreadcrumbItem item,
    int index,
    bool isLast,
  ) {
    final isInteractive = !disabled && !item.disabled && item.onTap != null;
    final isActive = isLast; // Last item is considered active
    
    // Determine text color
    Color textColor;
    if (item.disabled || disabled) {
      textColor = resolveColor(
        context,
        theme.disabledItemColor,
        (materialTheme) => materialTheme.colorScheme.onSurface.withOpacity(0.38),
      );
    } else if (isActive) {
      textColor = resolveColor(
        context,
        theme.activeItemColor,
        (materialTheme) => materialTheme.colorScheme.onSurface,
      );
    } else {
      textColor = resolveColor(
        context,
        theme.itemColor,
        (materialTheme) => materialTheme.colorScheme.onSurface.withOpacity(0.6),
      );
    }
    
    // Determine text style
    final baseTextStyle = resolveTextStyle(
      context,
      isActive ? theme.activeTextStyle : theme.textStyle,
      (textTheme) => textTheme.bodyMedium!,
    );
    
    final effectiveTextStyle = baseTextStyle.copyWith(
      color: textColor,
      fontWeight: isActive 
          ? ShadcnTokens.fontWeightMedium
          : ShadcnTokens.fontWeightNormal,
    );
    
    Widget child = Container(
      constraints: BoxConstraints(
        minHeight: resolveDouble(
          context,
          theme.itemMinHeight,
          24.0,
        ),
      ),
      padding: resolveSpacing(
        context,
        theme.itemPadding,
        const EdgeInsets.symmetric(
          horizontal: ShadcnTokens.spacing2,
          vertical: ShadcnTokens.spacing1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (item.icon != null) ...[
            IconTheme(
              data: IconThemeData(
                color: textColor,
                size: ShadcnTokens.iconSizeSm,
              ),
              child: item.icon!,
            ),
            const SizedBox(width: ShadcnTokens.spacing1),
          ],
          Text(
            item.text,
            style: effectiveTextStyle,
          ),
        ],
      ),
    );
    
    // Wrap with interactive behavior if needed
    if (isInteractive) {
      child = Material(
        type: MaterialType.transparency,
        child: InkWell(
          onTap: () {
            item.onTap?.call();
            onItemTap?.call(index);
          },
          borderRadius: resolveBorderRadius(
            context,
            theme.itemBorderRadius,
            BorderRadius.circular(ShadcnTokens.radiusSm),
          ),
          hoverColor: resolveColor(
            context,
            theme.hoverColor,
            (materialTheme) => materialTheme.colorScheme.onSurface.withOpacity(0.04),
          ),
          focusColor: resolveColor(
            context,
            theme.focusColor,
            (materialTheme) => materialTheme.colorScheme.primary.withOpacity(0.12),
          ),
          splashColor: resolveColor(
            context,
            theme.pressedColor,
            (materialTheme) => materialTheme.colorScheme.onSurface.withOpacity(0.08),
          ),
          child: child,
        ),
      );
    }
    
    // Add semantic information
    if (item.semanticLabel != null || item.tooltip != null) {
      child = Semantics(
        label: item.semanticLabel,
        button: isInteractive,
        child: child,
      );
      
      if (item.tooltip != null) {
        child = Tooltip(
          message: item.tooltip!,
          child: child,
        );
      }
    }
    
    return child;
  }
}