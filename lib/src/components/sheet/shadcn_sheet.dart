import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_sheet_theme.dart';
import '../../constants/shadcn_tokens.dart';

/// A shadcn-styled sheet component with comprehensive theming and Material integration.
/// 
/// This sheet component provides bottom sheet, side sheet, and modal sheet variants
/// with full Material Design integration. It supports drag-to-dismiss, custom positioning,
/// animation controls, and comprehensive accessibility features.
/// 
/// The sheet automatically derives its styling from the current theme context,
/// with fallback to Material Design values. All styling is customizable through
/// the [ShadcnSheetTheme] extension.
/// 
/// Example usage:
/// ```dart
/// ShadcnSheet.show(
///   context: context,
///   builder: (context) => ShadcnSheet(
///     title: 'Settings',
///     description: 'Configure your preferences',
///     content: Column(
///       children: [
///         ListTile(title: Text('Dark mode')),
///         ListTile(title: Text('Notifications')),
///       ],
///     ),
///     actions: [
///       ShadcnButton(
///         text: 'Cancel',
///         variant: ShadcnButtonVariant.outline,
///         onPressed: () => Navigator.of(context).pop(),
///       ),
///       ShadcnButton(
///         text: 'Save',
///         onPressed: () => Navigator.of(context).pop(),
///       ),
///     ],
///   ),
/// );
/// ```
class ShadcnSheet extends ShadcnComponent with ShadcnComponentValidation {
  /// The sheet's title text.
  final String? title;
  
  /// Optional description text displayed below the title.
  final String? description;
  
  /// The main content widget to display in the sheet body.
  final Widget? content;
  
  /// Optional action buttons displayed in the footer.
  final List<Widget>? actions;
  
  /// Optional custom header widget. If provided, overrides title and description.
  final Widget? header;
  
  /// Optional custom footer widget. If provided, overrides actions.
  final Widget? footer;
  
  /// The position/type of the sheet.
  final ShadcnSheetPosition position;
  
  /// Whether the sheet is modal (blocks interaction with content behind).
  final bool? isModal;
  
  /// Whether the sheet can be dismissed by tapping the backdrop.
  final bool? isDismissible;
  
  /// Whether the sheet can be dragged to dismiss.
  final bool? enableDrag;
  
  /// Whether to show the drag handle at the top of the sheet.
  final bool? showDragHandle;
  
  /// Whether to show the close button in the header.
  final bool? showCloseButton;
  
  /// Custom width for side sheets.
  final double? width;
  
  /// Custom height constraint.
  final double? height;
  
  /// Custom maximum height constraint.
  final double? maxHeight;
  
  /// Custom minimum height constraint.
  final double? minHeight;
  
  /// Custom animation duration.
  final Duration? animationDuration;
  
  /// Custom animation curve.
  final Curve? animationCurve;
  
  /// Custom backdrop color.
  final Color? backdropColor;
  
  /// Custom backdrop opacity.
  final double? backdropOpacity;
  
  /// Custom barrier label for accessibility.
  final String? barrierLabel;
  
  /// Called when the sheet is dismissed.
  final VoidCallback? onDismissed;
  
  /// Called when drag starts.
  final VoidCallback? onDragStart;
  
  /// Called when drag ends.
  final VoidCallback? onDragEnd;
  
  /// Custom padding for the sheet content.
  final EdgeInsets? padding;
  
  /// Custom background color for the sheet.
  final Color? backgroundColor;
  
  /// Custom border radius.
  final BorderRadius? borderRadius;
  
  /// Whether to exclude from semantics tree.
  final bool excludeFromSemantics;
  
  /// Custom semantic label for the sheet.
  final String? semanticLabel;
  
  const ShadcnSheet({
    super.key,
    this.title,
    this.description,
    this.content,
    this.actions,
    this.header,
    this.footer,
    this.position = ShadcnSheetPosition.bottom,
    this.isModal,
    this.isDismissible,
    this.enableDrag,
    this.showDragHandle,
    this.showCloseButton,
    this.width,
    this.height,
    this.maxHeight,
    this.minHeight,
    this.animationDuration,
    this.animationCurve,
    this.backdropColor,
    this.backdropOpacity,
    this.barrierLabel,
    this.onDismissed,
    this.onDragStart,
    this.onDragEnd,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
    this.excludeFromSemantics = false,
    this.semanticLabel,
  });

  /// Shows a bottom sheet with the given sheet widget.
  /// 
  /// This is a convenience method that wraps [showModalBottomSheet] and
  /// [showBottomSheet] with proper Material integration and theming.
  static Future<T?> show<T>({
    required BuildContext context,
    required WidgetBuilder builder,
    bool isModal = true,
    bool isDismissible = true,
    bool enableDrag = true,
    Color? backgroundColor,
    double? elevation,
    ShapeBorder? shape,
    Clip? clipBehavior,
    BoxConstraints? constraints,
    Color? barrierColor,
    bool isScrollControlled = false,
    bool useRootNavigator = false,
    bool useSafeArea = false,
    RouteSettings? routeSettings,
    AnimationController? transitionAnimationController,
    Offset? anchorPoint,
  }) {
    if (isModal) {
      return showModalBottomSheet<T>(
        context: context,
        builder: builder,
        backgroundColor: backgroundColor,
        elevation: elevation,
        shape: shape,
        clipBehavior: clipBehavior,
        constraints: constraints,
        barrierColor: barrierColor,
        isScrollControlled: isScrollControlled,
        useRootNavigator: useRootNavigator,
        isDismissible: isDismissible,
        enableDrag: enableDrag,
        useSafeArea: useSafeArea,
        routeSettings: routeSettings,
        transitionAnimationController: transitionAnimationController,
        anchorPoint: anchorPoint,
      );
    } else {
      final scaffoldState = Scaffold.of(context);
      final controller = scaffoldState.showBottomSheet(
        builder,
        backgroundColor: backgroundColor,
        elevation: elevation,
        shape: shape,
        clipBehavior: clipBehavior,
        constraints: constraints,
      );
      return controller.closed.then((value) => value as T?);
    }
  }

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    // Validate theme context and properties
    validateThemeProperties(context);
    validateAccessibility(
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      componentName: 'ShadcnSheet',
    );

    // Resolve sheet theme
    final sheetTheme = resolveTheme<ShadcnSheetTheme>(
      context,
      ShadcnSheetTheme.defaultTheme,
    );

    return _ShadcnSheetWidget(
      title: title,
      description: description,
      content: content,
      actions: actions,
      header: header,
      footer: footer,
      position: position,
      isModal: isModal,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      showDragHandle: showDragHandle,
      showCloseButton: showCloseButton,
      width: width,
      height: height,
      maxHeight: maxHeight,
      minHeight: minHeight,
      animationDuration: animationDuration,
      animationCurve: animationCurve,
      backdropColor: backdropColor,
      backdropOpacity: backdropOpacity,
      barrierLabel: barrierLabel,
      onDismissed: onDismissed,
      onDragStart: onDragStart,
      onDragEnd: onDragEnd,
      padding: padding,
      backgroundColor: backgroundColor,
      borderRadius: borderRadius,
      excludeFromSemantics: excludeFromSemantics,
      semanticLabel: semanticLabel,
      theme: sheetTheme,
    );
  }
}

class _ShadcnSheetWidget extends StatelessWidget {
  final String? title;
  final String? description;
  final Widget? content;
  final List<Widget>? actions;
  final Widget? header;
  final Widget? footer;
  final ShadcnSheetPosition position;
  final bool? isModal;
  final bool? isDismissible;
  final bool? enableDrag;
  final bool? showDragHandle;
  final bool? showCloseButton;
  final double? width;
  final double? height;
  final double? maxHeight;
  final double? minHeight;
  final Duration? animationDuration;
  final Curve? animationCurve;
  final Color? backdropColor;
  final double? backdropOpacity;
  final String? barrierLabel;
  final VoidCallback? onDismissed;
  final VoidCallback? onDragStart;
  final VoidCallback? onDragEnd;
  final EdgeInsets? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final bool excludeFromSemantics;
  final String? semanticLabel;
  final ShadcnSheetTheme theme;
  
  const _ShadcnSheetWidget({
    this.title,
    this.description,
    this.content,
    this.actions,
    this.header,
    this.footer,
    required this.position,
    this.isModal,
    this.isDismissible,
    this.enableDrag,
    this.showDragHandle,
    this.showCloseButton,
    this.width,
    this.height,
    this.maxHeight,
    this.minHeight,
    this.animationDuration,
    this.animationCurve,
    this.backdropColor,
    this.backdropOpacity,
    this.barrierLabel,
    this.onDismissed,
    this.onDragStart,
    this.onDragEnd,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
    required this.excludeFromSemantics,
    this.semanticLabel,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    final effectivePadding = padding ?? theme.padding ?? const EdgeInsets.all(ShadcnTokens.spacing6);
    final effectiveBackgroundColor = backgroundColor ?? theme.backgroundColor ?? Colors.white;
    final effectiveBorderRadius = borderRadius ?? theme.borderRadius ?? 
        const BorderRadius.only(
          topLeft: Radius.circular(ShadcnTokens.radiusXl),
          topRight: Radius.circular(ShadcnTokens.radiusXl),
        );
    final effectiveShowDragHandle = showDragHandle ?? theme.showDragHandle ?? true;
    final effectiveShowCloseButton = showCloseButton ?? theme.showCloseButton ?? true;

    return Container(
      width: _getEffectiveWidth(context),
      height: height,
      constraints: BoxConstraints(
        maxWidth: theme.maxWidth ?? 600.0,
        maxHeight: maxHeight ?? theme.maxHeight ?? MediaQuery.of(context).size.height * 0.9,
        minWidth: theme.minWidth ?? 300.0,
        minHeight: minHeight ?? theme.minHeight ?? 200.0,
      ),
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: effectiveBorderRadius,
        border: theme.borderColor != null
            ? Border.all(
                color: theme.borderColor!,
                width: theme.borderWidth ?? ShadcnTokens.borderWidth,
              )
            : null,
        boxShadow: theme.elevation != null
            ? [
                BoxShadow(
                  color: theme.shadowColor ?? Colors.black.withOpacity(0.1),
                  blurRadius: theme.elevation!,
                  offset: const Offset(0, -2),
                ),
              ]
            : null,
      ),
      child: Semantics(
        label: semanticLabel ?? (title != null ? 'Sheet: $title' : 'Sheet'),
        scopesRoute: true,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Drag handle
            if (effectiveShowDragHandle && position == ShadcnSheetPosition.bottom)
              _buildDragHandle(context),
            
            // Header
            if (header != null || title != null || effectiveShowCloseButton)
              _buildHeader(context),
            
            // Content
            if (content != null)
              Flexible(
                child: Container(
                  padding: theme.contentPadding ?? EdgeInsets.symmetric(vertical: ShadcnTokens.spacing4),
                  decoration: BoxDecoration(
                    color: theme.contentBackground,
                  ),
                  child: content,
                ),
              ),
            
            // Footer
            if (footer != null || (actions != null && actions!.isNotEmpty))
              _buildFooter(context),
          ],
        ),
      ),
    );
  }

  double? _getEffectiveWidth(BuildContext context) {
    switch (position) {
      case ShadcnSheetPosition.left:
      case ShadcnSheetPosition.right:
        return width ?? theme.sideSheetWidth ?? 400.0;
      case ShadcnSheetPosition.center:
        return width ?? theme.maxWidth ?? 600.0;
      case ShadcnSheetPosition.bottom:
      case ShadcnSheetPosition.top:
      default:
        return width;
    }
  }

  Widget _buildDragHandle(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: ShadcnTokens.spacing2),
      child: Center(
        child: Container(
          width: theme.dragHandleWidth ?? 32.0,
          height: theme.dragHandleHeight ?? 4.0,
          decoration: BoxDecoration(
            color: theme.dragHandleColor ?? Colors.grey,
            borderRadius: theme.dragHandleBorderRadius ?? BorderRadius.circular(ShadcnTokens.radiusSm),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    if (header != null) {
      return Container(
        padding: theme.headerPadding ?? const EdgeInsets.only(bottom: ShadcnTokens.spacing4),
        decoration: BoxDecoration(
          color: theme.headerBackground,
          borderRadius: theme.headerBorderRadius,
        ),
        child: header,
      );
    }

    return Container(
      padding: theme.headerPadding ?? const EdgeInsets.only(bottom: ShadcnTokens.spacing4),
      decoration: BoxDecoration(
        color: theme.headerBackground,
        borderRadius: theme.headerBorderRadius,
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                if (title != null)
                  Text(
                    title!,
                    style: theme.titleTextStyle ?? TextStyle(
                      fontSize: ShadcnTokens.fontSizeXl,
                      fontWeight: ShadcnTokens.fontWeightSemibold,
                      color: theme.headerForeground,
                    ),
                  ),
                if (description != null) ...[
                  const SizedBox(height: ShadcnTokens.spacing1),
                  Text(
                    description!,
                    style: theme.descriptionTextStyle ?? TextStyle(
                      fontSize: ShadcnTokens.fontSizeMd,
                      color: theme.headerForeground?.withOpacity(0.7),
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (showCloseButton ?? theme.showCloseButton ?? true)
            _buildCloseButton(context),
        ],
      ),
    );
  }

  Widget _buildCloseButton(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: InkWell(
        onTap: () {
          Navigator.of(context).pop();
          onDismissed?.call();
        },
        borderRadius: theme.closeButtonBorderRadius ?? BorderRadius.circular(ShadcnTokens.radiusMd),
        child: Container(
          width: theme.closeButtonSize ?? 32.0,
          height: theme.closeButtonSize ?? 32.0,
          padding: theme.closeButtonPadding ?? const EdgeInsets.all(ShadcnTokens.spacing2),
          decoration: BoxDecoration(
            color: theme.closeButtonBackground,
            borderRadius: theme.closeButtonBorderRadius ?? BorderRadius.circular(ShadcnTokens.radiusMd),
          ),
          child: Icon(
            Icons.close,
            size: 16.0,
            color: theme.closeButtonForeground,
          ),
        ),
      ),
    );
  }

  Widget _buildFooter(BuildContext context) {
    if (footer != null) {
      return Container(
        padding: theme.footerPadding ?? const EdgeInsets.only(top: ShadcnTokens.spacing4),
        decoration: BoxDecoration(
          color: theme.footerBackground,
          borderRadius: theme.footerBorderRadius,
        ),
        child: footer,
      );
    }

    if (actions == null || actions!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: theme.footerPadding ?? const EdgeInsets.only(top: ShadcnTokens.spacing4),
      decoration: BoxDecoration(
        color: theme.footerBackground,
        borderRadius: theme.footerBorderRadius,
      ),
      child: Row(
        mainAxisAlignment: theme.footerButtonAlignment ?? MainAxisAlignment.end,
        children: actions!.map((action) {
          final index = actions!.indexOf(action);
          return Padding(
            padding: EdgeInsets.only(
              left: index > 0 ? ShadcnTokens.spacing2 : 0,
            ),
            child: action,
          );
        }).toList(),
      ),
    );
  }
}

/// Shows a bottom sheet using the Material bottom sheet system.
/// 
/// This is a convenience function that creates and shows a [ShadcnSheet]
/// using Flutter's built-in bottom sheet functionality.
Future<T?> showShadcnBottomSheet<T>({
  required BuildContext context,
  required WidgetBuilder builder,
  Color? backgroundColor,
  double? elevation,
  ShapeBorder? shape,
  Clip? clipBehavior,
  BoxConstraints? constraints,
  Color? barrierColor,
  bool isScrollControlled = true,
  bool useRootNavigator = false,
  bool isDismissible = true,
  bool enableDrag = true,
  bool useSafeArea = false,
  RouteSettings? routeSettings,
  AnimationController? transitionAnimationController,
  Offset? anchorPoint,
}) {
  return showModalBottomSheet<T>(
    context: context,
    builder: builder,
    backgroundColor: backgroundColor,
    elevation: elevation,
    shape: shape,
    clipBehavior: clipBehavior,
    constraints: constraints,
    barrierColor: barrierColor,
    isScrollControlled: isScrollControlled,
    useRootNavigator: useRootNavigator,
    isDismissible: isDismissible,
    enableDrag: enableDrag,
    useSafeArea: useSafeArea,
    routeSettings: routeSettings,
    transitionAnimationController: transitionAnimationController,
    anchorPoint: anchorPoint,
  );
}

/// Shows a side sheet using a custom slide transition.
/// 
/// This creates a full-screen overlay with a slide-in animation for
/// side sheets (left or right positioned sheets).
Future<T?> showShadcnSideSheet<T>({
  required BuildContext context,
  required WidgetBuilder builder,
  ShadcnSheetPosition position = ShadcnSheetPosition.right,
  bool barrierDismissible = true,
  Color? barrierColor,
  String? barrierLabel,
  bool useRootNavigator = true,
  RouteSettings? routeSettings,
  Duration transitionDuration = const Duration(milliseconds: 300),
}) {
  assert(
    position == ShadcnSheetPosition.left || position == ShadcnSheetPosition.right,
    'showShadcnSideSheet only supports left and right positions',
  );

  return Navigator.of(context, rootNavigator: useRootNavigator).push<T>(
    PageRouteBuilder<T>(
      settings: routeSettings,
      pageBuilder: (context, animation, secondaryAnimation) => builder(context),
      transitionDuration: transitionDuration,
      reverseTransitionDuration: transitionDuration,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor ?? Colors.black54,
      barrierLabel: barrierLabel ?? 'Close sheet',
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        final slideAnimation = Tween<Offset>(
          begin: position == ShadcnSheetPosition.left 
              ? const Offset(-1.0, 0.0)
              : const Offset(1.0, 0.0),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Curves.easeOutCubic,
        ));

        return SlideTransition(
          position: slideAnimation,
          child: child,
        );
      },
    ),
  );
}