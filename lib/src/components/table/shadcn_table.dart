import 'package:flutter/material.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_table_theme.dart';

/// Column configuration for ShadcnTable
class ShadcnTableColumn<T> {
  /// Header text for the column
  final String label;
  
  /// Function to extract display value from row data
  final String Function(T) valueGetter;
  
  /// Whether this column is sortable
  final bool sortable;
  
  /// Width of the column (null for flexible width)
  final double? width;
  
  /// Minimum width of the column
  final double? minWidth;
  
  /// Maximum width of the column
  final double? maxWidth;
  
  /// Alignment for cell content
  final Alignment alignment;
  
  /// Custom cell builder (overrides valueGetter display)
  final Widget Function(BuildContext, T)? cellBuilder;
  
  /// Custom header builder (overrides label)
  final Widget Function(BuildContext)? headerBuilder;
  
  /// Sort comparison function (required if sortable is true)
  final int Function(T, T)? sortComparator;

  const ShadcnTableColumn({
    required this.label,
    required this.valueGetter,
    this.sortable = false,
    this.width,
    this.minWidth,
    this.maxWidth,
    this.alignment = Alignment.centerLeft,
    this.cellBuilder,
    this.headerBuilder,
    this.sortComparator,
  }) : assert(!sortable || sortComparator != null, 
              'sortComparator is required when sortable is true');
}

/// Sort configuration for table
class ShadcnTableSort {
  /// Index of the column being sorted
  final int columnIndex;
  
  /// Sort direction
  final bool ascending;

  const ShadcnTableSort({
    required this.columnIndex,
    this.ascending = true,
  });

  ShadcnTableSort copyWith({
    int? columnIndex,
    bool? ascending,
  }) {
    return ShadcnTableSort(
      columnIndex: columnIndex ?? this.columnIndex,
      ascending: ascending ?? this.ascending,
    );
  }
}

/// A shadcn-styled table widget with sorting and selection capabilities.
/// 
/// This widget provides a highly customizable table implementation that
/// follows shadcn design principles while integrating with Flutter's
/// Material Design system.
class ShadcnTable<T> extends ShadcnComponent {
  /// List of data items to display
  final List<T> data;
  
  /// Column definitions
  final List<ShadcnTableColumn<T>> columns;
  
  /// Whether to show alternating row colors
  final bool striped;
  
  /// Whether rows can be hovered
  final bool hoverable;
  
  /// Whether rows can be selected
  final bool selectable;
  
  /// Currently selected rows (if selectable is true)
  final Set<T> selectedRows;
  
  /// Callback when row selection changes
  final void Function(Set<T>)? onSelectionChanged;
  
  /// Current sort configuration
  final ShadcnTableSort? sort;
  
  /// Callback when sort changes
  final void Function(ShadcnTableSort?)? onSortChanged;
  
  /// Whether to show header
  final bool showHeader;
  
  /// Custom header height
  final double? headerHeight;
  
  /// Custom row height
  final double? rowHeight;
  
  /// Whether to show borders
  final bool showBorders;
  
  /// Custom table theme override
  final ShadcnTableTheme? theme;
  
  /// Callback when a row is tapped
  final void Function(T)? onRowTap;
  
  /// Empty state widget when data is empty
  final Widget? emptyWidget;

  const ShadcnTable({
    Key? key,
    required this.data,
    required this.columns,
    this.striped = false,
    this.hoverable = true,
    this.selectable = false,
    this.selectedRows = const {},
    this.onSelectionChanged,
    this.sort,
    this.onSortChanged,
    this.showHeader = true,
    this.headerHeight,
    this.rowHeight,
    this.showBorders = true,
    this.theme,
    this.onRowTap,
    this.emptyWidget,
  }) : assert(!selectable || onSelectionChanged != null,
              'onSelectionChanged is required when selectable is true'),
       super(key: key);

  @override
  Widget buildWithTheme(BuildContext context, ThemeData materialTheme) {
    final tableTheme = theme ?? 
                      materialTheme.extension<ShadcnTableTheme>() ?? 
                      ShadcnTableTheme.defaultTheme(materialTheme.colorScheme);

    // Handle empty state
    if (data.isEmpty) {
      return _buildEmptyState(context, tableTheme);
    }

    // Sort data if needed
    final sortedData = _getSortedData();

    return Container(
      decoration: BoxDecoration(
        color: tableTheme.resolveBackgroundColor(context),
        borderRadius: tableTheme.borderRadius,
        border: showBorders ? Border.all(
          color: tableTheme.resolveBorderColor(context),
          width: tableTheme.borderWidth ?? 1.0,
        ) : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          if (showHeader) _buildHeader(context, tableTheme),
          Expanded(child: _buildBody(context, tableTheme, sortedData)),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, ShadcnTableTheme tableTheme) {
    return Container(
      padding: const EdgeInsets.all(32.0),
      child: emptyWidget ?? Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.table_chart_outlined,
              size: 64,
              color: tableTheme.resolveColor(
                context,
                null,
                (theme) => theme.colorScheme.onSurface.withOpacity(0.3),
                Colors.grey,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'No data available',
              style: tableTheme.resolveCellTextStyle(context).copyWith(
                color: tableTheme.resolveColor(
                  context,
                  null,
                  (theme) => theme.colorScheme.onSurface.withOpacity(0.6),
                  Colors.grey,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, ShadcnTableTheme tableTheme) {
    return Container(
      height: headerHeight ?? tableTheme.headerHeight ?? 52.0,
      decoration: BoxDecoration(
        color: tableTheme.resolveHeaderBackgroundColor(context),
        border: showBorders ? Border(
          bottom: BorderSide(
            color: tableTheme.resolveBorderColor(context),
            width: tableTheme.borderWidth ?? 1.0,
          ),
        ) : null,
      ),
      child: Row(
        children: [
          if (selectable) _buildSelectAllCell(context, tableTheme),
          ...columns.asMap().entries.map((entry) {
            final index = entry.key;
            final column = entry.value;
            return _buildHeaderCell(context, tableTheme, column, index);
          }),
        ],
      ),
    );
  }

  Widget _buildSelectAllCell(BuildContext context, ShadcnTableTheme tableTheme) {
    final isAllSelected = selectedRows.length == data.length && data.isNotEmpty;
    final isPartiallySelected = selectedRows.isNotEmpty && !isAllSelected;
    
    return SizedBox(
      width: 48,
      child: Center(
        child: Checkbox(
          value: isAllSelected,
          tristate: true,
          onChanged: (value) {
            if (isAllSelected || isPartiallySelected) {
              onSelectionChanged?.call({});
            } else {
              onSelectionChanged?.call(data.toSet());
            }
          },
        ),
      ),
    );
  }

  Widget _buildHeaderCell(
    BuildContext context,
    ShadcnTableTheme tableTheme,
    ShadcnTableColumn<T> column,
    int columnIndex,
  ) {
    final isCurrentSort = sort?.columnIndex == columnIndex;
    
    Widget headerContent = column.headerBuilder?.call(context) ?? Text(
      column.label,
      style: tableTheme.resolveHeaderTextStyle(context),
    );

    if (column.sortable) {
      headerContent = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Expanded(child: headerContent),
          _buildSortIndicator(context, tableTheme, isCurrentSort),
        ],
      );
    }

    return Expanded(
      flex: column.width?.toInt() ?? 1,
      child: InkWell(
        onTap: column.sortable ? () => _handleSort(columnIndex) : null,
        child: Container(
          padding: tableTheme.headerPadding ?? 
                   const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          alignment: column.alignment,
          child: headerContent,
        ),
      ),
    );
  }

  Widget _buildSortIndicator(
    BuildContext context,
    ShadcnTableTheme tableTheme,
    bool isCurrentSort,
  ) {
    if (!isCurrentSort) {
      return Icon(
        Icons.unfold_more,
        size: tableTheme.sortIndicatorSize ?? 16,
        color: tableTheme.sortIndicatorColor ??
               Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
      );
    }

    return Icon(
      sort!.ascending ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
      size: tableTheme.sortIndicatorSize ?? 16,
      color: tableTheme.sortIndicatorColor ??
             Theme.of(context).colorScheme.primary,
    );
  }

  Widget _buildBody(
    BuildContext context,
    ShadcnTableTheme tableTheme,
    List<T> sortedData,
  ) {
    return ListView.builder(
      itemCount: sortedData.length,
      itemBuilder: (context, index) {
        final item = sortedData[index];
        final isSelected = selectedRows.contains(item);
        final isStriped = striped && index.isOdd;
        
        return _buildRow(context, tableTheme, item, index, isSelected, isStriped);
      },
    );
  }

  Widget _buildRow(
    BuildContext context,
    ShadcnTableTheme tableTheme,
    T item,
    int index,
    bool isSelected,
    bool isStriped,
  ) {
    return InkWell(
      onTap: () {
        if (selectable) {
          final newSelection = Set<T>.from(selectedRows);
          if (isSelected) {
            newSelection.remove(item);
          } else {
            newSelection.add(item);
          }
          onSelectionChanged?.call(newSelection);
        }
        onRowTap?.call(item);
      },
      onHover: hoverable ? (hovering) => {} : null, // Enable hover effects
      child: Container(
        height: rowHeight ?? tableTheme.rowHeight ?? 48.0,
        decoration: BoxDecoration(
          color: _getRowBackgroundColor(context, tableTheme, isSelected, isStriped),
          border: showBorders && index > 0 ? Border(
            top: BorderSide(
              color: tableTheme.resolveBorderColor(context),
              width: tableTheme.borderWidth ?? 1.0,
            ),
          ) : null,
        ),
        child: Row(
          children: [
            if (selectable) _buildSelectCell(context, isSelected),
            ...columns.map((column) => _buildCell(context, tableTheme, column, item)),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectCell(BuildContext context, bool isSelected) {
    return SizedBox(
      width: 48,
      child: Center(
        child: Checkbox(
          value: isSelected,
          onChanged: (value) {
            // This is handled by row tap
          },
        ),
      ),
    );
  }

  Widget _buildCell(
    BuildContext context,
    ShadcnTableTheme tableTheme,
    ShadcnTableColumn<T> column,
    T item,
  ) {
    Widget cellContent = column.cellBuilder?.call(context, item) ?? Text(
      column.valueGetter(item),
      style: tableTheme.resolveCellTextStyle(context),
    );

    return Expanded(
      flex: column.width?.toInt() ?? 1,
      child: Container(
        padding: tableTheme.cellPadding ?? 
                 const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        alignment: column.alignment,
        child: cellContent,
      ),
    );
  }

  Color _getRowBackgroundColor(
    BuildContext context,
    ShadcnTableTheme tableTheme,
    bool isSelected,
    bool isStriped,
  ) {
    if (isSelected) {
      return tableTheme.resolveSelectedRowColor(context);
    }
    
    if (isStriped) {
      return tableTheme.alternatingRowColor ?? 
             tableTheme.resolveColor(
               context,
               null,
               (theme) => theme.colorScheme.surfaceVariant.withOpacity(0.1),
               Colors.grey.withOpacity(0.05),
             );
    }
    
    return tableTheme.cellBackgroundColor ?? 
           tableTheme.resolveBackgroundColor(context);
  }

  List<T> _getSortedData() {
    if (sort == null) return data;
    
    final column = columns[sort!.columnIndex];
    if (!column.sortable || column.sortComparator == null) return data;
    
    final sortedData = List<T>.from(data);
    sortedData.sort((a, b) {
      final result = column.sortComparator!(a, b);
      return sort!.ascending ? result : -result;
    });
    
    return sortedData;
  }

  void _handleSort(int columnIndex) {
    if (onSortChanged == null) return;
    
    ShadcnTableSort? newSort;
    
    if (sort?.columnIndex == columnIndex) {
      // Toggle sort direction or clear sort
      if (sort!.ascending) {
        newSort = sort!.copyWith(ascending: false);
      } else {
        newSort = null; // Clear sort
      }
    } else {
      // Set new column sort
      newSort = ShadcnTableSort(columnIndex: columnIndex, ascending: true);
    }
    
    onSortChanged!(newSort);
  }
}