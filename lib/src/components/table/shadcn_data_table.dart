import 'package:flutter/material.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_table_theme.dart';

/// A shadcn-styled data table that wraps Material's DataTable with custom theming.
/// 
/// This component provides a bridge between shadcn design system and Flutter's
/// built-in DataTable widget, applying consistent theming while maintaining
/// all the functionality of the Material DataTable.
class ShadcnDataTable extends ShadcnComponent {
  /// The data source for the table
  final List<DataRow> rows;
  
  /// Column definitions using Material's DataColumn
  final List<DataColumn> columns;
  
  /// Whether to show checkboxes for row selection
  final bool showCheckboxColumn;
  
  /// Current sort column index
  final int? sortColumnIndex;
  
  /// Whether sort is ascending
  final bool sortAscending;
  
  /// Callback when select all is triggered
  final void Function(bool?)? onSelectAll;
  
  /// Custom row height
  final double? dataRowHeight;
  
  /// Custom header height
  final double? headingRowHeight;
  
  /// Custom horizontal margin
  final double? horizontalMargin;
  
  /// Custom column spacing
  final double? columnSpacing;
  
  /// Whether rows show bottom divider
  final bool showBottomBorder;
  
  /// Custom theme override
  final ShadcnTableTheme? theme;
  
  /// Custom decoration for the data table
  final Decoration? decoration;
  
  /// Custom data table theme override
  final DataTableThemeData? dataTableTheme;
  
  /// Clip behavior for the table
  final Clip clipBehavior;

  const ShadcnDataTable({
    Key? key,
    required this.rows,
    required this.columns,
    this.showCheckboxColumn = true,
    this.sortColumnIndex,
    this.sortAscending = true,
    this.onSelectAll,
    this.dataRowHeight,
    this.headingRowHeight,
    this.horizontalMargin,
    this.columnSpacing,
    this.showBottomBorder = false,
    this.theme,
    this.decoration,
    this.dataTableTheme,
    this.clipBehavior = Clip.none,
  }) : super(key: key);

  @override
  Widget buildWithTheme(BuildContext context, ThemeData materialTheme) {
    final tableTheme = theme ?? 
                      materialTheme.extension<ShadcnTableTheme>() ?? 
                      ShadcnTableTheme.defaultTheme(materialTheme.colorScheme);

    // Create a custom DataTableTheme that applies shadcn styling
    final customDataTableTheme = _buildDataTableTheme(context, tableTheme, materialTheme);
    
    return Container(
      decoration: decoration ?? BoxDecoration(
        color: tableTheme.resolveBackgroundColor(context),
        borderRadius: tableTheme.borderRadius,
        border: Border.all(
          color: tableTheme.resolveBorderColor(context),
          width: tableTheme.borderWidth ?? 1.0,
        ),
      ),
      clipBehavior: clipBehavior,
      child: Theme(
        data: materialTheme.copyWith(
          dataTableTheme: customDataTableTheme,
        ),
        child: DataTable(
          columns: columns,
          rows: rows,
          sortColumnIndex: sortColumnIndex,
          sortAscending: sortAscending,
          onSelectAll: onSelectAll,
          dataRowHeight: dataRowHeight ?? tableTheme.rowHeight,
          headingRowHeight: headingRowHeight ?? tableTheme.headerHeight,
          horizontalMargin: horizontalMargin ?? 24.0,
          columnSpacing: columnSpacing ?? 56.0,
          showCheckboxColumn: showCheckboxColumn,
          showBottomBorder: showBottomBorder,
          clipBehavior: clipBehavior,
        ),
      ),
    );
  }

  DataTableThemeData _buildDataTableTheme(
    BuildContext context,
    ShadcnTableTheme tableTheme,
    ThemeData materialTheme,
  ) {
    final baseDataTableTheme = dataTableTheme ?? materialTheme.dataTableTheme;
    
    return DataTableThemeData(
      // Header styling
      headingRowColor: MaterialStateProperty.all(
        tableTheme.resolveHeaderBackgroundColor(context),
      ),
      headingTextStyle: tableTheme.resolveHeaderTextStyle(context),
      
      // Data row styling
      dataRowColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return tableTheme.resolveSelectedRowColor(context);
        }
        if (states.contains(MaterialState.hovered)) {
          return tableTheme.resolveHoverRowColor(context);
        }
        return tableTheme.cellBackgroundColor ?? 
               tableTheme.resolveBackgroundColor(context);
      }),
      dataTextStyle: tableTheme.resolveCellTextStyle(context),
      
      // Divider styling
      dividerThickness: tableTheme.borderWidth ?? 1.0,
      
      // Checkbox styling (if applicable)
      checkboxHorizontalMargin: 0.0,
      
      // Column spacing
      columnSpacing: columnSpacing,
      
      // Row height
      dataRowHeight: dataRowHeight,
      headingRowHeight: headingRowHeight,
      
      // Border and decoration
      decoration: BoxDecoration(
        border: Border.all(
          color: tableTheme.resolveBorderColor(context),
          width: tableTheme.borderWidth ?? 1.0,
        ),
      ),
    );
  }
}

/// A convenience widget for creating DataColumn with shadcn styling
class ShadcnDataColumn extends DataColumn {
  ShadcnDataColumn({
    required Widget label,
    String? tooltip,
    bool numeric = false,
    void Function(int, bool)? onSort,
  }) : super(
         label: label,
         tooltip: tooltip,
         numeric: numeric,
         onSort: onSort,
       );

  /// Creates a text-based data column with consistent styling
  factory ShadcnDataColumn.text(
    String text, {
    String? tooltip,
    bool numeric = false,
    void Function(int, bool)? onSort,
    TextStyle? style,
  }) {
    return ShadcnDataColumn(
      label: Text(
        text,
        style: style,
      ),
      tooltip: tooltip ?? text,
      numeric: numeric,
      onSort: onSort,
    );
  }
  
  /// Creates a sortable data column with sort indicator
  factory ShadcnDataColumn.sortable(
    String text, {
    required void Function(int, bool) onSort,
    String? tooltip,
    bool numeric = false,
    TextStyle? style,
  }) {
    return ShadcnDataColumn(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(text, style: style),
          const SizedBox(width: 4),
          const Icon(Icons.unfold_more, size: 16),
        ],
      ),
      tooltip: tooltip ?? text,
      numeric: numeric,
      onSort: onSort,
    );
  }
}

/// A convenience widget for creating DataRow with shadcn styling
class ShadcnDataRow extends DataRow {
  ShadcnDataRow({
    LocalKey? key,
    bool selected = false,
    void Function(bool?)? onSelectChanged,
    MaterialStateProperty<Color?>? color,
    required List<DataCell> cells,
  }) : super(
         key: key,
         selected: selected,
         onSelectChanged: onSelectChanged,
         color: color,
         cells: cells,
          );

  /// Creates a data row with consistent cell styling
  factory ShadcnDataRow.fromValues(
    List<String> values, {
    LocalKey? key,
    bool selected = false,
    void Function(bool?)? onSelectChanged,
    MaterialStateProperty<Color?>? color,
    TextStyle? cellStyle,
    void Function()? onTap,
  }) {
    return ShadcnDataRow(
      key: key,
      selected: selected,
      onSelectChanged: onSelectChanged,
      color: color,
      cells: values.map((value) => DataCell(
        Text(value, style: cellStyle),
        onTap: onTap,
      )).toList(),
    );
  }
  
  /// Creates a data row with custom cell builders
  factory ShadcnDataRow.custom(
    List<Widget> cellWidgets, {
    LocalKey? key,
    bool selected = false,
    void Function(bool?)? onSelectChanged,
    MaterialStateProperty<Color?>? color,
    List<void Function()?> onTapCallbacks = const [],
  }) {
    return ShadcnDataRow(
      key: key,
      selected: selected,
      onSelectChanged: onSelectChanged,
      color: color,
      cells: cellWidgets.asMap().entries.map((entry) {
        final index = entry.key;
        final widget = entry.value;
        final onTap = index < onTapCallbacks.length ? onTapCallbacks[index] : null;
        
        return DataCell(widget, onTap: onTap);
      }).toList(),
    );
  }
}

/// A data cell with consistent shadcn styling
class ShadcnDataCell extends DataCell {
  ShadcnDataCell(
    Widget child, {
    bool placeholder = false,
    bool showEditIcon = false,
    void Function()? onTap,
    void Function()? onLongPress,
    void Function(TapDownDetails)? onTapDown,
    void Function()? onDoubleTap,
    void Function()? onTapCancel,
  }) : super(
         child,
         placeholder: placeholder,
         showEditIcon: showEditIcon,
         onTap: onTap,
         onLongPress: onLongPress,
         onTapDown: onTapDown,
         onDoubleTap: onDoubleTap,
         onTapCancel: onTapCancel,
          );

  /// Creates a text data cell with consistent styling
  factory ShadcnDataCell.text(
    String text, {
    TextStyle? style,
    bool placeholder = false,
    bool showEditIcon = false,
    void Function()? onTap,
    void Function()? onLongPress,
    void Function(TapDownDetails)? onTapDown,
    void Function()? onDoubleTap,
    void Function()? onTapCancel,
  }) {
    return ShadcnDataCell(
      Text(
        text,
        style: style,
      ),
      placeholder: placeholder,
      showEditIcon: showEditIcon,
      onTap: onTap,
      onLongPress: onLongPress,
      onTapDown: onTapDown,
      onDoubleTap: onDoubleTap,
      onTapCancel: onTapCancel,
    );
  }
  
  /// Creates a numeric data cell with right alignment
  factory ShadcnDataCell.numeric(
    String value, {
    TextStyle? style,
    bool placeholder = false,
    bool showEditIcon = false,
    void Function()? onTap,
    void Function()? onLongPress,
    void Function(TapDownDetails)? onTapDown,
    void Function()? onDoubleTap,
    void Function()? onTapCancel,
  }) {
    return ShadcnDataCell(
      Align(
        alignment: Alignment.centerRight,
        child: Text(
          value,
          style: style,
          textAlign: TextAlign.right,
        ),
      ),
      placeholder: placeholder,
      showEditIcon: showEditIcon,
      onTap: onTap,
      onLongPress: onLongPress,
      onTapDown: onTapDown,
      onDoubleTap: onDoubleTap,
      onTapCancel: onTapCancel,
    );
  }
  
  /// Creates an action data cell with button styling
  factory ShadcnDataCell.action(
    Widget actionWidget, {
    bool showEditIcon = false,
    void Function()? onTap,
    void Function()? onLongPress,
    void Function(TapDownDetails)? onTapDown,
    void Function()? onDoubleTap,
    void Function()? onTapCancel,
  }) {
    return ShadcnDataCell(
      Center(child: actionWidget),
      showEditIcon: showEditIcon,
      onTap: onTap,
      onLongPress: onLongPress,
      onTapDown: onTapDown,
      onDoubleTap: onDoubleTap,
      onTapCancel: onTapCancel,
    );
  }
}