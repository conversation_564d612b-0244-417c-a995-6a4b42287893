import 'package:flutter/material.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_aspect_ratio_theme.dart';
import '../../constants/shadcn_tokens.dart';

/// A shadcn-styled aspect ratio container.
/// 
/// The [ShadcnAspectRatio] widget constrains its child to a specific aspect
/// ratio while providing theme-aware styling options like background colors,
/// borders, and shadows. It integrates with Material Design theming while
/// offering shadcn-specific customization.
/// 
/// The aspect ratio is maintained regardless of the available space, making
/// it ideal for media content, cards, and responsive layouts that need to
/// maintain proportional dimensions.
/// 
/// Example usage:
/// ```dart
/// // Basic square aspect ratio
/// ShadcnAspectRatio(
///   aspectRatio: 1.0,
///   child: Image.network('https://example.com/image.jpg'),
/// )
/// 
/// // Widescreen with border and background
/// ShadcnAspectRatio(
///   aspectRatio: 16 / 9,
///   backgroundColor: Colors.grey.shade100,
///   borderColor: Colors.grey.shade300,
///   child: VideoPlayer(controller),
/// )
/// 
/// // Using predefined aspect ratios
/// ShadcnAspectRatio(
///   aspectRatio: ShadcnAspectRatios.landscape,
///   borderRadius: BorderRadius.circular(12),
///   child: PhotoGallery(),
/// )
/// ```
class ShadcnAspectRatio extends ShadcnComponent with ShadcnComponentValidation {
  /// The aspect ratio to maintain (width / height).
  /// 
  /// Must be positive. Common values include 1.0 (square), 16/9 (widescreen),
  /// or 4/3 (standard). Use [ShadcnAspectRatios] for predefined ratios.
  final double aspectRatio;
  
  /// The child widget to display within the aspect ratio container.
  /// 
  /// The child will be sized to fit within the aspect ratio constraints
  /// and positioned according to the alignment property.
  final Widget child;
  
  /// Custom background color for the container.
  /// 
  /// If null, uses the background color from the theme. Set to
  /// Colors.transparent for no background.
  final Color? backgroundColor;
  
  /// Custom border color for the container.
  /// 
  /// If null, uses the border color from the theme. Set to null
  /// to remove the border entirely.
  final Color? borderColor;
  
  /// Custom border width for the container.
  /// 
  /// If null, uses the border width from the theme. Only applied
  /// when borderColor is not null.
  final double? borderWidth;
  
  /// Custom border radius for the container.
  /// 
  /// If null, uses the border radius from the theme.
  final BorderRadius? borderRadius;
  
  /// Custom padding inside the container.
  /// 
  /// If null, uses the padding from the theme.
  final EdgeInsets? padding;
  
  /// Custom margin outside the container.
  /// 
  /// If null, uses the margin from the theme.
  final EdgeInsets? margin;
  
  /// Custom box shadows for the container.
  /// 
  /// If null, uses the box shadow from the theme. This is ignored
  /// if useMaterialElevation is true.
  final List<BoxShadow>? boxShadow;
  
  /// Custom clip behavior for the container.
  /// 
  /// If null, uses the clip behavior from the theme.
  final Clip? clipBehavior;
  
  /// Whether to use Material elevation instead of box shadows.
  /// 
  /// When true, the container uses Material elevation for depth,
  /// ignoring any custom box shadows.
  final bool? useMaterialElevation;
  
  /// Material elevation value (if useMaterialElevation is true).
  /// 
  /// If null, uses the elevation from the theme.
  final double? elevation;
  
  /// Alignment of the child within the container.
  /// 
  /// If null, uses the alignment from the theme.
  final AlignmentGeometry? alignment;
  
  /// Optional callback for tap interactions.
  /// 
  /// When provided, makes the aspect ratio container interactive.
  final VoidCallback? onTap;
  
  /// Optional callback for long press interactions.
  final VoidCallback? onLongPress;
  
  /// Semantic label for accessibility.
  /// 
  /// Provides a description of the container's content for screen readers.
  final String? semanticLabel;
  
  /// Whether to exclude this container from the semantic tree.
  /// 
  /// Decorative containers that don't convey meaningful content
  /// can be excluded from accessibility tools.
  final bool excludeFromSemantics;
  
  /// Mouse cursor when hovering over the container.
  /// 
  /// Only applied when onTap is provided.
  final MouseCursor? mouseCursor;
  
  /// Creates a shadcn aspect ratio container.
  /// 
  /// The [aspectRatio] and [child] parameters are required. The aspect ratio
  /// must be positive and represents the width-to-height ratio.
  const ShadcnAspectRatio({
    super.key,
    required this.aspectRatio,
    required this.child,
    this.backgroundColor,
    this.borderColor,
    this.borderWidth,
    this.borderRadius,
    this.padding,
    this.margin,
    this.boxShadow,
    this.clipBehavior,
    this.useMaterialElevation,
    this.elevation,
    this.alignment,
    this.onTap,
    this.onLongPress,
    this.semanticLabel,
    this.excludeFromSemantics = false,
    this.mouseCursor,
  }) : assert(aspectRatio > 0, 'Aspect ratio must be positive');
  
  /// Creates a square aspect ratio container.
  /// 
  /// This is a convenience constructor for creating square containers
  /// with a 1:1 aspect ratio.
  const ShadcnAspectRatio.square({
    Key? key,
    required Widget child,
    Color? backgroundColor,
    Color? borderColor,
    double? borderWidth,
    BorderRadius? borderRadius,
    EdgeInsets? padding,
    EdgeInsets? margin,
    List<BoxShadow>? boxShadow,
    Clip? clipBehavior,
    bool? useMaterialElevation,
    double? elevation,
    AlignmentGeometry? alignment,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    MouseCursor? mouseCursor,
  }) : this(
    key: key,
    aspectRatio: ShadcnAspectRatios.square,
    child: child,
    backgroundColor: backgroundColor,
    borderColor: borderColor,
    borderWidth: borderWidth,
    borderRadius: borderRadius,
    padding: padding,
    margin: margin,
    boxShadow: boxShadow,
    clipBehavior: clipBehavior,
    useMaterialElevation: useMaterialElevation,
    elevation: elevation,
    alignment: alignment,
    onTap: onTap,
    onLongPress: onLongPress,
    semanticLabel: semanticLabel,
    excludeFromSemantics: excludeFromSemantics,
    mouseCursor: mouseCursor,
  );

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    // Validate component configuration in debug mode
    assert(() {
      validateThemeProperties(context);
      validateRequiredProperties({
        'aspectRatio': aspectRatio,
        'child': child,
      });
      validateAccessibility(
        semanticLabel: semanticLabel,
        excludeFromSemantics: excludeFromSemantics,
        componentName: 'ShadcnAspectRatio',
      );
      return true;
    }());

    // Resolve theme
    final aspectRatioTheme = resolveTheme<ShadcnAspectRatioTheme>(
      context,
      ShadcnAspectRatioTheme.defaultTheme,
    );

    // Resolve all styling properties
    final resolvedBackgroundColor = backgroundColor ?? aspectRatioTheme.getBackgroundColor(context);
    final resolvedBorderColor = borderColor ?? aspectRatioTheme.getBorderColor(context);
    final resolvedBorderWidth = borderWidth ?? aspectRatioTheme.getBorderWidth(context);
    final resolvedBorderRadius = borderRadius ?? aspectRatioTheme.getBorderRadius(context);
    final resolvedPadding = padding ?? aspectRatioTheme.getPadding(context);
    final resolvedMargin = margin ?? aspectRatioTheme.getMargin(context);
    final resolvedClipBehavior = clipBehavior ?? aspectRatioTheme.getClipBehavior(context);
    final resolvedUseMaterialElevation = useMaterialElevation ?? aspectRatioTheme.getUseMaterialElevation(context);
    final resolvedElevation = elevation ?? aspectRatioTheme.getElevation(context);
    final resolvedAlignment = alignment ?? aspectRatioTheme.getAlignment(context);
    final resolvedBoxShadow = boxShadow ?? aspectRatioTheme.getBoxShadow(context);
    
    // Build the aspect ratio container
    Widget container = AspectRatio(
      aspectRatio: aspectRatio,
      child: _buildContainer(
        context,
        resolvedBackgroundColor,
        resolvedBorderColor,
        resolvedBorderWidth,
        resolvedBorderRadius,
        resolvedPadding,
        resolvedClipBehavior,
        resolvedUseMaterialElevation,
        resolvedElevation,
        resolvedAlignment,
        resolvedBoxShadow,
      ),
    );

    // Apply margin if specified
    if (resolvedMargin != EdgeInsets.zero) {
      container = Padding(
        padding: resolvedMargin,
        child: container,
      );
    }

    // Add interaction handling if callbacks are provided
    if (onTap != null || onLongPress != null) {
      container = GestureDetector(
        onTap: onTap,
        onLongPress: onLongPress,
        behavior: HitTestBehavior.opaque,
        child: MouseRegion(
          cursor: mouseCursor ?? SystemMouseCursors.click,
          child: container,
        ),
      );
    }

    // Add semantics for accessibility
    if (!excludeFromSemantics) {
      container = Semantics(
        label: semanticLabel,
        container: true,
        child: container,
      );
    }

    return container;
  }

  /// Builds the styled container that wraps the child.
  /// 
  /// This method handles the visual construction of the container with
  /// all the resolved styling properties.
  Widget _buildContainer(
    BuildContext context,
    Color? backgroundColor,
    Color? borderColor,
    double borderWidth,
    BorderRadius borderRadius,
    EdgeInsets padding,
    Clip clipBehavior,
    bool useMaterialElevation,
    double elevation,
    AlignmentGeometry alignment,
    List<BoxShadow>? boxShadow,
  ) {
    if (useMaterialElevation) {
      return Material(
        color: backgroundColor ?? Colors.transparent,
        elevation: elevation,
        borderRadius: borderRadius,
        clipBehavior: clipBehavior,
        child: _buildChildContainer(
          context,
          borderColor,
          borderWidth,
          borderRadius,
          padding,
          alignment,
        ),
      );
    } else {
      return Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          border: borderColor != null && borderWidth > 0
              ? Border.all(color: borderColor, width: borderWidth)
              : null,
          borderRadius: borderRadius,
          boxShadow: boxShadow,
        ),
        clipBehavior: clipBehavior,
        alignment: alignment,
        padding: padding,
        child: child,
      );
    }
  }

  /// Builds the child container when using Material elevation.
  /// 
  /// This method creates the inner container with border and padding
  /// when Material elevation is being used.
  Widget _buildChildContainer(
    BuildContext context,
    Color? borderColor,
    double borderWidth,
    BorderRadius borderRadius,
    EdgeInsets padding,
    AlignmentGeometry alignment,
  ) {
    if (borderColor != null && borderWidth > 0) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: borderColor, width: borderWidth),
          borderRadius: borderRadius,
        ),
        alignment: alignment,
        padding: padding,
        child: child,
      );
    } else {
      return Container(
        alignment: alignment,
        padding: padding,
        child: child,
      );
    }
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ShadcnAspectRatio('
        'aspectRatio: $aspectRatio, '
        'backgroundColor: $backgroundColor, '
        'borderColor: $borderColor, '
        'borderRadius: $borderRadius, '
        'padding: $padding, '
        'margin: $margin, '
        'elevation: $elevation, '
        'excludeFromSemantics: $excludeFromSemantics'
        ')';
  }
}

/// Helper class for creating commonly used aspect ratio configurations.
/// 
/// Provides pre-configured aspect ratio widgets for common use cases,
/// reducing boilerplate and ensuring consistent styling patterns.
class ShadcnAspectRatioHelpers {
  ShadcnAspectRatioHelpers._();

  /// Creates an image container with aspect ratio.
  /// 
  /// Optimized for displaying images with consistent aspect ratios
  /// and optional loading states.
  static Widget image({
    Key? key,
    required double aspectRatio,
    required Widget child,
    Color? backgroundColor,
    BorderRadius? borderRadius,
    bool showLoadingPlaceholder = true,
    Widget? loadingPlaceholder,
    BoxFit? fit,
  }) {
    Widget content = child;
    
    if (child is Image && fit != null) {
      // Wrap Image with proper BoxFit
      content = ClipRRect(
        borderRadius: borderRadius ?? BorderRadius.zero,
        child: SizedBox.expand(
          child: FittedBox(
            fit: fit,
            child: child,
          ),
        ),
      );
    }

    return ShadcnAspectRatio(
      key: key,
      aspectRatio: aspectRatio,
      backgroundColor: backgroundColor ?? Colors.grey.shade100,
      borderRadius: borderRadius ?? BorderRadius.circular(ShadcnTokens.radiusMd),
      clipBehavior: Clip.antiAlias,
      child: content,
    );
  }

  /// Creates a video container with aspect ratio.
  /// 
  /// Optimized for video content with standard video aspect ratios
  /// and player controls overlay support.
  static Widget video({
    Key? key,
    double aspectRatio = ShadcnAspectRatios.widescreen,
    required Widget child,
    Widget? overlay,
    Color? backgroundColor,
    BorderRadius? borderRadius,
    bool showControls = true,
  }) {
    Widget content = child;
    
    if (overlay != null) {
      content = Stack(
        fit: StackFit.expand,
        children: [
          child,
          if (showControls) overlay,
        ],
      );
    }

    return ShadcnAspectRatio(
      key: key,
      aspectRatio: aspectRatio,
      backgroundColor: backgroundColor ?? Colors.black,
      borderRadius: borderRadius ?? BorderRadius.circular(ShadcnTokens.radiusMd),
      clipBehavior: Clip.antiAlias,
      child: content,
    );
  }

  /// Creates a card container with aspect ratio.
  /// 
  /// Provides card-style styling with elevation and borders,
  /// suitable for content cards and tiles.
  static Widget card({
    Key? key,
    double aspectRatio = ShadcnAspectRatios.landscape,
    required Widget child,
    Color? backgroundColor,
    Color? borderColor,
    EdgeInsets? padding,
    double? elevation,
    VoidCallback? onTap,
  }) {
    return ShadcnAspectRatio(
      key: key,
      aspectRatio: aspectRatio,
      backgroundColor: backgroundColor,
      borderColor: borderColor,
      padding: padding ?? ShadcnTokens.paddingAll(ShadcnTokens.spacing4),
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusLg),
      useMaterialElevation: true,
      elevation: elevation ?? ShadcnTokens.elevationSm,
      onTap: onTap,
      child: child,
    );
  }

  /// Creates a placeholder container with aspect ratio.
  /// 
  /// Useful for loading states or empty content areas with
  /// skeleton-style appearance.
  static Widget placeholder({
    Key? key,
    double aspectRatio = ShadcnAspectRatios.landscape,
    Color? color,
    Widget? icon,
    String? text,
    TextStyle? textStyle,
  }) {
    return ShadcnAspectRatio(
      key: key,
      aspectRatio: aspectRatio,
      backgroundColor: color ?? Colors.grey.shade200,
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusMd),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) icon,
            if (icon != null && text != null) 
              const SizedBox(height: ShadcnTokens.spacing2),
            if (text != null)
              Text(
                text,
                style: textStyle ?? TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: ShadcnTokens.fontSizeSm,
                ),
                textAlign: TextAlign.center,
              ),
          ],
        ),
      ),
    );
  }

  /// Creates a responsive aspect ratio container.
  /// 
  /// Automatically adjusts aspect ratio based on screen size for
  /// better responsive behavior.
  static Widget responsive({
    Key? key,
    required Widget child,
    double mobileAspectRatio = ShadcnAspectRatios.portrait,
    double tabletAspectRatio = ShadcnAspectRatios.landscape,
    double desktopAspectRatio = ShadcnAspectRatios.widescreen,
    double mobileBreakpoint = 600,
    double tabletBreakpoint = 1024,
    Color? backgroundColor,
    BorderRadius? borderRadius,
    EdgeInsets? padding,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        double aspectRatio;
        
        if (width >= tabletBreakpoint) {
          aspectRatio = desktopAspectRatio;
        } else if (width >= mobileBreakpoint) {
          aspectRatio = tabletAspectRatio;
        } else {
          aspectRatio = mobileAspectRatio;
        }
        
        return ShadcnAspectRatio(
          key: key,
          aspectRatio: aspectRatio,
          backgroundColor: backgroundColor,
          borderRadius: borderRadius,
          padding: padding,
          child: child,
        );
      },
    );
  }
}

/// Extension methods for convenient aspect ratio usage.
/// 
/// Provides extension methods on Widget to wrap them in aspect ratio
/// containers with minimal syntax.
extension ShadcnAspectRatioExtensions on Widget {
  /// Wraps this widget in an aspect ratio container.
  /// 
  /// Provides a fluent interface for applying aspect ratio constraints
  /// to any widget.
  Widget withAspectRatio(
    double aspectRatio, {
    Color? backgroundColor,
    Color? borderColor,
    BorderRadius? borderRadius,
    EdgeInsets? padding,
    EdgeInsets? margin,
    AlignmentGeometry? alignment,
    VoidCallback? onTap,
  }) {
    return ShadcnAspectRatio(
      aspectRatio: aspectRatio,
      backgroundColor: backgroundColor,
      borderColor: borderColor,
      borderRadius: borderRadius,
      padding: padding,
      margin: margin,
      alignment: alignment,
      onTap: onTap,
      child: this,
    );
  }

  /// Wraps this widget in a square aspect ratio container.
  /// 
  /// Convenience method for creating square containers.
  Widget asSquare({
    Color? backgroundColor,
    Color? borderColor,
    BorderRadius? borderRadius,
    EdgeInsets? padding,
    EdgeInsets? margin,
    AlignmentGeometry? alignment,
    VoidCallback? onTap,
  }) {
    return withAspectRatio(
      ShadcnAspectRatios.square,
      backgroundColor: backgroundColor,
      borderColor: borderColor,
      borderRadius: borderRadius,
      padding: padding,
      margin: margin,
      alignment: alignment,
      onTap: onTap,
    );
  }

  /// Wraps this widget in a landscape aspect ratio container.
  /// 
  /// Convenience method for creating landscape containers.
  Widget asLandscape({
    Color? backgroundColor,
    Color? borderColor,
    BorderRadius? borderRadius,
    EdgeInsets? padding,
    EdgeInsets? margin,
    AlignmentGeometry? alignment,
    VoidCallback? onTap,
  }) {
    return withAspectRatio(
      ShadcnAspectRatios.landscape,
      backgroundColor: backgroundColor,
      borderColor: borderColor,
      borderRadius: borderRadius,
      padding: padding,
      margin: margin,
      alignment: alignment,
      onTap: onTap,
    );
  }

  /// Wraps this widget in a widescreen aspect ratio container.
  /// 
  /// Convenience method for creating widescreen containers.
  Widget asWidescreen({
    Color? backgroundColor,
    Color? borderColor,
    BorderRadius? borderRadius,
    EdgeInsets? padding,
    EdgeInsets? margin,
    AlignmentGeometry? alignment,
    VoidCallback? onTap,
  }) {
    return withAspectRatio(
      ShadcnAspectRatios.widescreen,
      backgroundColor: backgroundColor,
      borderColor: borderColor,
      borderRadius: borderRadius,
      padding: padding,
      margin: margin,
      alignment: alignment,
      onTap: onTap,
    );
  }
}