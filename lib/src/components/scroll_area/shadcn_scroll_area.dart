import 'package:flutter/material.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_scroll_area_theme.dart';
import '../../constants/shadcn_tokens.dart';

/// A shadcn-styled scrollable area with custom scrollbar styling.
/// 
/// The [ShadcnScrollArea] provides a scrollable container with enhanced
/// scrollbar customization beyond what's available in standard Material
/// scrollbars. It integrates with the Material Design theme system while
/// offering shadcn-specific styling options.
/// 
/// Features include:
/// - Custom scrollbar colors and dimensions
/// - Hover and active state styling
/// - Fade in/out animations
/// - Track and thumb customization
/// - Interactive scrollbar behavior
/// - Theme-aware styling
/// 
/// Example usage:
/// ```dart
/// // Basic scrollable area
/// ShadcnScrollArea(
///   height: 300,
///   child: Column(
///     children: List.generate(50, (i) => ListTile(title: Text('Item $i'))),
///   ),
/// )
/// 
/// // Custom scrollbar styling
/// ShadcnScrollArea(
///   height: 400,
///   scrollbarAlwaysVisible: true,
///   scrollbarThumbColor: Colors.blue,
///   scrollbarShowTrack: true,
///   child: longContentWidget,
/// )
/// 
/// // Horizontal scrolling
/// ShadcnScrollArea.horizontal(
///   width: double.infinity,
///   child: Row(
///     children: horizontalItems,
///   ),
/// )
/// ```
class ShadcnScrollArea extends ShadcnComponent with ShadcnComponentValidation {
  /// The child widget to make scrollable.
  final Widget child;
  
  /// Fixed height for the scroll area.
  /// 
  /// If null, the scroll area will expand to fit its parent's constraints.
  /// For vertical scrolling, this constrains the viewport height.
  final double? height;
  
  /// Fixed width for the scroll area.
  /// 
  /// If null, the scroll area will expand to fit its parent's constraints.
  /// For horizontal scrolling, this constrains the viewport width.
  final double? width;
  
  /// Maximum height constraint for the scroll area.
  /// 
  /// The scroll area will not exceed this height even if the parent
  /// provides more space.
  final double? maxHeight;
  
  /// Maximum width constraint for the scroll area.
  /// 
  /// The scroll area will not exceed this width even if the parent
  /// provides more space.
  final double? maxWidth;
  
  /// Scroll controller for the scroll area.
  /// 
  /// If null, a default controller will be created automatically.
  final ScrollController? controller;
  
  /// Scroll direction (vertical or horizontal).
  /// 
  /// Defaults to vertical scrolling.
  final Axis scrollDirection;
  
  /// Whether reverse scrolling is enabled.
  /// 
  /// When true, the scroll area starts at the maximum scroll extent
  /// and scrolls towards zero.
  final bool reverse;
  
  /// Custom scroll physics for the scroll area.
  /// 
  /// If null, uses the platform default physics.
  final ScrollPhysics? physics;
  
  /// Custom background color for the scroll area container.
  final Color? backgroundColor;
  
  /// Custom border color for the scroll area container.
  final Color? borderColor;
  
  /// Custom border width for the scroll area container.
  final double? borderWidth;
  
  /// Custom border radius for the scroll area container.
  final BorderRadius? borderRadius;
  
  /// Custom padding inside the scroll area container.
  final EdgeInsets? padding;
  
  /// Custom margin outside the scroll area container.
  final EdgeInsets? margin;
  
  /// Custom scrollbar track color.
  final Color? scrollbarTrackColor;
  
  /// Custom scrollbar thumb color.
  final Color? scrollbarThumbColor;
  
  /// Custom scrollbar thumb color when hovered.
  final Color? scrollbarThumbHoverColor;
  
  /// Custom scrollbar thumb color when active/dragged.
  final Color? scrollbarThumbActiveColor;
  
  /// Custom scrollbar thickness.
  final double? scrollbarThickness;
  
  /// Custom scrollbar thumb border radius.
  final BorderRadius? scrollbarThumbRadius;
  
  /// Custom scrollbar track border radius.
  final BorderRadius? scrollbarTrackRadius;
  
  /// Custom minimum height for the scrollbar thumb.
  final double? scrollbarThumbMinHeight;
  
  /// Whether the scrollbar should always be visible.
  /// 
  /// When false, the scrollbar fades in/out based on scroll activity.
  final bool? scrollbarAlwaysVisible;
  
  /// Whether the scrollbar should show the track.
  final bool? scrollbarShowTrack;
  
  /// Duration for scrollbar fade animations.
  final Duration? scrollbarFadeDuration;
  
  /// Margin around the scrollbar.
  final EdgeInsets? scrollbarMargin;
  
  /// Whether the scrollbar should be interactive (clickable).
  final bool? scrollbarInteractive;
  
  /// Minimum thumb extent as a fraction of the viewport.
  final double? scrollbarMinThumbExtent;
  
  /// Semantic label for accessibility.
  final String? semanticLabel;
  
  /// Whether to exclude from the semantic tree.
  final bool excludeFromSemantics;
  
  /// Creates a shadcn scroll area.
  /// 
  /// The [child] parameter is required. All other parameters are optional
  /// and will fall back to theme values when not specified.
  const ShadcnScrollArea({
    super.key,
    required this.child,
    this.height,
    this.width,
    this.maxHeight,
    this.maxWidth,
    this.controller,
    this.scrollDirection = Axis.vertical,
    this.reverse = false,
    this.physics,
    this.backgroundColor,
    this.borderColor,
    this.borderWidth,
    this.borderRadius,
    this.padding,
    this.margin,
    this.scrollbarTrackColor,
    this.scrollbarThumbColor,
    this.scrollbarThumbHoverColor,
    this.scrollbarThumbActiveColor,
    this.scrollbarThickness,
    this.scrollbarThumbRadius,
    this.scrollbarTrackRadius,
    this.scrollbarThumbMinHeight,
    this.scrollbarAlwaysVisible,
    this.scrollbarShowTrack,
    this.scrollbarFadeDuration,
    this.scrollbarMargin,
    this.scrollbarInteractive,
    this.scrollbarMinThumbExtent,
    this.semanticLabel,
    this.excludeFromSemantics = false,
  });
  
  /// Creates a horizontal scroll area.
  /// 
  /// This is a convenience constructor for creating horizontal scrolling areas.
  const ShadcnScrollArea.horizontal({
    Key? key,
    required Widget child,
    double? height,
    double? width,
    double? maxHeight,
    double? maxWidth,
    ScrollController? controller,
    bool reverse = false,
    ScrollPhysics? physics,
    Color? backgroundColor,
    Color? borderColor,
    double? borderWidth,
    BorderRadius? borderRadius,
    EdgeInsets? padding,
    EdgeInsets? margin,
    Color? scrollbarTrackColor,
    Color? scrollbarThumbColor,
    Color? scrollbarThumbHoverColor,
    Color? scrollbarThumbActiveColor,
    double? scrollbarThickness,
    BorderRadius? scrollbarThumbRadius,
    BorderRadius? scrollbarTrackRadius,
    double? scrollbarThumbMinHeight,
    bool? scrollbarAlwaysVisible,
    bool? scrollbarShowTrack,
    Duration? scrollbarFadeDuration,
    EdgeInsets? scrollbarMargin,
    bool? scrollbarInteractive,
    double? scrollbarMinThumbExtent,
    String? semanticLabel,
    bool excludeFromSemantics = false,
  }) : this(
    key: key,
    child: child,
    height: height,
    width: width,
    maxHeight: maxHeight,
    maxWidth: maxWidth,
    controller: controller,
    scrollDirection: Axis.horizontal,
    reverse: reverse,
    physics: physics,
    backgroundColor: backgroundColor,
    borderColor: borderColor,
    borderWidth: borderWidth,
    borderRadius: borderRadius,
    padding: padding,
    margin: margin,
    scrollbarTrackColor: scrollbarTrackColor,
    scrollbarThumbColor: scrollbarThumbColor,
    scrollbarThumbHoverColor: scrollbarThumbHoverColor,
    scrollbarThumbActiveColor: scrollbarThumbActiveColor,
    scrollbarThickness: scrollbarThickness,
    scrollbarThumbRadius: scrollbarThumbRadius,
    scrollbarTrackRadius: scrollbarTrackRadius,
    scrollbarThumbMinHeight: scrollbarThumbMinHeight,
    scrollbarAlwaysVisible: scrollbarAlwaysVisible,
    scrollbarShowTrack: scrollbarShowTrack,
    scrollbarFadeDuration: scrollbarFadeDuration,
    scrollbarMargin: scrollbarMargin,
    scrollbarInteractive: scrollbarInteractive,
    scrollbarMinThumbExtent: scrollbarMinThumbExtent,
    semanticLabel: semanticLabel,
    excludeFromSemantics: excludeFromSemantics,
  );

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    // Validate component configuration in debug mode
    assert(() {
      validateThemeProperties(context);
      validateRequiredProperties({
        'child': child,
      });
      validateAccessibility(
        semanticLabel: semanticLabel,
        excludeFromSemantics: excludeFromSemantics,
        componentName: 'ShadcnScrollArea',
      );
      return true;
    }());

    // Resolve theme
    final scrollAreaTheme = resolveTheme<ShadcnScrollAreaTheme>(
      context,
      ShadcnScrollAreaTheme.defaultTheme,
    );

    // Build the scroll area
    Widget scrollArea = _buildScrollArea(context, theme, scrollAreaTheme);

    // Apply container styling if needed
    final resolvedBackgroundColor = backgroundColor ?? scrollAreaTheme.getBackgroundColor(context);
    final resolvedBorderColor = borderColor ?? scrollAreaTheme.getBorderColor(context);
    final resolvedBorderWidth = borderWidth ?? scrollAreaTheme.getBorderWidth(context);
    final resolvedBorderRadius = borderRadius ?? scrollAreaTheme.getBorderRadius(context);
    final resolvedMargin = margin ?? scrollAreaTheme.getMargin(context);

    if (resolvedBackgroundColor != null || 
        (resolvedBorderColor != null && resolvedBorderWidth > 0) ||
        resolvedBorderRadius != BorderRadius.zero) {
      scrollArea = Container(
        decoration: BoxDecoration(
          color: resolvedBackgroundColor,
          border: resolvedBorderColor != null && resolvedBorderWidth > 0
              ? Border.all(color: resolvedBorderColor, width: resolvedBorderWidth)
              : null,
          borderRadius: resolvedBorderRadius,
        ),
        clipBehavior: Clip.antiAlias,
        child: scrollArea,
      );
    }

    // Apply margin if specified
    if (resolvedMargin != EdgeInsets.zero) {
      scrollArea = Padding(
        padding: resolvedMargin,
        child: scrollArea,
      );
    }

    // Add semantics for accessibility
    if (!excludeFromSemantics) {
      final defaultLabel = scrollDirection == Axis.vertical
          ? 'Vertical scrollable area'
          : 'Horizontal scrollable area';
      
      scrollArea = Semantics(
        label: semanticLabel ?? defaultLabel,
        child: scrollArea,
      );
    }

    return scrollArea;
  }

  /// Builds the core scrollable area with custom scrollbar.
  Widget _buildScrollArea(
    BuildContext context, 
    ThemeData theme, 
    ShadcnScrollAreaTheme scrollAreaTheme,
  ) {
    // Resolve scrollbar styling
    final resolvedScrollbarThickness = scrollbarThickness ?? scrollAreaTheme.getScrollbarThickness(context);
    final resolvedScrollbarThumbColor = scrollbarThumbColor ?? scrollAreaTheme.getScrollbarThumbColor(context);
    final resolvedScrollbarThumbHoverColor = scrollbarThumbHoverColor ?? scrollAreaTheme.getScrollbarThumbHoverColor(context);
    final resolvedScrollbarThumbActiveColor = scrollbarThumbActiveColor ?? scrollAreaTheme.getScrollbarThumbActiveColor(context);
    final resolvedScrollbarTrackColor = scrollbarTrackColor ?? scrollAreaTheme.getScrollbarTrackColor(context);
    final resolvedScrollbarThumbRadius = scrollbarThumbRadius ?? scrollAreaTheme.getScrollbarThumbRadius(context);
    final resolvedScrollbarTrackRadius = scrollbarTrackRadius ?? scrollAreaTheme.getScrollbarTrackRadius(context);
    final resolvedScrollbarThumbMinHeight = scrollbarThumbMinHeight ?? scrollAreaTheme.getScrollbarThumbMinHeight(context);
    final resolvedScrollbarAlwaysVisible = scrollbarAlwaysVisible ?? scrollAreaTheme.getScrollbarAlwaysVisible(context);
    final resolvedScrollbarShowTrack = scrollbarShowTrack ?? scrollAreaTheme.getScrollbarShowTrack(context);
    final resolvedScrollbarFadeDuration = scrollbarFadeDuration ?? scrollAreaTheme.getScrollbarFadeDuration(context);
    final resolvedScrollbarMargin = scrollbarMargin ?? scrollAreaTheme.getScrollbarMargin(context);
    final resolvedScrollbarInteractive = scrollbarInteractive ?? scrollAreaTheme.getScrollbarInteractive(context);
    final resolvedScrollbarMinThumbExtent = scrollbarMinThumbExtent ?? scrollAreaTheme.getScrollbarMinThumbExtent(context);
    final resolvedPadding = padding ?? scrollAreaTheme.getPadding(context);

    // Create scroll controller if not provided
    final scrollController = controller ?? ScrollController();

    // Build the scrollable content
    Widget scrollableContent = SingleChildScrollView(
      controller: scrollController,
      scrollDirection: scrollDirection,
      reverse: reverse,
      physics: physics,
      padding: resolvedPadding,
      child: child,
    );

    // Apply size constraints
    if (height != null || width != null || maxHeight != null || maxWidth != null) {
      scrollableContent = ConstrainedBox(
        constraints: BoxConstraints(
          maxHeight: maxHeight ?? double.infinity,
          maxWidth: maxWidth ?? double.infinity,
        ),
        child: SizedBox(
          height: height,
          width: width,
          child: scrollableContent,
        ),
      );
    }

    // Wrap with custom scrollbar
    return RawScrollbar(
      controller: scrollController,
      thumbVisibility: resolvedScrollbarAlwaysVisible,
      trackVisibility: resolvedScrollbarShowTrack,
      thickness: resolvedScrollbarThickness,
      trackRadius: resolvedScrollbarTrackRadius.topLeft,
      minThumbLength: resolvedScrollbarThumbMinHeight,
      fadeDuration: resolvedScrollbarFadeDuration,
      // Note: margin parameter not available in RawScrollbar
      interactive: resolvedScrollbarInteractive,
      minOverscrollLength: resolvedScrollbarThickness * 2,
      thumbColor: resolvedScrollbarThumbColor,
      trackColor: resolvedScrollbarShowTrack ? resolvedScrollbarTrackColor : null,
      trackBorderColor: null,
      crossAxisMargin: 0,
      mainAxisMargin: 0,
      shape: RoundedRectangleBorder(
        borderRadius: resolvedScrollbarThumbRadius,
      ),
      child: scrollableContent,
    );
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ShadcnScrollArea('
        'scrollDirection: $scrollDirection, '
        'height: $height, '
        'width: $width, '
        'scrollbarAlwaysVisible: $scrollbarAlwaysVisible, '
        'scrollbarThickness: $scrollbarThickness, '
        'backgroundColor: $backgroundColor, '
        'excludeFromSemantics: $excludeFromSemantics'
        ')';
  }
}

/// Helper class for creating commonly used scroll area configurations.
/// 
/// Provides pre-configured scroll area widgets for common use cases,
/// reducing boilerplate and ensuring consistent styling patterns.
class ShadcnScrollAreas {
  ShadcnScrollAreas._();

  /// Creates a list scroll area.
  /// 
  /// Optimized for displaying lists with subtle scrollbar styling.
  static Widget list({
    Key? key,
    required Widget child,
    double? height,
    ScrollController? controller,
    EdgeInsets? padding,
    bool showScrollbar = false,
  }) {
    return ShadcnScrollArea(
      key: key,
      height: height,
      controller: controller,
      padding: padding ?? ShadcnTokens.paddingAll(ShadcnTokens.spacing2),
      scrollbarAlwaysVisible: showScrollbar,
      scrollbarThickness: 4.0,
      scrollbarShowTrack: false,
      child: child,
    );
  }

  /// Creates a content scroll area.
  /// 
  /// Optimized for text content with comfortable padding and styling.
  static Widget content({
    Key? key,
    required Widget child,
    double? height,
    double? maxHeight,
    ScrollController? controller,
    Color? backgroundColor,
    bool showScrollbar = true,
  }) {
    return ShadcnScrollArea(
      key: key,
      height: height,
      maxHeight: maxHeight,
      controller: controller,
      backgroundColor: backgroundColor,
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusMd),
      padding: ShadcnTokens.paddingAll(ShadcnTokens.spacing4),
      scrollbarAlwaysVisible: showScrollbar,
      scrollbarShowTrack: true,
      child: child,
    );
  }

  /// Creates a code scroll area.
  /// 
  /// Optimized for displaying code with monospace content and
  /// horizontal scrolling support.
  static Widget code({
    Key? key,
    required Widget child,
    double? height,
    double? maxHeight,
    ScrollController? horizontalController,
    ScrollController? verticalController,
    Color? backgroundColor,
  }) {
    return ShadcnScrollArea(
      key: key,
      height: height,
      maxHeight: maxHeight,
      controller: verticalController,
      backgroundColor: backgroundColor ?? Colors.grey.shade50,
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusSm),
      padding: ShadcnTokens.paddingAll(ShadcnTokens.spacing3),
      scrollbarAlwaysVisible: true,
      scrollbarShowTrack: true,
      scrollbarThickness: 6.0,
      child: SingleChildScrollView(
        controller: horizontalController,
        scrollDirection: Axis.horizontal,
        child: child,
      ),
    );
  }

  /// Creates a modal scroll area.
  /// 
  /// Optimized for modal dialogs and overlays with constrained height.
  static Widget modal({
    Key? key,
    required Widget child,
    double maxHeight = 400,
    ScrollController? controller,
    EdgeInsets? padding,
  }) {
    return ShadcnScrollArea(
      key: key,
      maxHeight: maxHeight,
      controller: controller,
      padding: padding ?? ShadcnTokens.paddingAll(ShadcnTokens.spacing6),
      scrollbarAlwaysVisible: false,
      scrollbarShowTrack: false,
      scrollbarThickness: 3.0,
      scrollbarFadeDuration: const Duration(milliseconds: 150),
      child: child,
    );
  }

  /// Creates a sidebar scroll area.
  /// 
  /// Optimized for navigation sidebars with minimal scrollbar styling.
  static Widget sidebar({
    Key? key,
    required Widget child,
    double? width,
    ScrollController? controller,
    Color? backgroundColor,
  }) {
    return ShadcnScrollArea(
      key: key,
      width: width,
      controller: controller,
      backgroundColor: backgroundColor,
      padding: ShadcnTokens.paddingAll(ShadcnTokens.spacing2),
      scrollbarAlwaysVisible: false,
      scrollbarThickness: 2.0,
      scrollbarShowTrack: false,
      child: child,
    );
  }

  /// Creates a table scroll area.
  /// 
  /// Optimized for data tables with both horizontal and vertical scrolling.
  static Widget table({
    Key? key,
    required Widget child,
    double? height,
    double? maxHeight,
    ScrollController? horizontalController,
    ScrollController? verticalController,
    Color? backgroundColor,
  }) {
    return ShadcnScrollArea(
      key: key,
      height: height,
      maxHeight: maxHeight,
      controller: verticalController,
      backgroundColor: backgroundColor,
      borderColor: Colors.grey.shade300,
      borderWidth: 1.0,
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusMd),
      scrollbarAlwaysVisible: true,
      scrollbarShowTrack: true,
      child: ShadcnScrollArea.horizontal(
        controller: horizontalController,
        scrollbarAlwaysVisible: true,
        scrollbarShowTrack: true,
        child: child,
      ),
    );
  }
}

/// Extension methods for convenient scroll area usage.
/// 
/// Provides extension methods on Widget to wrap them in scroll areas
/// with minimal syntax.
extension ShadcnScrollAreaExtensions on Widget {
  /// Wraps this widget in a scrollable area.
  /// 
  /// Provides a fluent interface for making any widget scrollable.
  Widget scrollable({
    double? height,
    double? width,
    double? maxHeight,
    double? maxWidth,
    Axis scrollDirection = Axis.vertical,
    ScrollController? controller,
    ScrollPhysics? physics,
    EdgeInsets? padding,
    Color? backgroundColor,
    bool scrollbarAlwaysVisible = false,
  }) {
    return ShadcnScrollArea(
      height: height,
      width: width,
      maxHeight: maxHeight,
      maxWidth: maxWidth,
      scrollDirection: scrollDirection,
      controller: controller,
      physics: physics,
      padding: padding,
      backgroundColor: backgroundColor,
      scrollbarAlwaysVisible: scrollbarAlwaysVisible,
      child: this,
    );
  }

  /// Wraps this widget in a vertically scrollable area.
  /// 
  /// Convenience method for vertical scrolling.
  Widget verticallyScrollable({
    double? height,
    double? maxHeight,
    ScrollController? controller,
    ScrollPhysics? physics,
    EdgeInsets? padding,
    bool scrollbarAlwaysVisible = false,
  }) {
    return scrollable(
      height: height,
      maxHeight: maxHeight,
      scrollDirection: Axis.vertical,
      controller: controller,
      physics: physics,
      padding: padding,
      scrollbarAlwaysVisible: scrollbarAlwaysVisible,
    );
  }

  /// Wraps this widget in a horizontally scrollable area.
  /// 
  /// Convenience method for horizontal scrolling.
  Widget horizontallyScrollable({
    double? width,
    double? maxWidth,
    ScrollController? controller,
    ScrollPhysics? physics,
    EdgeInsets? padding,
    bool scrollbarAlwaysVisible = false,
  }) {
    return scrollable(
      width: width,
      maxWidth: maxWidth,
      scrollDirection: Axis.horizontal,
      controller: controller,
      physics: physics,
      padding: padding,
      scrollbarAlwaysVisible: scrollbarAlwaysVisible,
    );
  }
}