import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_input_otp_theme.dart';
import '../../constants/shadcn_tokens.dart';

/// A shadcn-styled OTP (One-Time Password) input component with comprehensive theming support.
/// 
/// This component provides individual character input cells for entering OTP codes,
/// verification codes, or other fixed-length character sequences. It maintains full
/// Material Design integration while providing shadcn-specific styling and animations.
/// 
/// The component automatically derives its styling from the current theme context,
/// with fallback to Material Design values. All styling is customizable through
/// the [ShadcnInputOTPTheme] extension.
/// 
/// Example usage:
/// ```dart
/// ShadcnInputOTP(
///   length: 6,
///   onCompleted: (value) => print('OTP: $value'),
///   onChanged: (value) => print('Current: $value'),
/// )
/// ```
class ShadcnInputOTP extends ShadcnComponent with ShadcnComponentValidation {
  /// The number of OTP characters to input.
  final int length;
  
  /// Callback when the OTP value changes.
  final ValueChanged<String>? onChanged;
  
  /// Callback when the OTP is completed (all fields filled).
  final ValueChanged<String>? onCompleted;
  
  /// Whether the OTP input is enabled.
  final bool enabled;
  
  /// Whether to obscure the entered text (like a password field).
  final bool obscureText;
  
  /// Character to show when text is obscured.
  final String obscuringCharacter;
  
  /// Input formatters to apply to each character.
  final List<TextInputFormatter>? inputFormatters;
  
  /// Keyboard type for the input.
  final TextInputType keyboardType;
  
  /// Text input action.
  final TextInputAction? textInputAction;
  
  /// Whether to show cursor in the active cell.
  final bool? showCursor;
  
  /// Custom cursor color.
  final Color? cursorColor;
  
  /// Whether to autofocus the first cell.
  final bool autofocus;
  
  /// Focus node for the input.
  final FocusNode? focusNode;
  
  /// Initial value for the OTP.
  final String? initialValue;
  
  /// Whether the input is in an error state.
  final bool hasError;
  
  /// Custom error message.
  final String? errorText;
  
  /// Whether to auto-validate the input.
  final bool autovalidate;
  
  /// Validation function for the complete OTP.
  final String? Function(String?)? validator;
  
  /// Custom cell width override.
  final double? cellWidth;
  
  /// Custom cell height override.
  final double? cellHeight;
  
  /// Custom spacing between cells.
  final double? cellSpacing;
  
  /// Custom border radius for cells.
  final BorderRadius? cellBorderRadius;
  
  /// Custom cell padding.
  final EdgeInsets? cellPadding;
  
  /// Custom text style for the cell content.
  final TextStyle? textStyle;
  
  /// Custom animation duration.
  final Duration? animationDuration;
  
  /// Custom animation curve.
  final Curve? animationCurve;
  
  /// Semantic label for accessibility.
  final String? semanticLabel;
  
  /// Whether to exclude from semantics tree.
  final bool excludeFromSemantics;
  
  /// Whether to enable haptic feedback on input.
  final bool enableFeedback;
  
  /// Custom input decoration for each cell.
  final InputDecoration? decoration;

  const ShadcnInputOTP({
    super.key,
    required this.length,
    this.onChanged,
    this.onCompleted,
    this.enabled = true,
    this.obscureText = false,
    this.obscuringCharacter = '•',
    this.inputFormatters,
    this.keyboardType = TextInputType.number,
    this.textInputAction,
    this.showCursor,
    this.cursorColor,
    this.autofocus = false,
    this.focusNode,
    this.initialValue,
    this.hasError = false,
    this.errorText,
    this.autovalidate = false,
    this.validator,
    this.cellWidth,
    this.cellHeight,
    this.cellSpacing,
    this.cellBorderRadius,
    this.cellPadding,
    this.textStyle,
    this.animationDuration,
    this.animationCurve,
    this.semanticLabel,
    this.excludeFromSemantics = false,
    this.enableFeedback = true,
    this.decoration,
  }) : assert(length > 0, 'OTP length must be positive');

  /// Factory constructor for numeric OTP input.
  factory ShadcnInputOTP.numeric({
    Key? key,
    required int length,
    ValueChanged<String>? onChanged,
    ValueChanged<String>? onCompleted,
    bool enabled = true,
    bool obscureText = false,
    bool autofocus = false,
    String? initialValue,
    bool hasError = false,
    String? errorText,
  }) {
    return ShadcnInputOTP(
      key: key,
      length: length,
      onChanged: onChanged,
      onCompleted: onCompleted,
      enabled: enabled,
      obscureText: obscureText,
      autofocus: autofocus,
      initialValue: initialValue,
      hasError: hasError,
      errorText: errorText,
      keyboardType: TextInputType.number,
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
    );
  }

  /// Factory constructor for alphanumeric OTP input.
  factory ShadcnInputOTP.alphanumeric({
    Key? key,
    required int length,
    ValueChanged<String>? onChanged,
    ValueChanged<String>? onCompleted,
    bool enabled = true,
    bool obscureText = false,
    bool autofocus = false,
    String? initialValue,
    bool hasError = false,
    String? errorText,
  }) {
    return ShadcnInputOTP(
      key: key,
      length: length,
      onChanged: onChanged,
      onCompleted: onCompleted,
      enabled: enabled,
      obscureText: obscureText,
      autofocus: autofocus,
      initialValue: initialValue,
      hasError: hasError,
      errorText: errorText,
      keyboardType: TextInputType.text,
      inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9]'))],
    );
  }

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    // Validate theme context and properties
    validateThemeProperties(context);
    validateSize(componentName: 'ShadcnInputOTP');
    validateAccessibility(
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      componentName: 'ShadcnInputOTP',
    );

    // Resolve OTP theme
    final otpTheme = resolveTheme<ShadcnInputOTPTheme>(
      context,
      ShadcnInputOTPTheme.defaultTheme,
    );

    return _ShadcnInputOTPStateful(
      otp: this,
      theme: otpTheme,
      materialTheme: theme,
    );
  }
}

/// Stateful widget that manages the OTP input state.
class _ShadcnInputOTPStateful extends StatefulWidget {
  final ShadcnInputOTP otp;
  final ShadcnInputOTPTheme theme;
  final ThemeData materialTheme;

  const _ShadcnInputOTPStateful({
    required this.otp,
    required this.theme,
    required this.materialTheme,
  });

  @override
  State<_ShadcnInputOTPStateful> createState() => _ShadcnInputOTPStatefulState();
}

class _ShadcnInputOTPStatefulState extends State<_ShadcnInputOTPStateful> {
  late List<TextEditingController> _controllers;
  late List<FocusNode> _focusNodes;
  late String _value;
  String? _errorText;

  ShadcnInputOTP get otp => widget.otp;
  ShadcnInputOTPTheme get theme => widget.theme;
  ThemeData get materialTheme => widget.materialTheme;

  @override
  void initState() {
    super.initState();
    
    _value = otp.initialValue ?? '';
    _controllers = List.generate(otp.length, (index) {
      final controller = TextEditingController();
      if (index < _value.length) {
        controller.text = _value[index];
      }
      return controller;
    });
    
    _focusNodes = List.generate(otp.length, (index) => FocusNode());
    
    // Auto-focus first cell if requested
    if (otp.autofocus && otp.enabled) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNodes[0].requestFocus();
      });
    }
    
    // Set up listeners
    for (int i = 0; i < otp.length; i++) {
      _controllers[i].addListener(() => _onTextChanged(i));
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    for (final focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  @override
  void didUpdateWidget(_ShadcnInputOTPStateful oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update controllers if initial value changed
    if (oldWidget.otp.initialValue != otp.initialValue) {
      final newValue = otp.initialValue ?? '';
      for (int i = 0; i < otp.length; i++) {
        if (i < newValue.length) {
          _controllers[i].text = newValue[i];
        } else {
          _controllers[i].clear();
        }
      }
      _value = newValue;
    }
  }

  void _onTextChanged(int index) {
    final text = _controllers[index].text;
    
    if (text.isNotEmpty) {
      // Take only the last character if multiple characters are entered
      final char = text.characters.last;
      _controllers[index].text = char;
      _controllers[index].selection = TextSelection.fromPosition(
        TextPosition(offset: char.length),
      );
      
      // Move to next field
      if (index < otp.length - 1) {
        _focusNodes[index + 1].requestFocus();
      } else {
        // Last field, remove focus
        _focusNodes[index].unfocus();
      }
    }
    
    _updateValue();
  }

  void _updateValue() {
    final newValue = _controllers.map((c) => c.text).join();
    
    if (newValue != _value) {
      setState(() {
        _value = newValue;
        _errorText = null;
      });
      
      // Provide haptic feedback
      if (otp.enableFeedback) {
        HapticFeedback.selectionClick();
      }
      
      // Call callbacks
      otp.onChanged?.call(newValue);
      
      if (newValue.length == otp.length) {
        // Validate if needed
        if (otp.autovalidate && otp.validator != null) {
          final error = otp.validator!(newValue);
          if (error != null) {
            setState(() {
              _errorText = error;
            });
          }
        }
        
        otp.onCompleted?.call(newValue);
      }
    }
  }

  void _onKeyPressed(int index, RawKeyEvent event) {
    if (event is RawKeyDownEvent) {
      // Handle backspace
      if (event.logicalKey == LogicalKeyboardKey.backspace) {
        if (_controllers[index].text.isEmpty && index > 0) {
          // Move to previous field and clear it
          _controllers[index - 1].clear();
          _focusNodes[index - 1].requestFocus();
          _updateValue();
        }
      }
      // Handle arrow keys for navigation
      else if (event.logicalKey == LogicalKeyboardKey.arrowLeft && index > 0) {
        _focusNodes[index - 1].requestFocus();
      } else if (event.logicalKey == LogicalKeyboardKey.arrowRight && index < otp.length - 1) {
        _focusNodes[index + 1].requestFocus();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final effectiveCellWidth = otp.cellWidth ?? theme.cellWidth ?? 48.0;
    final effectiveCellHeight = otp.cellHeight ?? theme.cellHeight ?? 48.0;
    final effectiveCellSpacing = otp.cellSpacing ?? theme.cellSpacing ?? ShadcnTokens.spacing2;
    
    Widget result = Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(otp.length, (index) {
        return Padding(
          padding: EdgeInsets.only(
            right: index < otp.length - 1 ? effectiveCellSpacing : 0,
          ),
          child: _buildCell(index, effectiveCellWidth, effectiveCellHeight),
        );
      }),
    );
    
    // Add error text if present
    if ((otp.hasError && otp.errorText != null) || _errorText != null) {
      result = Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          result,
          const SizedBox(height: ShadcnTokens.spacing1),
          Text(
            _errorText ?? otp.errorText!,
            style: TextStyle(
              color: theme.cellErrorForegroundColor ?? materialTheme.colorScheme.error,
              fontSize: ShadcnTokens.fontSizeSm,
            ),
          ),
        ],
      );
    }
    
    // Add semantics
    if (!otp.excludeFromSemantics) {
      result = Semantics(
        label: otp.semanticLabel ?? 'OTP input with ${otp.length} digits',
        textField: true,
        enabled: otp.enabled,
        child: result,
      );
    }
    
    return result;
  }

  Widget _buildCell(int index, double width, double height) {
    final isFocused = _focusNodes[index].hasFocus;
    final isFilled = _controllers[index].text.isNotEmpty;
    final hasError = otp.hasError || _errorText != null;
    
    // Determine cell colors based on state
    Color backgroundColor;
    Color foregroundColor;
    Color borderColor;
    
    if (!otp.enabled) {
      backgroundColor = theme.cellDisabledBackgroundColor ?? materialTheme.colorScheme.onSurface.withOpacity(0.04);
      foregroundColor = theme.cellDisabledForegroundColor ?? materialTheme.colorScheme.onSurface.withOpacity(0.38);
      borderColor = theme.cellDisabledBorderColor ?? materialTheme.colorScheme.onSurface.withOpacity(0.12);
    } else if (hasError) {
      backgroundColor = theme.cellErrorBackgroundColor ?? materialTheme.colorScheme.surface;
      foregroundColor = theme.cellErrorForegroundColor ?? materialTheme.colorScheme.error;
      borderColor = theme.cellErrorBorderColor ?? materialTheme.colorScheme.error;
    } else if (isFocused) {
      backgroundColor = theme.cellFocusedBackgroundColor ?? materialTheme.colorScheme.surface;
      foregroundColor = theme.cellFocusedForegroundColor ?? materialTheme.colorScheme.onSurface;
      borderColor = theme.cellFocusedBorderColor ?? materialTheme.colorScheme.primary;
    } else if (isFilled) {
      backgroundColor = theme.cellFilledBackgroundColor ?? materialTheme.colorScheme.surface;
      foregroundColor = theme.cellFilledForegroundColor ?? materialTheme.colorScheme.onSurface;
      borderColor = theme.cellFilledBorderColor ?? materialTheme.colorScheme.outline;
    } else {
      backgroundColor = theme.cellBackgroundColor ?? materialTheme.colorScheme.surface;
      foregroundColor = theme.cellForegroundColor ?? materialTheme.colorScheme.onSurface;
      borderColor = theme.cellBorderColor ?? materialTheme.colorScheme.outline;
    }
    
    final effectiveBorderRadius = otp.cellBorderRadius ?? theme.cellBorderRadius ?? 
        BorderRadius.circular(ShadcnTokens.radiusMd);
    
    final effectivePadding = otp.cellPadding ?? theme.cellPadding ?? 
        const EdgeInsets.all(ShadcnTokens.spacing2);
    
    final effectiveTextStyle = (otp.textStyle ?? theme.textStyle ?? TextStyle(
      fontSize: theme.fontSize ?? ShadcnTokens.fontSizeLg,
      fontWeight: theme.fontWeight ?? ShadcnTokens.fontWeightMedium,
    )).copyWith(color: foregroundColor);
    
    return AnimatedContainer(
      duration: otp.animationDuration ?? theme.animationDuration ?? ShadcnTokens.durationFast,
      curve: otp.animationCurve ?? theme.animationCurve ?? Curves.easeInOut,
      width: width,
      height: height,
      padding: effectivePadding,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: effectiveBorderRadius,
        border: Border.all(
          color: borderColor,
          width: theme.cellBorderWidth ?? ShadcnTokens.borderWidth,
        ),
      ),
      child: RawKeyboardListener(
        focusNode: FocusNode(),
        onKey: (event) => _onKeyPressed(index, event),
        child: TextFormField(
          controller: _controllers[index],
          focusNode: _focusNodes[index],
          enabled: otp.enabled,
          obscureText: otp.obscureText,
          obscuringCharacter: otp.obscuringCharacter,
          keyboardType: otp.keyboardType,
          textInputAction: otp.textInputAction,
          inputFormatters: [
            LengthLimitingTextInputFormatter(1),
            ...(otp.inputFormatters ?? []),
          ],
          textAlign: TextAlign.center,
          style: effectiveTextStyle,
          showCursor: otp.showCursor ?? theme.showCursor ?? true,
          cursorColor: otp.cursorColor ?? theme.cursorColor ?? materialTheme.colorScheme.primary,
          cursorWidth: theme.cursorWidth ?? 2.0,
          cursorRadius: theme.cursorRadius ?? const Radius.circular(1.0),
          decoration: otp.decoration ?? const InputDecoration(
            border: InputBorder.none,
            contentPadding: EdgeInsets.zero,
          ),
          onChanged: (value) {
            // The actual logic is handled by the controller listener
            // This is just to ensure the TextField behaves correctly
          },
        ),
      ),
    );
  }
}