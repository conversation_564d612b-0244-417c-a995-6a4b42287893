import 'package:flutter/material.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_sidebar_theme.dart';

/// A shadcn-styled sidebar component.
class ShadcnSidebar extends ShadcnComponent {
  final String? title;
  final Widget? header;
  final List<ShadcnSidebarItem> items;
  final int? selectedIndex;
  final ValueChanged<int>? onItemSelected;
  final bool enabled;
  final Widget? footer;

  const ShadcnSidebar({
    super.key,
    this.title,
    this.header,
    required this.items,
    this.selectedIndex,
    this.onItemSelected,
    this.enabled = true,
    this.footer,
  });

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    final sidebarTheme = resolveTheme<ShadcnSidebarTheme>(
      context,
      ShadcnSidebarTheme.defaultTheme,
    );

    return Container(
      width: sidebarTheme.width,
      padding: sidebarTheme.padding,
      decoration: BoxDecoration(
        color: sidebarTheme.backgroundColor,
        border: Border(
          right: BorderSide(
            color: sidebarTheme.borderColor ?? theme.colorScheme.outline,
            width: sidebarTheme.borderWidth ?? 1.0,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          if (header != null || title != null) _buildHeader(sidebarTheme, theme),
          
          // Items
          Expanded(
            child: ListView.separated(
              itemCount: items.length,
              separatorBuilder: (context, index) => SizedBox(height: sidebarTheme.itemSpacing ?? 0),
              itemBuilder: (context, index) {
                final item = items[index];
                final isSelected = selectedIndex == index;
                
                return _ShadcnSidebarItemWidget(
                  item: item,
                  isSelected: isSelected,
                  enabled: enabled && item.enabled,
                  theme: sidebarTheme,
                  materialTheme: theme,
                  onTap: () => onItemSelected?.call(index),
                );
              },
            ),
          ),
          
          // Footer
          if (footer != null) footer!,
        ],
      ),
    );
  }

  Widget _buildHeader(ShadcnSidebarTheme sidebarTheme, ThemeData theme) {
    return Container(
      padding: sidebarTheme.headerPadding,
      color: sidebarTheme.headerBackground,
      child: header ?? (title != null
          ? Text(
              title!,
              style: (sidebarTheme.headerTextStyle ?? const TextStyle())
                  .copyWith(color: sidebarTheme.foregroundColor ?? theme.colorScheme.onSurface),
            )
          : null),
    );
  }
}

class ShadcnSidebarItem {
  final String text;
  final Widget? icon;
  final VoidCallback? onTap;
  final bool enabled;
  final String? tooltip;
  final List<ShadcnSidebarItem>? children;

  const ShadcnSidebarItem({
    required this.text,
    this.icon,
    this.onTap,
    this.enabled = true,
    this.tooltip,
    this.children,
  });
}

class _ShadcnSidebarItemWidget extends StatelessWidget {
  final ShadcnSidebarItem item;
  final bool isSelected;
  final bool enabled;
  final ShadcnSidebarTheme theme;
  final ThemeData materialTheme;
  final VoidCallback? onTap;

  const _ShadcnSidebarItemWidget({
    required this.item,
    required this.isSelected,
    required this.enabled,
    required this.theme,
    required this.materialTheme,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final backgroundColor = isSelected
        ? (theme.itemSelectedColor ?? materialTheme.colorScheme.primary)
        : Colors.transparent;
    
    final foregroundColor = isSelected
        ? (theme.itemSelectedForegroundColor ?? materialTheme.colorScheme.onPrimary)
        : (theme.foregroundColor ?? materialTheme.colorScheme.onSurface);

    Widget result = AnimatedContainer(
      duration: theme.animationDuration ?? const Duration(milliseconds: 150),
      padding: theme.itemPadding,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: theme.borderRadius,
      ),
      child: Material(
        type: MaterialType.transparency,
        child: InkWell(
          onTap: enabled ? (onTap ?? item.onTap) : null,
          borderRadius: theme.borderRadius,
          hoverColor: theme.itemHoverColor,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
            child: Row(
              children: [
                if (item.icon != null) ...[
                  IconTheme(
                    data: IconThemeData(
                      color: foregroundColor,
                      size: theme.iconSize,
                    ),
                    child: item.icon!,
                  ),
                  const SizedBox(width: 12),
                ],
                Expanded(
                  child: Text(
                    item.text,
                    style: (theme.itemTextStyle ?? const TextStyle())
                        .copyWith(color: foregroundColor),
                  ),
                ),
                if (item.children != null && item.children!.isNotEmpty)
                  Icon(
                    Icons.chevron_right,
                    color: foregroundColor,
                    size: theme.iconSize,
                  ),
              ],
            ),
          ),
        ),
      ),
    );

    if (item.tooltip != null) {
      result = Tooltip(
        message: item.tooltip!,
        child: result,
      );
    }

    return result;
  }
}