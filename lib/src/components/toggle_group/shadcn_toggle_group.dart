import 'package:flutter/material.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_toggle_group_theme.dart';

/// A shadcn-styled toggle group component.
class ShadcnToggleGroup<T> extends ShadcnComponent {
  final List<ShadcnToggleGroupItem<T>> items;
  final List<T> selectedValues;
  final ValueChanged<List<T>>? onSelectionChanged;
  final bool multiSelect;
  final bool enabled;
  final Axis direction;

  const ShadcnToggleGroup({
    super.key,
    required this.items,
    this.selectedValues = const [],
    this.onSelectionChanged,
    this.multiSelect = false,
    this.enabled = true,
    this.direction = Axis.horizontal,
  });

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    final toggleGroupTheme = resolveTheme<ShadcnToggleGroupTheme>(
      context,
      ShadcnToggleGroupTheme.defaultTheme,
    );

    return Container(
      padding: toggleGroupTheme.padding,
      decoration: BoxDecoration(
        color: toggleGroupTheme.backgroundColor,
        border: Border.all(
          color: toggleGroupTheme.borderColor ?? theme.colorScheme.outline,
          width: toggleGroupTheme.borderWidth ?? 1.0,
        ),
        borderRadius: toggleGroupTheme.borderRadius,
      ),
      child: direction == Axis.horizontal
          ? Row(
              mainAxisSize: MainAxisSize.min,
              children: _buildItems(toggleGroupTheme, theme),
            )
          : Column(
              mainAxisSize: MainAxisSize.min,
              children: _buildItems(toggleGroupTheme, theme),
            ),
    );
  }

  List<Widget> _buildItems(ShadcnToggleGroupTheme toggleGroupTheme, ThemeData theme) {
    return items.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      final isSelected = selectedValues.contains(item.value);
      final isFirst = index == 0;
      final isLast = index == items.length - 1;

      return _ShadcnToggleGroupItemWidget<T>(
        item: item,
        isSelected: isSelected,
        enabled: enabled && item.enabled,
        theme: toggleGroupTheme,
        materialTheme: theme,
        isFirst: isFirst,
        isLast: isLast,
        direction: direction,
        onTap: () => _handleItemTap(item.value),
      );
    }).toList();
  }

  void _handleItemTap(T value) {
    List<T> newSelection;
    
    if (multiSelect) {
      if (selectedValues.contains(value)) {
        newSelection = List.from(selectedValues)..remove(value);
      } else {
        newSelection = List.from(selectedValues)..add(value);
      }
    } else {
      newSelection = selectedValues.contains(value) ? [] : [value];
    }
    
    onSelectionChanged?.call(newSelection);
  }
}

class ShadcnToggleGroupItem<T> {
  final T value;
  final String? text;
  final Widget? child;
  final Widget? icon;
  final bool enabled;
  final String? tooltip;

  const ShadcnToggleGroupItem({
    required this.value,
    this.text,
    this.child,
    this.icon,
    this.enabled = true,
    this.tooltip,
  }) : assert(text != null || child != null || icon != null,
         'At least one of text, child, or icon must be provided');
}

class _ShadcnToggleGroupItemWidget<T> extends StatelessWidget {
  final ShadcnToggleGroupItem<T> item;
  final bool isSelected;
  final bool enabled;
  final ShadcnToggleGroupTheme theme;
  final ThemeData materialTheme;
  final bool isFirst;
  final bool isLast;
  final Axis direction;
  final VoidCallback? onTap;

  const _ShadcnToggleGroupItemWidget({
    required this.item,
    required this.isSelected,
    required this.enabled,
    required this.theme,
    required this.materialTheme,
    required this.isFirst,
    required this.isLast,
    required this.direction,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final backgroundColor = isSelected
        ? (theme.selectedBackgroundColor ?? materialTheme.colorScheme.primary)
        : Colors.transparent;
    
    final foregroundColor = isSelected
        ? (theme.selectedForegroundColor ?? materialTheme.colorScheme.onPrimary)
        : (theme.foregroundColor ?? materialTheme.colorScheme.onSurface);

    // Calculate border radius based on position in group
    BorderRadius? borderRadius;
    if (theme.borderRadius != null) {
      if (direction == Axis.horizontal) {
        if (isFirst && isLast) {
          borderRadius = theme.borderRadius;
        } else if (isFirst) {
          borderRadius = BorderRadius.only(
            topLeft: theme.borderRadius!.topLeft,
            bottomLeft: theme.borderRadius!.bottomLeft,
          );
        } else if (isLast) {
          borderRadius = BorderRadius.only(
            topRight: theme.borderRadius!.topRight,
            bottomRight: theme.borderRadius!.bottomRight,
          );
        } else {
          borderRadius = BorderRadius.zero;
        }
      } else {
        if (isFirst && isLast) {
          borderRadius = theme.borderRadius;
        } else if (isFirst) {
          borderRadius = BorderRadius.only(
            topLeft: theme.borderRadius!.topLeft,
            topRight: theme.borderRadius!.topRight,
          );
        } else if (isLast) {
          borderRadius = BorderRadius.only(
            bottomLeft: theme.borderRadius!.bottomLeft,
            bottomRight: theme.borderRadius!.bottomRight,
          );
        } else {
          borderRadius = BorderRadius.zero;
        }
      }
    }

    Widget result = AnimatedContainer(
      duration: const Duration(milliseconds: 150),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: borderRadius,
      ),
      child: Material(
        type: MaterialType.transparency,
        child: InkWell(
          onTap: enabled ? onTap : null,
          borderRadius: borderRadius,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (item.icon != null) ...[
                  IconTheme(
                    data: IconThemeData(color: foregroundColor),
                    child: item.icon!,
                  ),
                  if (item.text != null || item.child != null)
                    const SizedBox(width: 8),
                ],
                if (item.child != null)
                  item.child!
                else if (item.text != null)
                  Text(
                    item.text!,
                    style: (theme.textStyle ?? const TextStyle())
                        .copyWith(color: foregroundColor),
                  ),
              ],
            ),
          ),
        ),
      ),
    );

    if (item.tooltip != null) {
      result = Tooltip(
        message: item.tooltip!,
        child: result,
      );
    }

    return result;
  }
}