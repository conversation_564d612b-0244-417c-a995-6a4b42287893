import 'package:flutter/material.dart';
import '../../theme/extensions/shadcn_dialog_theme.dart';
import '../../constants/shadcn_tokens.dart';
import '../shadcn_component.dart';

/// A shadcn-style dialog component that extends Material Design dialog functionality.
/// 
/// The ShadcnDialog component provides modal dialog boxes that integrate with
/// Flutter's Material Design dialog system while following shadcn design principles.
/// It offers flexible content layout, theming integration, and proper accessibility support.
/// 
/// This component supports:
/// - Custom or default Material dialog behavior
/// - Title, content, and actions with flexible layout
/// - Custom content widgets for advanced layouts
/// - Theme-aware styling with shadcn design tokens
/// - Proper barrier management and overlay positioning
/// - Accessibility support with semantic labels
/// - Animation customization and transition control
/// - Both horizontal and vertical action button layouts
/// 
/// Example usage:
/// ```dart
/// // Show a simple dialog
/// showDialog(
///   context: context,
///   builder: (context) => ShadcnDialog(
///     title: 'Confirmation',
///     content: 'Are you sure you want to delete this item?',
///     actions: [
///       TextButton(
///         onPressed: () => Navigator.of(context).pop(false),
///         child: Text('Cancel'),
///       ),
///       ElevatedButton(
///         onPressed: () => Navigator.of(context).pop(true),
///         child: Text('Delete'),
///       ),
///     ],
///   ),
/// );
/// 
/// // Using the convenience method
/// final result = await ShadcnDialog.show<bool>(
///   context: context,
///   title: 'Custom Dialog',
///   content: 'This is a custom dialog with shadcn styling.',
///   actions: [
///     TextButton(
///       onPressed: () => Navigator.of(context).pop(false),
///       child: Text('Cancel'),
///     ),
///     ElevatedButton(
///       onPressed: () => Navigator.of(context).pop(true),
///       child: Text('Confirm'),
///     ),
///   ],
/// );
/// ```
class ShadcnDialog extends ShadcnComponent with ShadcnComponentValidation {
  /// The title of the dialog
  final String? title;
  
  /// The content/body text of the dialog
  final String? content;
  
  /// Custom content widget that replaces the content text
  final Widget? contentWidget;
  
  /// Custom title widget that replaces the title text
  final Widget? titleWidget;
  
  /// List of action buttons for the dialog
  final List<Widget>? actions;
  
  /// Custom padding for the dialog content
  final EdgeInsets? contentPadding;
  
  /// Custom padding for the title area
  final EdgeInsets? titlePadding;
  
  /// Custom padding for the actions area
  final EdgeInsets? actionsPadding;
  
  /// Whether actions should be arranged vertically (default is horizontal)
  final bool verticalActions;
  
  /// Custom border radius override for the dialog
  final BorderRadius? borderRadius;
  
  /// Custom elevation override for the dialog shadow
  final double? elevation;
  
  /// Custom background color override for the dialog
  final Color? backgroundColor;
  
  /// Custom maximum width constraint for the dialog
  final double? maxWidth;
  
  /// Custom maximum height constraint for the dialog
  final double? maxHeight;
  
  /// Custom minimum width constraint for the dialog
  final double? minWidth;
  
  /// Custom minimum height constraint for the dialog
  final double? minHeight;
  
  /// Semantic label for accessibility
  final String? semanticLabel;
  
  /// Whether to exclude this dialog from semantics tree
  final bool excludeFromSemantics;
  
  /// Whether the dialog can be dismissed by tapping outside
  final bool barrierDismissible;
  
  /// Color of the barrier (background overlay)
  final Color? barrierColor;
  
  /// Label for the barrier for accessibility
  final String? barrierLabel;
  
  /// Custom shape for the dialog container
  final ShapeBorder? shape;
  
  /// Clip behavior for the dialog container
  final Clip? clipBehavior;
  
  /// Inset padding around the dialog
  final EdgeInsets? insetPadding;
  
  /// Alignment for action buttons
  final MainAxisAlignment? actionsAlignment;
  
  /// Custom surface tint color (set to transparent to disable Material 3 tinting)
  final Color? surfaceTintColor;
  
  const ShadcnDialog({
    super.key,
    this.title,
    this.content,
    this.contentWidget,
    this.titleWidget,
    this.actions,
    this.contentPadding,
    this.titlePadding,
    this.actionsPadding,
    this.verticalActions = false,
    this.borderRadius,
    this.elevation,
    this.backgroundColor,
    this.maxWidth,
    this.maxHeight,
    this.minWidth,
    this.minHeight,
    this.semanticLabel,
    this.excludeFromSemantics = false,
    this.barrierDismissible = true,
    this.barrierColor,
    this.barrierLabel,
    this.shape,
    this.clipBehavior,
    this.insetPadding,
    this.actionsAlignment,
    this.surfaceTintColor,
  }) : assert(
         title != null || content != null || contentWidget != null || titleWidget != null,
         'Either title, content, contentWidget, or titleWidget must be provided',
       );
  
  /// Creates a dialog with a title and content
  const ShadcnDialog.titled({
    Key? key,
    required String title,
    String? content,
    Widget? contentWidget,
    List<Widget>? actions,
    EdgeInsets? contentPadding,
    EdgeInsets? titlePadding,
    EdgeInsets? actionsPadding,
    bool verticalActions = false,
    BorderRadius? borderRadius,
    double? elevation,
    Color? backgroundColor,
    double? maxWidth,
    double? maxHeight,
    double? minWidth,
    double? minHeight,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    bool barrierDismissible = true,
    Color? barrierColor,
    String? barrierLabel,
    ShapeBorder? shape,
    Clip? clipBehavior,
    EdgeInsets? insetPadding,
    MainAxisAlignment? actionsAlignment,
    Color? surfaceTintColor,
  }) : this(
    key: key,
    title: title,
    content: content,
    contentWidget: contentWidget,
    actions: actions,
    contentPadding: contentPadding,
    titlePadding: titlePadding,
    actionsPadding: actionsPadding,
    verticalActions: verticalActions,
    borderRadius: borderRadius,
    elevation: elevation,
    backgroundColor: backgroundColor,
    maxWidth: maxWidth,
    maxHeight: maxHeight,
    minWidth: minWidth,
    minHeight: minHeight,
    semanticLabel: semanticLabel,
    excludeFromSemantics: excludeFromSemantics,
    barrierDismissible: barrierDismissible,
    barrierColor: barrierColor,
    barrierLabel: barrierLabel,
    shape: shape,
    clipBehavior: clipBehavior,
    insetPadding: insetPadding,
    actionsAlignment: actionsAlignment,
    surfaceTintColor: surfaceTintColor,
  );
  
  /// Creates a dialog with only content (no title)
  const ShadcnDialog.content({
    Key? key,
    String? content,
    Widget? contentWidget,
    List<Widget>? actions,
    EdgeInsets? contentPadding,
    EdgeInsets? actionsPadding,
    bool verticalActions = false,
    BorderRadius? borderRadius,
    double? elevation,
    Color? backgroundColor,
    double? maxWidth,
    double? maxHeight,
    double? minWidth,
    double? minHeight,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    bool barrierDismissible = true,
    Color? barrierColor,
    String? barrierLabel,
    ShapeBorder? shape,
    Clip? clipBehavior,
    EdgeInsets? insetPadding,
    MainAxisAlignment? actionsAlignment,
    Color? surfaceTintColor,
  }) : this(
    key: key,
    content: content,
    contentWidget: contentWidget,
    actions: actions,
    contentPadding: contentPadding,
    actionsPadding: actionsPadding,
    verticalActions: verticalActions,
    borderRadius: borderRadius,
    elevation: elevation,
    backgroundColor: backgroundColor,
    maxWidth: maxWidth,
    maxHeight: maxHeight,
    minWidth: minWidth,
    minHeight: minHeight,
    semanticLabel: semanticLabel,
    excludeFromSemantics: excludeFromSemantics,
    barrierDismissible: barrierDismissible,
    barrierColor: barrierColor,
    barrierLabel: barrierLabel,
    shape: shape,
    clipBehavior: clipBehavior,
    insetPadding: insetPadding,
    actionsAlignment: actionsAlignment,
    surfaceTintColor: surfaceTintColor,
  );
  
  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    // Validate theme context and component configuration
    assert(() {
      validateThemeProperties(context);
      validateAccessibility(
        semanticLabel: semanticLabel,
        excludeFromSemantics: excludeFromSemantics,
        componentName: 'ShadcnDialog',
      );
      
      // Validate required content
      if (title == null && content == null && contentWidget == null && titleWidget == null) {
        throw FlutterError(
          'ShadcnDialog requires at least one of: title, content, contentWidget, or titleWidget. '
          'Provide at least one of these properties.',
        );
      }
      
      return true;
    }());
    
    // Resolve the dialog theme with fallbacks
    final dialogTheme = resolveTheme<ShadcnDialogTheme>(
      context,
      ShadcnDialogTheme.defaultTheme,
    );
    
    // Resolve layout properties
    final resolvedBorderRadius = resolveBorderRadius(
      context,
      borderRadius,
      dialogTheme.borderRadius ?? ShadcnTokens.borderRadius(ShadcnTokens.radiusLg),
    );
    
    final resolvedContentPadding = resolveSpacing(
      context,
      contentPadding,
      dialogTheme.contentPadding ?? const EdgeInsets.fromLTRB(24.0, 20.0, 24.0, 24.0),
    );
    
    final resolvedTitlePadding = resolveSpacing(
      context,
      titlePadding,
      dialogTheme.titlePadding ?? const EdgeInsets.fromLTRB(24.0, 24.0, 24.0, 0.0),
    );
    
    final resolvedActionsPadding = resolveSpacing(
      context,
      actionsPadding,
      dialogTheme.actionsPadding ?? const EdgeInsets.fromLTRB(24.0, 0.0, 24.0, 24.0),
    );
    
    final resolvedElevation = resolveDouble(
      context,
      elevation,
      dialogTheme.elevation ?? ShadcnTokens.elevationLg,
      applyVerticalDensity: false,
    );
    
    final resolvedBackgroundColor = resolveColor(
      context,
      backgroundColor ?? dialogTheme.backgroundColor,
      (theme) => theme.colorScheme.surface,
    );
    
    // Build the dialog shape
    final resolvedShape = shape ?? RoundedRectangleBorder(
      borderRadius: resolvedBorderRadius,
      side: BorderSide(
        color: dialogTheme.borderColor ?? theme.colorScheme.outline.withValues(alpha: 0.2),
        width: dialogTheme.borderWidth ?? ShadcnTokens.borderWidth,
      ),
    );
    
    // Build constraints
    final dialogConstraints = BoxConstraints(
      minWidth: minWidth ?? dialogTheme.minWidth ?? 280.0,
      maxWidth: maxWidth ?? dialogTheme.maxWidth ?? 512.0,
      minHeight: minHeight ?? dialogTheme.minHeight ?? 0.0,
      maxHeight: maxHeight ?? dialogTheme.maxHeight ?? double.infinity,
    );
    
    return ConstrainedBox(
      constraints: dialogConstraints,
      child: AlertDialog(
        // Material dialog properties
        clipBehavior: clipBehavior ?? dialogTheme.clipBehavior ?? Clip.hardEdge,
        backgroundColor: resolvedBackgroundColor,
        surfaceTintColor: surfaceTintColor ?? 
                         dialogTheme.surfaceTintColor ?? 
                         Colors.transparent, // Disable Material 3 tint
        shadowColor: dialogTheme.shadowColor ?? theme.colorScheme.shadow,
        elevation: resolvedElevation,
        shape: resolvedShape,
        contentPadding: resolvedContentPadding,
        titlePadding: resolvedTitlePadding,
        actionsPadding: resolvedActionsPadding,
        buttonPadding: dialogTheme.buttonPadding ?? EdgeInsets.zero,
        insetPadding: insetPadding ?? 
                     dialogTheme.insetPadding ?? 
                     const EdgeInsets.symmetric(horizontal: 40.0, vertical: 24.0),
        
        // Content sections
        title: _buildTitle(context, dialogTheme),
        content: _buildContent(context, dialogTheme),
        actions: _buildActions(context, dialogTheme),
        
        // Layout configuration
        actionsAlignment: actionsAlignment ?? 
                         dialogTheme.actionsAlignment ?? 
                         MainAxisAlignment.end,
        
        // Semantic properties
        semanticLabel: semanticLabel ?? _buildSemanticLabel(),
      ),
    );
  }
  
  /// Builds the title section of the dialog
  Widget? _buildTitle(BuildContext context, ShadcnDialogTheme theme) {
    if (titleWidget != null) {
      return titleWidget!;
    }
    
    if (title == null) {
      return null;
    }
    
    final titleStyle = resolveTextStyle(
      context,
      theme.titleTextStyle,
      (textTheme) => textTheme.headlineSmall ?? const TextStyle(),
    ).copyWith(
      color: theme.foregroundColor ?? Theme.of(context).colorScheme.onSurface,
      fontWeight: ShadcnTokens.fontWeightSemibold,
    );
    
    return Text(
      title!,
      style: titleStyle,
    );
  }
  
  /// Builds the content section of the dialog
  Widget? _buildContent(BuildContext context, ShadcnDialogTheme theme) {
    if (contentWidget != null) {
      return contentWidget!;
    }
    
    if (content == null) {
      return null;
    }
    
    final contentStyle = resolveTextStyle(
      context,
      theme.contentTextStyle,
      (textTheme) => textTheme.bodyMedium ?? const TextStyle(),
    ).copyWith(
      color: theme.foregroundColor?.withValues(alpha: 0.8) ?? 
             Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
    );
    
    return Text(
      content!,
      style: contentStyle,
    );
  }
  
  /// Builds the actions section of the dialog
  List<Widget>? _buildActions(BuildContext context, ShadcnDialogTheme theme) {
    if (actions == null || actions!.isEmpty) {
      return null;
    }
    
    if (verticalActions || (theme.verticalActions == true)) {
      // For vertical actions, wrap each in a SizedBox for consistent width
      final spacedActions = <Widget>[];
      for (int i = 0; i < actions!.length; i++) {
        spacedActions.add(
          SizedBox(
            width: double.infinity,
            child: actions![i],
          ),
        );
        
        if (i < actions!.length - 1) {
          spacedActions.add(SizedBox(
            height: theme.actionButtonGap ?? ShadcnTokens.spacing2,
          ));
        }
      }
      return spacedActions;
    }
    
    return actions;
  }
  
  /// Builds a semantic label for accessibility
  String _buildSemanticLabel() {
    final parts = <String>['Dialog'];
    
    // Add title content
    if (title?.isNotEmpty == true) {
      parts.add(title!);
    }
    
    // Add content text
    if (content?.isNotEmpty == true) {
      parts.add(content!);
    }
    
    return parts.join('. ');
  }
  
  /// Shows this dialog using the Material dialog system
  /// 
  /// This is a convenience method that wraps the standard [showDialog] function
  /// with proper configuration for the ShadcnDialog.
  /// 
  /// Returns a Future that resolves to the value passed to [Navigator.pop]
  /// when the dialog is dismissed.
  Future<T?> show<T>(BuildContext context) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor,
      barrierLabel: barrierLabel ?? 
          MaterialLocalizations.of(context).modalBarrierDismissLabel,
      builder: (context) => this,
    );
  }
}

/// Extension methods to provide convenient static show methods for ShadcnDialog
extension ShadcnDialogExtension on ShadcnDialog {
  /// Shows a dialog with the provided configuration
  static Future<T?> show<T>(
    BuildContext context, {
    String? title,
    String? content,
    Widget? contentWidget,
    Widget? titleWidget,
    List<Widget>? actions,
    bool barrierDismissible = true,
    Color? barrierColor,
    String? barrierLabel,
    bool verticalActions = false,
    BorderRadius? borderRadius,
    double? elevation,
    Color? backgroundColor,
    double? maxWidth,
    double? maxHeight,
    EdgeInsets? contentPadding,
    EdgeInsets? titlePadding,
    EdgeInsets? actionsPadding,
  }) {
    return ShadcnDialog(
      title: title,
      content: content,
      contentWidget: contentWidget,
      titleWidget: titleWidget,
      actions: actions,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor,
      barrierLabel: barrierLabel,
      verticalActions: verticalActions,
      borderRadius: borderRadius,
      elevation: elevation,
      backgroundColor: backgroundColor,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
      contentPadding: contentPadding,
      titlePadding: titlePadding,
      actionsPadding: actionsPadding,
    ).show<T>(context);
  }
  
  /// Shows a confirmation dialog with Yes/No buttons
  /// 
  /// Returns `true` if the user confirms, `false` if they cancel,
  /// or `null` if the dialog is dismissed without selection.
  static Future<bool?> showConfirmation(
    BuildContext context, {
    String title = 'Confirmation',
    required String content,
    String confirmText = 'Yes',
    String cancelText = 'No',
    bool barrierDismissible = true,
    bool verticalActions = false,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => ShadcnDialog.titled(
        title: title,
        content: content,
        verticalActions: verticalActions,
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(cancelText),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }
  
  /// Shows an information dialog with an OK button
  /// 
  /// Returns when the user dismisses the dialog.
  static Future<void> showInfo(
    BuildContext context, {
    String title = 'Information',
    required String content,
    String okText = 'OK',
    bool barrierDismissible = true,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => ShadcnDialog.titled(
        title: title,
        content: content,
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(okText),
          ),
        ],
      ),
    );
  }
  
  /// Shows a custom content dialog
  /// 
  /// Useful for dialogs with complex layouts or custom widgets.
  static Future<T?> showCustom<T>(
    BuildContext context, {
    Widget? title,
    required Widget content,
    List<Widget>? actions,
    bool barrierDismissible = true,
    bool verticalActions = false,
    double? maxWidth,
    double? maxHeight,
  }) {
    return ShadcnDialog(
      titleWidget: title,
      contentWidget: content,
      actions: actions,
      barrierDismissible: barrierDismissible,
      verticalActions: verticalActions,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
    ).show<T>(context);
  }
}