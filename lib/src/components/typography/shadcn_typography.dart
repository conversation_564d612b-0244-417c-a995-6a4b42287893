import 'package:flutter/material.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_typography_theme.dart';

/// A shadcn-styled typography component.
class ShadcnTypography extends ShadcnComponent {
  final String text;
  final ShadcnTypographyVariant variant;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final bool? softWrap;
  final double? textScaleFactor;
  final String? semanticLabel;
  final TextDirection? textDirection;

  const ShadcnTypography({
    super.key,
    required this.text,
    this.variant = ShadcnTypographyVariant.bodyMedium,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.softWrap,
    this.textScaleFactor,
    this.semanticLabel,
    this.textDirection,
  });

  // Factory constructors for common variants
  factory ShadcnTypography.h1(String text, {Key? key, TextStyle? style}) =>
      ShadcnTypography._variant(key: key, text: text, variant: ShadcnTypographyVariant.h1, style: style);
  
  factory ShadcnTypography.h2(String text, {Key? key, TextStyle? style}) =>
      ShadcnTypography._variant(key: key, text: text, variant: ShadcnTypographyVariant.h2, style: style);
  
  factory ShadcnTypography.h3(String text, {Key? key, TextStyle? style}) =>
      ShadcnTypography._variant(key: key, text: text, variant: ShadcnTypographyVariant.h3, style: style);
  
  factory ShadcnTypography.h4(String text, {Key? key, TextStyle? style}) =>
      ShadcnTypography._variant(key: key, text: text, variant: ShadcnTypographyVariant.h4, style: style);
  
  factory ShadcnTypography.body(String text, {Key? key, TextStyle? style}) =>
      ShadcnTypography._variant(key: key, text: text, variant: ShadcnTypographyVariant.bodyMedium, style: style);
  
  factory ShadcnTypography.muted(String text, {Key? key, TextStyle? style}) =>
      ShadcnTypography._variant(key: key, text: text, variant: ShadcnTypographyVariant.muted, style: style);
  
  factory ShadcnTypography.small(String text, {Key? key, TextStyle? style}) =>
      ShadcnTypography._variant(key: key, text: text, variant: ShadcnTypographyVariant.small, style: style);
  
  factory ShadcnTypography.code(String text, {Key? key, TextStyle? style}) =>
      ShadcnTypography._variant(key: key, text: text, variant: ShadcnTypographyVariant.code, style: style);

  const ShadcnTypography._variant({
    super.key,
    required this.text,
    required this.variant,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.softWrap,
    this.textScaleFactor,
    this.semanticLabel,
    this.textDirection,
  });

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    final typographyTheme = resolveTheme<ShadcnTypographyTheme>(
      context,
      ShadcnTypographyTheme.defaultTheme,
    );

    final effectiveStyle = _getVariantStyle(typographyTheme, theme);

    return Text(
      text,
      style: effectiveStyle?.merge(style) ?? style,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
      softWrap: softWrap,
      textScaleFactor: textScaleFactor,
      semanticsLabel: semanticLabel,
      textDirection: textDirection,
    );
  }

  TextStyle? _getVariantStyle(ShadcnTypographyTheme typographyTheme, ThemeData materialTheme) {
    switch (variant) {
      case ShadcnTypographyVariant.h1:
        return typographyTheme.h1Style ?? materialTheme.textTheme.displayLarge;
      case ShadcnTypographyVariant.h2:
        return typographyTheme.h2Style ?? materialTheme.textTheme.displayMedium;
      case ShadcnTypographyVariant.h3:
        return typographyTheme.h3Style ?? materialTheme.textTheme.displaySmall;
      case ShadcnTypographyVariant.h4:
        return typographyTheme.h4Style ?? materialTheme.textTheme.headlineLarge;
      case ShadcnTypographyVariant.h5:
        return typographyTheme.h5Style ?? materialTheme.textTheme.headlineMedium;
      case ShadcnTypographyVariant.h6:
        return typographyTheme.h6Style ?? materialTheme.textTheme.headlineSmall;
      case ShadcnTypographyVariant.bodyLarge:
        return typographyTheme.bodyLargeStyle ?? materialTheme.textTheme.bodyLarge;
      case ShadcnTypographyVariant.bodyMedium:
        return typographyTheme.bodyMediumStyle ?? materialTheme.textTheme.bodyMedium;
      case ShadcnTypographyVariant.bodySmall:
        return typographyTheme.bodySmallStyle ?? materialTheme.textTheme.bodySmall;
      case ShadcnTypographyVariant.displayLarge:
        return typographyTheme.displayLargeStyle ?? materialTheme.textTheme.displayLarge;
      case ShadcnTypographyVariant.displayMedium:
        return typographyTheme.displayMediumStyle ?? materialTheme.textTheme.displayMedium;
      case ShadcnTypographyVariant.displaySmall:
        return typographyTheme.displaySmallStyle ?? materialTheme.textTheme.displaySmall;
      case ShadcnTypographyVariant.labelLarge:
        return typographyTheme.labelLargeStyle ?? materialTheme.textTheme.labelLarge;
      case ShadcnTypographyVariant.labelMedium:
        return typographyTheme.labelMediumStyle ?? materialTheme.textTheme.labelMedium;
      case ShadcnTypographyVariant.labelSmall:
        return typographyTheme.labelSmallStyle ?? materialTheme.textTheme.labelSmall;
      case ShadcnTypographyVariant.code:
        return typographyTheme.codeStyle;
      case ShadcnTypographyVariant.muted:
        return typographyTheme.mutedStyle;
      case ShadcnTypographyVariant.lead:
        return typographyTheme.leadStyle;
      case ShadcnTypographyVariant.small:
        return typographyTheme.smallStyle;
    }
  }
}

enum ShadcnTypographyVariant {
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  bodyLarge,
  bodyMedium,
  bodySmall,
  displayLarge,
  displayMedium,
  displaySmall,
  labelLarge,
  labelMedium,
  labelSmall,
  code,
  muted,
  lead,
  small,
}