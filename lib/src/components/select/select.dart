/// Shadcn-styled select and combobox components with Material Design integration.
/// 
/// This library provides:
/// - [ShadcnSelect] - A styled dropdown select component
/// - [ShadcnCombobox] - A searchable select component with filtering
/// - [ShadcnSelectOption] - Data model for select options
/// - [ShadcnSelectTheme] - Theme extension for customization
/// 
/// Both components feature:
/// - Full keyboard navigation support
/// - Accessibility compliance
/// - Theme-aware styling with shadcn design principles
/// - Material Design integration
/// - Smooth animations and transitions

export 'shadcn_select.dart';
export 'shadcn_combobox.dart';