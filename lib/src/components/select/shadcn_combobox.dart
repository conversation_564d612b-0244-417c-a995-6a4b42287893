import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_select_theme.dart';
import '../../constants/shadcn_tokens.dart';
import 'shadcn_select.dart';

/// A shadcn-styled combobox component with search and filtering capabilities.
/// 
/// This component extends the select functionality with search input, filtering,
/// and keyboard navigation. It maintains shadcn design principles while providing
/// rich interaction capabilities.
class ShadcnCombobox<T> extends ShadcnComponent with ShadcnComponentValidation {
  /// List of options to choose from
  final List<ShadcnSelectOption<T>> options;
  
  /// Currently selected value
  final T? value;
  
  /// Callback when value changes
  final ValueChanged<T?>? onChanged;
  
  /// Placeholder text when no value is selected
  final String? placeholder;
  
  /// Placeholder text for the search input
  final String? searchPlaceholder;
  
  /// Whether the combobox is enabled
  final bool enabled;
  
  /// Whether to show the dropdown arrow
  final bool? showArrow;
  
  /// Whether to show the clear button
  final bool? showClearButton;
  
  /// Whether to allow multiple selection
  final bool? allowMultiSelect;
  
  /// Whether search is case sensitive
  final bool? caseSensitiveSearch;
  
  /// Custom width for the combobox
  final double? width;
  
  /// Custom height for the combobox
  final double? height;
  
  /// Custom filter function for searching options
  final bool Function(ShadcnSelectOption<T>, String)? filterFunction;
  
  /// Custom search input builder
  final Widget Function(BuildContext, TextEditingController, VoidCallback)? searchBuilder;
  
  /// Custom dropdown menu builder
  final Widget Function(BuildContext, List<ShadcnSelectOption<T>>, String)? dropdownBuilder;
  
  /// Focus node for keyboard navigation
  final FocusNode? focusNode;
  
  /// Focus node for search input
  final FocusNode? searchFocusNode;
  
  /// Semantic label for accessibility
  final String? semanticLabel;
  
  /// Tooltip text
  final String? tooltip;
  
  /// Maximum height for the dropdown menu
  final double? dropdownMaxHeight;
  
  /// Custom animation duration
  final Duration? animationDuration;
  
  /// Callback when search text changes
  final ValueChanged<String>? onSearchChanged;
  
  /// Initial search text
  final String? initialSearchText;
  
  const ShadcnCombobox({
    super.key,
    required this.options,
    this.value,
    this.onChanged,
    this.placeholder,
    this.searchPlaceholder,
    this.enabled = true,
    this.showArrow,
    this.showClearButton,
    this.allowMultiSelect,
    this.caseSensitiveSearch,
    this.width,
    this.height,
    this.filterFunction,
    this.searchBuilder,
    this.dropdownBuilder,
    this.focusNode,
    this.searchFocusNode,
    this.semanticLabel,
    this.tooltip,
    this.dropdownMaxHeight,
    this.animationDuration,
    this.onSearchChanged,
    this.initialSearchText,
  });
  
  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    final selectTheme = resolveTheme<ShadcnSelectTheme>(
      context,
      ShadcnSelectTheme.defaultTheme,
    );
    
    // Validate component properties
    validateRequiredProperties({
      'options': options,
    });
    
    validateAccessibility(
      semanticLabel: semanticLabel,
      tooltip: tooltip,
      componentName: 'ShadcnCombobox',
    );
    
    // Find selected option
    final selectedOption = value != null
        ? options.cast<ShadcnSelectOption<T>?>().firstWhere(
            (option) => option?.value == value,
            orElse: () => null,
          )
        : null;
    
    // Resolve dimensions
    final resolvedHeight = resolveDouble(
      context,
      height,
      selectTheme.triggerHeight ?? ShadcnTokens.inputHeightMd,
    );
    
    final resolvedWidth = width;
    
    // Build combobox widget
    Widget comboboxWidget = _ShadcnComboboxTrigger<T>(
      value: value,
      options: options,
      selectedOption: selectedOption,
      placeholder: placeholder,
      searchPlaceholder: searchPlaceholder,
      enabled: enabled,
      onChanged: onChanged,
      selectTheme: selectTheme,
      height: resolvedHeight,
      width: resolvedWidth,
      showArrow: showArrow ?? selectTheme.showArrow ?? true,
      showClearButton: showClearButton ?? selectTheme.showClearButton ?? true,
      allowMultiSelect: allowMultiSelect ?? selectTheme.allowMultiSelect ?? false,
      caseSensitiveSearch: caseSensitiveSearch ?? selectTheme.caseSensitiveSearch ?? false,
      filterFunction: filterFunction,
      searchBuilder: searchBuilder,
      dropdownBuilder: dropdownBuilder,
      focusNode: focusNode,
      searchFocusNode: searchFocusNode,
      dropdownMaxHeight: dropdownMaxHeight ?? selectTheme.dropdownMaxHeight,
      animationDuration: animationDuration ?? selectTheme.animationDuration,
      onSearchChanged: onSearchChanged,
      initialSearchText: initialSearchText,
    );
    
    // Add semantic label if provided
    if (semanticLabel != null) {
      comboboxWidget = Semantics(
        label: semanticLabel,
        textField: true,
        child: comboboxWidget,
      );
    }
    
    // Add tooltip if provided
    if (tooltip != null) {
      comboboxWidget = Tooltip(
        message: tooltip,
        child: comboboxWidget,
      );
    }
    
    return comboboxWidget;
  }
}

/// Internal widget for the combobox trigger
class _ShadcnComboboxTrigger<T> extends StatefulWidget {
  final T? value;
  final List<ShadcnSelectOption<T>> options;
  final ShadcnSelectOption<T>? selectedOption;
  final String? placeholder;
  final String? searchPlaceholder;
  final bool enabled;
  final ValueChanged<T?>? onChanged;
  final ShadcnSelectTheme selectTheme;
  final double height;
  final double? width;
  final bool showArrow;
  final bool showClearButton;
  final bool allowMultiSelect;
  final bool caseSensitiveSearch;
  final bool Function(ShadcnSelectOption<T>, String)? filterFunction;
  final Widget Function(BuildContext, TextEditingController, VoidCallback)? searchBuilder;
  final Widget Function(BuildContext, List<ShadcnSelectOption<T>>, String)? dropdownBuilder;
  final FocusNode? focusNode;
  final FocusNode? searchFocusNode;
  final double? dropdownMaxHeight;
  final Duration? animationDuration;
  final ValueChanged<String>? onSearchChanged;
  final String? initialSearchText;
  
  const _ShadcnComboboxTrigger({
    required this.value,
    required this.options,
    required this.selectedOption,
    required this.placeholder,
    required this.searchPlaceholder,
    required this.enabled,
    required this.onChanged,
    required this.selectTheme,
    required this.height,
    required this.width,
    required this.showArrow,
    required this.showClearButton,
    required this.allowMultiSelect,
    required this.caseSensitiveSearch,
    required this.filterFunction,
    required this.searchBuilder,
    required this.dropdownBuilder,
    required this.focusNode,
    required this.searchFocusNode,
    required this.dropdownMaxHeight,
    required this.animationDuration,
    required this.onSearchChanged,
    required this.initialSearchText,
  });
  
  @override
  State<_ShadcnComboboxTrigger<T>> createState() => _ShadcnComboboxTriggerState<T>();
}

class _ShadcnComboboxTriggerState<T> extends State<_ShadcnComboboxTrigger<T>>
    with SingleTickerProviderStateMixin {
  late FocusNode _focusNode;
  late FocusNode _searchFocusNode;
  late TextEditingController _searchController;
  bool _isHovered = false;
  bool _isFocused = false;
  bool _isDropdownOpen = false;
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();
  List<ShadcnSelectOption<T>> _filteredOptions = [];
  int _highlightedIndex = -1;
  
  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _searchFocusNode = widget.searchFocusNode ?? FocusNode();
    _searchController = TextEditingController(text: widget.initialSearchText ?? '');
    _filteredOptions = widget.options;
    
    _focusNode.addListener(_onFocusChanged);
    _searchFocusNode.addListener(_onSearchFocusChanged);
    _searchController.addListener(_onSearchChanged);
  }
  
  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChanged);
    _searchFocusNode.removeListener(_onSearchFocusChanged);
    _searchController.removeListener(_onSearchChanged);
    
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    if (widget.searchFocusNode == null) {
      _searchFocusNode.dispose();
    }
    _searchController.dispose();
    _overlayEntry?.remove();
    super.dispose();
  }
  
  void _onFocusChanged() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
    
    if (!_isFocused && !_searchFocusNode.hasFocus) {
      _closeDropdown();
    }
  }
  
  void _onSearchFocusChanged() {
    if (_searchFocusNode.hasFocus && !_isDropdownOpen) {
      _openDropdown();
    }
    if (!_searchFocusNode.hasFocus && !_focusNode.hasFocus) {
      _closeDropdown();
    }
  }
  
  void _onSearchChanged() {
    final searchText = _searchController.text;
    widget.onSearchChanged?.call(searchText);
    _filterOptions(searchText);
  }
  
  void _filterOptions(String searchText) {
    if (searchText.isEmpty) {
      setState(() {
        _filteredOptions = widget.options;
        _highlightedIndex = -1;
      });
      return;
    }
    
    final filtered = widget.options.where((option) {
      return _filterOption(option, searchText);
    }).toList();
    
    setState(() {
      _filteredOptions = filtered;
      _highlightedIndex = filtered.isNotEmpty ? 0 : -1;
    });
  }
  
  bool _filterOption(ShadcnSelectOption<T> option, String searchText) {
    if (widget.filterFunction != null) {
      return widget.filterFunction!(option, searchText);
    }
    
    final optionText = widget.caseSensitiveSearch ? option.label : option.label.toLowerCase();
    final search = widget.caseSensitiveSearch ? searchText : searchText.toLowerCase();
    
    return optionText.contains(search);
  }
  
  void _onTap() {
    if (!widget.enabled) return;
    
    HapticFeedback.selectionClick();
    _searchFocusNode.requestFocus();
    
    if (_isDropdownOpen) {
      _closeDropdown();
    } else {
      _openDropdown();
    }
  }
  
  void _openDropdown() {
    if (_overlayEntry != null) return;
    
    setState(() => _isDropdownOpen = true);
    
    final overlay = Overlay.of(context);
    _overlayEntry = OverlayEntry(
      builder: (context) => _ShadcnComboboxDropdown<T>(
        layerLink: _layerLink,
        options: _filteredOptions,
        selectedValue: widget.value,
        selectTheme: widget.selectTheme,
        searchController: _searchController,
        searchFocusNode: _searchFocusNode,
        highlightedIndex: _highlightedIndex,
        onHighlightChanged: (index) => setState(() => _highlightedIndex = index),
        onSelected: (option) {
          widget.onChanged?.call(option?.value);
          if (!widget.allowMultiSelect) {
            _closeDropdown();
          }
        },
        onClose: _closeDropdown,
        maxHeight: widget.dropdownMaxHeight,
        animationDuration: widget.animationDuration,
        searchPlaceholder: widget.searchPlaceholder,
        customSearchBuilder: widget.searchBuilder,
        customDropdownBuilder: widget.dropdownBuilder,
        onKeyEvent: _handleKeyEvent,
      ),
    );
    
    overlay.insert(_overlayEntry!);
  }
  
  void _closeDropdown() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    setState(() => _isDropdownOpen = false);
  }
  
  bool _handleKeyEvent(KeyEvent event) {
    if (event is! KeyDownEvent) return false;
    
    switch (event.logicalKey) {
      case LogicalKeyboardKey.arrowDown:
        if (_highlightedIndex < _filteredOptions.length - 1) {
          setState(() => _highlightedIndex++);
        }
        return true;
        
      case LogicalKeyboardKey.arrowUp:
        if (_highlightedIndex > 0) {
          setState(() => _highlightedIndex--);
        }
        return true;
        
      case LogicalKeyboardKey.enter:
        if (_highlightedIndex >= 0 && _highlightedIndex < _filteredOptions.length) {
          final option = _filteredOptions[_highlightedIndex];
          widget.onChanged?.call(option.value);
          if (!widget.allowMultiSelect) {
            _closeDropdown();
          }
        }
        return true;
        
      case LogicalKeyboardKey.escape:
        _closeDropdown();
        return true;
        
      default:
        return false;
    }
  }
  
  void _onClear() {
    if (!widget.enabled) return;
    
    HapticFeedback.selectionClick();
    widget.onChanged?.call(null);
    _searchController.clear();
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    // Resolve colors
    final backgroundColor = widget.enabled
        ? widget.selectTheme.triggerBackground ?? theme.colorScheme.surface
        : widget.selectTheme.triggerDisabledBackground ?? theme.colorScheme.surfaceContainerHighest;
    
    final foregroundColor = widget.enabled
        ? widget.selectTheme.triggerForeground ?? theme.colorScheme.onSurface
        : widget.selectTheme.triggerDisabledForeground ?? theme.colorScheme.onSurface.withAlpha(128);
    
    final borderColor = widget.selectTheme.triggerBorder ?? theme.colorScheme.outline;
    final arrowColor = widget.selectTheme.arrowColor ?? theme.colorScheme.onSurface.withAlpha(179);
    final clearColor = widget.selectTheme.clearButtonColor ?? theme.colorScheme.onSurface.withAlpha(179);
    
    // Resolve styling
    final borderRadius = widget.selectTheme.triggerBorderRadius ?? 
                        ShadcnTokens.borderRadius(ShadcnTokens.radiusMd);
    
    final padding = widget.selectTheme.triggerPadding ?? 
                    ShadcnTokens.paddingHorizontal(ShadcnTokens.spacing3);
    
    final textStyle = widget.selectTheme.resolveTriggerTextStyle(context).copyWith(
      color: foregroundColor,
    );
    
    final currentBackgroundColor = _getCurrentBackgroundColor(backgroundColor);
    final currentBorderColor = _getCurrentBorderColor(borderColor, theme);
    
    return CompositedTransformTarget(
      link: _layerLink,
      child: MouseRegion(
        onEnter: (_) => setState(() => _isHovered = true),
        onExit: (_) => setState(() => _isHovered = false),
        cursor: widget.enabled ? SystemMouseCursors.text : SystemMouseCursors.basic,
        child: GestureDetector(
          onTap: _onTap,
          child: Focus(
            focusNode: _focusNode,
            child: AnimatedContainer(
              duration: widget.animationDuration ?? ShadcnTokens.durationFast,
              curve: Curves.easeInOut,
              height: widget.height,
              width: widget.width,
              padding: padding,
              decoration: BoxDecoration(
                color: currentBackgroundColor,
                border: Border.all(
                  color: currentBorderColor,
                  width: widget.selectTheme.triggerBorderWidth ?? ShadcnTokens.borderWidth,
                ),
                borderRadius: borderRadius,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: _buildContent(textStyle),
                  ),
                  if (widget.showClearButton && widget.value != null) ...[
                    SizedBox(width: ShadcnTokens.spacing2),
                    GestureDetector(
                      onTap: _onClear,
                      child: Icon(
                        Icons.close,
                        size: widget.selectTheme.clearButtonSize ?? ShadcnTokens.iconSizeSm,
                        color: clearColor,
                      ),
                    ),
                  ],
                  if (widget.showArrow) ...[
                    SizedBox(width: ShadcnTokens.spacing2),
                    AnimatedRotation(
                      duration: widget.animationDuration ?? ShadcnTokens.durationFast,
                      turns: _isDropdownOpen ? 0.5 : 0,
                      child: Icon(
                        Icons.keyboard_arrow_down,
                        size: widget.selectTheme.arrowSize ?? ShadcnTokens.iconSizeSm,
                        color: arrowColor,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
  
  Color _getCurrentBackgroundColor(Color baseColor) {
    if (!widget.enabled) {
      return widget.selectTheme.triggerDisabledBackground ?? baseColor;
    }
    if (_isFocused || _searchFocusNode.hasFocus) {
      return widget.selectTheme.triggerFocusBackground ?? baseColor;
    }
    if (_isHovered) {
      return widget.selectTheme.triggerHoverBackground ?? baseColor;
    }
    return baseColor;
  }
  
  Color _getCurrentBorderColor(Color baseColor, ThemeData theme) {
    if (!widget.enabled) {
      return baseColor.withAlpha(128);
    }
    if (_isFocused || _searchFocusNode.hasFocus) {
      return widget.selectTheme.triggerFocusBorder ?? theme.colorScheme.primary;
    }
    return baseColor;
  }
  
  Widget _buildContent(TextStyle textStyle) {
    if (widget.selectedOption != null) {
      return Row(
        children: [
          if (widget.selectedOption!.leading != null) ...[
            widget.selectedOption!.leading!,
            SizedBox(width: ShadcnTokens.spacing2),
          ],
          Expanded(
            child: Text(
              widget.selectedOption!.label,
              style: textStyle,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      );
    }
    
    return Text(
      widget.placeholder ?? 'Search options...',
      style: textStyle.copyWith(
        color: textStyle.color?.withAlpha(179),
      ),
      overflow: TextOverflow.ellipsis,
    );
  }
}

/// Internal widget for the combobox dropdown
class _ShadcnComboboxDropdown<T> extends StatefulWidget {
  final LayerLink layerLink;
  final List<ShadcnSelectOption<T>> options;
  final T? selectedValue;
  final ShadcnSelectTheme selectTheme;
  final TextEditingController searchController;
  final FocusNode searchFocusNode;
  final int highlightedIndex;
  final ValueChanged<int> onHighlightChanged;
  final ValueChanged<ShadcnSelectOption<T>?> onSelected;
  final VoidCallback onClose;
  final double? maxHeight;
  final Duration? animationDuration;
  final String? searchPlaceholder;
  final Widget Function(BuildContext, TextEditingController, VoidCallback)? customSearchBuilder;
  final Widget Function(BuildContext, List<ShadcnSelectOption<T>>, String)? customDropdownBuilder;
  final bool Function(KeyEvent)? onKeyEvent;
  
  const _ShadcnComboboxDropdown({
    required this.layerLink,
    required this.options,
    required this.selectedValue,
    required this.selectTheme,
    required this.searchController,
    required this.searchFocusNode,
    required this.highlightedIndex,
    required this.onHighlightChanged,
    required this.onSelected,
    required this.onClose,
    required this.maxHeight,
    required this.animationDuration,
    required this.searchPlaceholder,
    required this.customSearchBuilder,
    required this.customDropdownBuilder,
    required this.onKeyEvent,
  });
  
  @override
  State<_ShadcnComboboxDropdown<T>> createState() => _ShadcnComboboxDropdownState<T>();
}

class _ShadcnComboboxDropdownState<T> extends State<_ShadcnComboboxDropdown<T>>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;
  
  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: widget.animationDuration ?? ShadcnTokens.durationFast,
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.95,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _animationController.forward();
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onClose,
      child: Stack(
        children: [
          // Invisible barrier to detect taps outside
          Positioned.fill(
            child: Container(color: Colors.transparent),
          ),
          
          // Dropdown menu
          CompositedTransformFollower(
            link: widget.layerLink,
            showWhenUnlinked: false,
            offset: Offset(0, 4),
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimation.value,
                  alignment: Alignment.topCenter,
                  child: Opacity(
                    opacity: _opacityAnimation.value,
                    child: child,
                  ),
                );
              },
              child: _buildDropdownContent(),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildDropdownContent() {
    final theme = Theme.of(context);
    
    return KeyboardListener(
      focusNode: FocusNode(),
      onKeyEvent: (event) {
        if (widget.onKeyEvent != null) {
          widget.onKeyEvent!(event);
        }
      },
      child: Material(
        elevation: widget.selectTheme.dropdownElevation ?? ShadcnTokens.elevationMd,
        color: widget.selectTheme.dropdownBackground ?? theme.colorScheme.surfaceContainer,
        borderRadius: widget.selectTheme.dropdownBorderRadius ?? 
                     ShadcnTokens.borderRadius(ShadcnTokens.radiusMd),
        shadowColor: widget.selectTheme.dropdownShadow ?? theme.colorScheme.shadow,
        child: Container(
          constraints: BoxConstraints(
            maxHeight: widget.maxHeight ?? 300.0,
            minWidth: widget.selectTheme.dropdownMinWidth ?? 180.0,
          ),
          decoration: BoxDecoration(
            border: Border.all(
              color: widget.selectTheme.dropdownBorder ?? theme.colorScheme.outline,
              width: widget.selectTheme.dropdownBorderWidth ?? ShadcnTokens.borderWidth,
            ),
            borderRadius: widget.selectTheme.dropdownBorderRadius ?? 
                           ShadcnTokens.borderRadius(ShadcnTokens.radiusMd),
          ),
          child: ClipRRect(
            borderRadius: widget.selectTheme.dropdownBorderRadius ?? 
                           ShadcnTokens.borderRadius(ShadcnTokens.radiusMd),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Search input
                _buildSearchInput(),
                
                // Separator
                Divider(
                  height: 1,
                  color: widget.selectTheme.dropdownBorder ?? theme.colorScheme.outline,
                ),
                
                // Options list
                Flexible(
                  child: _buildOptionsList(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildSearchInput() {
    if (widget.customSearchBuilder != null) {
      return widget.customSearchBuilder!(context, widget.searchController, widget.onClose);
    }
    
    final theme = Theme.of(context);
    
    return Container(
      height: widget.selectTheme.searchHeight ?? ShadcnTokens.inputHeightSm,
      padding: widget.selectTheme.searchPadding ?? 
               ShadcnTokens.paddingHorizontal(ShadcnTokens.spacing2),
      child: TextField(
        controller: widget.searchController,
        focusNode: widget.searchFocusNode,
        style: widget.selectTheme.resolveSearchTextStyle(context),
        decoration: InputDecoration(
          hintText: widget.searchPlaceholder ?? 'Search...',
          hintStyle: widget.selectTheme.resolveSearchPlaceholderStyle(context),
          border: InputBorder.none,
          isDense: true,
          contentPadding: EdgeInsets.zero,
          prefixIcon: Icon(
            Icons.search,
            size: ShadcnTokens.iconSizeSm,
            color: widget.selectTheme.searchPlaceholder ?? 
                   theme.colorScheme.onSurface.withAlpha(153),
          ),
        ),
      ),
    );
  }
  
  Widget _buildOptionsList() {
    if (widget.customDropdownBuilder != null) {
      return widget.customDropdownBuilder!(
        context,
        widget.options,
        widget.searchController.text,
      );
    }
    
    if (widget.options.isEmpty) {
      return _buildNoResultsMessage();
    }
    
    return ListView.builder(
      padding: widget.selectTheme.dropdownPadding ?? 
               ShadcnTokens.paddingAll(ShadcnTokens.spacing1),
      shrinkWrap: true,
      itemCount: widget.options.length,
      itemBuilder: (context, index) {
        final option = widget.options[index];
        return _ShadcnComboboxItem<T>(
          option: option,
          isSelected: option.value == widget.selectedValue,
          isHighlighted: index == widget.highlightedIndex,
          selectTheme: widget.selectTheme,
          onTap: () => widget.onSelected(option),
          onHover: () => widget.onHighlightChanged(index),
        );
      },
    );
  }
  
  Widget _buildNoResultsMessage() {
    final theme = Theme.of(context);
    
    return Padding(
      padding: ShadcnTokens.paddingAll(ShadcnTokens.spacing4),
      child: Center(
        child: Text(
          'No results found',
          style: widget.selectTheme.resolveItemTextStyle(context).copyWith(
            color: theme.colorScheme.onSurface.withAlpha(153),
          ),
        ),
      ),
    );
  }
}

/// Internal widget for combobox dropdown items
class _ShadcnComboboxItem<T> extends StatelessWidget {
  final ShadcnSelectOption<T> option;
  final bool isSelected;
  final bool isHighlighted;
  final ShadcnSelectTheme selectTheme;
  final VoidCallback onTap;
  final VoidCallback onHover;
  
  const _ShadcnComboboxItem({
    required this.option,
    required this.isSelected,
    required this.isHighlighted,
    required this.selectTheme,
    required this.onTap,
    required this.onHover,
  });
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    Color backgroundColor;
    Color textColor;
    
    if (!option.enabled) {
      backgroundColor = selectTheme.itemDisabledBackground ?? Colors.transparent;
      textColor = selectTheme.itemDisabledForeground ?? 
                  theme.colorScheme.onSurface.withAlpha(128);
    } else if (isSelected) {
      backgroundColor = selectTheme.itemSelectedBackground ?? theme.colorScheme.primary;
      textColor = selectTheme.itemSelectedForeground ?? theme.colorScheme.onPrimary;
    } else if (isHighlighted) {
      backgroundColor = selectTheme.itemFocusBackground ?? 
                       theme.colorScheme.surfaceContainerHighest;
      textColor = selectTheme.itemForeground ?? theme.colorScheme.onSurface;
    } else {
      backgroundColor = selectTheme.itemBackground ?? Colors.transparent;
      textColor = selectTheme.itemForeground ?? theme.colorScheme.onSurface;
    }
    
    final textStyle = selectTheme.resolveItemTextStyle(context).copyWith(
      color: textColor,
    );
    
    return MouseRegion(
      onEnter: (_) => onHover(),
      cursor: option.enabled ? SystemMouseCursors.click : SystemMouseCursors.basic,
      child: GestureDetector(
        onTap: option.enabled ? onTap : null,
        child: AnimatedContainer(
          duration: ShadcnTokens.durationFast,
          curve: Curves.easeInOut,
          height: selectTheme.itemHeight ?? 32.0,
          padding: selectTheme.itemPadding ?? 
                   ShadcnTokens.paddingHorizontal(ShadcnTokens.spacing2),
          margin: const EdgeInsets.symmetric(vertical: 1),
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: selectTheme.itemBorderRadius ?? 
                           ShadcnTokens.borderRadius(ShadcnTokens.radiusSm),
          ),
          child: Row(
            children: [
              if (option.leading != null) ...[
                option.leading!,
                SizedBox(width: ShadcnTokens.spacing2),
              ],
              Expanded(
                child: Text(
                  option.label,
                  style: textStyle,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (option.trailing != null) ...[
                SizedBox(width: ShadcnTokens.spacing2),
                option.trailing!,
              ],
              if (isSelected && option.trailing == null) ...[
                SizedBox(width: ShadcnTokens.spacing2),
                Icon(
                  Icons.check,
                  size: ShadcnTokens.iconSizeSm,
                  color: textColor,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}