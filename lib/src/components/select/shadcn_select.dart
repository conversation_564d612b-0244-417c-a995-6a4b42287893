import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_select_theme.dart';
import '../../constants/shadcn_tokens.dart';

/// Data model for select options
class ShadcnSelectOption<T> {
  final T value;
  final String label;
  final Widget? leading;
  final Widget? trailing;
  final bool enabled;
  
  const ShadcnSelectOption({
    required this.value,
    required this.label,
    this.leading,
    this.trailing,
    this.enabled = true,
  });
  
  @override
  bool operator ==(Object other) =>
    identical(this, other) ||
    other is ShadcnSelectOption &&
      runtimeType == other.runtimeType &&
      value == other.value;
  
  @override
  int get hashCode => value.hashCode;
}

/// A shadcn-styled select component with Material dropdown integration.
/// 
/// This component provides a consistent select interface that follows shadcn
/// design principles while maintaining full Material Design compatibility.
/// It supports keyboard navigation, accessibility, and theming.
class ShadcnSelect<T> extends ShadcnComponent with ShadcnComponentValidation {
  /// List of options to choose from
  final List<ShadcnSelectOption<T>> options;
  
  /// Currently selected value
  final T? value;
  
  /// Callback when value changes
  final ValueChanged<T?>? onChanged;
  
  /// Placeholder text when no value is selected
  final String? placeholder;
  
  /// Whether the select is enabled
  final bool enabled;
  
  /// Whether to show the dropdown arrow
  final bool? showArrow;
  
  /// Custom width for the select trigger
  final double? width;
  
  /// Custom height for the select trigger
  final double? height;
  
  /// Custom dropdown menu builder
  final Widget Function(BuildContext, List<ShadcnSelectOption<T>>)? dropdownBuilder;
  
  /// Focus node for keyboard navigation
  final FocusNode? focusNode;
  
  /// Semantic label for accessibility
  final String? semanticLabel;
  
  /// Tooltip text
  final String? tooltip;
  
  /// Maximum height for the dropdown menu
  final double? dropdownMaxHeight;
  
  /// Custom animation duration
  final Duration? animationDuration;
  
  const ShadcnSelect({
    super.key,
    required this.options,
    this.value,
    this.onChanged,
    this.placeholder,
    this.enabled = true,
    this.showArrow,
    this.width,
    this.height,
    this.dropdownBuilder,
    this.focusNode,
    this.semanticLabel,
    this.tooltip,
    this.dropdownMaxHeight,
    this.animationDuration,
  });
  
  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    final selectTheme = resolveTheme<ShadcnSelectTheme>(
      context,
      ShadcnSelectTheme.defaultTheme,
    );
    
    // Validate component properties
    validateRequiredProperties({
      'options': options,
    });
    
    validateAccessibility(
      semanticLabel: semanticLabel,
      tooltip: tooltip,
      componentName: 'ShadcnSelect',
    );
    
    // Find selected option
    final selectedOption = value != null
        ? options.cast<ShadcnSelectOption<T>?>().firstWhere(
            (option) => option?.value == value,
            orElse: () => null,
          )
        : null;
    
    // Resolve dimensions
    final resolvedHeight = resolveDouble(
      context,
      height,
      selectTheme.triggerHeight ?? ShadcnTokens.inputHeightMd,
    );
    
    final resolvedWidth = width;
    
    // Resolve colors
    final backgroundColor = enabled
        ? resolveColor(
            context,
            selectTheme.triggerBackground,
            (theme) => theme.colorScheme.surface,
          )
        : resolveColor(
            context,
            selectTheme.triggerDisabledBackground,
            (theme) => theme.colorScheme.surfaceContainerHighest,
          );
    
    final foregroundColor = enabled
        ? resolveColor(
            context,
            selectTheme.triggerForeground,
            (theme) => theme.colorScheme.onSurface,
          )
        : resolveColor(
            context,
            selectTheme.triggerDisabledForeground,
            (theme) => theme.colorScheme.onSurface.withAlpha(128),
          );
    
    final borderColor = resolveColor(
      context,
      selectTheme.triggerBorder,
      (theme) => theme.colorScheme.outline,
    );
    
    final arrowColor = resolveColor(
      context,
      selectTheme.arrowColor,
      (theme) => theme.colorScheme.onSurface.withAlpha(179),
    );
    
    // Resolve styling
    final borderRadius = resolveBorderRadius(
      context,
      selectTheme.triggerBorderRadius,
      ShadcnTokens.borderRadius(ShadcnTokens.radiusMd),
    );
    
    final padding = resolveSpacing(
      context,
      selectTheme.triggerPadding,
      ShadcnTokens.paddingHorizontal(ShadcnTokens.spacing3),
    );
    
    final textStyle = selectTheme.resolveTriggerTextStyle(context).copyWith(
      color: foregroundColor,
    );
    
    // Build dropdown button
    Widget selectWidget = _ShadcnSelectTrigger<T>(
      value: value,
      options: options,
      selectedOption: selectedOption,
      placeholder: placeholder,
      enabled: enabled,
      onChanged: onChanged,
      selectTheme: selectTheme,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      borderColor: borderColor,
      arrowColor: arrowColor,
      borderRadius: borderRadius,
      padding: padding,
      textStyle: textStyle,
      height: resolvedHeight,
      width: resolvedWidth,
      showArrow: showArrow ?? selectTheme.showArrow ?? true,
      focusNode: focusNode,
      dropdownMaxHeight: dropdownMaxHeight ?? selectTheme.dropdownMaxHeight,
      animationDuration: animationDuration ?? selectTheme.animationDuration,
      dropdownBuilder: dropdownBuilder,
    );
    
    // Add semantic label if provided
    if (semanticLabel != null) {
      selectWidget = Semantics(
        label: semanticLabel,
        child: selectWidget,
      );
    }
    
    // Add tooltip if provided
    if (tooltip != null) {
      selectWidget = Tooltip(
        message: tooltip,
        child: selectWidget,
      );
    }
    
    return selectWidget;
  }
}

/// Internal widget for the select trigger button
class _ShadcnSelectTrigger<T> extends StatefulWidget {
  final T? value;
  final List<ShadcnSelectOption<T>> options;
  final ShadcnSelectOption<T>? selectedOption;
  final String? placeholder;
  final bool enabled;
  final ValueChanged<T?>? onChanged;
  final ShadcnSelectTheme selectTheme;
  final Color backgroundColor;
  final Color foregroundColor;
  final Color borderColor;
  final Color arrowColor;
  final BorderRadius borderRadius;
  final EdgeInsets padding;
  final TextStyle textStyle;
  final double height;
  final double? width;
  final bool showArrow;
  final FocusNode? focusNode;
  final double? dropdownMaxHeight;
  final Duration? animationDuration;
  final Widget Function(BuildContext, List<ShadcnSelectOption<T>>)? dropdownBuilder;
  
  const _ShadcnSelectTrigger({
    required this.value,
    required this.options,
    required this.selectedOption,
    required this.placeholder,
    required this.enabled,
    required this.onChanged,
    required this.selectTheme,
    required this.backgroundColor,
    required this.foregroundColor,
    required this.borderColor,
    required this.arrowColor,
    required this.borderRadius,
    required this.padding,
    required this.textStyle,
    required this.height,
    required this.width,
    required this.showArrow,
    required this.focusNode,
    required this.dropdownMaxHeight,
    required this.animationDuration,
    required this.dropdownBuilder,
  });
  
  @override
  State<_ShadcnSelectTrigger<T>> createState() => _ShadcnSelectTriggerState<T>();
}

class _ShadcnSelectTriggerState<T> extends State<_ShadcnSelectTrigger<T>>
    with SingleTickerProviderStateMixin {
  late FocusNode _focusNode;
  bool _isHovered = false;
  bool _isFocused = false;
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();
  
  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChanged);
  }
  
  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChanged);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    _overlayEntry?.remove();
    super.dispose();
  }
  
  void _onFocusChanged() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
    
    if (!_isFocused) {
      _closeDropdown();
    }
  }
  
  void _onTap() {
    if (!widget.enabled) return;
    
    HapticFeedback.selectionClick();
    _focusNode.requestFocus();
    
    if (_overlayEntry != null) {
      _closeDropdown();
    } else {
      _openDropdown();
    }
  }
  
  void _openDropdown() {
    if (_overlayEntry != null) return;
    
    final overlay = Overlay.of(context);
    _overlayEntry = OverlayEntry(
      builder: (context) => _ShadcnSelectDropdown<T>(
        layerLink: _layerLink,
        options: widget.options,
        selectedValue: widget.value,
        selectTheme: widget.selectTheme,
        onSelected: (option) {
          widget.onChanged?.call(option?.value);
          _closeDropdown();
        },
        onClose: _closeDropdown,
        maxHeight: widget.dropdownMaxHeight,
        animationDuration: widget.animationDuration,
        customBuilder: widget.dropdownBuilder,
      ),
    );
    
    overlay.insert(_overlayEntry!);
  }
  
  void _closeDropdown() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
  
  Color get _currentBackgroundColor {
    if (!widget.enabled) {
      return widget.selectTheme.triggerDisabledBackground ?? widget.backgroundColor;
    }
    if (_isFocused) {
      return widget.selectTheme.triggerFocusBackground ?? widget.backgroundColor;
    }
    if (_isHovered) {
      return widget.selectTheme.triggerHoverBackground ?? widget.backgroundColor;
    }
    return widget.backgroundColor;
  }
  
  Color get _currentBorderColor {
    if (!widget.enabled) {
      return widget.borderColor.withAlpha(128);
    }
    if (_isFocused) {
      return widget.selectTheme.triggerFocusBorder ?? widget.borderColor;
    }
    return widget.borderColor;
  }
  
  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: MouseRegion(
        onEnter: (_) => setState(() => _isHovered = true),
        onExit: (_) => setState(() => _isHovered = false),
        cursor: widget.enabled ? SystemMouseCursors.click : SystemMouseCursors.basic,
        child: GestureDetector(
          onTap: _onTap,
          child: Focus(
            focusNode: _focusNode,
            child: AnimatedContainer(
              duration: widget.animationDuration ?? ShadcnTokens.durationFast,
              curve: Curves.easeInOut,
              height: widget.height,
              width: widget.width,
              padding: widget.padding,
              decoration: BoxDecoration(
                color: _currentBackgroundColor,
                border: Border.all(
                  color: _currentBorderColor,
                  width: widget.selectTheme.triggerBorderWidth ?? ShadcnTokens.borderWidth,
                ),
                borderRadius: widget.borderRadius,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: _buildContent(),
                  ),
                  if (widget.showArrow) ...[
                    SizedBox(width: ShadcnTokens.spacing2),
                    AnimatedRotation(
                      duration: widget.animationDuration ?? ShadcnTokens.durationFast,
                      turns: _overlayEntry != null ? 0.5 : 0,
                      child: Icon(
                        Icons.keyboard_arrow_down,
                        size: widget.selectTheme.arrowSize ?? ShadcnTokens.iconSizeSm,
                        color: widget.arrowColor,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildContent() {
    if (widget.selectedOption != null) {
      return Row(
        children: [
          if (widget.selectedOption!.leading != null) ...[
            widget.selectedOption!.leading!,
            SizedBox(width: ShadcnTokens.spacing2),
          ],
          Expanded(
            child: Text(
              widget.selectedOption!.label,
              style: widget.textStyle,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      );
    }
    
    return Text(
      widget.placeholder ?? 'Select an option...',
      style: widget.textStyle.copyWith(
        color: widget.textStyle.color?.withAlpha(179),
      ),
      overflow: TextOverflow.ellipsis,
    );
  }
}

/// Internal widget for the dropdown menu
class _ShadcnSelectDropdown<T> extends StatefulWidget {
  final LayerLink layerLink;
  final List<ShadcnSelectOption<T>> options;
  final T? selectedValue;
  final ShadcnSelectTheme selectTheme;
  final ValueChanged<ShadcnSelectOption<T>?> onSelected;
  final VoidCallback onClose;
  final double? maxHeight;
  final Duration? animationDuration;
  final Widget Function(BuildContext, List<ShadcnSelectOption<T>>)? customBuilder;
  
  const _ShadcnSelectDropdown({
    required this.layerLink,
    required this.options,
    required this.selectedValue,
    required this.selectTheme,
    required this.onSelected,
    required this.onClose,
    required this.maxHeight,
    required this.animationDuration,
    required this.customBuilder,
  });
  
  @override
  State<_ShadcnSelectDropdown<T>> createState() => _ShadcnSelectDropdownState<T>();
}

class _ShadcnSelectDropdownState<T> extends State<_ShadcnSelectDropdown<T>>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;
  
  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: widget.animationDuration ?? ShadcnTokens.durationFast,
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.95,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _animationController.forward();
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onClose,
      child: Stack(
        children: [
          // Invisible barrier to detect taps outside
          Positioned.fill(
            child: Container(color: Colors.transparent),
          ),
          
          // Dropdown menu
          CompositedTransformFollower(
            link: widget.layerLink,
            showWhenUnlinked: false,
            offset: Offset(0, 4),
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimation.value,
                  alignment: Alignment.topCenter,
                  child: Opacity(
                    opacity: _opacityAnimation.value,
                    child: child,
                  ),
                );
              },
              child: _buildDropdownContent(),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildDropdownContent() {
    if (widget.customBuilder != null) {
      return widget.customBuilder!(context, widget.options);
    }
    
    final theme = Theme.of(context);
    
    return Material(
      elevation: widget.selectTheme.dropdownElevation ?? ShadcnTokens.elevationMd,
      color: widget.selectTheme.dropdownBackground ?? theme.colorScheme.surfaceContainer,
      borderRadius: widget.selectTheme.dropdownBorderRadius ?? 
                   ShadcnTokens.borderRadius(ShadcnTokens.radiusMd),
      shadowColor: widget.selectTheme.dropdownShadow ?? theme.colorScheme.shadow,
      child: Container(
        constraints: BoxConstraints(
          maxHeight: widget.maxHeight ?? 300.0,
          minWidth: widget.selectTheme.dropdownMinWidth ?? 180.0,
        ),
        decoration: BoxDecoration(
          border: Border.all(
            color: widget.selectTheme.dropdownBorder ?? theme.colorScheme.outline,
            width: widget.selectTheme.dropdownBorderWidth ?? ShadcnTokens.borderWidth,
          ),
          borderRadius: widget.selectTheme.dropdownBorderRadius ?? 
                         ShadcnTokens.borderRadius(ShadcnTokens.radiusMd),
        ),
        child: ClipRRect(
          borderRadius: widget.selectTheme.dropdownBorderRadius ?? 
                         ShadcnTokens.borderRadius(ShadcnTokens.radiusMd),
          child: ListView.builder(
            padding: widget.selectTheme.dropdownPadding ?? 
                     ShadcnTokens.paddingAll(ShadcnTokens.spacing1),
            shrinkWrap: true,
            itemCount: widget.options.length,
            itemBuilder: (context, index) {
              final option = widget.options[index];
              return _ShadcnSelectItem<T>(
                option: option,
                isSelected: option.value == widget.selectedValue,
                selectTheme: widget.selectTheme,
                onTap: () => widget.onSelected(option),
              );
            },
          ),
        ),
      ),
    );
  }
}

/// Internal widget for dropdown menu items
class _ShadcnSelectItem<T> extends StatefulWidget {
  final ShadcnSelectOption<T> option;
  final bool isSelected;
  final ShadcnSelectTheme selectTheme;
  final VoidCallback onTap;
  
  const _ShadcnSelectItem({
    required this.option,
    required this.isSelected,
    required this.selectTheme,
    required this.onTap,
  });
  
  @override
  State<_ShadcnSelectItem<T>> createState() => _ShadcnSelectItemState<T>();
}

class _ShadcnSelectItemState<T> extends State<_ShadcnSelectItem<T>> {
  bool _isHovered = false;
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    Color backgroundColor;
    Color textColor;
    
    if (!widget.option.enabled) {
      backgroundColor = widget.selectTheme.itemDisabledBackground ?? Colors.transparent;
      textColor = widget.selectTheme.itemDisabledForeground ?? 
                  theme.colorScheme.onSurface.withAlpha(128);
    } else if (widget.isSelected) {
      backgroundColor = widget.selectTheme.itemSelectedBackground ?? theme.colorScheme.primary;
      textColor = widget.selectTheme.itemSelectedForeground ?? theme.colorScheme.onPrimary;
    } else if (_isHovered) {
      backgroundColor = widget.selectTheme.itemHoverBackground ?? 
                       theme.colorScheme.surfaceContainerHighest;
      textColor = widget.selectTheme.itemForeground ?? theme.colorScheme.onSurface;
    } else {
      backgroundColor = widget.selectTheme.itemBackground ?? Colors.transparent;
      textColor = widget.selectTheme.itemForeground ?? theme.colorScheme.onSurface;
    }
    
    final textStyle = widget.selectTheme.resolveItemTextStyle(context).copyWith(
      color: textColor,
    );
    
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      cursor: widget.option.enabled ? SystemMouseCursors.click : SystemMouseCursors.basic,
      child: GestureDetector(
        onTap: widget.option.enabled ? widget.onTap : null,
        child: AnimatedContainer(
          duration: ShadcnTokens.durationFast,
          curve: Curves.easeInOut,
          height: widget.selectTheme.itemHeight ?? 32.0,
          padding: widget.selectTheme.itemPadding ?? 
                   ShadcnTokens.paddingHorizontal(ShadcnTokens.spacing2),
          margin: const EdgeInsets.symmetric(vertical: 1),
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: widget.selectTheme.itemBorderRadius ?? 
                           ShadcnTokens.borderRadius(ShadcnTokens.radiusSm),
          ),
          child: Row(
            children: [
              if (widget.option.leading != null) ...[
                widget.option.leading!,
                SizedBox(width: ShadcnTokens.spacing2),
              ],
              Expanded(
                child: Text(
                  widget.option.label,
                  style: textStyle,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (widget.option.trailing != null) ...[
                SizedBox(width: ShadcnTokens.spacing2),
                widget.option.trailing!,
              ],
              if (widget.isSelected && widget.option.trailing == null) ...[
                SizedBox(width: ShadcnTokens.spacing2),
                Icon(
                  Icons.check,
                  size: ShadcnTokens.iconSizeSm,
                  color: textColor,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}