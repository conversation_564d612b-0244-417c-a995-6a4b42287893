import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_accordion_theme.dart';
import '../../constants/shadcn_tokens.dart';

/// A shadcn-styled accordion widget that provides collapsible content areas.
/// 
/// The accordion follows the shadcn design system and provides smooth animations,
/// keyboard navigation, and accessibility features. It supports both single and
/// multiple item expansion modes.
/// 
/// This component integrates with <PERSON><PERSON><PERSON>'s theme system and automatically
/// adapts to light/dark mode changes.
/// 
/// ## Usage
/// 
/// ```dart
/// ShadcnAccordion(
///   items: [
///     ShadcnAccordionItem(
///       value: 'item-1',
///       header: Text('Section 1'),
///       content: Text('This is the content for section 1.'),
///     ),
///     ShadcnAccordionItem(
///       value: 'item-2', 
///       header: Text('Section 2'),
///       content: Text('This is the content for section 2.'),
///     ),
///   ],
/// )
/// ```
class ShadcnAccordion extends ShadcnComponent {
  /// List of accordion items to display
  final List<ShadcnAccordionItem> items;
  
  /// Currently expanded items (for multiple expansion mode)
  final Set<String>? expandedValues;
  
  /// Currently expanded item (for single expansion mode)
  final String? expandedValue;
  
  /// Whether multiple items can be expanded at once
  final bool allowMultipleExpanded;
  
  /// Called when an item is expanded or collapsed
  final ValueChanged<Set<String>>? onExpandedValuesChanged;
  
  /// Called when an item is expanded or collapsed (single mode)
  final ValueChanged<String?>? onExpandedValueChanged;
  
  /// Whether the accordion is collapsible (can close the last expanded item)
  final bool collapsible;
  
  /// Custom theme data for the accordion
  final ShadcnAccordionTheme? theme;
  
  /// Whether the accordion is disabled
  final bool disabled;
  
  /// Semantic label for accessibility
  final String? semanticLabel;

  const ShadcnAccordion({
    super.key,
    required this.items,
    this.expandedValues,
    this.expandedValue,
    this.allowMultipleExpanded = false,
    this.onExpandedValuesChanged,
    this.onExpandedValueChanged,
    this.collapsible = false,
    this.theme,
    this.disabled = false,
    this.semanticLabel,
  }) : assert(
         allowMultipleExpanded == false || (expandedValue == null && onExpandedValueChanged == null),
         'Cannot use expandedValue/onExpandedValueChanged with allowMultipleExpanded=true',
       ),
       assert(
         allowMultipleExpanded == true || (expandedValues == null && onExpandedValuesChanged == null),
         'Cannot use expandedValues/onExpandedValuesChanged with allowMultipleExpanded=false',
       );

  @override
  Widget buildWithTheme(BuildContext context, ThemeData materialTheme) {
    final accordionTheme = theme ?? 
        materialTheme.extension<ShadcnAccordionTheme>() ??
        ShadcnAccordionTheme.defaultTheme(materialTheme.colorScheme);
    
    // Determine currently expanded items
    final Set<String> currentlyExpanded = allowMultipleExpanded 
        ? (expandedValues ?? <String>{})
        : (expandedValue != null ? {expandedValue!} : <String>{});
    
    return Semantics(
      label: semanticLabel ?? 'Accordion',
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: items.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          final isExpanded = currentlyExpanded.contains(item.value);
          final isFirst = index == 0;
          final isLast = index == items.length - 1;
          
          return _AccordionItemWidget(
            item: item,
            isExpanded: isExpanded,
            isFirst: isFirst,
            isLast: isLast,
            theme: accordionTheme,
            disabled: disabled || item.disabled,
            onToggle: () => _handleItemToggle(item.value, isExpanded),
          );
        }).toList(),
      ),
    );
  }
  
  void _handleItemToggle(String value, bool isCurrentlyExpanded) {
    if (disabled) return;
    
    if (allowMultipleExpanded) {
      // Multiple expansion mode
      final Set<String> newExpandedValues = Set.from(expandedValues ?? <String>{});
      
      if (isCurrentlyExpanded) {
        if (collapsible || newExpandedValues.length > 1) {
          newExpandedValues.remove(value);
        }
      } else {
        newExpandedValues.add(value);
      }
      
      onExpandedValuesChanged?.call(newExpandedValues);
    } else {
      // Single expansion mode
      final String? newExpandedValue = isCurrentlyExpanded && collapsible ? null : value;
      onExpandedValueChanged?.call(newExpandedValue);
    }
  }
}

/// Represents a single item in an accordion.
/// 
/// Each item consists of a header that can be tapped to expand/collapse
/// the content area.
class ShadcnAccordionItem {
  /// Unique identifier for this accordion item
  final String value;
  
  /// Widget to display in the header
  final Widget header;
  
  /// Widget to display in the content area when expanded
  final Widget content;
  
  /// Whether this specific item is disabled
  final bool disabled;
  
  /// Custom trigger icon (defaults to chevron)
  final Widget? triggerIcon;
  
  /// Custom expanded trigger icon
  final Widget? expandedTriggerIcon;
  
  /// Semantic label for this item
  final String? semanticLabel;

  const ShadcnAccordionItem({
    required this.value,
    required this.header,
    required this.content,
    this.disabled = false,
    this.triggerIcon,
    this.expandedTriggerIcon,
    this.semanticLabel,
  });
}

/// Internal widget that renders a single accordion item.
class _AccordionItemWidget extends StatefulWidget {
  final ShadcnAccordionItem item;
  final bool isExpanded;
  final bool isFirst;
  final bool isLast;
  final ShadcnAccordionTheme theme;
  final bool disabled;
  final VoidCallback onToggle;

  const _AccordionItemWidget({
    required this.item,
    required this.isExpanded,
    required this.isFirst,
    required this.isLast,
    required this.theme,
    required this.disabled,
    required this.onToggle,
  });

  @override
  State<_AccordionItemWidget> createState() => _AccordionItemWidgetState();
}

class _AccordionItemWidgetState extends State<_AccordionItemWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;
  late Animation<double> _iconRotationAnimation;
  
  bool _isHovering = false;
  FocusNode? _focusNode;

  @override
  void initState() {
    super.initState();
    
    _focusNode = FocusNode();
    
    _animationController = AnimationController(
      duration: widget.theme.animationDuration ?? ShadcnTokens.durationNormal,
      vsync: this,
    );
    
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: widget.theme.animationCurve ?? Curves.easeInOut,
    );
    
    _iconRotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.5,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: widget.theme.animationCurve ?? Curves.easeInOut,
    ));
    
    if (widget.isExpanded) {
      _animationController.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(_AccordionItemWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isExpanded != oldWidget.isExpanded) {
      if (widget.isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
    
    // Update animation duration if theme changed
    if (widget.theme.animationDuration != oldWidget.theme.animationDuration) {
      _animationController.duration = widget.theme.animationDuration ?? ShadcnTokens.durationNormal;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _focusNode?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final borderRadius = widget.theme.borderRadius ?? BorderRadius.circular(ShadcnTokens.radiusMd);
    final borderColor = widget.theme.borderColor ?? Theme.of(context).colorScheme.outline;
    final borderWidth = widget.theme.borderWidth ?? ShadcnTokens.borderWidth;
    
    // Determine border radius for this item
    BorderRadius itemBorderRadius = BorderRadius.zero;
    if (widget.isFirst && widget.isLast) {
      itemBorderRadius = borderRadius;
    } else if (widget.isFirst) {
      itemBorderRadius = BorderRadius.only(
        topLeft: borderRadius.topLeft,
        topRight: borderRadius.topRight,
      );
    } else if (widget.isLast) {
      itemBorderRadius = BorderRadius.only(
        bottomLeft: borderRadius.bottomLeft,
        bottomRight: borderRadius.bottomRight,
      );
    }
    
    return Container(
      decoration: BoxDecoration(
        border: Border(
          top: widget.isFirst ? BorderSide(color: borderColor, width: borderWidth) : BorderSide.none,
          left: BorderSide(color: borderColor, width: borderWidth),
          right: BorderSide(color: borderColor, width: borderWidth),
          bottom: BorderSide(color: borderColor, width: borderWidth),
        ),
        borderRadius: itemBorderRadius,
      ),
      child: Column(
        children: [
          _buildHeader(context),
          _buildContent(context),
        ],
      ),
    );
  }
  
  Widget _buildHeader(BuildContext context) {
    final headerBackground = _isHovering && !widget.disabled
        ? widget.theme.headerHoverBackground ?? widget.theme.headerBackground
        : widget.theme.headerBackground;
    
    final headerForeground = _isHovering && !widget.disabled
        ? widget.theme.headerHoverForeground ?? widget.theme.headerForeground
        : widget.theme.headerForeground;
    
    final iconColor = _isHovering && !widget.disabled
        ? widget.theme.triggerIconHoverColor ?? widget.theme.triggerIconColor
        : widget.theme.triggerIconColor;
    
    return Semantics(
      button: true,
      expanded: widget.isExpanded,
      label: widget.item.semanticLabel ?? 'Accordion item',
      onTap: widget.disabled ? null : widget.onToggle,
      child: Focus(
        focusNode: _focusNode,
        onFocusChange: (focused) {
          if (mounted) {
            setState(() {});
          }
        },
        onKeyEvent: (node, event) {
          if (event is KeyDownEvent &&
              (event.logicalKey == LogicalKeyboardKey.enter ||
               event.logicalKey == LogicalKeyboardKey.space)) {
            if (!widget.disabled) {
              widget.onToggle();
            }
            return KeyEventResult.handled;
          }
          return KeyEventResult.ignored;
        },
        child: MouseRegion(
          onEnter: (_) => _setHovering(true),
          onExit: (_) => _setHovering(false),
          cursor: widget.disabled ? SystemMouseCursors.basic : SystemMouseCursors.click,
          child: GestureDetector(
            onTap: widget.disabled ? null : widget.onToggle,
            child: Container(
              width: double.infinity,
              padding: widget.theme.headerPadding ?? ShadcnTokens.paddingAll(ShadcnTokens.spacing4),
              decoration: BoxDecoration(
                color: headerBackground,
                borderRadius: widget.isFirst && !widget.isExpanded 
                    ? BorderRadius.only(
                        topLeft: widget.theme.borderRadius?.topLeft ?? const Radius.circular(ShadcnTokens.radiusMd),
                        topRight: widget.theme.borderRadius?.topRight ?? const Radius.circular(ShadcnTokens.radiusMd),
                      )
                    : null,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: DefaultTextStyle(
                      style: widget.theme.headerTextStyle ?? TextStyle(
                        color: headerForeground,
                        fontSize: ShadcnTokens.fontSizeMd,
                        fontWeight: ShadcnTokens.fontWeightMedium,
                      ),
                      child: widget.item.header,
                    ),
                  ),
                  const SizedBox(width: ShadcnTokens.spacing2),
                  RotationTransition(
                    turns: _iconRotationAnimation,
                    child: widget.item.triggerIcon ?? Icon(
                      Icons.keyboard_arrow_down,
                      size: widget.theme.triggerIconSize ?? ShadcnTokens.iconSizeMd,
                      color: iconColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildContent(BuildContext context) {
    return SizeTransition(
      sizeFactor: _expandAnimation,
      child: Container(
        width: double.infinity,
        padding: widget.theme.contentPadding ?? const EdgeInsets.all(ShadcnTokens.spacing4),
        decoration: BoxDecoration(
          color: widget.theme.contentBackground,
          borderRadius: widget.isLast && widget.isExpanded 
              ? BorderRadius.only(
                  bottomLeft: widget.theme.borderRadius?.bottomLeft ?? const Radius.circular(ShadcnTokens.radiusMd),
                  bottomRight: widget.theme.borderRadius?.bottomRight ?? const Radius.circular(ShadcnTokens.radiusMd),
                )
              : null,
        ),
        child: DefaultTextStyle(
          style: widget.theme.contentTextStyle ?? TextStyle(
            color: widget.theme.contentForeground,
            fontSize: ShadcnTokens.fontSizeMd,
            fontWeight: ShadcnTokens.fontWeightNormal,
          ),
          child: widget.item.content,
        ),
      ),
    );
  }
  
  void _setHovering(bool hovering) {
    if (mounted && !widget.disabled) {
      setState(() {
        _isHovering = hovering;
      });
    }
  }
}