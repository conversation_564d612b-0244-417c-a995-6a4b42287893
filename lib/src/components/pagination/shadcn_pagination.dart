import 'package:flutter/material.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_pagination_theme.dart';

/// A shadcn-styled pagination component.
class ShadcnPagination extends ShadcnComponent {
  final int currentPage;
  final int totalPages;
  final ValueChanged<int>? onPageChanged;
  final bool showFirstLast;
  final bool showPreviousNext;
  final int visiblePages;
  final bool enabled;

  const ShadcnPagination({
    super.key,
    required this.currentPage,
    required this.totalPages,
    this.onPageChanged,
    this.showFirstLast = true,
    this.showPreviousNext = true,
    this.visiblePages = 5,
    this.enabled = true,
  }) : assert(currentPage >= 1 && currentPage <= totalPages),
       assert(totalPages >= 1),
       assert(visiblePages >= 1);

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    final paginationTheme = resolveTheme<ShadcnPaginationTheme>(
      context,
      ShadcnPaginationTheme.defaultTheme,
    );

    final pages = _calculateVisiblePages();

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // First page
        if (showFirstLast && currentPage > 1)
          _buildPageItem(context, paginationTheme, theme, 1, 'First'),
        
        // Previous page
        if (showPreviousNext && currentPage > 1)
          _buildPageItem(context, paginationTheme, theme, currentPage - 1, 'Previous'),
        
        // Ellipsis before
        if (pages.first > 2)
          _buildEllipsis(context, paginationTheme, theme),
        
        // Page numbers
        ...pages.map((page) => _buildPageItem(
          context,
          paginationTheme,
          theme,
          page,
          page.toString(),
          isSelected: page == currentPage,
        )),
        
        // Ellipsis after
        if (pages.last < totalPages - 1)
          _buildEllipsis(context, paginationTheme, theme),
        
        // Next page
        if (showPreviousNext && currentPage < totalPages)
          _buildPageItem(context, paginationTheme, theme, currentPage + 1, 'Next'),
        
        // Last page
        if (showFirstLast && currentPage < totalPages)
          _buildPageItem(context, paginationTheme, theme, totalPages, 'Last'),
      ],
    );
  }

  List<int> _calculateVisiblePages() {
    if (totalPages <= visiblePages) {
      return List.generate(totalPages, (i) => i + 1);
    }

    int start = (currentPage - visiblePages ~/ 2).clamp(1, totalPages - visiblePages + 1);
    int end = (start + visiblePages - 1).clamp(1, totalPages);
    
    return List.generate(end - start + 1, (i) => start + i);
  }

  Widget _buildPageItem(
    BuildContext context,
    ShadcnPaginationTheme paginationTheme,
    ThemeData theme,
    int page,
    String label, {
    bool isSelected = false,
  }) {
    final isDisabled = !enabled || page < 1 || page > totalPages;
    
    Color backgroundColor;
    Color foregroundColor;
    Color borderColor;

    if (isDisabled) {
      backgroundColor = paginationTheme.disabledBackgroundColor ?? theme.colorScheme.onSurface.withOpacity(0.04);
      foregroundColor = paginationTheme.disabledForegroundColor ?? theme.colorScheme.onSurface.withOpacity(0.38);
      borderColor = paginationTheme.borderColor ?? theme.colorScheme.outline;
    } else if (isSelected) {
      backgroundColor = paginationTheme.selectedBackgroundColor ?? theme.colorScheme.primary;
      foregroundColor = paginationTheme.selectedForegroundColor ?? theme.colorScheme.onPrimary;
      borderColor = backgroundColor;
    } else {
      backgroundColor = paginationTheme.backgroundColor ?? theme.colorScheme.surface;
      foregroundColor = paginationTheme.foregroundColor ?? theme.colorScheme.onSurface;
      borderColor = paginationTheme.borderColor ?? theme.colorScheme.outline;
    }

    return Padding(
      padding: EdgeInsets.only(right: paginationTheme.itemSpacing ?? 4.0),
      child: SizedBox(
        width: paginationTheme.itemSize,
        height: paginationTheme.itemSize,
        child: Material(
          color: backgroundColor,
          borderRadius: paginationTheme.itemBorderRadius,
          child: InkWell(
            onTap: isDisabled ? null : () => onPageChanged?.call(page),
            borderRadius: paginationTheme.itemBorderRadius,
            hoverColor: paginationTheme.hoverBackgroundColor,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: borderColor,
                  width: paginationTheme.borderWidth ?? 1.0,
                ),
                borderRadius: paginationTheme.itemBorderRadius,
              ),
              child: Center(
                child: _getPageIcon(label) ?? Text(
                  label,
                  style: (paginationTheme.textStyle ?? const TextStyle())
                      .copyWith(color: foregroundColor),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEllipsis(BuildContext context, ShadcnPaginationTheme paginationTheme, ThemeData theme) {
    return Padding(
      padding: EdgeInsets.only(right: paginationTheme.itemSpacing ?? 4.0),
      child: SizedBox(
        width: paginationTheme.itemSize,
        height: paginationTheme.itemSize,
        child: Center(
          child: Text(
            '...',
            style: (paginationTheme.textStyle ?? const TextStyle())
                .copyWith(color: paginationTheme.foregroundColor ?? theme.colorScheme.onSurface),
          ),
        ),
      ),
    );
  }

  Widget? _getPageIcon(String label) {
    switch (label) {
      case 'First':
        return const Icon(Icons.first_page);
      case 'Previous':
        return const Icon(Icons.chevron_left);
      case 'Next':
        return const Icon(Icons.chevron_right);
      case 'Last':
        return const Icon(Icons.last_page);
      default:
        return null;
    }
  }
}