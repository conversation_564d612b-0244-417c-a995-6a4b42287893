import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_resizable_theme.dart';
import '../../utils/theme_resolver.dart';

/// Direction for resizable panels
enum ShadcnResizableDirection {
  horizontal,
  vertical,
}

/// A resizable component that allows users to adjust the size of panels.
/// 
/// This component provides drag handles for resizing panels with visual feedback
/// and theme-aware styling.
class ShadcnResizable extends ShadcnComponent {
  /// The first (top/left) child widget
  final Widget firstChild;
  
  /// The second (bottom/right) child widget
  final Widget secondChild;
  
  /// The direction of the resizable split
  final ShadcnResizableDirection direction;
  
  /// Initial flex value for the first child (0.0 to 1.0)
  final double initialFlex;
  
  /// Minimum flex value for the first child
  final double minFlex;
  
  /// Maximum flex value for the first child
  final double maxFlex;
  
  /// Callback when the flex value changes
  final ValueChanged<double>? onFlexChanged;
  
  /// Whether the resize handle is visible
  final bool showHandle;
  
  /// Custom theme for this resizable component
  final ShadcnResizableTheme? theme;

  const ShadcnResizable({
    super.key,
    required this.firstChild,
    required this.secondChild,
    this.direction = ShadcnResizableDirection.horizontal,
    this.initialFlex = 0.5,
    this.minFlex = 0.1,
    this.maxFlex = 0.9,
    this.onFlexChanged,
    this.showHandle = true,
    this.theme,
  }) : assert(initialFlex >= 0.0 && initialFlex <= 1.0, 'initialFlex must be between 0.0 and 1.0'),
       assert(minFlex >= 0.0 && minFlex <= 1.0, 'minFlex must be between 0.0 and 1.0'),
       assert(maxFlex >= 0.0 && maxFlex <= 1.0, 'maxFlex must be between 0.0 and 1.0'),
       assert(minFlex <= maxFlex, 'minFlex must be less than or equal to maxFlex');

  @override
  Widget buildWithTheme(BuildContext context, ThemeData themeData) {
    final resizableTheme = theme ?? 
        themeData.extension<ShadcnResizableTheme>() ?? 
        ShadcnResizableTheme.defaultTheme(themeData.colorScheme);

    return _ResizableWidget(
      firstChild: firstChild,
      secondChild: secondChild,
      direction: direction,
      initialFlex: initialFlex,
      minFlex: minFlex,
      maxFlex: maxFlex,
      onFlexChanged: onFlexChanged,
      showHandle: showHandle,
      theme: resizableTheme,
    );
  }
}

class _ResizableWidget extends StatefulWidget {
  final Widget firstChild;
  final Widget secondChild;
  final ShadcnResizableDirection direction;
  final double initialFlex;
  final double minFlex;
  final double maxFlex;
  final ValueChanged<double>? onFlexChanged;
  final bool showHandle;
  final ShadcnResizableTheme theme;

  const _ResizableWidget({
    required this.firstChild,
    required this.secondChild,
    required this.direction,
    required this.initialFlex,
    required this.minFlex,
    required this.maxFlex,
    this.onFlexChanged,
    required this.showHandle,
    required this.theme,
  });

  @override
  State<_ResizableWidget> createState() => _ResizableWidgetState();
}

class _ResizableWidgetState extends State<_ResizableWidget>
    with TickerProviderStateMixin {
  late double _flex;
  bool _isDragging = false;
  bool _isHovering = false;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _flex = widget.initialFlex;
    _animationController = AnimationController(
      duration: widget.theme.animationDuration ?? const Duration(milliseconds: 150),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onPanStart(DragStartDetails details) {
    setState(() {
      _isDragging = true;
    });
    _animationController.forward();
  }

  void _onPanEnd(DragEndDetails details) {
    setState(() {
      _isDragging = false;
    });
    _animationController.reverse();
  }

  void _onPanUpdate(DragUpdateDetails details, Size containerSize) {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final localPosition = renderBox.globalToLocal(details.globalPosition);
    
    double newFlex;
    if (widget.direction == ShadcnResizableDirection.horizontal) {
      newFlex = localPosition.dx / containerSize.width;
    } else {
      newFlex = localPosition.dy / containerSize.height;
    }
    
    // Clamp the flex value within bounds
    newFlex = newFlex.clamp(widget.minFlex, widget.maxFlex);
    
    if (newFlex != _flex) {
      setState(() {
        _flex = newFlex;
      });
      widget.onFlexChanged?.call(newFlex);
    }
  }

  Widget _buildHandle() {
    if (!widget.showHandle) {
      return const SizedBox.shrink();
    }

    return MouseRegion(
      cursor: widget.direction == ShadcnResizableDirection.horizontal
          ? SystemMouseCursors.resizeColumn
          : SystemMouseCursors.resizeRow,
      onEnter: (_) {
        setState(() {
          _isHovering = true;
        });
      },
      onExit: (_) {
        setState(() {
          _isHovering = false;
        });
      },
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          final handleColor = _isDragging
              ? widget.theme.handleActive
              : _isHovering
                  ? widget.theme.handleHover
                  : widget.theme.handleBackground;

          final dividerOpacity = _isDragging || _isHovering
              ? widget.theme.dividerHoverOpacity ?? 0.6
              : widget.theme.dividerOpacity ?? 0.3;

          return Container(
            width: widget.direction == ShadcnResizableDirection.horizontal
                ? widget.theme.handleWidth ?? 4.0
                : double.infinity,
            height: widget.direction == ShadcnResizableDirection.vertical
                ? widget.theme.handleHeight ?? 4.0
                : double.infinity,
            decoration: BoxDecoration(
              color: handleColor,
              borderRadius: widget.theme.handleBorderRadius,
              boxShadow: _isDragging ? widget.theme.handleShadow : null,
            ),
            child: Stack(
              children: [
                // Divider line
                Positioned.fill(
                  child: Container(
                    color: widget.theme.dividerColor?.withOpacity(dividerOpacity),
                  ),
                ),
                // Handle indicator dots
                Center(
                  child: _buildHandleIndicator(),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildHandleIndicator() {
    final indicatorSize = widget.theme.indicatorSize ?? 2.0;
    final indicatorSpacing = widget.theme.indicatorSpacing ?? 2.0;
    final indicatorColor = widget.theme.indicatorColor;

    if (indicatorColor == null) {
      return const SizedBox.shrink();
    }

    final dots = List.generate(
      3,
      (index) => Container(
        width: indicatorSize,
        height: indicatorSize,
        decoration: BoxDecoration(
          color: indicatorColor,
          shape: BoxShape.circle,
        ),
      ),
    );

    if (widget.direction == ShadcnResizableDirection.horizontal) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: dots
            .map((dot) => Padding(
                  padding: EdgeInsets.symmetric(vertical: indicatorSpacing / 2),
                  child: dot,
                ))
            .toList(),
      );
    } else {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: dots
            .map((dot) => Padding(
                  padding: EdgeInsets.symmetric(horizontal: indicatorSpacing / 2),
                  child: dot,
                ))
            .toList(),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (widget.direction == ShadcnResizableDirection.horizontal) {
          return Row(
            children: [
              Expanded(
                flex: (_flex * 100).round(),
                child: widget.firstChild,
              ),
              GestureDetector(
                onPanStart: _onPanStart,
                onPanEnd: _onPanEnd,
                onPanUpdate: (details) => _onPanUpdate(details, constraints.biggest),
                child: _buildHandle(),
              ),
              Expanded(
                flex: ((1.0 - _flex) * 100).round(),
                child: widget.secondChild,
              ),
            ],
          );
        } else {
          return Column(
            children: [
              Expanded(
                flex: (_flex * 100).round(),
                child: widget.firstChild,
              ),
              GestureDetector(
                onPanStart: _onPanStart,
                onPanEnd: _onPanEnd,
                onPanUpdate: (details) => _onPanUpdate(details, constraints.biggest),
                child: _buildHandle(),
              ),
              Expanded(
                flex: ((1.0 - _flex) * 100).round(),
                child: widget.secondChild,
              ),
            ],
          );
        }
      },
    );
  }
}