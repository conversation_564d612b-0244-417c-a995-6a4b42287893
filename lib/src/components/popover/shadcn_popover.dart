import 'package:flutter/material.dart';
import '../../../shadcn.dart';

/// Enum defining where the popover should be positioned relative to its anchor.
enum ShadcnPopoverPosition {
  top,
  topStart,
  topEnd,
  bottom,
  bottomStart,
  bottomEnd,
  left,
  leftStart,
  leftEnd,
  right,
  rightStart,
  rightEnd,
}

/// A shadcn-styled popover component that displays content in a floating overlay.
/// 
/// The popover provides rich positioning options, arrow indicators, and smooth
/// animations. It integrates with Material Design overlay system while maintaining
/// shadcn aesthetics and behavior patterns.
class ShadcnPopover extends ShadcnComponent with ShadcnComponentValidation {
  /// The widget that triggers the popover when interacted with.
  final Widget child;
  
  /// The content to display inside the popover.
  final Widget content;
  
  /// Whether the popover is currently visible.
  final bool visible;
  
  /// Callback when the popover visibility should change.
  final ValueChanged<bool>? onVisibilityChanged;
  
  /// The preferred position of the popover relative to the anchor.
  final ShadcnPopoverPosition position;
  
  /// Whether to show an arrow pointing to the anchor.
  final bool showArrow;
  
  /// Whether to dismiss the popover when clicking outside.
  final bool dismissible;
  
  /// Whether to maintain the popover within screen boundaries.
  final bool constrainToScreen;
  
  /// Custom offset from the calculated position.
  final Offset? customOffset;
  
  /// Animation controller for custom animations (optional).
  final AnimationController? animationController;
  
  /// Callback when the popover is dismissed.
  final VoidCallback? onDismissed;
  
  /// Semantic label for accessibility.
  final String? semanticLabel;
  
  /// Custom theme for this popover.
  final ShadcnPopoverTheme? theme;

  const ShadcnPopover({
    super.key,
    required this.child,
    required this.content,
    this.visible = false,
    this.onVisibilityChanged,
    this.position = ShadcnPopoverPosition.bottom,
    this.showArrow = true,
    this.dismissible = true,
    this.constrainToScreen = true,
    this.customOffset,
    this.animationController,
    this.onDismissed,
    this.semanticLabel,
    this.theme,
  });

  @override
  Widget buildWithTheme(BuildContext context, ThemeData materialTheme) {
    // Resolve theme
    final popoverTheme = theme ?? resolveTheme<ShadcnPopoverTheme>(
      context,
      ShadcnPopoverTheme.defaultTheme,
    );

    // Validate component configuration
    validateThemeProperties(context);
    validateAccessibility(
      semanticLabel: semanticLabel,
      componentName: 'ShadcnPopover',
    );

    // Build the anchor widget with popover overlay
    return PopoverAnchor(
      visible: visible,
      content: content,
      position: position,
      showArrow: showArrow,
      dismissible: dismissible,
      constrainToScreen: constrainToScreen,
      customOffset: customOffset,
      popoverTheme: popoverTheme,
      onVisibilityChanged: onVisibilityChanged,
      onDismissed: onDismissed,
      semanticLabel: semanticLabel,
      animationController: animationController,
      child: child,
    );
  }
}

/// Internal widget that manages the popover overlay and positioning logic.
class PopoverAnchor extends StatefulWidget {
  final Widget child;
  final Widget content;
  final bool visible;
  final ShadcnPopoverPosition position;
  final bool showArrow;
  final bool dismissible;
  final bool constrainToScreen;
  final Offset? customOffset;
  final ShadcnPopoverTheme popoverTheme;
  final ValueChanged<bool>? onVisibilityChanged;
  final VoidCallback? onDismissed;
  final String? semanticLabel;
  final AnimationController? animationController;

  const PopoverAnchor({
    super.key,
    required this.child,
    required this.content,
    required this.visible,
    required this.position,
    required this.showArrow,
    required this.dismissible,
    required this.constrainToScreen,
    required this.customOffset,
    required this.popoverTheme,
    this.onVisibilityChanged,
    this.onDismissed,
    this.semanticLabel,
    this.animationController,
  });

  @override
  State<PopoverAnchor> createState() => _PopoverAnchorState();
}

class _PopoverAnchorState extends State<PopoverAnchor> with SingleTickerProviderStateMixin {
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize animation controller
    _animationController = widget.animationController ?? AnimationController(
      duration: widget.popoverTheme.animationDuration ?? ShadcnTokens.durationFast,
      vsync: this,
    );
    
    // Setup animations
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: widget.popoverTheme.animationCurve ?? Curves.easeOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: widget.popoverTheme.animationCurve ?? Curves.easeOut,
    ));
    
    // Show popover if initially visible
    if (widget.visible) {
      WidgetsBinding.instance.addPostFrameCallback((_) => _showPopover());
    }
  }

  @override
  void didUpdateWidget(PopoverAnchor oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.visible != oldWidget.visible) {
      if (widget.visible) {
        _showPopover();
      } else {
        _hidePopover();
      }
    }
  }

  @override
  void dispose() {
    _hidePopover();
    if (widget.animationController == null) {
      _animationController.dispose();
    }
    super.dispose();
  }

  void _showPopover() {
    if (_overlayEntry != null) return;

    _overlayEntry = OverlayEntry(
      builder: (context) => PopoverOverlay(
        layerLink: _layerLink,
        content: widget.content,
        position: widget.position,
        showArrow: widget.showArrow,
        constrainToScreen: widget.constrainToScreen,
        customOffset: widget.customOffset,
        popoverTheme: widget.popoverTheme,
        dismissible: widget.dismissible,
        fadeAnimation: _fadeAnimation,
        scaleAnimation: _scaleAnimation,
        semanticLabel: widget.semanticLabel,
        onDismissed: () {
          _hidePopover();
          widget.onDismissed?.call();
          widget.onVisibilityChanged?.call(false);
        },
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
    _animationController.forward();
  }

  void _hidePopover() {
    if (_overlayEntry == null) return;

    _animationController.reverse().then((_) {
      _overlayEntry?.remove();
      _overlayEntry = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: widget.child,
    );
  }
}

/// The actual popover overlay that positions itself relative to the anchor.
class PopoverOverlay extends StatelessWidget {
  final LayerLink layerLink;
  final Widget content;
  final ShadcnPopoverPosition position;
  final bool showArrow;
  final bool constrainToScreen;
  final Offset? customOffset;
  final ShadcnPopoverTheme popoverTheme;
  final bool dismissible;
  final Animation<double> fadeAnimation;
  final Animation<double> scaleAnimation;
  final String? semanticLabel;
  final VoidCallback onDismissed;

  const PopoverOverlay({
    super.key,
    required this.layerLink,
    required this.content,
    required this.position,
    required this.showArrow,
    required this.constrainToScreen,
    required this.customOffset,
    required this.popoverTheme,
    required this.dismissible,
    required this.fadeAnimation,
    required this.scaleAnimation,
    required this.onDismissed,
    this.semanticLabel,
  });

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final positionData = _calculatePosition(context);

    return Stack(
      children: [
        // Barrier for dismissible popover
        if (dismissible)
          Positioned.fill(
            child: GestureDetector(
              onTap: onDismissed,
              child: Container(
                color: popoverTheme.barrierColor ?? Colors.transparent,
              ),
            ),
          ),
        
        // Popover content
        CompositedTransformFollower(
          link: layerLink,
          targetAnchor: positionData.targetAnchor,
          followerAnchor: positionData.followerAnchor,
          offset: positionData.offset,
          child: AnimatedBuilder(
            animation: fadeAnimation,
            builder: (context, child) {
              return FadeTransition(
                opacity: fadeAnimation,
                child: ScaleTransition(
                  scale: scaleAnimation,
                  alignment: positionData.scaleAlignment,
                  child: Material(
                    type: MaterialType.transparency,
                    child: _buildPopoverContent(context, positionData),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildPopoverContent(BuildContext context, _PositionData positionData) {
    final backgroundColor = popoverTheme.resolveBackgroundColor(context);
    final borderRadius = popoverTheme.resolvePopoverBorderRadius(context);
    final contentPadding = popoverTheme.resolveContentPadding(context);
    final borderColor = popoverTheme.resolveBorderColor(context);

    Widget popoverContent = Container(
      constraints: BoxConstraints(
        maxWidth: popoverTheme.maxWidth ?? 320.0,
        maxHeight: popoverTheme.maxHeight ?? 400.0,
        minWidth: popoverTheme.minWidth ?? 0.0,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: borderRadius,
        border: popoverTheme.borderWidth != null && popoverTheme.borderWidth! > 0
            ? Border.all(
                color: borderColor,
                width: popoverTheme.borderWidth!,
              )
            : null,
        boxShadow: popoverTheme.elevation != null && popoverTheme.elevation! > 0
            ? [
                BoxShadow(
                  color: (popoverTheme.shadowColor ?? Colors.black).withOpacity(0.1),
                  blurRadius: popoverTheme.elevation! * 2,
                  offset: Offset(0, popoverTheme.elevation!),
                ),
              ]
            : null,
      ),
      padding: contentPadding,
      child: DefaultTextStyle(
        style: popoverTheme.resolvePopoverTextStyle(context),
        child: content,
      ),
    );

    // Add arrow if requested
    if (showArrow) {
      return Stack(
        clipBehavior: Clip.none,
        children: [
          popoverContent,
          _buildArrow(context, positionData, backgroundColor),
        ],
      );
    }

    // Add semantic wrapper if label provided
    if (semanticLabel != null) {
      popoverContent = Semantics(
        label: semanticLabel,
        child: popoverContent,
      );
    }

    return popoverContent;
  }

  Widget _buildArrow(BuildContext context, _PositionData positionData, Color arrowColor) {
    final arrowSize = popoverTheme.arrowSize ?? 12.0;
    
    return Positioned(
      top: positionData.arrowPosition.dy,
      left: positionData.arrowPosition.dx,
      child: CustomPaint(
        size: Size(arrowSize, arrowSize),
        painter: _ArrowPainter(
          color: arrowColor,
          direction: positionData.arrowDirection,
        ),
      ),
    );
  }

  _PositionData _calculatePosition(BuildContext context) {
    final offset = popoverTheme.offset ?? 8.0;
    final arrowSize = showArrow ? (popoverTheme.arrowSize ?? 12.0) : 0.0;
    final totalOffset = offset + (arrowSize / 2);

    switch (position) {
      case ShadcnPopoverPosition.top:
        return _PositionData(
          targetAnchor: Alignment.topCenter,
          followerAnchor: Alignment.bottomCenter,
          offset: Offset(0, -totalOffset) + (customOffset ?? Offset.zero),
          scaleAlignment: Alignment.bottomCenter,
          arrowDirection: _ArrowDirection.down,
          arrowPosition: Offset(-arrowSize / 2, -arrowSize),
        );
      
      case ShadcnPopoverPosition.topStart:
        return _PositionData(
          targetAnchor: Alignment.topLeft,
          followerAnchor: Alignment.bottomLeft,
          offset: Offset(0, -totalOffset) + (customOffset ?? Offset.zero),
          scaleAlignment: Alignment.bottomLeft,
          arrowDirection: _ArrowDirection.down,
          arrowPosition: Offset(arrowSize, -arrowSize),
        );
      
      case ShadcnPopoverPosition.topEnd:
        return _PositionData(
          targetAnchor: Alignment.topRight,
          followerAnchor: Alignment.bottomRight,
          offset: Offset(0, -totalOffset) + (customOffset ?? Offset.zero),
          scaleAlignment: Alignment.bottomRight,
          arrowDirection: _ArrowDirection.down,
          arrowPosition: Offset(-arrowSize * 1.5, -arrowSize),
        );
      
      case ShadcnPopoverPosition.bottom:
        return _PositionData(
          targetAnchor: Alignment.bottomCenter,
          followerAnchor: Alignment.topCenter,
          offset: Offset(0, totalOffset) + (customOffset ?? Offset.zero),
          scaleAlignment: Alignment.topCenter,
          arrowDirection: _ArrowDirection.up,
          arrowPosition: Offset(-arrowSize / 2, -arrowSize / 2),
        );
      
      case ShadcnPopoverPosition.bottomStart:
        return _PositionData(
          targetAnchor: Alignment.bottomLeft,
          followerAnchor: Alignment.topLeft,
          offset: Offset(0, totalOffset) + (customOffset ?? Offset.zero),
          scaleAlignment: Alignment.topLeft,
          arrowDirection: _ArrowDirection.up,
          arrowPosition: Offset(arrowSize, -arrowSize / 2),
        );
      
      case ShadcnPopoverPosition.bottomEnd:
        return _PositionData(
          targetAnchor: Alignment.bottomRight,
          followerAnchor: Alignment.topRight,
          offset: Offset(0, totalOffset) + (customOffset ?? Offset.zero),
          scaleAlignment: Alignment.topRight,
          arrowDirection: _ArrowDirection.up,
          arrowPosition: Offset(-arrowSize * 1.5, -arrowSize / 2),
        );
      
      case ShadcnPopoverPosition.left:
        return _PositionData(
          targetAnchor: Alignment.centerLeft,
          followerAnchor: Alignment.centerRight,
          offset: Offset(-totalOffset, 0) + (customOffset ?? Offset.zero),
          scaleAlignment: Alignment.centerRight,
          arrowDirection: _ArrowDirection.right,
          arrowPosition: Offset(-arrowSize / 2, -arrowSize / 2),
        );
      
      case ShadcnPopoverPosition.leftStart:
        return _PositionData(
          targetAnchor: Alignment.topLeft,
          followerAnchor: Alignment.topRight,
          offset: Offset(-totalOffset, 0) + (customOffset ?? Offset.zero),
          scaleAlignment: Alignment.topRight,
          arrowDirection: _ArrowDirection.right,
          arrowPosition: Offset(-arrowSize / 2, arrowSize),
        );
      
      case ShadcnPopoverPosition.leftEnd:
        return _PositionData(
          targetAnchor: Alignment.bottomLeft,
          followerAnchor: Alignment.bottomRight,
          offset: Offset(-totalOffset, 0) + (customOffset ?? Offset.zero),
          scaleAlignment: Alignment.bottomRight,
          arrowDirection: _ArrowDirection.right,
          arrowPosition: Offset(-arrowSize / 2, -arrowSize * 1.5),
        );
      
      case ShadcnPopoverPosition.right:
        return _PositionData(
          targetAnchor: Alignment.centerRight,
          followerAnchor: Alignment.centerLeft,
          offset: Offset(totalOffset, 0) + (customOffset ?? Offset.zero),
          scaleAlignment: Alignment.centerLeft,
          arrowDirection: _ArrowDirection.left,
          arrowPosition: Offset(-arrowSize / 2, -arrowSize / 2),
        );
      
      case ShadcnPopoverPosition.rightStart:
        return _PositionData(
          targetAnchor: Alignment.topRight,
          followerAnchor: Alignment.topLeft,
          offset: Offset(totalOffset, 0) + (customOffset ?? Offset.zero),
          scaleAlignment: Alignment.topLeft,
          arrowDirection: _ArrowDirection.left,
          arrowPosition: Offset(-arrowSize / 2, arrowSize),
        );
      
      case ShadcnPopoverPosition.rightEnd:
        return _PositionData(
          targetAnchor: Alignment.bottomRight,
          followerAnchor: Alignment.bottomLeft,
          offset: Offset(totalOffset, 0) + (customOffset ?? Offset.zero),
          scaleAlignment: Alignment.bottomLeft,
          arrowDirection: _ArrowDirection.left,
          arrowPosition: Offset(-arrowSize / 2, -arrowSize * 1.5),
        );
    }
  }
}

/// Data class holding position calculation results.
class _PositionData {
  final Alignment targetAnchor;
  final Alignment followerAnchor;
  final Offset offset;
  final Alignment scaleAlignment;
  final _ArrowDirection arrowDirection;
  final Offset arrowPosition;

  const _PositionData({
    required this.targetAnchor,
    required this.followerAnchor,
    required this.offset,
    required this.scaleAlignment,
    required this.arrowDirection,
    required this.arrowPosition,
  });
}

/// Enum for arrow directions.
enum _ArrowDirection { up, down, left, right }

/// Custom painter for drawing the popover arrow.
class _ArrowPainter extends CustomPainter {
  final Color color;
  final _ArrowDirection direction;

  const _ArrowPainter({
    required this.color,
    required this.direction,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    final halfSize = size.width / 2;

    switch (direction) {
      case _ArrowDirection.up:
        path.moveTo(halfSize, 0);
        path.lineTo(0, size.height);
        path.lineTo(size.width, size.height);
        break;
      
      case _ArrowDirection.down:
        path.moveTo(0, 0);
        path.lineTo(size.width, 0);
        path.lineTo(halfSize, size.height);
        break;
      
      case _ArrowDirection.left:
        path.moveTo(0, halfSize);
        path.lineTo(size.width, 0);
        path.lineTo(size.width, size.height);
        break;
      
      case _ArrowDirection.right:
        path.moveTo(0, 0);
        path.lineTo(size.width, halfSize);
        path.lineTo(0, size.height);
        break;
    }

    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(_ArrowPainter oldDelegate) {
    return color != oldDelegate.color || direction != oldDelegate.direction;
  }
}