import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_context_menu_theme.dart';

/// Data model for a context menu item
class ShadcnContextMenuItem {
  /// The unique identifier for the menu item
  final String id;
  
  /// The display label for the menu item
  final String label;
  
  /// Optional icon for the menu item
  final Widget? icon;
  
  /// Optional keyboard shortcut for the menu item
  final List<String>? shortcut;
  
  /// Callback when the menu item is selected
  final VoidCallback? onPressed;
  
  /// Whether the menu item is enabled
  final bool enabled;
  
  /// Whether this is a destructive action (uses destructive styling)
  final bool destructive;
  
  /// Optional submenu items
  final List<ShadcnContextMenuItem>? submenu;

  const ShadcnContextMenuItem({
    required this.id,
    required this.label,
    this.icon,
    this.shortcut,
    this.onPressed,
    this.enabled = true,
    this.destructive = false,
    this.submenu,
  });
}

/// A separator item for context menus
class ShadcnContextMenuSeparator extends ShadcnContextMenuItem {
  const ShadcnContextMenuSeparator({String? id})
      : super(id: id ?? 'separator', label: '');
}

/// A context menu component with Material integration
/// 
/// The ShadcnContextMenu widget provides a context menu that can be triggered
/// by right-click or long-press gestures, with theme-aware styling and keyboard navigation.
class ShadcnContextMenu extends ShadcnComponent {
  /// The child widget that triggers the context menu
  final Widget child;
  
  /// List of menu items to display
  final List<ShadcnContextMenuItem> items;
  
  /// Whether to show the menu on right-click
  final bool enableRightClick;
  
  /// Whether to show the menu on long-press
  final bool enableLongPress;
  
  /// Custom menu position offset
  final Offset? offset;
  
  /// Callback when the menu is opened
  final VoidCallback? onOpen;
  
  /// Callback when the menu is closed
  final VoidCallback? onClose;
  
  /// Whether to close the menu when an item is selected
  final bool closeOnSelect;

  const ShadcnContextMenu({
    Key? key,
    required this.child,
    required this.items,
    this.enableRightClick = true,
    this.enableLongPress = true,
    this.offset,
    this.onOpen,
    this.onClose,
    this.closeOnSelect = true,
  }) : super(key: key);

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    final menuTheme = theme.extension<ShadcnContextMenuTheme>() ??
        ShadcnContextMenuTheme.defaultTheme(theme.colorScheme);

    return GestureDetector(
      onSecondaryTapDown: enableRightClick
          ? (details) => _showContextMenu(context, details.globalPosition, menuTheme)
          : null,
      onLongPressStart: enableLongPress
          ? (details) => _showContextMenu(context, details.globalPosition, menuTheme)
          : null,
      child: child,
    );
  }

  void _showContextMenu(
    BuildContext context, 
    Offset position, 
    ShadcnContextMenuTheme theme,
  ) {
    onOpen?.call();

    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => _ContextMenuOverlay(
        position: offset != null ? position + offset! : position,
        items: items,
        theme: theme,
        onItemPressed: (item) {
          if (closeOnSelect) {
            overlayEntry.remove();
            onClose?.call();
          }
          item.onPressed?.call();
        },
        onDismiss: () {
          overlayEntry.remove();
          onClose?.call();
        },
      ),
    );

    overlay.insert(overlayEntry);
  }
}

class _ContextMenuOverlay extends StatefulWidget {
  final Offset position;
  final List<ShadcnContextMenuItem> items;
  final ShadcnContextMenuTheme theme;
  final ValueChanged<ShadcnContextMenuItem> onItemPressed;
  final VoidCallback onDismiss;

  const _ContextMenuOverlay({
    required this.position,
    required this.items,
    required this.theme,
    required this.onItemPressed,
    required this.onDismiss,
  });

  @override
  State<_ContextMenuOverlay> createState() => _ContextMenuOverlayState();
}

class _ContextMenuOverlayState extends State<_ContextMenuOverlay>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;
  
  int _selectedIndex = -1;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.95,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _animationController.forward();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _handleKeyEvent(RawKeyEvent event) {
    if (event is RawKeyDownEvent) {
      final enabledItems = widget.items.where((item) => 
        item is! ShadcnContextMenuSeparator && item.enabled).toList();
      
      if (event.logicalKey == LogicalKeyboardKey.arrowDown) {
        setState(() {
          _selectedIndex = (_selectedIndex + 1) % enabledItems.length;
        });
      } else if (event.logicalKey == LogicalKeyboardKey.arrowUp) {
        setState(() {
          _selectedIndex = (_selectedIndex - 1 + enabledItems.length) % enabledItems.length;
        });
      } else if (event.logicalKey == LogicalKeyboardKey.enter) {
        if (_selectedIndex >= 0 && _selectedIndex < enabledItems.length) {
          widget.onItemPressed(enabledItems[_selectedIndex]);
        }
      } else if (event.logicalKey == LogicalKeyboardKey.escape) {
        widget.onDismiss();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Positioned.fill(
      child: GestureDetector(
        onTap: widget.onDismiss,
        behavior: HitTestBehavior.translucent,
        child: Stack(
          children: [
            Positioned(
              left: widget.position.dx,
              top: widget.position.dy,
              child: RawKeyboardListener(
                focusNode: _focusNode,
                onKey: _handleKeyEvent,
                child: AnimatedBuilder(
                  animation: _animationController,
                  builder: (context, child) => Transform.scale(
                    scale: _scaleAnimation.value,
                    alignment: Alignment.topLeft,
                    child: Opacity(
                      opacity: _opacityAnimation.value,
                      child: _buildMenu(),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenu() {
    return Material(
      elevation: widget.theme.elevation ?? 8,
      color: widget.theme.background,
      borderRadius: widget.theme.borderRadius,
      child: Container(
        constraints: BoxConstraints(
          minWidth: widget.theme.minWidth ?? 220,
          maxWidth: widget.theme.maxWidth ?? 320,
        ),
        padding: widget.theme.menuPadding,
        decoration: BoxDecoration(
          border: Border.all(color: widget.theme.border ?? Colors.transparent),
          borderRadius: widget.theme.borderRadius,
          boxShadow: widget.theme.shadow,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: _buildMenuItems(),
        ),
      ),
    );
  }

  List<Widget> _buildMenuItems() {
    final widgets = <Widget>[];
    int enabledIndex = 0;

    for (int i = 0; i < widget.items.length; i++) {
      final item = widget.items[i];
      
      if (item is ShadcnContextMenuSeparator) {
        widgets.add(_buildSeparator());
      } else {
        final isSelected = item.enabled && enabledIndex == _selectedIndex;
        widgets.add(_buildMenuItem(item, isSelected));
        if (item.enabled) enabledIndex++;
      }
    }

    return widgets;
  }

  Widget _buildMenuItem(ShadcnContextMenuItem item, bool isSelected) {
    return _ContextMenuItemWidget(
      item: item,
      theme: widget.theme,
      isSelected: isSelected,
      onPressed: () => widget.onItemPressed(item),
    );
  }

  Widget _buildSeparator() {
    return Container(
      height: widget.theme.separatorHeight ?? 1,
      margin: const EdgeInsets.symmetric(vertical: 4),
      color: widget.theme.separatorColor,
    );
  }
}

class _ContextMenuItemWidget extends StatefulWidget {
  final ShadcnContextMenuItem item;
  final ShadcnContextMenuTheme theme;
  final bool isSelected;
  final VoidCallback onPressed;

  const _ContextMenuItemWidget({
    required this.item,
    required this.theme,
    required this.isSelected,
    required this.onPressed,
  });

  @override
  State<_ContextMenuItemWidget> createState() => _ContextMenuItemWidgetState();
}

class _ContextMenuItemWidgetState extends State<_ContextMenuItemWidget> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    final backgroundColor = _getBackgroundColor();
    final foregroundColor = _getForegroundColor();

    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: InkWell(
        onTap: widget.item.enabled ? widget.onPressed : null,
        child: Container(
          height: widget.theme.itemHeight,
          padding: widget.theme.itemPadding,
          color: backgroundColor,
          child: Row(
            children: [
              if (widget.item.icon != null) ...[
                IconTheme(
                  data: IconThemeData(
                    color: foregroundColor,
                    size: 16,
                  ),
                  child: widget.item.icon!,
                ),
                const SizedBox(width: 12),
              ],
              Expanded(
                child: Text(
                  widget.item.label,
                  style: widget.theme.itemTextStyle?.copyWith(
                    color: foregroundColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (widget.item.shortcut != null && widget.item.shortcut!.isNotEmpty) ...[
                const SizedBox(width: 8),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: widget.item.shortcut!.map((key) =>
                    Container(
                      margin: const EdgeInsets.only(left: 2),
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: widget.theme.shortcutBackground,
                        borderRadius: BorderRadius.circular(3),
                      ),
                      child: Text(
                        key,
                        style: widget.theme.shortcutTextStyle,
                      ),
                    ),
                  ).toList(),
                ),
              ],
              if (widget.item.submenu != null && widget.item.submenu!.isNotEmpty) ...[
                const SizedBox(width: 8),
                Icon(
                  Icons.chevron_right,
                  size: 16,
                  color: foregroundColor,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color? _getBackgroundColor() {
    if (!widget.item.enabled) {
      return widget.theme.itemDisabledBackground;
    }
    
    if (widget.isSelected || _isHovered) {
      return widget.item.destructive
          ? widget.theme.itemDestructiveHoverBackground
          : widget.theme.itemHoverBackground;
    }
    
    return widget.item.destructive
        ? widget.theme.itemDestructiveBackground
        : widget.theme.itemBackground;
  }

  Color? _getForegroundColor() {
    if (!widget.item.enabled) {
      return widget.theme.itemDisabledForeground;
    }
    
    if (widget.isSelected || _isHovered) {
      return widget.item.destructive
          ? widget.theme.itemDestructiveHoverForeground
          : widget.theme.itemHoverForeground;
    }
    
    return widget.item.destructive
        ? widget.theme.itemDestructiveForeground
        : widget.theme.itemForeground;
  }
}