import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_collapsible_theme.dart';
import '../../constants/shadcn_tokens.dart';

/// A shadcn-styled collapsible content component with comprehensive theming support.
/// 
/// This component provides collapsible/expandable content areas with smooth animations
/// and theme-aware styling. It maintains full Material Design integration and supports
/// all interactive states with keyboard navigation and accessibility features.
/// 
/// The component automatically derives its styling from the current theme context,
/// with fallback to Material Design values. All styling is customizable through
/// the [ShadcnCollapsibleTheme] extension.
/// 
/// Example usage:
/// ```dart
/// ShadcnCollapsible(
///   title: 'Click to expand',
///   content: Text('This is the collapsible content'),
///   initiallyExpanded: false,
/// )
/// ```
class ShadcnCollapsible extends ShadcnComponent with ShadcnComponentValidation {
  /// The title displayed on the collapsible trigger.
  final String? title;
  
  /// Custom trigger widget. Takes precedence over [title].
  final Widget? trigger;
  
  /// The content to show when expanded.
  final Widget content;
  
  /// Whether the collapsible is initially expanded.
  final bool initiallyExpanded;
  
  /// Callback when the expansion state changes.
  final ValueChanged<bool>? onExpansionChanged;
  
  /// Whether the collapsible can be interacted with.
  final bool enabled;
  
  /// Whether to show a border around the collapsible.
  final bool? showBorder;
  
  /// Custom border radius override.
  final BorderRadius? borderRadius;
  
  /// Custom content padding override.
  final EdgeInsets? contentPadding;
  
  /// Custom trigger padding override.
  final EdgeInsets? triggerPadding;
  
  /// Custom trigger height override.
  final double? triggerHeight;
  
  /// Custom expand icon widget.
  final Widget? expandIcon;
  
  /// Custom collapse icon widget.
  final Widget? collapseIcon;
  
  /// Whether to animate the icon rotation.
  final bool? animateIcon;
  
  /// Custom animation duration.
  final Duration? animationDuration;
  
  /// Custom animation curve.
  final Curve? animationCurve;
  
  /// Focus node for keyboard navigation.
  final FocusNode? focusNode;
  
  /// Whether to autofocus when first built.
  final bool autofocus;
  
  /// Tooltip for the trigger.
  final String? tooltip;
  
  /// Semantic label for accessibility.
  final String? semanticLabel;
  
  /// Whether to exclude from semantics tree.
  final bool excludeFromSemantics;
  
  /// Custom mouse cursor for the trigger.
  final MouseCursor? mouseCursor;
  
  /// Whether to enable haptic feedback.
  final bool enableFeedback;
  
  /// Custom background color for the trigger.
  final Color? triggerBackgroundColor;
  
  /// Custom background color for the content.
  final Color? contentBackgroundColor;
  
  /// Custom foreground color for the trigger text.
  final Color? triggerForegroundColor;
  
  /// Custom foreground color for the content.
  final Color? contentForegroundColor;
  
  /// Custom text style for the trigger.
  final TextStyle? triggerTextStyle;
  
  /// Custom text style for the content.
  final TextStyle? contentTextStyle;

  const ShadcnCollapsible({
    super.key,
    this.title,
    this.trigger,
    required this.content,
    this.initiallyExpanded = false,
    this.onExpansionChanged,
    this.enabled = true,
    this.showBorder,
    this.borderRadius,
    this.contentPadding,
    this.triggerPadding,
    this.triggerHeight,
    this.expandIcon,
    this.collapseIcon,
    this.animateIcon,
    this.animationDuration,
    this.animationCurve,
    this.focusNode,
    this.autofocus = false,
    this.tooltip,
    this.semanticLabel,
    this.excludeFromSemantics = false,
    this.mouseCursor,
    this.enableFeedback = true,
    this.triggerBackgroundColor,
    this.contentBackgroundColor,
    this.triggerForegroundColor,
    this.contentForegroundColor,
    this.triggerTextStyle,
    this.contentTextStyle,
  }) : assert(title != null || trigger != null, 'Either title or trigger must be provided');

  /// Factory constructor for a simple text-based collapsible.
  factory ShadcnCollapsible.text({
    Key? key,
    required String title,
    required Widget content,
    bool initiallyExpanded = false,
    ValueChanged<bool>? onExpansionChanged,
    bool enabled = true,
    String? tooltip,
  }) {
    return ShadcnCollapsible(
      key: key,
      title: title,
      content: content,
      initiallyExpanded: initiallyExpanded,
      onExpansionChanged: onExpansionChanged,
      enabled: enabled,
      tooltip: tooltip,
    );
  }

  /// Factory constructor for a collapsible with custom trigger.
  factory ShadcnCollapsible.custom({
    Key? key,
    required Widget trigger,
    required Widget content,
    bool initiallyExpanded = false,
    ValueChanged<bool>? onExpansionChanged,
    bool enabled = true,
    String? tooltip,
  }) {
    return ShadcnCollapsible(
      key: key,
      trigger: trigger,
      content: content,
      initiallyExpanded: initiallyExpanded,
      onExpansionChanged: onExpansionChanged,
      enabled: enabled,
      tooltip: tooltip,
    );
  }

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    // Validate theme context and properties
    validateThemeProperties(context);
    validateRequiredProperties({'content': content});
    validateAccessibility(
      semanticLabel: semanticLabel,
      tooltip: tooltip,
      excludeFromSemantics: excludeFromSemantics,
      componentName: 'ShadcnCollapsible',
    );

    // Resolve collapsible theme
    final collapsibleTheme = resolveTheme<ShadcnCollapsibleTheme>(
      context,
      ShadcnCollapsibleTheme.defaultTheme,
    );

    // Create stateful widget for expansion state management
    return _ShadcnCollapsibleStateful(
      collapsible: this,
      theme: collapsibleTheme,
      materialTheme: theme,
    );
  }
}

/// Stateful widget that manages the expansion state of the collapsible.
class _ShadcnCollapsibleStateful extends StatefulWidget {
  final ShadcnCollapsible collapsible;
  final ShadcnCollapsibleTheme theme;
  final ThemeData materialTheme;

  const _ShadcnCollapsibleStateful({
    required this.collapsible,
    required this.theme,
    required this.materialTheme,
  });

  @override
  State<_ShadcnCollapsibleStateful> createState() => _ShadcnCollapsibleStatefulState();
}

class _ShadcnCollapsibleStatefulState extends State<_ShadcnCollapsibleStateful>
    with TickerProviderStateMixin {
  late bool _isExpanded;
  late AnimationController _expansionController;
  late AnimationController _iconController;
  late Animation<double> _expansionAnimation;
  late Animation<double> _iconAnimation;

  ShadcnCollapsible get collapsible => widget.collapsible;
  ShadcnCollapsibleTheme get theme => widget.theme;
  ThemeData get materialTheme => widget.materialTheme;

  @override
  void initState() {
    super.initState();
    
    _isExpanded = collapsible.initiallyExpanded;
    
    // Initialize expansion animation controller
    _expansionController = AnimationController(
      duration: collapsible.animationDuration ?? theme.animationDuration ?? ShadcnTokens.durationNormal,
      vsync: this,
    );
    
    // Initialize icon animation controller
    _iconController = AnimationController(
      duration: collapsible.animationDuration ?? theme.animationDuration ?? ShadcnTokens.durationNormal,
      vsync: this,
    );
    
    // Create animations
    _expansionAnimation = CurvedAnimation(
      parent: _expansionController,
      curve: collapsible.animationCurve ?? theme.animationCurve ?? Curves.easeInOut,
    );
    
    _iconAnimation = Tween<double>(
      begin: 0.0,
      end: theme.iconRotation ?? 0.5,
    ).animate(CurvedAnimation(
      parent: _iconController,
      curve: collapsible.animationCurve ?? theme.animationCurve ?? Curves.easeInOut,
    ));
    
    // Set initial states
    if (_isExpanded) {
      _expansionController.value = 1.0;
      _iconController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _expansionController.dispose();
    _iconController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(_ShadcnCollapsibleStateful oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update animation duration if changed
    final newDuration = collapsible.animationDuration ?? theme.animationDuration ?? ShadcnTokens.durationNormal;
    if (newDuration != _expansionController.duration) {
      _expansionController.duration = newDuration;
      _iconController.duration = newDuration;
    }
  }

  void _handleTap() {
    if (!collapsible.enabled) return;
    
    if (collapsible.enableFeedback) {
      HapticFeedback.selectionClick();
    }
    
    setState(() {
      _isExpanded = !_isExpanded;
    });
    
    if (_isExpanded) {
      _expansionController.forward();
      if (collapsible.animateIcon ?? theme.animateIcon ?? true) {
        _iconController.forward();
      }
    } else {
      _expansionController.reverse();
      if (collapsible.animateIcon ?? theme.animateIcon ?? true) {
        _iconController.reverse();
      }
    }
    
    collapsible.onExpansionChanged?.call(_isExpanded);
  }

  @override
  Widget build(BuildContext context) {
    final effectiveShowBorder = collapsible.showBorder ?? theme.showBorder ?? false;
    final effectiveBorderRadius = collapsible.borderRadius ?? theme.borderRadius ?? 
        BorderRadius.circular(ShadcnTokens.radiusMd);
    
    Widget result = Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        _buildTrigger(),
        _buildContent(),
      ],
    );
    
    // Add border if specified
    if (effectiveShowBorder) {
      result = Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: theme.borderColor ?? materialTheme.colorScheme.outline,
            width: theme.borderWidth ?? ShadcnTokens.borderWidth,
          ),
          borderRadius: effectiveBorderRadius,
        ),
        child: ClipRRect(
          borderRadius: effectiveBorderRadius,
          child: result,
        ),
      );
    }
    
    // Add semantics
    if (!collapsible.excludeFromSemantics) {
      result = Semantics(
        label: collapsible.semanticLabel ?? collapsible.title,
        button: true,
        enabled: collapsible.enabled,
        expanded: _isExpanded,
        child: result,
      );
    }
    
    // Add tooltip
    if (collapsible.tooltip != null) {
      result = Tooltip(
        message: collapsible.tooltip!,
        child: result,
      );
    }
    
    return result;
  }

  Widget _buildTrigger() {
    final effectiveTriggerPadding = collapsible.triggerPadding ?? theme.triggerPadding ?? 
        const EdgeInsets.symmetric(horizontal: ShadcnTokens.spacing4, vertical: ShadcnTokens.spacing2);
    
    final effectiveTriggerHeight = collapsible.triggerHeight ?? theme.triggerHeight ?? 44.0;
    
    final triggerBackgroundColor = collapsible.enabled
        ? (collapsible.triggerBackgroundColor ?? theme.triggerBackground ?? Colors.transparent)
        : (theme.triggerDisabledBackground ?? Colors.transparent);
    
    final triggerForegroundColor = collapsible.enabled
        ? (collapsible.triggerForegroundColor ?? theme.triggerForeground ?? materialTheme.colorScheme.onSurface)
        : (theme.triggerDisabledForeground ?? materialTheme.colorScheme.onSurface.withOpacity(0.38));
    
    final triggerTextStyle = (collapsible.triggerTextStyle ?? theme.triggerTextStyle ?? 
        TextStyle(
          fontSize: ShadcnTokens.fontSizeMd,
          fontWeight: theme.triggerFontWeight ?? ShadcnTokens.fontWeightMedium,
        )).copyWith(color: triggerForegroundColor);
    
    return Material(
      color: triggerBackgroundColor,
      child: InkWell(
        onTap: collapsible.enabled ? _handleTap : null,
        hoverColor: theme.triggerHoverBackground ?? materialTheme.colorScheme.onSurface.withOpacity(0.04),
        splashColor: theme.triggerPressedBackground ?? materialTheme.colorScheme.onSurface.withOpacity(0.08),
        mouseCursor: collapsible.mouseCursor ?? (collapsible.enabled ? SystemMouseCursors.click : SystemMouseCursors.basic),
        focusNode: collapsible.focusNode,
        autofocus: collapsible.autofocus,
        child: Container(
          height: effectiveTriggerHeight,
          padding: effectiveTriggerPadding,
          child: Row(
            mainAxisAlignment: theme.triggerAlignment ?? MainAxisAlignment.spaceBetween,
            crossAxisAlignment: theme.triggerCrossAlignment ?? CrossAxisAlignment.center,
            children: [
              // Trigger content
              Expanded(
                child: collapsible.trigger ?? Text(
                  collapsible.title!,
                  style: triggerTextStyle,
                ),
              ),
              
              // Icon
              if (theme.iconSpacing != null) SizedBox(width: theme.iconSpacing!),
              _buildIcon(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIcon() {
    final iconSize = theme.iconSize ?? ShadcnTokens.iconSizeMd;
    final iconColor = collapsible.enabled
        ? (collapsible.triggerForegroundColor ?? theme.triggerForeground ?? materialTheme.colorScheme.onSurface)
        : (theme.triggerDisabledForeground ?? materialTheme.colorScheme.onSurface.withOpacity(0.38));
    
    Widget icon = _isExpanded
        ? (collapsible.collapseIcon ?? theme.collapseIcon ?? const Icon(Icons.keyboard_arrow_up))
        : (collapsible.expandIcon ?? theme.expandIcon ?? const Icon(Icons.keyboard_arrow_down));
    
    // Apply icon theme
    icon = IconTheme(
      data: IconThemeData(
        size: iconSize,
        color: iconColor,
      ),
      child: icon,
    );
    
    // Apply animation if enabled
    if (collapsible.animateIcon ?? theme.animateIcon ?? true) {
      icon = AnimatedBuilder(
        animation: _iconAnimation,
        builder: (context, child) {
          return Transform.rotate(
            angle: _iconAnimation.value * 3.14159, // Convert to radians
            child: child,
          );
        },
        child: collapsible.expandIcon ?? theme.expandIcon ?? const Icon(Icons.keyboard_arrow_down),
      );
      
      // Apply icon theme after animation
      icon = IconTheme(
        data: IconThemeData(
          size: iconSize,
          color: iconColor,
        ),
        child: icon,
      );
    }
    
    return SizedBox(
      width: iconSize,
      height: iconSize,
      child: icon,
    );
  }

  Widget _buildContent() {
    final contentPadding = collapsible.contentPadding ?? theme.contentPadding ?? 
        const EdgeInsets.all(ShadcnTokens.spacing4);
    
    final contentBackgroundColor = collapsible.contentBackgroundColor ?? theme.contentBackground ?? Colors.transparent;
    
    return SizeTransition(
      axisAlignment: 0.0,
      sizeFactor: _expansionAnimation,
      child: Container(
        color: contentBackgroundColor,
        padding: contentPadding,
        child: DefaultTextStyle(
          style: (collapsible.contentTextStyle ?? theme.contentTextStyle ?? 
              TextStyle(
                fontSize: ShadcnTokens.fontSizeMd,
                color: collapsible.contentForegroundColor ?? theme.contentForeground ?? materialTheme.colorScheme.onSurface,
              )),
          child: collapsible.content,
        ),
      ),
    );
  }
}