import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_navigation_menu_theme.dart';
import '../../constants/shadcn_tokens.dart';

/// A shadcn-styled navigation menu component with dropdown and nested menu support.
/// 
/// Navigation menus provide a way to organize and display navigation options
/// with support for dropdowns, nested menus, and active state management.
/// This component follows shadcn design principles while integrating
/// seamlessly with Material Design theming.
/// 
/// Features:
/// - Horizontal and vertical orientations
/// - Dropdown menu support with positioning
/// - Nested menu capabilities
/// - Active/inactive state management
/// - Theme-aware hover and focus states
/// - Full accessibility and keyboard navigation
/// - Material Design integration
/// 
/// Example usage:
/// ```dart
/// ShadcnNavigationMenu(
///   items: [
///     ShadcnNavigationMenuItem(
///       text: 'Home',
///       onTap: () => Navigator.pushNamed(context, '/'),
///     ),
///     ShadcnNavigationMenuItem(
///       text: 'Products',
///       children: [
///         ShadcnNavigationMenuItem(
///           text: 'Electronics',
///           onTap: () => Navigator.pushNamed(context, '/products/electronics'),
///         ),
///         ShadcnNavigationMenuItem(
///           text: 'Clothing',
///           onTap: () => Navigator.pushNamed(context, '/products/clothing'),
///         ),
///       ],
///     ),
///     ShadcnNavigationMenuItem(
///       text: 'About',
///       active: true,
///       onTap: () => Navigator.pushNamed(context, '/about'),
///     ),
///   ],
/// )
/// ```
class ShadcnNavigationMenu extends ShadcnComponent with ShadcnComponentValidation {
  /// List of navigation menu items to display
  final List<ShadcnNavigationMenuItem> items;
  
  /// Orientation of the navigation menu
  final Axis orientation;
  
  /// Callback when a menu item is selected
  final ValueChanged<ShadcnNavigationMenuItem>? onItemSelected;
  
  /// Whether the entire navigation menu is disabled
  final bool disabled;
  
  /// Custom theme data override
  final ShadcnNavigationMenuTheme? theme;
  
  /// Optional semantic label for the navigation menu
  final String? semanticLabel;
  
  /// Whether to show indicator for active items
  final bool showActiveIndicator;
  
  /// Position of the active indicator
  final NavigationIndicatorPosition indicatorPosition;
  
  const ShadcnNavigationMenu({
    super.key,
    required this.items,
    this.orientation = Axis.horizontal,
    this.onItemSelected,
    this.disabled = false,
    this.theme,
    this.semanticLabel,
    this.showActiveIndicator = true,
    this.indicatorPosition = NavigationIndicatorPosition.bottom,
  });

  @override
  Widget buildWithTheme(BuildContext context, ThemeData materialTheme) {
    // Validate inputs
    validateRequiredProperties({'items': items});
    validateThemeProperties(context);
    
    if (items.isEmpty) {
      return const SizedBox.shrink();
    }
    
    // Resolve theme
    final navTheme = theme ?? 
        resolveTheme<ShadcnNavigationMenuTheme>(
          context,
          ShadcnNavigationMenuTheme.defaultTheme,
        );
    
    return Semantics(
      label: semanticLabel ?? 'Navigation menu',
      container: true,
      child: Container(
        constraints: BoxConstraints(
          minHeight: orientation == Axis.horizontal 
              ? resolveDouble(
                  context,
                  navTheme.minHeight,
                  ShadcnTokens.buttonHeightMd,
                )
              : 0,
        ),
        padding: resolveSpacing(
          context,
          navTheme.containerPadding,
          const EdgeInsets.symmetric(
            horizontal: ShadcnTokens.spacing1,
            vertical: ShadcnTokens.spacing1,
          ),
        ),
        decoration: BoxDecoration(
          color: resolveColor(
            context,
            navTheme.backgroundColor,
            (theme) => Colors.transparent,
          ),
          border: navTheme.border,
          borderRadius: resolveBorderRadius(
            context,
            navTheme.borderRadius,
            BorderRadius.circular(ShadcnTokens.radiusMd),
          ),
          boxShadow: navTheme.shadows,
        ),
        child: orientation == Axis.horizontal
            ? Row(
                mainAxisSize: MainAxisSize.min,
                children: _buildMenuItems(context, navTheme),
              )
            : Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: _buildMenuItems(context, navTheme),
              ),
      ),
    );
  }
  
  /// Builds the list of menu items
  List<Widget> _buildMenuItems(BuildContext context, ShadcnNavigationMenuTheme theme) {
    final children = <Widget>[];
    
    for (int i = 0; i < items.length; i++) {
      final item = items[i];
      
      // Add spacing between items (except first)
      if (i > 0) {
        if (orientation == Axis.horizontal) {
          children.add(SizedBox(
            width: resolveDouble(
              context,
              theme.itemSpacing,
              ShadcnTokens.spacing1,
            ),
          ));
        } else {
          children.add(SizedBox(
            height: resolveDouble(
              context,
              theme.itemSpacing,
              ShadcnTokens.spacing1,
            ),
          ));
        }
      }
      
      // Add the menu item
      children.add(_buildMenuItem(context, theme, item, i));
      
      // Add divider if requested
      if (item.showDividerAfter && i < items.length - 1) {
        children.add(_buildDivider(context, theme));
      }
    }
    
    return children;
  }
  
  /// Builds a divider between menu items
  Widget _buildDivider(BuildContext context, ShadcnNavigationMenuTheme theme) {
    return Container(
      margin: resolveSpacing(
        context,
        theme.dividerMargin,
        const EdgeInsets.symmetric(vertical: ShadcnTokens.spacing1),
      ),
      child: orientation == Axis.horizontal
          ? Container(
              width: resolveDouble(
                context,
                theme.dividerThickness,
                ShadcnTokens.borderWidth,
              ),
              height: resolveDouble(
                context,
                theme.dividerHeight,
                24.0,
              ),
              color: resolveColor(
                context,
                theme.dividerColor,
                (materialTheme) => materialTheme.colorScheme.outline.withOpacity(0.2),
              ),
            )
          : Container(
              height: resolveDouble(
                context,
                theme.dividerThickness,
                ShadcnTokens.borderWidth,
              ),
              color: resolveColor(
                context,
                theme.dividerColor,
                (materialTheme) => materialTheme.colorScheme.outline.withOpacity(0.2),
              ),
            ),
    );
  }
  
  /// Builds an individual menu item
  Widget _buildMenuItem(
    BuildContext context, 
    ShadcnNavigationMenuTheme theme,
    ShadcnNavigationMenuItem item,
    int index,
  ) {
    if (item.hasChildren) {
      return _buildDropdownMenuItem(context, theme, item, index);
    } else {
      return _buildSimpleMenuItem(context, theme, item, index);
    }
  }
  
  /// Builds a simple (non-dropdown) menu item
  Widget _buildSimpleMenuItem(
    BuildContext context,
    ShadcnNavigationMenuTheme theme,
    ShadcnNavigationMenuItem item,
    int index,
  ) {
    final isInteractive = !disabled && !item.disabled && item.isInteractive;
    
    // Determine colors based on state
    Color textColor;
    Color backgroundColor;
    
    if (item.disabled || disabled) {
      textColor = resolveColor(
        context,
        theme.itemDisabledColor,
        (materialTheme) => materialTheme.colorScheme.onSurface.withOpacity(0.38),
      );
      backgroundColor = resolveColor(
        context,
        theme.itemDisabledBackgroundColor,
        (materialTheme) => Colors.transparent,
      );
    } else if (item.active) {
      textColor = resolveColor(
        context,
        theme.itemActiveColor,
        (materialTheme) => materialTheme.colorScheme.onSurface,
      );
      backgroundColor = resolveColor(
        context,
        theme.itemActiveBackgroundColor,
        (materialTheme) => materialTheme.colorScheme.primary.withOpacity(0.1),
      );
    } else {
      textColor = resolveColor(
        context,
        theme.itemColor,
        (materialTheme) => materialTheme.colorScheme.onSurface.withOpacity(0.7),
      );
      backgroundColor = resolveColor(
        context,
        theme.itemBackgroundColor,
        (materialTheme) => Colors.transparent,
      );
    }
    
    // Determine text style
    final baseTextStyle = resolveTextStyle(
      context,
      item.active ? theme.activeItemTextStyle : theme.itemTextStyle,
      (textTheme) => textTheme.labelLarge!,
    );
    
    final effectiveTextStyle = baseTextStyle.copyWith(
      color: textColor,
      fontWeight: item.active 
          ? ShadcnTokens.fontWeightSemibold
          : ShadcnTokens.fontWeightMedium,
    );
    
    Widget child = Container(
      constraints: BoxConstraints(
        minHeight: resolveDouble(
          context,
          theme.itemHeight,
          ShadcnTokens.buttonHeightSm,
        ),
        minWidth: orientation == Axis.horizontal
            ? resolveDouble(
                context,
                theme.itemMinWidth,
                64.0,
              )
            : 0,
      ),
      padding: resolveSpacing(
        context,
        theme.itemPadding,
        const EdgeInsets.symmetric(
          horizontal: ShadcnTokens.spacing3,
          vertical: ShadcnTokens.spacing2,
        ),
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: resolveBorderRadius(
          context,
          theme.itemBorderRadius,
          BorderRadius.circular(ShadcnTokens.radiusSm),
        ),
      ),
      child: _buildItemContent(context, theme, item, effectiveTextStyle),
    );
    
    // Add active indicator if needed
    if (item.active && showActiveIndicator) {
      child = _buildItemWithIndicator(context, theme, child);
    }
    
    // Wrap with interactive behavior
    if (isInteractive) {
      child = Material(
        type: MaterialType.transparency,
        child: InkWell(
          onTap: () {
            item.onTap?.call();
            onItemSelected?.call(item);
          },
          borderRadius: resolveBorderRadius(
            context,
            theme.itemBorderRadius,
            BorderRadius.circular(ShadcnTokens.radiusSm),
          ),
          hoverColor: resolveColor(
            context,
            theme.itemHoverBackgroundColor,
            (materialTheme) => materialTheme.colorScheme.onSurface.withOpacity(0.04),
          ),
          focusColor: resolveColor(
            context,
            theme.itemFocusBackgroundColor,
            (materialTheme) => materialTheme.colorScheme.primary.withOpacity(0.12),
          ),
          splashColor: resolveColor(
            context,
            theme.itemPressedBackgroundColor,
            (materialTheme) => materialTheme.colorScheme.onSurface.withOpacity(0.08),
          ),
          child: child,
        ),
      );
    }
    
    // Add semantic information
    if (item.semanticLabel != null || item.tooltip != null) {
      child = Semantics(
        label: item.semanticLabel ?? item.text,
        button: isInteractive,
        child: child,
      );
      
      if (item.tooltip != null) {
        child = Tooltip(
          message: item.tooltip!,
          child: child,
        );
      }
    }
    
    return child;
  }
  
  /// Builds a dropdown menu item with children
  Widget _buildDropdownMenuItem(
    BuildContext context,
    ShadcnNavigationMenuTheme theme,
    ShadcnNavigationMenuItem item,
    int index,
  ) {
    return PopupMenuButton<ShadcnNavigationMenuItem>(
      itemBuilder: (context) => item.children!
          .map((child) => _buildPopupMenuItem(context, theme, child))
          .toList(),
      onSelected: (selectedItem) {
        selectedItem.onTap?.call();
        onItemSelected?.call(selectedItem);
      },
      offset: Offset(0, resolveDouble(
        context,
        theme.dropdownOffset,
        4.0,
      )),
      elevation: resolveDouble(
        context,
        theme.dropdownElevation,
        ShadcnTokens.elevationMd,
      ),
      color: resolveColor(
        context,
        theme.dropdownBackgroundColor,
        (materialTheme) => materialTheme.colorScheme.surface,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: resolveBorderRadius(
          context,
          theme.dropdownBorderRadius,
          BorderRadius.circular(ShadcnTokens.radiusMd),
        ),
      ),
      child: _buildSimpleMenuItem(context, theme, item.copyWith(
        trailing: item.trailing ?? Icon(
          Icons.keyboard_arrow_down,
          size: resolveDouble(
            context,
            theme.iconSize,
            ShadcnTokens.iconSizeSm,
          ),
        ),
      ), index),
    );
  }
  
  /// Builds a popup menu item for dropdown
  PopupMenuEntry<ShadcnNavigationMenuItem> _buildPopupMenuItem(
    BuildContext context,
    ShadcnNavigationMenuTheme theme,
    ShadcnNavigationMenuItem item,
  ) {
    return PopupMenuItem<ShadcnNavigationMenuItem>(
      value: item,
      enabled: !item.disabled,
      padding: resolveSpacing(
        context,
        theme.dropdownItemPadding,
        const EdgeInsets.symmetric(
          horizontal: ShadcnTokens.spacing3,
          vertical: ShadcnTokens.spacing2,
        ),
      ),
      child: _buildItemContent(
        context,
        theme,
        item,
        resolveTextStyle(
          context,
          theme.itemTextStyle,
          (textTheme) => textTheme.bodyMedium!,
        ),
      ),
    );
  }
  
  /// Builds the content of a menu item (icon + text + trailing)
  Widget _buildItemContent(
    BuildContext context,
    ShadcnNavigationMenuTheme theme,
    ShadcnNavigationMenuItem item,
    TextStyle textStyle,
  ) {
    final iconColor = item.active
        ? resolveColor(
            context,
            theme.activeIconColor,
            (materialTheme) => materialTheme.colorScheme.onSurface,
          )
        : resolveColor(
            context,
            theme.iconColor,
            (materialTheme) => materialTheme.colorScheme.onSurface.withOpacity(0.7),
          );
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (item.leading != null) ...[
          IconTheme(
            data: IconThemeData(
              color: iconColor,
              size: resolveDouble(
                context,
                theme.iconSize,
                ShadcnTokens.iconSizeSm,
              ),
            ),
            child: item.leading!,
          ),
          SizedBox(width: resolveDouble(
            context,
            theme.iconSpacing,
            ShadcnTokens.spacing2,
          )),
        ],
        if (item.icon != null) ...[
          IconTheme(
            data: IconThemeData(
              color: iconColor,
              size: resolveDouble(
                context,
                theme.iconSize,
                ShadcnTokens.iconSizeSm,
              ),
            ),
            child: item.icon!,
          ),
          SizedBox(width: resolveDouble(
            context,
            theme.iconSpacing,
            ShadcnTokens.spacing2,
          )),
        ],
        Flexible(
          child: Text(
            item.text,
            style: textStyle,
          ),
        ),
        if (item.trailing != null) ...[
          SizedBox(width: resolveDouble(
            context,
            theme.iconSpacing,
            ShadcnTokens.spacing2,
          )),
          IconTheme(
            data: IconThemeData(
              color: iconColor,
              size: resolveDouble(
                context,
                theme.iconSize,
                ShadcnTokens.iconSizeSm,
              ),
            ),
            child: item.trailing!,
          ),
        ],
      ],
    );
  }
  
  /// Builds a menu item with an active indicator
  Widget _buildItemWithIndicator(
    BuildContext context,
    ShadcnNavigationMenuTheme theme,
    Widget child,
  ) {
    final indicatorColor = resolveColor(
      context,
      theme.indicatorColor,
      (materialTheme) => materialTheme.colorScheme.primary,
    );
    
    final indicator = Container(
      width: indicatorPosition == NavigationIndicatorPosition.bottom ||
             indicatorPosition == NavigationIndicatorPosition.top
          ? resolveDouble(
              context,
              theme.indicatorWidth,
              24.0,
            )
          : resolveDouble(
              context,
              theme.indicatorHeight,
              2.0,
            ),
      height: indicatorPosition == NavigationIndicatorPosition.left ||
              indicatorPosition == NavigationIndicatorPosition.right
          ? resolveDouble(
              context,
              theme.indicatorWidth,
              24.0,
            )
          : resolveDouble(
              context,
              theme.indicatorHeight,
              2.0,
            ),
      decoration: BoxDecoration(
        color: indicatorColor,
        borderRadius: resolveBorderRadius(
          context,
          theme.indicatorBorderRadius,
          BorderRadius.circular(1.0),
        ),
      ),
    );
    
    switch (indicatorPosition) {
      case NavigationIndicatorPosition.top:
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [indicator, child],
        );
      case NavigationIndicatorPosition.bottom:
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [child, indicator],
        );
      case NavigationIndicatorPosition.left:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [indicator, child],
        );
      case NavigationIndicatorPosition.right:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [child, indicator],
        );
    }
  }
}

/// Extension to add copyWith functionality to ShadcnNavigationMenuItem
extension ShadcnNavigationMenuItemCopyWith on ShadcnNavigationMenuItem {
  ShadcnNavigationMenuItem copyWith({
    String? text,
    Widget? icon,
    Widget? leading,
    Widget? trailing,
    VoidCallback? onTap,
    List<ShadcnNavigationMenuItem>? children,
    bool? active,
    bool? disabled,
    String? semanticLabel,
    String? tooltip,
    bool? showDividerAfter,
  }) {
    return ShadcnNavigationMenuItem(
      text: text ?? this.text,
      icon: icon ?? this.icon,
      leading: leading ?? this.leading,
      trailing: trailing ?? this.trailing,
      onTap: onTap ?? this.onTap,
      children: children ?? this.children,
      active: active ?? this.active,
      disabled: disabled ?? this.disabled,
      semanticLabel: semanticLabel ?? this.semanticLabel,
      tooltip: tooltip ?? this.tooltip,
      showDividerAfter: showDividerAfter ?? this.showDividerAfter,
    );
  }
}

/// Enum for navigation indicator positions
enum NavigationIndicatorPosition {
  top,
  bottom,
  left,
  right,
}