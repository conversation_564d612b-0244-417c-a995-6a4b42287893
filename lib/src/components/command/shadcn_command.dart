import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_command_theme.dart';

/// Data model for a command item
class ShadcnCommandItem {
  /// The unique identifier for the command
  final String id;
  
  /// The display label for the command
  final String label;
  
  /// Optional description for the command
  final String? description;
  
  /// Optional icon for the command
  final Widget? icon;
  
  /// Optional keyboard shortcut for the command
  final List<String>? shortcut;
  
  /// Callback when the command is executed
  final VoidCallback? onPressed;
  
  /// Whether the command is enabled
  final bool enabled;
  
  /// Optional keywords for search matching
  final List<String>? keywords;

  const ShadcnCommandItem({
    required this.id,
    required this.label,
    this.description,
    this.icon,
    this.shortcut,
    this.onPressed,
    this.enabled = true,
    this.keywords,
  });
}

/// Data model for a command group
class ShadcnCommandGroup {
  /// The unique identifier for the group
  final String id;
  
  /// The display label for the group
  final String label;
  
  /// List of commands in this group
  final List<ShadcnCommandItem> commands;

  const ShadcnCommandGroup({
    required this.id,
    required this.label,
    required this.commands,
  });
}

/// A command palette component with search functionality and keyboard navigation
/// 
/// The ShadcnCommand widget provides a searchable command palette interface
/// with keyboard navigation, grouping, and theme-aware styling.
class ShadcnCommand extends ShadcnComponent {
  /// The list of command groups to display
  final List<ShadcnCommandGroup> groups;
  
  /// Placeholder text for the search input
  final String? placeholder;
  
  /// Whether to show the search input
  final bool showSearch;
  
  /// Maximum height of the command palette
  final double? maxHeight;
  
  /// Maximum width of the command palette
  final double? maxWidth;
  
  /// Callback when a command is selected
  final ValueChanged<ShadcnCommandItem>? onCommand;
  
  /// Whether to auto-focus the search input
  final bool autofocus;
  
  /// Custom empty message when no commands match
  final Widget? emptyMessage;
  
  /// Whether to close on command selection
  final bool closeOnSelect;

  const ShadcnCommand({
    Key? key,
    required this.groups,
    this.placeholder,
    this.showSearch = true,
    this.maxHeight,
    this.maxWidth,
    this.onCommand,
    this.autofocus = true,
    this.emptyMessage,
    this.closeOnSelect = true,
  }) : super(key: key);

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    final commandTheme = theme.extension<ShadcnCommandTheme>() ??
        ShadcnCommandTheme.defaultTheme(theme.colorScheme);

    return Container(
      constraints: BoxConstraints(
        maxHeight: maxHeight ?? commandTheme.maxHeight ?? 400,
        maxWidth: maxWidth ?? commandTheme.maxWidth ?? 640,
      ),
      decoration: BoxDecoration(
        color: commandTheme.background,
        border: Border.all(color: commandTheme.border ?? Colors.transparent),
        borderRadius: commandTheme.borderRadius,
        boxShadow: commandTheme.shadow,
      ),
      child: Material(
        color: Colors.transparent,
        child: _CommandPalette(
          groups: groups,
          theme: commandTheme,
          placeholder: placeholder,
          showSearch: showSearch,
          onCommand: onCommand,
          autofocus: autofocus,
          emptyMessage: emptyMessage,
          closeOnSelect: closeOnSelect,
        ),
      ),
    );
  }
}

class _CommandPalette extends StatefulWidget {
  final List<ShadcnCommandGroup> groups;
  final ShadcnCommandTheme theme;
  final String? placeholder;
  final bool showSearch;
  final ValueChanged<ShadcnCommandItem>? onCommand;
  final bool autofocus;
  final Widget? emptyMessage;
  final bool closeOnSelect;

  const _CommandPalette({
    required this.groups,
    required this.theme,
    this.placeholder,
    required this.showSearch,
    this.onCommand,
    required this.autofocus,
    this.emptyMessage,
    required this.closeOnSelect,
  });

  @override
  State<_CommandPalette> createState() => _CommandPaletteState();
}

class _CommandPaletteState extends State<_CommandPalette> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  final ScrollController _scrollController = ScrollController();
  
  List<ShadcnCommandGroup> _filteredGroups = [];
  List<ShadcnCommandItem> _flatCommands = [];
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    _updateFilteredGroups();
    _searchController.addListener(_onSearchChanged);
    
    if (widget.autofocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _searchFocusNode.requestFocus();
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    _updateFilteredGroups();
    setState(() {
      _selectedIndex = 0;
    });
  }

  void _updateFilteredGroups() {
    final query = _searchController.text.toLowerCase();
    
    if (query.isEmpty) {
      _filteredGroups = widget.groups;
    } else {
      _filteredGroups = widget.groups.map((group) {
        final filteredCommands = group.commands.where((command) {
          return _matchesSearch(command, query);
        }).toList();
        
        return filteredCommands.isEmpty 
            ? null 
            : ShadcnCommandGroup(
                id: group.id,
                label: group.label,
                commands: filteredCommands,
              );
      }).where((group) => group != null).cast<ShadcnCommandGroup>().toList();
    }
    
    // Create flat list for keyboard navigation
    _flatCommands = _filteredGroups
        .expand((group) => group.commands)
        .where((command) => command.enabled)
        .toList();
    
    if (_selectedIndex >= _flatCommands.length && _flatCommands.isNotEmpty) {
      _selectedIndex = _flatCommands.length - 1;
    }
  }

  bool _matchesSearch(ShadcnCommandItem command, String query) {
    // Check label
    if (command.label.toLowerCase().contains(query)) return true;
    
    // Check description
    if (command.description?.toLowerCase().contains(query) == true) return true;
    
    // Check keywords
    if (command.keywords?.any((keyword) => 
        keyword.toLowerCase().contains(query)) == true) {
      return true;
    }
    
    return false;
  }

  void _executeCommand(ShadcnCommandItem command) {
    if (command.enabled) {
      widget.onCommand?.call(command);
      command.onPressed?.call();
      
      if (widget.closeOnSelect) {
        Navigator.of(context).maybePop();
      }
    }
  }

  void _onKeyPressed(RawKeyEvent event) {
    if (event is RawKeyDownEvent) {
      if (event.logicalKey == LogicalKeyboardKey.arrowDown) {
        setState(() {
          _selectedIndex = (_selectedIndex + 1) % _flatCommands.length;
        });
        _scrollToSelected();
      } else if (event.logicalKey == LogicalKeyboardKey.arrowUp) {
        setState(() {
          _selectedIndex = (_selectedIndex - 1 + _flatCommands.length) % _flatCommands.length;
        });
        _scrollToSelected();
      } else if (event.logicalKey == LogicalKeyboardKey.enter) {
        if (_flatCommands.isNotEmpty && _selectedIndex < _flatCommands.length) {
          _executeCommand(_flatCommands[_selectedIndex]);
        }
      } else if (event.logicalKey == LogicalKeyboardKey.escape) {
        Navigator.of(context).maybePop();
      }
    }
  }

  void _scrollToSelected() {
    // Simple scroll to keep selected item visible
    final itemHeight = widget.theme.itemHeight ?? 48;
    final targetOffset = _selectedIndex * itemHeight;
    
    _scrollController.animateTo(
      targetOffset,
      duration: const Duration(milliseconds: 150),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return RawKeyboardListener(
      focusNode: FocusNode(),
      onKey: _onKeyPressed,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          if (widget.showSearch) _buildSearchInput(),
          Flexible(
            child: _filteredGroups.isEmpty 
                ? _buildEmptyState()
                : _buildCommandList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchInput() {
    return Container(
      padding: widget.theme.searchPadding,
      child: TextField(
        controller: _searchController,
        focusNode: _searchFocusNode,
        style: widget.theme.searchTextStyle,
        decoration: InputDecoration(
          hintText: widget.placeholder ?? 'Type a command or search...',
          hintStyle: TextStyle(color: widget.theme.searchPlaceholder),
          border: InputBorder.none,
          filled: true,
          fillColor: widget.theme.searchBackground,
          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: widget.emptyMessage ?? 
          Text(
            'No commands found.',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: widget.theme.foreground?.withOpacity(0.7),
              fontSize: 14,
            ),
          ),
    );
  }

  Widget _buildCommandList() {
    int commandIndex = 0;
    
    return Scrollbar(
      controller: _scrollController,
      child: ListView.builder(
        controller: _scrollController,
        shrinkWrap: true,
        itemCount: _filteredGroups.length,
        itemBuilder: (context, groupIndex) {
          final group = _filteredGroups[groupIndex];
          final groupStartIndex = commandIndex;
          commandIndex += group.commands.where((c) => c.enabled).length;
          
          return _CommandGroupWidget(
            group: group,
            theme: widget.theme,
            startIndex: groupStartIndex,
            selectedIndex: _selectedIndex,
            onCommandPressed: _executeCommand,
          );
        },
      ),
    );
  }
}

class _CommandGroupWidget extends StatelessWidget {
  final ShadcnCommandGroup group;
  final ShadcnCommandTheme theme;
  final int startIndex;
  final int selectedIndex;
  final ValueChanged<ShadcnCommandItem> onCommandPressed;

  const _CommandGroupWidget({
    required this.group,
    required this.theme,
    required this.startIndex,
    required this.selectedIndex,
    required this.onCommandPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Group header
        Container(
          padding: theme.groupPadding,
          color: theme.groupBackground,
          child: Text(
            group.label,
            style: theme.groupTextStyle,
          ),
        ),
        // Group commands
        ...group.commands.asMap().entries.map((entry) {
          final index = entry.key;
          final command = entry.value;
          final globalIndex = startIndex + index;
          final isSelected = globalIndex == selectedIndex && command.enabled;
          
          return _CommandItemWidget(
            command: command,
            theme: theme,
            isSelected: isSelected,
            onPressed: () => onCommandPressed(command),
          );
        }),
      ],
    );
  }
}

class _CommandItemWidget extends StatefulWidget {
  final ShadcnCommandItem command;
  final ShadcnCommandTheme theme;
  final bool isSelected;
  final VoidCallback onPressed;

  const _CommandItemWidget({
    required this.command,
    required this.theme,
    required this.isSelected,
    required this.onPressed,
  });

  @override
  State<_CommandItemWidget> createState() => _CommandItemWidgetState();
}

class _CommandItemWidgetState extends State<_CommandItemWidget> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    final backgroundColor = widget.isSelected
        ? widget.theme.itemSelectedBackground
        : _isHovered
            ? widget.theme.itemHoverBackground
            : widget.theme.itemBackground;

    final foregroundColor = widget.isSelected
        ? widget.theme.itemSelectedForeground
        : _isHovered
            ? widget.theme.itemHoverForeground
            : widget.theme.itemForeground;

    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: InkWell(
        onTap: widget.command.enabled ? widget.onPressed : null,
        child: Container(
          height: widget.theme.itemHeight,
          padding: widget.theme.itemPadding,
          color: backgroundColor,
          child: Row(
            children: [
              if (widget.command.icon != null) ...[
                IconTheme(
                  data: IconThemeData(
                    color: foregroundColor,
                    size: 16,
                  ),
                  child: widget.command.icon!,
                ),
                const SizedBox(width: 12),
              ],
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      widget.command.label,
                      style: widget.theme.itemTextStyle?.copyWith(
                        color: widget.command.enabled 
                            ? foregroundColor 
                            : foregroundColor?.withOpacity(0.5),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (widget.command.description != null) ...[
                      const SizedBox(height: 2),
                      Text(
                        widget.command.description!,
                        style: widget.theme.itemTextStyle?.copyWith(
                          fontSize: 12,
                          color: (widget.command.enabled 
                              ? foregroundColor 
                              : foregroundColor?.withOpacity(0.5))?.withOpacity(0.7),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
              if (widget.command.shortcut != null && widget.command.shortcut!.isNotEmpty) ...[
                const SizedBox(width: 8),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: widget.command.shortcut!.map((key) =>
                    Container(
                      margin: const EdgeInsets.only(left: 2),
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: widget.theme.shortcutBackground,
                        borderRadius: BorderRadius.circular(3),
                      ),
                      child: Text(
                        key,
                        style: widget.theme.shortcutTextStyle,
                      ),
                    ),
                  ).toList(),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}