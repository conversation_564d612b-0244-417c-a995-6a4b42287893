import 'package:flutter/material.dart';
import '../../theme/extensions/shadcn_skeleton_theme.dart';
import '../../utils/theme_resolver.dart';
import '../shadcn_component.dart';

/// A skeleton loading component that follows shadcn design principles.
/// 
/// The [ShadcnSkeleton] widget displays animated placeholder content
/// while actual content is loading. It features a shimmer animation effect
/// and theme-aware styling.
class ShadcnSkeleton extends ShadcnComponent {
  /// Width of the skeleton element
  final double? width;
  
  /// Height of the skeleton element
  final double? height;
  
  /// Custom border radius (overrides theme)
  final BorderRadius? borderRadius;
  
  /// Custom base color (overrides theme)
  final Color? baseColor;
  
  /// Custom highlight color for shimmer (overrides theme)
  final Color? highlightColor;
  
  /// Whether to enable shimmer animation
  final bool? enableShimmer;
  
  /// Custom animation duration (overrides theme)
  final Duration? animationDuration;
  
  /// Child widget to use as skeleton shape (overrides width/height)
  final Widget? child;

  const ShadcnSkeleton({
    super.key,
    this.width,
    this.height,
    this.borderRadius,
    this.baseColor,
    this.highlightColor,
    this.enableShimmer,
    this.animationDuration,
    this.child,
  });

  /// Creates a circular skeleton (typically for avatars)
  const ShadcnSkeleton.circle({
    super.key,
    required double size,
    this.baseColor,
    this.highlightColor,
    this.enableShimmer,
    this.animationDuration,
    this.child,
  }) : width = size,
       height = size,
       borderRadius = null; // Will be set to circular in build

  /// Creates a skeleton for text lines
  const ShadcnSkeleton.text({
    super.key,
    this.width,
    double? fontSize,
    this.baseColor,
    this.highlightColor,
    this.enableShimmer,
    this.animationDuration,
    this.child,
  }) : height = fontSize ?? 16.0,
       borderRadius = null; // Will use theme default

  /// Creates a rectangular skeleton
  const ShadcnSkeleton.rect({
    super.key,
    required this.width,
    required this.height,
    this.borderRadius,
    this.baseColor,
    this.highlightColor,
    this.enableShimmer,
    this.animationDuration,
    this.child,
  });

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    final skeletonTheme = ShadcnThemeResolver.resolveThemeExtension<ShadcnSkeletonTheme>(
      context,
      ShadcnSkeletonTheme.defaultTheme,
    );

    final resolvedBaseColor = baseColor ?? skeletonTheme.baseColor ?? 
        theme.colorScheme.surfaceVariant.withOpacity(0.3);
    final resolvedHighlightColor = highlightColor ?? skeletonTheme.highlightColor ?? 
        theme.colorScheme.surfaceVariant.withOpacity(0.5);
    final shouldAnimate = enableShimmer ?? skeletonTheme.enableShimmer ?? true;
    final duration = animationDuration ?? skeletonTheme.animationDuration ?? 
        const Duration(milliseconds: 1500);

    BorderRadius? resolvedBorderRadius = borderRadius;
    
    // Special handling for circular skeleton
    if (borderRadius == null && width != null && height != null && width == height) {
      resolvedBorderRadius = BorderRadius.circular(width! / 2);
    } else {
      resolvedBorderRadius ??= skeletonTheme.borderRadius ?? BorderRadius.circular(4.0);
    }

    final skeletonContainer = Container(
      width: width ?? skeletonTheme.defaultWidth,
      height: height ?? skeletonTheme.lineHeight,
      decoration: BoxDecoration(
        color: resolvedBaseColor,
        borderRadius: resolvedBorderRadius,
      ),
      child: child,
    );

    if (!shouldAnimate) {
      return skeletonContainer;
    }

    return _ShimmerEffect(
      baseColor: resolvedBaseColor,
      highlightColor: resolvedHighlightColor,
      duration: duration,
      curve: skeletonTheme.animationCurve ?? Curves.easeInOut,
      gradientStops: skeletonTheme.gradientStops ?? [0.0, 0.5, 1.0],
      child: skeletonContainer,
    );
  }
}

/// Shimmer effect widget for skeleton animations
class _ShimmerEffect extends StatefulWidget {
  final Widget child;
  final Color baseColor;
  final Color highlightColor;
  final Duration duration;
  final Curve curve;
  final List<double> gradientStops;

  const _ShimmerEffect({
    required this.child,
    required this.baseColor,
    required this.highlightColor,
    required this.duration,
    required this.curve,
    required this.gradientStops,
  });

  @override
  State<_ShimmerEffect> createState() => _ShimmerEffectState();
}

class _ShimmerEffectState extends State<_ShimmerEffect>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    );
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return ShaderMask(
          blendMode: BlendMode.srcATop,
          shaderCallback: (bounds) {
            return LinearGradient(
              begin: const Alignment(-1.0, -0.3),
              end: const Alignment(1.0, 0.3),
              colors: [
                widget.baseColor,
                widget.highlightColor,
                widget.baseColor,
              ],
              stops: widget.gradientStops,
              transform: _SlidingGradientTransform(slidePercent: _animation.value),
            ).createShader(bounds);
          },
          child: widget.child,
        );
      },
    );
  }
}

/// Transform for animating the shimmer gradient
class _SlidingGradientTransform extends GradientTransform {
  final double slidePercent;

  const _SlidingGradientTransform({required this.slidePercent});

  @override
  Matrix4? transform(Rect bounds, {TextDirection? textDirection}) {
    return Matrix4.translationValues(bounds.width * 2 * (slidePercent - 0.5), 0, 0);
  }
}

/// Utility widget for creating skeleton placeholder layouts
class ShadcnSkeletonLoader extends StatelessWidget {
  /// List of skeleton items to display
  final List<Widget> children;
  
  /// Spacing between skeleton items
  final double spacing;
  
  /// Cross axis alignment for the skeleton items
  final CrossAxisAlignment crossAxisAlignment;
  
  /// Main axis alignment for the skeleton items
  final MainAxisAlignment mainAxisAlignment;

  const ShadcnSkeletonLoader({
    super.key,
    required this.children,
    this.spacing = 8.0,
    this.crossAxisAlignment = CrossAxisAlignment.start,
    this.mainAxisAlignment = MainAxisAlignment.start,
  });

  /// Creates a skeleton loader for a typical card layout
  factory ShadcnSkeletonLoader.card({
    Key? key,
    bool includeAvatar = true,
    int lineCount = 3,
    double spacing = 8.0,
  }) {
    return ShadcnSkeletonLoader(
      key: key,
      spacing: spacing,
      children: [
        if (includeAvatar) ...[
          Row(
            children: [
              const ShadcnSkeleton.circle(size: 40),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ShadcnSkeleton.text(width: 120),
                    const SizedBox(height: 4),
                    ShadcnSkeleton.text(width: 80),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
        ],
        for (int i = 0; i < lineCount; i++) ...[
          ShadcnSkeleton.text(
            width: i == lineCount - 1 ? 200 : null,
          ),
          if (i < lineCount - 1) SizedBox(height: spacing),
        ],
      ],
    );
  }

  /// Creates a skeleton loader for a list item
  factory ShadcnSkeletonLoader.listItem({
    Key? key,
    bool includeLeading = true,
    bool includeTrailing = false,
    double spacing = 8.0,
  }) {
    return ShadcnSkeletonLoader(
      key: key,
      spacing: spacing,
      children: [
        Row(
          children: [
            if (includeLeading) ...[
              const ShadcnSkeleton.circle(size: 32),
              const SizedBox(width: 12),
            ],
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ShadcnSkeleton.text(width: 150),
                  SizedBox(height: spacing / 2),
                  ShadcnSkeleton.text(width: 100),
                ],
              ),
            ),
            if (includeTrailing) ...[
              const SizedBox(width: 12),
              const ShadcnSkeleton.rect(width: 60, height: 24),
            ],
          ],
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: crossAxisAlignment,
      mainAxisAlignment: mainAxisAlignment,
      children: children
          .expand((child) => [child, SizedBox(height: spacing)])
          .take(children.length * 2 - 1)
          .toList(),
    );
  }
}