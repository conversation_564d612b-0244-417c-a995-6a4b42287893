import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_button_theme.dart';
import '../../constants/shadcn_tokens.dart';

/// A shadcn-styled button component with comprehensive theming support.
/// 
/// This button component provides all shadcn button variants and sizes while
/// maintaining full Material Design integration. It supports all interactive
/// states (hover, pressed, focused, disabled) with theme-aware styling.
/// 
/// The button automatically derives its styling from the current theme context,
/// with fallback to Material Design values. All styling is customizable through
/// the [ShadcnButtonTheme] extension.
/// 
/// Example usage:
/// ```dart
/// ShadcnButton(
///   text: 'Click me',
///   onPressed: () => print('Button pressed'),
///   variant: ShadcnButtonVariant.primary,
///   size: ShadcnButtonSize.medium,
/// )
/// ```
class ShadcnButton extends ShadcnComponent with ShadcnComponentValidation {
  /// The text displayed on the button.
  final String? text;
  
  /// The child widget to display. Takes precedence over [text].
  final Widget? child;
  
  /// Callback function called when the button is pressed.
  final VoidCallback? onPressed;
  
  /// Callback function called when the button is long pressed.
  final VoidCallback? onLongPress;
  
  /// The visual variant of the button.
  final ShadcnButtonVariant variant;
  
  /// The size of the button.
  final ShadcnButtonSize size;
  
  /// Optional icon to display before the text/child.
  final Widget? leading;
  
  /// Optional icon to display after the text/child.
  final Widget? trailing;
  
  /// Whether the button is in a loading state.
  final bool isLoading;
  
  /// Custom loading indicator widget. If null, uses default spinner.
  final Widget? loadingIndicator;
  
  /// Whether to expand the button to fill available width.
  final bool expand;
  
  /// Custom width for the button. Ignored if [expand] is true.
  final double? width;
  
  /// Custom height for the button. Overrides theme size settings.
  final double? height;
  
  /// Custom padding for the button content.
  final EdgeInsets? padding;
  
  /// Custom border radius. Overrides theme settings.
  final BorderRadius? borderRadius;
  
  /// Focus node for keyboard navigation.
  final FocusNode? focusNode;
  
  /// Whether the button should autofocus when first built.
  final bool autofocus;
  
  /// Tooltip message displayed on hover.
  final String? tooltip;
  
  /// Semantic label for accessibility.
  final String? semanticLabel;
  
  /// Whether to exclude this button from semantics tree.
  final bool excludeFromSemantics;
  
  /// Custom mouse cursor when hovering over the button.
  final MouseCursor? mouseCursor;
  
  /// Whether to enable haptic feedback on press.
  final bool enableFeedback;
  
  /// Custom splash color for press animations.
  final Color? splashColor;
  
  /// Custom highlight color for press states.
  final Color? highlightColor;
  
  /// Custom hover color overlay.
  final Color? hoverColor;
  
  /// Custom focus color overlay.
  final Color? focusColor;
  
  /// Animation duration for state transitions.
  final Duration? animationDuration;
  
  const ShadcnButton({
    super.key,
    this.text,
    this.child,
    this.onPressed,
    this.onLongPress,
    this.variant = ShadcnButtonVariant.primary,
    this.size = ShadcnButtonSize.medium,
    this.leading,
    this.trailing,
    this.isLoading = false,
    this.loadingIndicator,
    this.expand = false,
    this.width,
    this.height,
    this.padding,
    this.borderRadius,
    this.focusNode,
    this.autofocus = false,
    this.tooltip,
    this.semanticLabel,
    this.excludeFromSemantics = false,
    this.mouseCursor,
    this.enableFeedback = true,
    this.splashColor,
    this.highlightColor,
    this.hoverColor,
    this.focusColor,
    this.animationDuration,
  }) : assert(text != null || child != null, 'Either text or child must be provided');

  /// Factory constructor for primary button variant.
  factory ShadcnButton.primary({
    Key? key,
    String? text,
    Widget? child,
    VoidCallback? onPressed,
    ShadcnButtonSize size = ShadcnButtonSize.medium,
    Widget? leading,
    Widget? trailing,
    bool isLoading = false,
    bool expand = false,
    String? tooltip,
  }) {
    return ShadcnButton(
      key: key,
      text: text,
      child: child,
      onPressed: onPressed,
      variant: ShadcnButtonVariant.primary,
      size: size,
      leading: leading,
      trailing: trailing,
      isLoading: isLoading,
      expand: expand,
      tooltip: tooltip,
    );
  }

  /// Factory constructor for secondary button variant.
  factory ShadcnButton.secondary({
    Key? key,
    String? text,
    Widget? child,
    VoidCallback? onPressed,
    ShadcnButtonSize size = ShadcnButtonSize.medium,
    Widget? leading,
    Widget? trailing,
    bool isLoading = false,
    bool expand = false,
    String? tooltip,
  }) {
    return ShadcnButton(
      key: key,
      text: text,
      child: child,
      onPressed: onPressed,
      variant: ShadcnButtonVariant.secondary,
      size: size,
      leading: leading,
      trailing: trailing,
      isLoading: isLoading,
      expand: expand,
      tooltip: tooltip,
    );
  }

  /// Factory constructor for destructive button variant.
  factory ShadcnButton.destructive({
    Key? key,
    String? text,
    Widget? child,
    VoidCallback? onPressed,
    ShadcnButtonSize size = ShadcnButtonSize.medium,
    Widget? leading,
    Widget? trailing,
    bool isLoading = false,
    bool expand = false,
    String? tooltip,
  }) {
    return ShadcnButton(
      key: key,
      text: text,
      child: child,
      onPressed: onPressed,
      variant: ShadcnButtonVariant.destructive,
      size: size,
      leading: leading,
      trailing: trailing,
      isLoading: isLoading,
      expand: expand,
      tooltip: tooltip,
    );
  }

  /// Factory constructor for outline button variant.
  factory ShadcnButton.outline({
    Key? key,
    String? text,
    Widget? child,
    VoidCallback? onPressed,
    ShadcnButtonSize size = ShadcnButtonSize.medium,
    Widget? leading,
    Widget? trailing,
    bool isLoading = false,
    bool expand = false,
    String? tooltip,
  }) {
    return ShadcnButton(
      key: key,
      text: text,
      child: child,
      onPressed: onPressed,
      variant: ShadcnButtonVariant.outline,
      size: size,
      leading: leading,
      trailing: trailing,
      isLoading: isLoading,
      expand: expand,
      tooltip: tooltip,
    );
  }

  /// Factory constructor for ghost button variant.
  factory ShadcnButton.ghost({
    Key? key,
    String? text,
    Widget? child,
    VoidCallback? onPressed,
    ShadcnButtonSize size = ShadcnButtonSize.medium,
    Widget? leading,
    Widget? trailing,
    bool isLoading = false,
    bool expand = false,
    String? tooltip,
  }) {
    return ShadcnButton(
      key: key,
      text: text,
      child: child,
      onPressed: onPressed,
      variant: ShadcnButtonVariant.ghost,
      size: size,
      leading: leading,
      trailing: trailing,
      isLoading: isLoading,
      expand: expand,
      tooltip: tooltip,
    );
  }

  /// Factory constructor for link button variant.
  factory ShadcnButton.link({
    Key? key,
    String? text,
    Widget? child,
    VoidCallback? onPressed,
    ShadcnButtonSize size = ShadcnButtonSize.medium,
    Widget? leading,
    Widget? trailing,
    bool isLoading = false,
    bool expand = false,
    String? tooltip,
  }) {
    return ShadcnButton(
      key: key,
      text: text,
      child: child,
      onPressed: onPressed,
      variant: ShadcnButtonVariant.link,
      size: size,
      leading: leading,
      trailing: trailing,
      isLoading: isLoading,
      expand: expand,
      tooltip: tooltip,
    );
  }

  /// Whether the button is enabled (has onPressed callback and not loading).
  bool get enabled => (onPressed != null || onLongPress != null) && !isLoading;

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    // Validate theme context and properties
    validateThemeProperties(context);
    validateVariant(variant, ShadcnButtonVariant.values, 'ShadcnButton');
    validateCallbacks({'onPressed': onPressed, 'onLongPress': onLongPress}, 'ShadcnButton');
    validateAccessibility(
      semanticLabel: semanticLabel,
      tooltip: tooltip,
      excludeFromSemantics: excludeFromSemantics,
      componentName: 'ShadcnButton',
    );

    // Resolve button theme
    final buttonTheme = resolveTheme<ShadcnButtonTheme>(
      context,
      ShadcnButtonTheme.defaultTheme,
    );

    // Resolve variant colors and properties
    final variantColors = _resolveVariantColors(buttonTheme, variant, theme.colorScheme);
    final sizeProperties = _resolveSizeProperties(buttonTheme, size);
    final interactionProperties = _resolveInteractionProperties(buttonTheme, variant, theme.colorScheme);

    // Build button content
    final buttonContent = _buildButtonContent(
      context,
      buttonTheme,
      variantColors,
      sizeProperties,
      theme,
    );

    // Calculate effective dimensions
    final effectiveHeight = height ?? sizeProperties.height;
    final effectiveWidth = expand ? double.infinity : width;
    final effectivePadding = padding ?? sizeProperties.padding;
    final effectiveBorderRadius = borderRadius ?? buttonTheme.borderRadius ?? 
        BorderRadius.circular(ShadcnTokens.radiusMd);

    // Build the button widget
    Widget button = AnimatedContainer(
      duration: animationDuration ?? buttonTheme.animationDuration ?? ShadcnTokens.durationFast,
      curve: buttonTheme.animationCurve ?? Curves.easeInOut,
      width: effectiveWidth,
      height: effectiveHeight,
      constraints: BoxConstraints(
        minWidth: buttonTheme.minWidth ?? 64,
        minHeight: effectiveHeight,
      ),
      decoration: BoxDecoration(
        color: enabled ? variantColors.background : variantColors.disabledBackground,
        border: _buildBorder(variantColors, buttonTheme),
        borderRadius: effectiveBorderRadius,
      ),
      child: Material(
        type: MaterialType.transparency,
        child: InkWell(
          onTap: enabled ? () => _handleTap(context) : null,
          onLongPress: enabled ? onLongPress : null,
          borderRadius: effectiveBorderRadius,
          splashColor: splashColor ?? interactionProperties.splashColor,
          highlightColor: highlightColor ?? interactionProperties.highlightColor,
          hoverColor: hoverColor ?? interactionProperties.hoverColor,
          focusColor: focusColor ?? interactionProperties.focusColor,
          mouseCursor: mouseCursor ?? (enabled ? SystemMouseCursors.click : SystemMouseCursors.basic),
          enableFeedback: enableFeedback,
          excludeFromSemantics: true, // We handle semantics separately
          focusNode: focusNode,
          autofocus: autofocus,
          child: Padding(
            padding: effectivePadding,
            child: buttonContent,
          ),
        ),
      ),
    );

    // Add focus indicator
    if (focusNode != null) {
      button = Focus(
        focusNode: focusNode,
        child: button,
      );
    }

    // Add semantics
    if (!excludeFromSemantics) {
      button = Semantics(
        label: semanticLabel ?? text,
        button: true,
        enabled: enabled,
        focusable: enabled,
        child: button,
      );
    }

    // Add tooltip
    if (tooltip != null) {
      button = Tooltip(
        message: tooltip!,
        child: button,
      );
    }

    return button;
  }

  /// Resolves colors for the current variant and state.
  _ButtonColors _resolveVariantColors(ShadcnButtonTheme theme, ShadcnButtonVariant variant, ColorScheme colorScheme) {
    switch (variant) {
      case ShadcnButtonVariant.primary:
        return _ButtonColors(
          background: theme.primaryBackground ?? colorScheme.primary,
          foreground: theme.primaryForeground ?? colorScheme.onPrimary,
          border: theme.primaryBorder ?? theme.primaryBackground ?? colorScheme.primary,
          hoverOverlay: theme.primaryHover,
          pressedOverlay: theme.primaryPressed,
          focusedOverlay: theme.primaryFocused,
          disabledBackground: theme.disabledBackground ?? colorScheme.onSurface.withOpacity(0.12),
          disabledForeground: theme.disabledForeground ?? colorScheme.onSurface.withOpacity(0.38),
          disabledBorder: theme.disabledBorder ?? theme.disabledBackground ?? colorScheme.onSurface.withOpacity(0.12),
        );

      case ShadcnButtonVariant.secondary:
        return _ButtonColors(
          background: theme.secondaryBackground ?? colorScheme.secondary,
          foreground: theme.secondaryForeground ?? colorScheme.onSecondary,
          border: theme.secondaryBorder ?? theme.secondaryBackground ?? colorScheme.secondary,
          hoverOverlay: theme.secondaryHover,
          pressedOverlay: theme.secondaryPressed,
          focusedOverlay: theme.secondaryFocused,
          disabledBackground: theme.disabledBackground ?? colorScheme.onSurface.withOpacity(0.12),
          disabledForeground: theme.disabledForeground ?? colorScheme.onSurface.withOpacity(0.38),
          disabledBorder: theme.disabledBorder ?? theme.disabledBackground ?? colorScheme.onSurface.withOpacity(0.12),
        );

      case ShadcnButtonVariant.destructive:
        return _ButtonColors(
          background: theme.destructiveBackground ?? colorScheme.error,
          foreground: theme.destructiveForeground ?? colorScheme.onError,
          border: theme.destructiveBorder ?? theme.destructiveBackground ?? colorScheme.error,
          hoverOverlay: theme.destructiveHover,
          pressedOverlay: theme.destructivePressed,
          focusedOverlay: theme.destructiveFocused,
          disabledBackground: theme.disabledBackground ?? colorScheme.onSurface.withOpacity(0.12),
          disabledForeground: theme.disabledForeground ?? colorScheme.onSurface.withOpacity(0.38),
          disabledBorder: theme.disabledBorder ?? theme.disabledBackground ?? colorScheme.onSurface.withOpacity(0.12),
        );

      case ShadcnButtonVariant.outline:
        return _ButtonColors(
          background: theme.outlineBackground ?? Colors.transparent,
          foreground: theme.outlineForeground ?? colorScheme.onSurface,
          border: theme.outlineBorder ?? colorScheme.outline,
          hoverOverlay: theme.outlineHover,
          pressedOverlay: theme.outlinePressed,
          focusedOverlay: theme.outlineFocused,
          disabledBackground: theme.disabledBackground ?? Colors.transparent,
          disabledForeground: theme.disabledForeground ?? colorScheme.onSurface.withOpacity(0.38),
          disabledBorder: theme.disabledBorder ?? colorScheme.onSurface.withOpacity(0.12),
        );

      case ShadcnButtonVariant.ghost:
        return _ButtonColors(
          background: theme.ghostBackground ?? Colors.transparent,
          foreground: theme.ghostForeground ?? colorScheme.onSurface,
          border: theme.ghostBorder ?? Colors.transparent,
          hoverOverlay: theme.ghostHover,
          pressedOverlay: theme.ghostPressed,
          focusedOverlay: theme.ghostFocused,
          disabledBackground: theme.disabledBackground ?? Colors.transparent,
          disabledForeground: theme.disabledForeground ?? colorScheme.onSurface.withOpacity(0.38),
          disabledBorder: theme.disabledBorder ?? Colors.transparent,
        );

      case ShadcnButtonVariant.link:
        return _ButtonColors(
          background: theme.linkBackground ?? Colors.transparent,
          foreground: theme.linkForeground ?? colorScheme.primary,
          border: theme.linkBorder ?? Colors.transparent,
          hoverOverlay: theme.linkHover,
          pressedOverlay: theme.linkPressed,
          focusedOverlay: theme.linkFocused,
          disabledBackground: theme.disabledBackground ?? Colors.transparent,
          disabledForeground: theme.disabledForeground ?? colorScheme.onSurface.withOpacity(0.38),
          disabledBorder: theme.disabledBorder ?? Colors.transparent,
        );
    }
  }

  /// Resolves size properties for the current size.
  _ButtonSizeProperties _resolveSizeProperties(ShadcnButtonTheme theme, ShadcnButtonSize size) {
    switch (size) {
      case ShadcnButtonSize.small:
        return _ButtonSizeProperties(
          height: theme.smallHeight ?? ShadcnTokens.buttonHeightSm,
          padding: theme.smallPadding ?? const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          fontSize: theme.smallFontSize ?? ShadcnTokens.fontSizeSm,
          iconSize: theme.smallIconSize ?? ShadcnTokens.iconSizeSm,
        );

      case ShadcnButtonSize.medium:
        return _ButtonSizeProperties(
          height: theme.mediumHeight ?? ShadcnTokens.buttonHeightMd,
          padding: theme.mediumPadding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          fontSize: theme.mediumFontSize ?? ShadcnTokens.fontSizeMd,
          iconSize: theme.mediumIconSize ?? ShadcnTokens.iconSizeMd,
        );

      case ShadcnButtonSize.large:
        return _ButtonSizeProperties(
          height: theme.largeHeight ?? ShadcnTokens.buttonHeightLg,
          padding: theme.largePadding ?? const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          fontSize: theme.largeFontSize ?? ShadcnTokens.fontSizeLg,
          iconSize: theme.largeIconSize ?? ShadcnTokens.iconSizeLg,
        );
    }
  }

  /// Resolves interaction properties for hover, press, and focus states.
  _ButtonInteractionProperties _resolveInteractionProperties(
    ShadcnButtonTheme theme, 
    ShadcnButtonVariant variant, 
    ColorScheme colorScheme
  ) {
    final variantColors = _resolveVariantColors(theme, variant, colorScheme);
    
    return _ButtonInteractionProperties(
      splashColor: variantColors.pressedOverlay ?? variantColors.foreground.withOpacity(0.12),
      highlightColor: variantColors.pressedOverlay ?? variantColors.foreground.withOpacity(0.08),
      hoverColor: variantColors.hoverOverlay ?? variantColors.foreground.withOpacity(0.04),
      focusColor: variantColors.focusedOverlay ?? colorScheme.primary.withOpacity(0.12),
    );
  }

  /// Builds the border for the button based on variant and theme.
  Border? _buildBorder(_ButtonColors colors, ShadcnButtonTheme theme) {
    if (colors.border == Colors.transparent) {
      return null;
    }
    
    return Border.all(
      color: enabled ? colors.border : colors.disabledBorder,
      width: theme.borderWidth ?? ShadcnTokens.borderWidth,
    );
  }

  /// Builds the main content of the button.
  Widget _buildButtonContent(
    BuildContext context,
    ShadcnButtonTheme theme,
    _ButtonColors colors,
    _ButtonSizeProperties sizeProps,
    ThemeData materialTheme,
  ) {
    final effectiveTextStyle = resolveTextStyle(
      context,
      theme.textStyle?.copyWith(
        fontSize: sizeProps.fontSize,
        fontWeight: theme.fontWeight ?? ShadcnTokens.fontWeightMedium,
        color: enabled ? colors.foreground : colors.disabledForeground,
      ),
      (textTheme) => textTheme.labelLarge ?? const TextStyle(),
    ).copyWith(
      fontSize: sizeProps.fontSize,
      fontWeight: theme.fontWeight ?? ShadcnTokens.fontWeightMedium,
      color: enabled ? colors.foreground : colors.disabledForeground,
    );

    // Show loading state
    if (isLoading) {
      return _buildLoadingContent(colors, sizeProps.iconSize, effectiveTextStyle);
    }

    // Build content with icons
    final List<Widget> children = [];

    // Leading icon
    if (leading != null) {
      children.add(_buildIcon(leading!, sizeProps.iconSize, colors.foreground));
      if ((text != null && text!.isNotEmpty) || child != null) {
        children.add(SizedBox(width: theme.iconSpacing ?? ShadcnTokens.spacing2));
      }
    }

    // Main content (text or child)
    if (child != null) {
      children.add(child!);
    } else if (text != null && text!.isNotEmpty) {
      children.add(
        Text(
          text!,
          style: effectiveTextStyle,
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        ),
      );
    }

    // Trailing icon
    if (trailing != null) {
      if ((text != null && text!.isNotEmpty) || child != null) {
        children.add(SizedBox(width: theme.iconSpacing ?? ShadcnTokens.spacing2));
      }
      children.add(_buildIcon(trailing!, sizeProps.iconSize, colors.foreground));
    }

    return Row(
      mainAxisSize: theme.mainAxisSize ?? MainAxisSize.min,
      mainAxisAlignment: theme.mainAxisAlignment ?? MainAxisAlignment.center,
      crossAxisAlignment: theme.crossAxisAlignment ?? CrossAxisAlignment.center,
      children: children,
    );
  }

  /// Builds loading content with spinner and optional text.
  Widget _buildLoadingContent(
    _ButtonColors colors,
    double iconSize,
    TextStyle textStyle,
  ) {
    final List<Widget> children = [];

    // Loading indicator
    children.add(
      loadingIndicator ?? SizedBox(
        width: iconSize,
        height: iconSize,
        child: CircularProgressIndicator(
          strokeWidth: 2.0,
          valueColor: AlwaysStoppedAnimation<Color>(
            enabled ? colors.foreground : colors.disabledForeground,
          ),
        ),
      ),
    );

    // Loading text
    if (text != null && text!.isNotEmpty) {
      children.add(const SizedBox(width: ShadcnTokens.spacing2));
      children.add(
        Text(
          text!,
          style: textStyle,
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        ),
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: children,
    );
  }

  /// Builds an icon with proper sizing and color.
  Widget _buildIcon(Widget icon, double size, Color color) {
    return SizedBox(
      width: size,
      height: size,
      child: IconTheme(
        data: IconThemeData(
          size: size,
          color: enabled ? color : color.withOpacity(0.38),
        ),
        child: icon,
      ),
    );
  }

  /// Handles button tap with haptic feedback.
  void _handleTap(BuildContext context) {
    if (enableFeedback) {
      HapticFeedback.lightImpact();
    }
    onPressed?.call();
  }
}

/// Helper class to hold resolved button colors for different states.
class _ButtonColors {
  final Color background;
  final Color foreground;
  final Color border;
  final Color? hoverOverlay;
  final Color? pressedOverlay;
  final Color? focusedOverlay;
  final Color disabledBackground;
  final Color disabledForeground;
  final Color disabledBorder;

  const _ButtonColors({
    required this.background,
    required this.foreground,
    required this.border,
    this.hoverOverlay,
    this.pressedOverlay,
    this.focusedOverlay,
    required this.disabledBackground,
    required this.disabledForeground,
    required this.disabledBorder,
  });
}

/// Helper class to hold resolved button size properties.
class _ButtonSizeProperties {
  final double height;
  final EdgeInsets padding;
  final double fontSize;
  final double iconSize;

  const _ButtonSizeProperties({
    required this.height,
    required this.padding,
    required this.fontSize,
    required this.iconSize,
  });
}

/// Helper class to hold resolved button interaction properties.
class _ButtonInteractionProperties {
  final Color splashColor;
  final Color highlightColor;
  final Color hoverColor;
  final Color focusColor;

  const _ButtonInteractionProperties({
    required this.splashColor,
    required this.highlightColor,
    required this.hoverColor,
    required this.focusColor,
  });
}