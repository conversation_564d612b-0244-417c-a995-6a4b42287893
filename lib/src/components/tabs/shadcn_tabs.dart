import 'package:flutter/material.dart';

import '../../theme/extensions/shadcn_tabs_theme.dart';
import '../shadcn_component.dart';

/// A tab data model that holds information about each tab.
class ShadcnTabData {
  /// The unique identifier for this tab.
  final String id;

  /// The display label for this tab.
  final String label;

  /// The content widget to display when this tab is active.
  final Widget content;

  /// Whether this tab is disabled.
  final bool disabled;

  const ShadcnTabData({
    required this.id,
    required this.label,
    required this.content,
    this.disabled = false,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ShadcnTabData && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// A shadcn-styled tabs component.
class ShadcnTabs extends ShadcnComponent with ShadcnComponentValidation {
  /// The list of tabs to display.
  final List<ShadcnTabData> tabs;

  /// The ID of the initially selected tab. If null, the first tab is selected.
  final String? initialTabId;

  /// Callback function called when a tab is selected.
  final ValueChanged<String>? onTabChanged;

  const ShadcnTabs({
    super.key,
    required this.tabs,
    this.initialTabId,
    this.onTabChanged,
  });

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    // Validate
    assert(tabs.isNotEmpty, 'At least one tab must be provided');

    // Resolve theme
    final tabsTheme = resolveTheme<ShadcnTabsTheme>(
      context,
      (colorScheme) => ShadcnTabsTheme.defaultTheme(colorScheme),
    );

    return _ShadcnTabsWidget(
      tabs: tabs,
      initialTabId: initialTabId,
      onTabChanged: onTabChanged,
      theme: tabsTheme,
    );
  }
}

class _ShadcnTabsWidget extends StatefulWidget {
  final List<ShadcnTabData> tabs;
  final String? initialTabId;
  final ValueChanged<String>? onTabChanged;
  final ShadcnTabsTheme theme;

  const _ShadcnTabsWidget({
    required this.tabs,
    this.initialTabId,
    this.onTabChanged,
    required this.theme,
  });

  @override
  State<_ShadcnTabsWidget> createState() => _ShadcnTabsWidgetState();
}

class _ShadcnTabsWidgetState extends State<_ShadcnTabsWidget>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();

    // Find initial tab index
    _currentIndex = 0;
    if (widget.initialTabId != null) {
      final index =
          widget.tabs.indexWhere((tab) => tab.id == widget.initialTabId);
      if (index != -1) {
        _currentIndex = index;
      }
    }

    // Initialize tab controller
    _tabController = TabController(
      length: widget.tabs.length,
      initialIndex: _currentIndex,
      vsync: this,
    );

    // Add listener for tab changes
    _tabController.addListener(_handleTabChange);
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) return;

    final newIndex = _tabController.index;
    if (newIndex != _currentIndex && newIndex < widget.tabs.length) {
      setState(() {
        _currentIndex = newIndex;
      });

      widget.onTabChanged?.call(widget.tabs[newIndex].id);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: widget.theme.backgroundColor,
        border: widget.theme.borderColor != null
            ? Border.all(color: widget.theme.borderColor!)
            : null,
        borderRadius: widget.theme.borderRadius,
      ),
      padding: widget.theme.padding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Tab Bar
          Container(
            height: widget.theme.tabHeight,
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: widget.theme.borderColor ??
                      Theme.of(context).dividerColor,
                  width: 1.0,
                ),
              ),
            ),
            child: TabBar(
              controller: _tabController,
              tabs: widget.tabs.map((tab) => _buildTab(tab)).toList(),
              isScrollable: false,
              labelColor: widget.theme.tabActiveForeground,
              unselectedLabelColor: widget.theme.tabForeground,
              labelStyle: widget.theme.tabTextStyle,
              unselectedLabelStyle: widget.theme.tabTextStyle,
              indicator: UnderlineTabIndicator(
                borderSide: BorderSide(
                  color: widget.theme.indicatorColor ??
                      Theme.of(context).colorScheme.primary,
                  width: widget.theme.indicatorHeight ?? 2.0,
                ),
              ),
              labelPadding: widget.theme.tabPadding,
              overlayColor: WidgetStateProperty.all(
                widget.theme.tabHoverBackground ?? Colors.transparent,
              ),
            ),
          ),

          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: widget.tabs.map((tab) => tab.content).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTab(ShadcnTabData tab) {
    return Tab(
      child: Container(
        padding: widget.theme.tabPadding,
        child: Text(
          tab.label,
          style: widget.theme.tabTextStyle,
        ),
      ),
    );
  }
}
