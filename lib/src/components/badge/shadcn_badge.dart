import 'package:flutter/material.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_badge_theme.dart';
import '../../constants/shadcn_tokens.dart';

/// A shadcn-styled badge component with variant colors and positioning support.
/// 
/// This badge component provides a flexible way to display small status indicators,
/// notification counts, or labels. It supports multiple variants and can be used
/// both as standalone badges or positioned over other widgets.
/// 
/// The badge automatically derives its styling from the current theme context,
/// with fallback to Material Design values. All styling is customizable through
/// the [ShadcnBadgeTheme] extension.
/// 
/// Example usage:
/// ```dart
/// // Standalone badge
/// ShadcnBadge(
///   text: 'New',
///   variant: ShadcnBadgeVariant.destructive,
/// )
/// 
/// // Positioned badge over a widget
/// ShadcnBadge.positioned(
///   text: '5',
///   child: Icon(Icons.notifications),
/// )
/// 
/// // Custom content badge
/// ShadcnBadge(
///   child: Row(
///     mainAxisSize: MainAxisSize.min,
///     children: [
///       Icon(Icons.star, size: 12),
///       Text('VIP'),
///     ],
///   ),
/// )
/// ```
class ShadcnBadge extends ShadcnComponent with ShadcnComponentValidation {
  /// Text content for the badge.
  final String? text;
  
  /// Custom child widget. Takes precedence over [text].
  final Widget? child;
  
  /// Widget to position the badge over (for positioned badges).
  final Widget? positionedChild;
  
  /// Visual variant of the badge.
  final ShadcnBadgeVariant variant;
  
  /// Custom background color. Overrides theme setting.
  final Color? backgroundColor;
  
  /// Custom foreground color for text and icons. Overrides theme setting.
  final Color? foregroundColor;
  
  /// Custom border color. Overrides theme setting.
  final Color? borderColor;
  
  /// Custom border width. Overrides theme setting.
  final double? borderWidth;
  
  /// Custom border radius. Overrides theme setting.
  final BorderRadius? borderRadius;
  
  /// Custom padding for badge content. Overrides theme setting.
  final EdgeInsets? padding;
  
  /// Custom minimum width. Overrides theme setting.
  final double? minWidth;
  
  /// Custom minimum height. Overrides theme setting.
  final double? minHeight;
  
  /// Custom text style. Overrides theme setting.
  final TextStyle? textStyle;
  
  /// Position offset for positioned badges.
  final Offset? positionOffset;
  
  /// Alignment for positioned badges.
  final Alignment? positionAlignment;
  
  /// Whether to show the badge. Useful for conditional display.
  final bool visible;
  
  /// Callback when the badge is tapped.
  final VoidCallback? onTap;
  
  /// Callback when the badge is long pressed.
  final VoidCallback? onLongPress;
  
  /// Focus node for keyboard navigation.
  final FocusNode? focusNode;
  
  /// Whether the badge should autofocus when first built.
  final bool autofocus;
  
  /// Tooltip message displayed on hover.
  final String? tooltip;
  
  /// Semantic label for accessibility.
  final String? semanticLabel;
  
  /// Whether to exclude this badge from semantics tree.
  final bool excludeFromSemantics;
  
  /// Custom mouse cursor when hovering over the badge.
  final MouseCursor? mouseCursor;
  
  /// Whether to enable haptic feedback on press.
  final bool enableFeedback;
  
  /// Custom splash color for press animations.
  final Color? splashColor;
  
  /// Custom highlight color for press states.
  final Color? highlightColor;

  const ShadcnBadge({
    super.key,
    this.text,
    this.child,
    this.positionedChild,
    this.variant = ShadcnBadgeVariant.defaultVariant,
    this.backgroundColor,
    this.foregroundColor,
    this.borderColor,
    this.borderWidth,
    this.borderRadius,
    this.padding,
    this.minWidth,
    this.minHeight,
    this.textStyle,
    this.positionOffset,
    this.positionAlignment,
    this.visible = true,
    this.onTap,
    this.onLongPress,
    this.focusNode,
    this.autofocus = false,
    this.tooltip,
    this.semanticLabel,
    this.excludeFromSemantics = false,
    this.mouseCursor,
    this.enableFeedback = true,
    this.splashColor,
    this.highlightColor,
  }) : assert(
         text != null || child != null,
         'Either text or child must be provided',
       );

  /// Factory constructor for positioned badge over another widget.
  factory ShadcnBadge.positioned({
    Key? key,
    String? text,
    Widget? child,
    required Widget positionedChild,
    ShadcnBadgeVariant variant = ShadcnBadgeVariant.defaultVariant,
    Offset? positionOffset,
    Alignment? positionAlignment,
    bool visible = true,
    VoidCallback? onTap,
    String? tooltip,
    String? semanticLabel,
  }) {
    return ShadcnBadge(
      key: key,
      text: text,
      child: child,
      positionedChild: positionedChild,
      variant: variant,
      positionOffset: positionOffset,
      positionAlignment: positionAlignment,
      visible: visible,
      onTap: onTap,
      tooltip: tooltip,
      semanticLabel: semanticLabel,
    );
  }

  /// Factory constructor for notification count badge.
  factory ShadcnBadge.count({
    Key? key,
    required int count,
    required Widget child,
    ShadcnBadgeVariant variant = ShadcnBadgeVariant.destructive,
    bool visible = true,
    VoidCallback? onTap,
    String? tooltip,
  }) {
    return ShadcnBadge(
      key: key,
      text: count > 99 ? '99+' : count.toString(),
      positionedChild: child,
      variant: variant,
      visible: visible && count > 0,
      onTap: onTap,
      tooltip: tooltip,
      semanticLabel: 'Notifications: $count',
    );
  }

  /// Factory constructor for simple text badge with specific variant.
  factory ShadcnBadge.variant({
    Key? key,
    required String text,
    required ShadcnBadgeVariant variant,
    VoidCallback? onTap,
    String? tooltip,
  }) {
    return ShadcnBadge(
      key: key,
      text: text,
      variant: variant,
      onTap: onTap,
      tooltip: tooltip,
    );
  }

  /// Whether the badge is interactive (has tap callbacks).
  bool get isInteractive => onTap != null || onLongPress != null;

  /// Whether this is a positioned badge.
  bool get isPositioned => positionedChild != null;

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    // Don't render if not visible
    if (!visible) {
      return isPositioned ? positionedChild! : const SizedBox.shrink();
    }

    // Validate theme context and properties
    validateThemeProperties(context);
    validateVariant(variant, ShadcnBadgeVariant.values, 'ShadcnBadge');
    validateCallbacks(
      {'onTap': onTap, 'onLongPress': onLongPress}, 
      'ShadcnBadge',
    );
    validateAccessibility(
      semanticLabel: semanticLabel,
      tooltip: tooltip,
      excludeFromSemantics: excludeFromSemantics,
      componentName: 'ShadcnBadge',
    );

    // Resolve badge theme
    final badgeTheme = resolveTheme<ShadcnBadgeTheme>(
      context,
      ShadcnBadgeTheme.defaultTheme,
    );

    // Resolve variant colors
    final variantColors = _resolveVariantColors(badgeTheme, variant, theme.colorScheme);

    // Build the badge widget
    final badge = _buildBadgeWidget(context, badgeTheme, variantColors, theme);

    // Return positioned badge if needed
    if (isPositioned) {
      return _buildPositionedBadge(badge, badgeTheme);
    }

    return badge;
  }

  /// Resolves colors for the current variant.
  _BadgeColors _resolveVariantColors(
    ShadcnBadgeTheme theme, 
    ShadcnBadgeVariant variant, 
    ColorScheme colorScheme,
  ) {
    switch (variant) {
      case ShadcnBadgeVariant.defaultVariant:
        return _BadgeColors(
          background: backgroundColor ?? 
              theme.defaultBackground ?? 
              colorScheme.primary,
          foreground: foregroundColor ?? 
              theme.defaultForeground ?? 
              colorScheme.onPrimary,
          border: borderColor ?? 
              theme.defaultBorder ?? 
              theme.defaultBackground ?? 
              colorScheme.primary,
        );

      case ShadcnBadgeVariant.secondary:
        return _BadgeColors(
          background: backgroundColor ?? 
              theme.secondaryBackground ?? 
              colorScheme.secondary,
          foreground: foregroundColor ?? 
              theme.secondaryForeground ?? 
              colorScheme.onSecondary,
          border: borderColor ?? 
              theme.secondaryBorder ?? 
              theme.secondaryBackground ?? 
              colorScheme.secondary,
        );

      case ShadcnBadgeVariant.destructive:
        return _BadgeColors(
          background: backgroundColor ?? 
              theme.destructiveBackground ?? 
              colorScheme.error,
          foreground: foregroundColor ?? 
              theme.destructiveForeground ?? 
              colorScheme.onError,
          border: borderColor ?? 
              theme.destructiveBorder ?? 
              theme.destructiveBackground ?? 
              colorScheme.error,
        );

      case ShadcnBadgeVariant.outline:
        return _BadgeColors(
          background: backgroundColor ?? 
              theme.outlineBackground ?? 
              Colors.transparent,
          foreground: foregroundColor ?? 
              theme.outlineForeground ?? 
              colorScheme.onSurface,
          border: borderColor ?? 
              theme.outlineBorder ?? 
              colorScheme.outline,
        );
    }
  }

  /// Builds the main badge widget.
  Widget _buildBadgeWidget(
    BuildContext context,
    ShadcnBadgeTheme theme,
    _BadgeColors colors,
    ThemeData materialTheme,
  ) {
    // Resolve styling properties
    final effectivePadding = padding ?? 
        theme.padding ?? 
        const EdgeInsets.symmetric(
          horizontal: ShadcnTokens.spacing2,
          vertical: ShadcnTokens.spacing1,
        );
    final effectiveBorderRadius = borderRadius ?? 
        theme.borderRadius ?? 
        BorderRadius.circular(ShadcnTokens.radiusFull);
    final effectiveBorderWidth = borderWidth ?? 
        theme.borderWidth ?? 
        ShadcnTokens.borderWidth;
    final effectiveMinWidth = minWidth ?? theme.minWidth ?? 20.0;
    final effectiveMinHeight = minHeight ?? theme.minHeight ?? 20.0;

    // Build badge content
    final badgeContent = _buildBadgeContent(context, theme, colors);

    // Create badge container
    Widget badge = AnimatedContainer(
      duration: theme.animationDuration ?? ShadcnTokens.durationFast,
      curve: theme.animationCurve ?? Curves.easeInOut,
      constraints: BoxConstraints(
        minWidth: effectiveMinWidth,
        minHeight: effectiveMinHeight,
      ),
      padding: effectivePadding,
      decoration: BoxDecoration(
        color: colors.background,
        border: _shouldShowBorder(colors) 
          ? Border.all(
              color: colors.border,
              width: effectiveBorderWidth,
            )
          : null,
        borderRadius: effectiveBorderRadius,
      ),
      child: badgeContent,
    );

    // Add interaction handling if needed
    if (isInteractive) {
      badge = Material(
        type: MaterialType.transparency,
        child: InkWell(
          onTap: onTap,
          onLongPress: onLongPress,
          borderRadius: effectiveBorderRadius,
          splashColor: splashColor,
          highlightColor: highlightColor,
          mouseCursor: mouseCursor ?? SystemMouseCursors.click,
          enableFeedback: enableFeedback,
          excludeFromSemantics: true, // We handle semantics separately
          focusNode: focusNode,
          autofocus: autofocus,
          child: badge,
        ),
      );
    }

    // Add focus handling
    if (focusNode != null) {
      badge = Focus(
        focusNode: focusNode,
        child: badge,
      );
    }

    // Add semantics
    if (!excludeFromSemantics) {
      badge = Semantics(
        label: semanticLabel ?? _generateSemanticLabel(),
        value: text,
        button: isInteractive,
        focusable: isInteractive,
        child: badge,
      );
    }

    // Add tooltip
    if (tooltip != null) {
      badge = Tooltip(
        message: tooltip!,
        child: badge,
      );
    }

    return badge;
  }

  /// Builds the content of the badge.
  Widget _buildBadgeContent(BuildContext context, ShadcnBadgeTheme theme, _BadgeColors colors) {
    // Build text style
    final effectiveTextStyle = resolveTextStyle(
      context,
      textStyle?.copyWith(color: colors.foreground),
      (textTheme) => textTheme.labelSmall ?? const TextStyle(),
    ).copyWith(
      color: colors.foreground,
      fontSize: theme.fontSize ?? ShadcnTokens.fontSizeXs,
      fontWeight: theme.fontWeight ?? ShadcnTokens.fontWeightMedium,
      letterSpacing: theme.letterSpacing ?? 0.025,
    );

    // Use custom child if provided
    if (child != null) {
      return DefaultTextStyle(
        style: effectiveTextStyle,
        child: IconTheme(
          data: IconThemeData(
            color: colors.foreground,
            size: theme.fontSize ?? ShadcnTokens.fontSizeXs,
          ),
          child: child!,
        ),
      );
    }

    // Otherwise use text
    return Text(
      text!,
      style: effectiveTextStyle,
      textAlign: TextAlign.center,
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
    );
  }

  /// Builds positioned badge over another widget.
  Widget _buildPositionedBadge(Widget badge, ShadcnBadgeTheme theme) {
    final effectiveOffset = positionOffset ?? 
        theme.positionOffset ?? 
        const Offset(-4, -4);
    final effectiveAlignment = positionAlignment ?? 
        theme.positionAlignment ?? 
        Alignment.topRight;

    return Stack(
      clipBehavior: Clip.none,
      children: [
        positionedChild!,
        Positioned.fill(
          child: Align(
            alignment: effectiveAlignment,
            child: Transform.translate(
              offset: effectiveOffset,
              child: badge,
            ),
          ),
        ),
      ],
    );
  }

  /// Determines whether to show border based on colors.
  bool _shouldShowBorder(_BadgeColors colors) {
    return colors.border != Colors.transparent &&
           colors.border != colors.background;
  }

  /// Generates semantic label for accessibility.
  String _generateSemanticLabel() {
    if (semanticLabel != null) return semanticLabel!;
    
    if (text != null && text!.isNotEmpty) {
      return 'Badge: $text';
    }
    
    return 'Badge';
  }
}

/// Helper class to hold resolved badge colors.
class _BadgeColors {
  final Color background;
  final Color foreground;
  final Color border;

  const _BadgeColors({
    required this.background,
    required this.foreground,
    required this.border,
  });
}