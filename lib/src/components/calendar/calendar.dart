/// Calendar and Date Picker components for the shadcn component library.
/// 
/// This library provides calendar and date selection components that follow
/// shadcn design principles while maintaining full Material Design integration.
/// 
/// ## Components
/// 
/// - [ShadcnCalendar]: A comprehensive calendar widget with month/year navigation
/// - [ShadcnDatePicker]: A date picker input with calendar popup integration
/// 
/// ## Theme Support
/// 
/// All components support comprehensive theming through [ShadcnCalendarTheme],
/// which provides styling for:
/// 
/// - Calendar container and structure
/// - Header navigation elements
/// - Date cells in all states (selected, today, disabled, hover)
/// - Weekday headers
/// - Navigation buttons
/// - Focus and interaction states
/// 
/// ## Usage Examples
/// 
/// ### Basic Calendar
/// ```dart
/// ShadcnCalendar(
///   selectedDate: DateTime.now(),
///   onDateSelected: (date) => print('Selected: $date'),
/// )
/// ```
/// 
/// ### Date Picker with Input
/// ```dart
/// ShadcnDatePicker(
///   selectedDate: selectedDate,
///   onDateSelected: (date) => setState(() {
///     selectedDate = date;
///   }),
///   placeholder: 'Choose a date',
/// )
/// ```
/// 
/// ### Calendar with Constraints
/// ```dart
/// ShadcnCalendar(
///   selectedDate: selectedDate,
///   onDateSelected: (date) => handleDateSelection(date),
///   minDate: DateTime.now(),
///   maxDate: DateTime.now().add(Duration(days: 365)),
///   isDateDisabled: (date) => date.weekday == 7, // Disable Sundays
/// )
/// ```

library calendar;

export 'shadcn_calendar.dart';
export 'shadcn_date_picker.dart';