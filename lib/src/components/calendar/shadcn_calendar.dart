import 'package:flutter/material.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_calendar_theme.dart';
import '../../constants/shadcn_tokens.dart';

/// A shadcn-styled calendar widget with month/year navigation.
class ShadcnCalendar extends ShadcnComponent with ShadcnComponentValidation {
  /// The currently selected date, if any.
  final DateTime? selectedDate;
  
  /// Callback fired when a date is selected.
  final ValueChanged<DateTime>? onDateSelected;
  
  /// The currently displayed month and year.
  final DateTime displayDate;
  
  /// Callback fired when the displayed month/year changes.
  final ValueChanged<DateTime>? onDisplayDateChanged;
  
  /// The minimum selectable date.
  final DateTime? minDate;
  
  /// The maximum selectable date.
  final DateTime? maxDate;
  
  /// Function to determine if a date should be disabled.
  final bool Function(DateTime date)? isDateDisabled;
  
  /// Whether to show weekday names at the top of the calendar.
  final bool showWeekdays;
  
  /// The first day of the week (0 = Sunday, 1 = Monday, etc.).
  final int firstDayOfWeek;
  
  /// Custom theme overrides for this calendar.
  final ShadcnCalendarTheme? theme;
  
  /// Whether the calendar is enabled for interaction.
  final bool enabled;
  
  /// Focus node for keyboard navigation.
  final FocusNode? focusNode;
  
  /// Whether the calendar should auto-focus on mount.
  final bool autofocus;
  
  /// Semantic label for accessibility.
  final String? semanticLabel;
  
  ShadcnCalendar({
    super.key,
    this.selectedDate,
    this.onDateSelected,
    DateTime? displayDate,
    this.onDisplayDateChanged,
    this.minDate,
    this.maxDate,
    this.isDateDisabled,
    this.showWeekdays = true,
    this.firstDayOfWeek = 0, // Sunday
    this.theme,
    this.enabled = true,
    this.focusNode,
    this.autofocus = false,
    this.semanticLabel,
  }) : displayDate = displayDate ?? _getDefaultDate();

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    final calendarTheme = resolveTheme<ShadcnCalendarTheme>(
      context,
      ShadcnCalendarTheme.defaultTheme,
    );
    
    return Semantics(
      label: semanticLabel ?? 'Calendar',
      child: Focus(
        focusNode: focusNode,
        autofocus: autofocus,
        child: Container(
          decoration: BoxDecoration(
            color: calendarTheme.backgroundColor ?? theme.colorScheme.surface,
            border: calendarTheme.borderColor != null
              ? Border.all(
                  color: calendarTheme.borderColor!,
                  width: calendarTheme.borderWidth ?? 1.0,
                )
              : null,
            borderRadius: calendarTheme.borderRadius ?? 
                         ShadcnTokens.borderRadius(ShadcnTokens.radiusMd),
          ),
          padding: calendarTheme.padding ?? 
                  ShadcnTokens.paddingAll(ShadcnTokens.spacing4),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(context, calendarTheme, theme),
              if (showWeekdays) ...[
                const SizedBox(height: ShadcnTokens.spacing2),
                _buildWeekdayHeaders(context, calendarTheme, theme),
              ],
              const SizedBox(height: ShadcnTokens.spacing2),
              _buildCalendarGrid(context, calendarTheme, theme),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(
    BuildContext context,
    ShadcnCalendarTheme calendarTheme,
    ThemeData theme,
  ) {
    return Container(
      padding: ShadcnTokens.paddingAll(ShadcnTokens.spacing2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            onPressed: enabled ? () => _navigateMonth(context, -1) : null,
            icon: const Icon(Icons.chevron_left),
          ),
          Expanded(
            child: GestureDetector(
              onTap: enabled ? () => _showMonthYearPicker(context) : null,
              child: Text(
                _formatMonthYear(displayDate),
                textAlign: TextAlign.center,
                style: theme.textTheme.titleMedium,
              ),
            ),
          ),
          IconButton(
            onPressed: enabled ? () => _navigateMonth(context, 1) : null,
            icon: const Icon(Icons.chevron_right),
          ),
        ],
      ),
    );
  }

  Widget _buildWeekdayHeaders(
    BuildContext context,
    ShadcnCalendarTheme calendarTheme,
    ThemeData theme,
  ) {
    final weekdayNames = _getWeekdayNames();
    
    return Row(
      children: weekdayNames.map((weekday) {
        return Expanded(
          child: Container(
            padding: ShadcnTokens.paddingAll(ShadcnTokens.spacing2),
            alignment: Alignment.center,
            child: Text(
              weekday,
              style: theme.textTheme.bodySmall,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCalendarGrid(
    BuildContext context,
    ShadcnCalendarTheme calendarTheme,
    ThemeData theme,
  ) {
    final dates = _generateCalendarDates(displayDate);
    const daysPerWeek = 7;
    final weeks = <List<DateTime>>[];
    
    for (int i = 0; i < dates.length; i += daysPerWeek) {
      weeks.add(dates.sublist(i, (i + daysPerWeek).clamp(0, dates.length)));
    }
    
    return Column(
      children: weeks.map((week) {
        return Row(
          children: week.map((date) {
            return Expanded(
              child: _buildDateCell(context, calendarTheme, theme, date),
            );
          }).toList(),
        );
      }).toList(),
    );
  }

  Widget _buildDateCell(
    BuildContext context,
    ShadcnCalendarTheme calendarTheme,
    ThemeData theme,
    DateTime date,
  ) {
    final isSelected = selectedDate != null && _isSameDay(date, selectedDate!);
    final isToday = _isSameDay(date, DateTime.now());
    final isCurrentMonth = date.month == displayDate.month;
    final isDisabled = _isDateDisabled(date);
    final cellSize = calendarTheme.dateCellSize ?? 32.0;
    
    Color? backgroundColor;
    Color? textColor;
    Color? borderColor;
    
    if (isSelected && !isDisabled) {
      backgroundColor = calendarTheme.selectedDateBackgroundColor ?? theme.colorScheme.primary;
      textColor = calendarTheme.selectedDateTextColor ?? theme.colorScheme.onPrimary;
    } else if (isToday && !isDisabled) {
      backgroundColor = Colors.transparent;
      textColor = calendarTheme.todayTextColor ?? theme.colorScheme.primary;
      borderColor = calendarTheme.todayBorderColor ?? theme.colorScheme.primary;
    } else if (isDisabled) {
      backgroundColor = Colors.transparent;
      textColor = calendarTheme.disabledDateTextColor ?? 
                  theme.colorScheme.onSurface.withOpacity(0.38);
    } else {
      backgroundColor = Colors.transparent;
      textColor = isCurrentMonth
        ? (calendarTheme.dateCellTextColor ?? theme.colorScheme.onSurface)
        : theme.colorScheme.onSurface.withOpacity(0.4);
    }
    
    return Padding(
      padding: ShadcnTokens.paddingAll(ShadcnTokens.spacing1),
      child: Material(
        color: backgroundColor,
        borderRadius: calendarTheme.dateCellBorderRadius ?? 
                     ShadcnTokens.borderRadius(ShadcnTokens.radiusSm),
        child: InkWell(
          onTap: !isDisabled && enabled ? () => _selectDate(context, date) : null,
          borderRadius: calendarTheme.dateCellBorderRadius ?? 
                       ShadcnTokens.borderRadius(ShadcnTokens.radiusSm),
          child: Container(
            width: cellSize,
            height: cellSize,
            decoration: borderColor != null
              ? BoxDecoration(
                  border: Border.all(color: borderColor, width: 1.0),
                  borderRadius: calendarTheme.dateCellBorderRadius ?? 
                               ShadcnTokens.borderRadius(ShadcnTokens.radiusSm),
                )
              : null,
            alignment: Alignment.center,
            child: Text(
              date.day.toString(),
              style: TextStyle(color: textColor),
            ),
          ),
        ),
      ),
    );
  }

  void _navigateMonth(BuildContext context, int monthDelta) {
    final newDate = DateTime(
      displayDate.year,
      displayDate.month + monthDelta,
      1,
    );
    onDisplayDateChanged?.call(newDate);
  }

  void _showMonthYearPicker(BuildContext context) async {
    final result = await showDatePicker(
      context: context,
      initialDate: displayDate,
      firstDate: minDate ?? DateTime(1900),
      lastDate: maxDate ?? DateTime(2100),
      initialDatePickerMode: DatePickerMode.year,
    );
    
    if (result != null) {
      onDisplayDateChanged?.call(result);
    }
  }

  void _selectDate(BuildContext context, DateTime date) {
    onDateSelected?.call(date);
  }

  String _formatMonthYear(DateTime date) {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return '${months[date.month - 1]} ${date.year}';
  }

  List<String> _getWeekdayNames() {
    const weekdays = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
    if (firstDayOfWeek == 0) {
      return weekdays;
    } else {
      return [
        ...weekdays.sublist(firstDayOfWeek),
        ...weekdays.sublist(0, firstDayOfWeek),
      ];
    }
  }

  List<DateTime> _generateCalendarDates(DateTime month) {
    final dates = <DateTime>[];
    final firstDay = DateTime(month.year, month.month, 1);
    int firstWeekday = firstDay.weekday % 7;
    firstWeekday = (firstWeekday - firstDayOfWeek) % 7;
    if (firstWeekday < 0) firstWeekday += 7;
    
    final startDate = firstDay.subtract(Duration(days: firstWeekday));
    
    for (int i = 0; i < 42; i++) {
      dates.add(startDate.add(Duration(days: i)));
    }
    
    return dates;
  }

  bool _isDateDisabled(DateTime date) {
    if (isDateDisabled?.call(date) == true) return true;
    if (minDate != null && date.isBefore(minDate!)) return true;
    if (maxDate != null && date.isAfter(maxDate!)) return true;
    return false;
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  static DateTime _getDefaultDate() {
    return DateTime.now();
  }
}