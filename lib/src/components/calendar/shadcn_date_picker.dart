import 'package:flutter/material.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_calendar_theme.dart';
import '../../theme/extensions/shadcn_input_theme.dart';
import '../../constants/shadcn_tokens.dart';
import 'shadcn_calendar.dart';

/// A shadcn-styled date picker with Material date picker integration.
class ShadcnDatePicker extends ShadcnComponent with ShadcnComponentValidation {
  final DateTime? selectedDate;
  final ValueChanged<DateTime>? onDateSelected;
  final String? placeholder;
  final DateTime? minDate;
  final DateTime? maxDate;
  final bool enabled;
  final String? errorText;
  final String? helperText;

  const ShadcnDatePicker({
    super.key,
    this.selectedDate,
    this.onDateSelected,
    this.placeholder = 'Select date',
    this.minDate,
    this.maxDate,
    this.enabled = true,
    this.errorText,
    this.helperText,
  });

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    return _DatePickerInput(
      selectedDate: selectedDate,
      onDateSelected: onDateSelected,
      placeholder: placeholder,
      minDate: minDate,
      maxDate: maxDate,
      theme: theme,
      enabled: enabled,
      errorText: errorText,
      helperText: helperText,
    );
  }
}

class _DatePickerInput extends StatefulWidget {
  final DateTime? selectedDate;
  final ValueChanged<DateTime>? onDateSelected;
  final String? placeholder;
  final DateTime? minDate;
  final DateTime? maxDate;
  final ThemeData theme;
  final bool enabled;
  final String? errorText;
  final String? helperText;
  
  const _DatePickerInput({
    required this.selectedDate,
    required this.onDateSelected,
    required this.placeholder,
    required this.minDate,
    required this.maxDate,
    required this.theme,
    required this.enabled,
    required this.errorText,
    required this.helperText,
  });

  @override
  State<_DatePickerInput> createState() => _DatePickerInputState();
}

class _DatePickerInputState extends State<_DatePickerInput> {
  late TextEditingController _controller;
  String? _parseError;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    if (widget.selectedDate != null) {
      _controller.text = _formatDate(widget.selectedDate!);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          height: 40.0,
          decoration: BoxDecoration(
            color: widget.theme.colorScheme.surface,
            border: Border.all(
              color: _getBorderColor(),
              width: 1.0,
            ),
            borderRadius: ShadcnTokens.borderRadius(ShadcnTokens.radiusMd),
          ),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _controller,
                  enabled: widget.enabled,
                  decoration: InputDecoration(
                    hintText: widget.placeholder,
                    border: InputBorder.none,
                    contentPadding: ShadcnTokens.inputPadding,
                  ),
                  onChanged: _onTextChanged,
                  onTap: _showMaterialDatePicker,
                  readOnly: true,
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(right: ShadcnTokens.spacing2),
                child: InkWell(
                  onTap: widget.enabled ? _showMaterialDatePicker : null,
                  borderRadius: ShadcnTokens.borderRadius(ShadcnTokens.radiusSm),
                  child: const Padding(
                    padding: EdgeInsets.all(ShadcnTokens.spacing1),
                    child: Icon(Icons.calendar_today, size: ShadcnTokens.iconSizeSm),
                  ),
                ),
              ),
            ],
          ),
        ),
        
        if (widget.helperText != null || _parseError != null || widget.errorText != null) ...[
          const SizedBox(height: ShadcnTokens.spacing1),
          Text(
            widget.errorText ?? _parseError ?? widget.helperText!,
            style: TextStyle(
              fontSize: ShadcnTokens.fontSizeSm,
              color: (widget.errorText != null || _parseError != null)
                ? widget.theme.colorScheme.error
                : widget.theme.colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
        ],
      ],
    );
  }

  Color _getBorderColor() {
    if (widget.errorText != null || _parseError != null) {
      return widget.theme.colorScheme.error;
    }
    return widget.theme.colorScheme.outline;
  }

  void _onTextChanged(String value) {
    // Handle text changes if needed
  }

  void _showMaterialDatePicker() async {
    if (!widget.enabled) return;
    
    final result = await showDatePicker(
      context: context,
      initialDate: widget.selectedDate ?? DateTime.now(),
      firstDate: widget.minDate ?? DateTime(1900),
      lastDate: widget.maxDate ?? DateTime(2100),
    );

    if (result != null) {
      _controller.text = _formatDate(result);
      widget.onDateSelected?.call(result);
      setState(() {
        _parseError = null;
      });
    }
  }

  String _formatDate(DateTime date) {
    return '${date.month.toString().padLeft(2, '0')}/'
           '${date.day.toString().padLeft(2, '0')}/'
           '${date.year}';
  }
}