import 'package:flutter/material.dart';
import '../../theme/extensions/shadcn_alert_theme.dart';
import '../../constants/shadcn_tokens.dart';
import '../shadcn_component.dart';

/// A shadcn-style alert component for displaying important messages.
/// 
/// The ShadcnAlert component provides a way to display contextual information
/// to users with support for different variants, custom icons, titles, and
/// descriptions. It follows shadcn design principles while integrating
/// seamlessly with Flutter's Material Design theme system.
/// 
/// This component supports:
/// - Multiple variants (default, destructive)
/// - Custom icons or default variant-based icons
/// - Title and description content
/// - Full theme customization through ShadcnAlertTheme
/// - Automatic theme resolution with Material Design fallbacks
/// 
/// Example usage:
/// ```dart
/// ShadcnAlert(
///   title: Text('Information'),
///   description: Text('This is important information for the user.'),
/// )
/// 
/// ShadcnAlert(
///   variant: ShadcnAlertVariant.destructive,
///   icon: Icon(Icons.error),
///   title: Text('Error'),
///   description: Text('Something went wrong. Please try again.'),
/// )
/// ```
class ShadcnAlert extends ShadcnComponent with ShadcnComponentValidation {
  /// The alert variant that determines styling and default icon
  final ShadcnAlertVariant variant;
  
  /// Optional custom icon to display. If null, uses variant's default icon
  final Widget? icon;
  
  /// The title widget for the alert
  final Widget? title;
  
  /// The description widget for the alert
  final Widget? description;
  
  /// Optional callback when the alert is tapped
  final VoidCallback? onTap;
  
  /// Whether the alert is dismissible
  final bool dismissible;
  
  /// Callback when the alert is dismissed
  final VoidCallback? onDismiss;
  
  /// Custom padding override for the alert
  final EdgeInsets? padding;
  
  /// Custom border radius override for the alert
  final BorderRadius? borderRadius;
  
  /// Semantic label for accessibility
  final String? semanticLabel;
  
  /// Whether to exclude this alert from semantics tree
  final bool excludeFromSemantics;
  
  const ShadcnAlert({
    super.key,
    this.variant = ShadcnAlertVariant.defaultVariant,
    this.icon,
    this.title,
    this.description,
    this.onTap,
    this.dismissible = false,
    this.onDismiss,
    this.padding,
    this.borderRadius,
    this.semanticLabel,
    this.excludeFromSemantics = false,
  });
  
  /// Creates a default variant alert with the provided content
  const ShadcnAlert.info({
    Key? key,
    Widget? icon,
    Widget? title,
    Widget? description,
    VoidCallback? onTap,
    bool dismissible = false,
    VoidCallback? onDismiss,
    EdgeInsets? padding,
    BorderRadius? borderRadius,
    String? semanticLabel,
    bool excludeFromSemantics = false,
  }) : this(
    key: key,
    variant: ShadcnAlertVariant.defaultVariant,
    icon: icon,
    title: title,
    description: description,
    onTap: onTap,
    dismissible: dismissible,
    onDismiss: onDismiss,
    padding: padding,
    borderRadius: borderRadius,
    semanticLabel: semanticLabel,
    excludeFromSemantics: excludeFromSemantics,
  );
  
  /// Creates a destructive variant alert with the provided content
  const ShadcnAlert.destructive({
    Key? key,
    Widget? icon,
    Widget? title,
    Widget? description,
    VoidCallback? onTap,
    bool dismissible = false,
    VoidCallback? onDismiss,
    EdgeInsets? padding,
    BorderRadius? borderRadius,
    String? semanticLabel,
    bool excludeFromSemantics = false,
  }) : this(
    key: key,
    variant: ShadcnAlertVariant.destructive,
    icon: icon,
    title: title,
    description: description,
    onTap: onTap,
    dismissible: dismissible,
    onDismiss: onDismiss,
    padding: padding,
    borderRadius: borderRadius,
    semanticLabel: semanticLabel,
    excludeFromSemantics: excludeFromSemantics,
  );
  
  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    // Validate theme context and component configuration
    assert(() {
      validateThemeProperties(context);
      validateAccessibility(
        semanticLabel: semanticLabel,
        excludeFromSemantics: excludeFromSemantics,
        componentName: 'ShadcnAlert',
      );
      
      // Ensure at least one content element is provided
      if (title == null && description == null) {
        debugPrint(
          'Warning: ShadcnAlert has neither title nor description. '
          'Consider providing content for better user experience.',
        );
      }
      
      return true;
    }());
    
    // Resolve the alert theme with fallbacks
    final alertTheme = resolveTheme<ShadcnAlertTheme>(
      context,
      ShadcnAlertTheme.defaultTheme,
    );
    
    // Resolve variant-specific colors
    final variantColors = _resolveVariantColors(alertTheme, variant);
    
    // Resolve layout properties with theme and custom overrides
    final resolvedPadding = resolveSpacing(
      context,
      padding,
      alertTheme.padding ?? ShadcnTokens.paddingAll(ShadcnTokens.spacing4),
    );
    
    final resolvedBorderRadius = resolveBorderRadius(
      context,
      borderRadius,
      alertTheme.borderRadius ?? ShadcnTokens.borderRadius(ShadcnTokens.radiusMd),
    );
    
    final resolvedIconGap = resolveDouble(
      context,
      null,
      alertTheme.iconGap ?? ShadcnTokens.spacing3,
      applyVerticalDensity: false,
    );
    
    final resolvedContentGap = resolveDouble(
      context,
      null,
      alertTheme.contentGap ?? ShadcnTokens.spacing1,
    );
    
    // Build the alert content
    Widget alertContent = _buildAlertContent(
      context,
      alertTheme,
      variantColors,
      resolvedIconGap,
      resolvedContentGap,
    );
    
    // Add dismiss functionality if enabled
    if (dismissible) {
      alertContent = _buildDismissibleAlert(
        context,
        alertContent,
        alertTheme,
        variantColors,
      );
    }
    
    // Build the container with proper styling
    Widget result = Container(
      padding: resolvedPadding,
      decoration: BoxDecoration(
        color: variantColors.background,
        border: Border.all(
          color: variantColors.border,
          width: alertTheme.borderWidth ?? ShadcnTokens.borderWidth,
        ),
        borderRadius: resolvedBorderRadius,
      ),
      child: alertContent,
    );
    
    // Add tap functionality if provided
    if (onTap != null) {
      result = Material(
        type: MaterialType.transparency,
        child: InkWell(
          onTap: onTap,
          borderRadius: resolvedBorderRadius,
          child: result,
        ),
      );
    }
    
    // Add semantic information
    if (!excludeFromSemantics) {
      result = Semantics(
        label: semanticLabel ?? _buildSemanticLabel(),
        liveRegion: variant.isCritical,
        child: result,
      );
    }
    
    return result;
  }
  
  /// Resolves colors based on the current variant and theme
  _AlertColors _resolveVariantColors(ShadcnAlertTheme theme, ShadcnAlertVariant variant) {
    switch (variant) {
      case ShadcnAlertVariant.defaultVariant:
        return _AlertColors(
          background: theme.defaultBackground ?? Colors.grey.withValues(alpha: 0.1),
          foreground: theme.defaultForeground ?? Colors.black87,
          border: theme.defaultBorder ?? Colors.grey.withValues(alpha: 0.2),
          iconColor: theme.defaultIconColor ?? Colors.grey.shade600,
        );
      case ShadcnAlertVariant.destructive:
        return _AlertColors(
          background: theme.destructiveBackground ?? Colors.red.withValues(alpha: 0.1),
          foreground: theme.destructiveForeground ?? Colors.red.shade700,
          border: theme.destructiveBorder ?? Colors.red.withValues(alpha: 0.2),
          iconColor: theme.destructiveIconColor ?? Colors.red.shade700,
        );
    }
  }
  
  /// Builds the main content of the alert including icon, title, and description
  Widget _buildAlertContent(
    BuildContext context,
    ShadcnAlertTheme theme,
    _AlertColors colors,
    double iconGap,
    double contentGap,
  ) {
    final iconWidget = _buildIcon(context, theme, colors);
    final contentWidget = _buildTextContent(context, theme, colors, contentGap);
    
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (iconWidget != null) ...[
          iconWidget,
          SizedBox(width: iconGap),
        ],
        Expanded(child: contentWidget),
      ],
    );
  }
  
  /// Builds the icon widget for the alert
  Widget? _buildIcon(
    BuildContext context,
    ShadcnAlertTheme theme,
    _AlertColors colors,
  ) {
    Widget? iconWidget;
    
    if (icon != null) {
      // Use custom icon
      iconWidget = icon!;
    } else {
      // Use default variant icon
      final iconSize = resolveDouble(
        context,
        null,
        theme.iconSize ?? ShadcnTokens.iconSizeMd,
        applyVerticalDensity: false,
      );
      
      iconWidget = Icon(
        variant.defaultIcon,
        size: iconSize,
        color: colors.iconColor,
      );
    }
    
    // Ensure icon uses the correct color if it's an Icon widget
    if (iconWidget is Icon) {
      iconWidget = Icon(
        iconWidget.icon,
        size: iconWidget.size ?? theme.iconSize ?? ShadcnTokens.iconSizeMd,
        color: colors.iconColor,
      );
    }
    
    return iconWidget;
  }
  
  /// Builds the text content (title and description) for the alert
  Widget _buildTextContent(
    BuildContext context,
    ShadcnAlertTheme theme,
    _AlertColors colors,
    double contentGap,
  ) {
    final children = <Widget>[];
    
    if (title != null) {
      Widget titleWidget = title!;
      
      // Apply theme styling if it's a Text widget
      if (titleWidget is Text) {
        final resolvedTitleStyle = resolveTextStyle(
          context,
          theme.titleTextStyle,
          (textTheme) => textTheme.bodyMedium ?? const TextStyle(),
        ).copyWith(
          color: colors.foreground,
          fontWeight: ShadcnTokens.fontWeightMedium,
        );
        
        titleWidget = Text(
          titleWidget.data ?? '',
          style: resolvedTitleStyle.merge(titleWidget.style),
          textAlign: titleWidget.textAlign,
          overflow: titleWidget.overflow,
          maxLines: titleWidget.maxLines,
        );
      }
      
      children.add(titleWidget);
    }
    
    if (title != null && description != null) {
      children.add(SizedBox(height: contentGap));
    }
    
    if (description != null) {
      Widget descriptionWidget = description!;
      
      // Apply theme styling if it's a Text widget
      if (descriptionWidget is Text) {
        final resolvedDescriptionStyle = resolveTextStyle(
          context,
          theme.descriptionTextStyle,
          (textTheme) => textTheme.bodySmall ?? const TextStyle(),
        ).copyWith(
          color: colors.foreground.withValues(alpha: 0.8),
        );
        
        descriptionWidget = Text(
          descriptionWidget.data ?? '',
          style: resolvedDescriptionStyle.merge(descriptionWidget.style),
          textAlign: descriptionWidget.textAlign,
          overflow: descriptionWidget.overflow,
          maxLines: descriptionWidget.maxLines,
        );
      }
      
      children.add(descriptionWidget);
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }
  
  /// Builds a dismissible version of the alert
  Widget _buildDismissibleAlert(
    BuildContext context,
    Widget alertContent,
    ShadcnAlertTheme theme,
    _AlertColors colors,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(child: alertContent),
        SizedBox(width: theme.iconGap ?? ShadcnTokens.spacing3),
        Material(
          type: MaterialType.transparency,
          child: InkWell(
            borderRadius: BorderRadius.circular(ShadcnTokens.radiusSm),
            onTap: onDismiss,
            child: Padding(
              padding: const EdgeInsets.all(4.0),
              child: Icon(
                Icons.close,
                size: theme.iconSize ?? ShadcnTokens.iconSizeSm,
                color: colors.iconColor,
              ),
            ),
          ),
        ),
      ],
    );
  }
  
  /// Builds a semantic label for accessibility
  String _buildSemanticLabel() {
    final parts = <String>[];
    
    // Add variant context
    if (variant.isCritical) {
      parts.add('Alert');
    } else {
      parts.add('Information');
    }
    
    // Add title content if it's a text widget
    if (title is Text) {
      final titleText = (title as Text).data;
      if (titleText?.isNotEmpty == true) {
        parts.add(titleText!);
      }
    }
    
    // Add description content if it's a text widget
    if (description is Text) {
      final descriptionText = (description as Text).data;
      if (descriptionText?.isNotEmpty == true) {
        parts.add(descriptionText!);
      }
    }
    
    return parts.join('. ');
  }
}

/// Helper class to hold resolved colors for an alert variant
class _AlertColors {
  final Color background;
  final Color foreground;
  final Color border;
  final Color iconColor;
  
  const _AlertColors({
    required this.background,
    required this.foreground,
    required this.border,
    required this.iconColor,
  });
}