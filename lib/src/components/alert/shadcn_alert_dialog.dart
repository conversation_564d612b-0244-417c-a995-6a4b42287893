import 'package:flutter/material.dart';
import '../../theme/extensions/shadcn_alert_theme.dart';
import '../../constants/shadcn_tokens.dart';
import '../shadcn_component.dart';

/// A shadcn-style alert dialog component for displaying critical messages requiring user action.
/// 
/// The ShadcnAlertDialog component provides a modal dialog that integrates with Flutter's
/// Material Design dialog system while following shadcn design principles. It's designed
/// for displaying important information that requires immediate user attention or action.
/// 
/// This component supports:
/// - Multiple variants (default, destructive)
/// - Custom icons with variant-based defaults
/// - Title and content text
/// - Action buttons with theme-aware styling
/// - Full theme customization through ShadcnAlertTheme
/// - Proper Material dialog integration and behavior
/// 
/// Example usage:
/// ```dart
/// // Show an info dialog
/// showDialog<bool>(
///   context: context,
///   builder: (context) => ShadcnAlertDialog(
///     title: 'Information',
///     content: 'This is important information.',
///     actions: [
///       TextButton(
///         onPressed: () => Navigator.of(context).pop(),
///         child: Text('OK'),
///       ),
///     ],
///   ),
/// );
/// 
/// // Show a destructive confirmation dialog
/// showDialog<bool>(
///   context: context,
///   builder: (context) => ShadcnAlertDialog.destructive(
///     title: 'Delete Item',
///     content: 'Are you sure you want to delete this item? This action cannot be undone.',
///     actions: [
///       TextButton(
///         onPressed: () => Navigator.of(context).pop(false),
///         child: Text('Cancel'),
///       ),
///       ElevatedButton(
///         onPressed: () => Navigator.of(context).pop(true),
///         child: Text('Delete'),
///       ),
///     ],
///   ),
/// );
/// ```
class ShadcnAlertDialog extends ShadcnComponent with ShadcnComponentValidation {
  /// The alert dialog variant that determines styling and default icon
  final ShadcnAlertVariant variant;
  
  /// Optional custom icon to display. If null, uses variant's default icon
  final Widget? icon;
  
  /// The title of the alert dialog
  final String? title;
  
  /// The content/body text of the alert dialog
  final String? content;
  
  /// Custom content widget that replaces the content text
  final Widget? contentWidget;
  
  /// List of action buttons for the dialog
  final List<Widget>? actions;
  
  /// Custom padding for the dialog content
  final EdgeInsets? contentPadding;
  
  /// Custom padding for the actions area
  final EdgeInsets? actionsPadding;
  
  /// Whether actions should be arranged vertically (default is horizontal)
  final bool verticalActions;
  
  /// Custom border radius override for the dialog
  final BorderRadius? borderRadius;
  
  /// Semantic label for accessibility
  final String? semanticLabel;
  
  /// Whether to exclude this dialog from semantics tree
  final bool excludeFromSemantics;
  
  /// Whether the dialog can be dismissed by tapping outside
  final bool barrierDismissible;
  
  /// Color of the barrier (background overlay)
  final Color? barrierColor;
  
  /// Label for the barrier for accessibility
  final String? barrierLabel;
  
  const ShadcnAlertDialog({
    super.key,
    this.variant = ShadcnAlertVariant.defaultVariant,
    this.icon,
    this.title,
    this.content,
    this.contentWidget,
    this.actions,
    this.contentPadding,
    this.actionsPadding,
    this.verticalActions = false,
    this.borderRadius,
    this.semanticLabel,
    this.excludeFromSemantics = false,
    this.barrierDismissible = true,
    this.barrierColor,
    this.barrierLabel,
  }) : assert(
         content != null || contentWidget != null,
         'Either content or contentWidget must be provided',
       );
  
  /// Creates a default variant alert dialog with the provided content
  const ShadcnAlertDialog.info({
    Key? key,
    Widget? icon,
    String? title,
    String? content,
    Widget? contentWidget,
    List<Widget>? actions,
    EdgeInsets? contentPadding,
    EdgeInsets? actionsPadding,
    bool verticalActions = false,
    BorderRadius? borderRadius,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    bool barrierDismissible = true,
    Color? barrierColor,
    String? barrierLabel,
  }) : this(
    key: key,
    variant: ShadcnAlertVariant.defaultVariant,
    icon: icon,
    title: title,
    content: content,
    contentWidget: contentWidget,
    actions: actions,
    contentPadding: contentPadding,
    actionsPadding: actionsPadding,
    verticalActions: verticalActions,
    borderRadius: borderRadius,
    semanticLabel: semanticLabel,
    excludeFromSemantics: excludeFromSemantics,
    barrierDismissible: barrierDismissible,
    barrierColor: barrierColor,
    barrierLabel: barrierLabel,
  );
  
  /// Creates a destructive variant alert dialog with the provided content
  const ShadcnAlertDialog.destructive({
    Key? key,
    Widget? icon,
    String? title,
    String? content,
    Widget? contentWidget,
    List<Widget>? actions,
    EdgeInsets? contentPadding,
    EdgeInsets? actionsPadding,
    bool verticalActions = false,
    BorderRadius? borderRadius,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    bool barrierDismissible = true,
    Color? barrierColor,
    String? barrierLabel,
  }) : this(
    key: key,
    variant: ShadcnAlertVariant.destructive,
    icon: icon,
    title: title,
    content: content,
    contentWidget: contentWidget,
    actions: actions,
    contentPadding: contentPadding,
    actionsPadding: actionsPadding,
    verticalActions: verticalActions,
    borderRadius: borderRadius,
    semanticLabel: semanticLabel,
    excludeFromSemantics: excludeFromSemantics,
    barrierDismissible: barrierDismissible,
    barrierColor: barrierColor,
    barrierLabel: barrierLabel,
  );
  
  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    // Validate theme context and component configuration
    assert(() {
      validateThemeProperties(context);
      validateAccessibility(
        semanticLabel: semanticLabel,
        excludeFromSemantics: excludeFromSemantics,
        componentName: 'ShadcnAlertDialog',
      );
      
      // Validate required content
      if (content == null && contentWidget == null) {
        throw FlutterError(
          'ShadcnAlertDialog requires either content text or contentWidget. '
          'Provide at least one of these properties.',
        );
      }
      
      return true;
    }());
    
    // Resolve the alert theme with fallbacks
    final alertTheme = resolveTheme<ShadcnAlertTheme>(
      context,
      ShadcnAlertTheme.defaultTheme,
    );
    
    // Resolve variant-specific colors
    final variantColors = _resolveVariantColors(alertTheme, variant);
    
    // Resolve layout properties
    final resolvedBorderRadius = resolveBorderRadius(
      context,
      borderRadius,
      alertTheme.borderRadius ?? ShadcnTokens.borderRadius(ShadcnTokens.radiusLg),
    );
    
    final resolvedContentPadding = resolveSpacing(
      context,
      contentPadding,
      const EdgeInsets.fromLTRB(24.0, 24.0, 24.0, 16.0), // Material 3 defaults
    );
    
    final resolvedActionsPadding = resolveSpacing(
      context,
      actionsPadding,
      const EdgeInsets.fromLTRB(24.0, 0.0, 24.0, 24.0), // Material 3 defaults
    );
    
    return AlertDialog(
      // Material dialog properties
      clipBehavior: Clip.hardEdge,
      backgroundColor: variantColors.background,
      surfaceTintColor: Colors.transparent, // Remove Material 3 tint
      shape: RoundedRectangleBorder(
        borderRadius: resolvedBorderRadius,
        side: BorderSide(
          color: variantColors.border,
          width: alertTheme.borderWidth ?? ShadcnTokens.borderWidth,
        ),
      ),
      contentPadding: resolvedContentPadding,
      actionsPadding: resolvedActionsPadding,
      
      // Title section
      title: _buildTitle(context, alertTheme, variantColors),
      
      // Content section
      content: _buildContent(context, alertTheme, variantColors),
      
      // Actions section
      actions: _buildActions(context, alertTheme, variantColors),
      
      // Semantic properties
      semanticLabel: semanticLabel ?? _buildSemanticLabel(),
      
      // Layout properties
      actionsAlignment: verticalActions 
          ? MainAxisAlignment.spaceEvenly
          : MainAxisAlignment.end,
      buttonPadding: EdgeInsets.zero,
    );
  }
  
  /// Resolves colors based on the current variant and theme
  _AlertDialogColors _resolveVariantColors(ShadcnAlertTheme theme, ShadcnAlertVariant variant) {
    switch (variant) {
      case ShadcnAlertVariant.defaultVariant:
        return _AlertDialogColors(
          background: theme.defaultBackground?.withValues(alpha: 1.0) ?? 
                     Colors.white, // Dialog needs opaque background
          foreground: theme.defaultForeground ?? Colors.black87,
          border: theme.defaultBorder ?? Colors.grey.withValues(alpha: 0.2),
          iconColor: theme.defaultIconColor ?? Colors.grey.shade600,
        );
      case ShadcnAlertVariant.destructive:
        return _AlertDialogColors(
          background: theme.destructiveBackground?.withValues(alpha: 1.0) ?? 
                     Colors.red.shade50, // Light destructive background for dialog
          foreground: theme.destructiveForeground ?? Colors.red.shade700,
          border: theme.destructiveBorder ?? Colors.red.withValues(alpha: 0.2),
          iconColor: theme.destructiveIconColor ?? Colors.red.shade700,
        );
    }
  }
  
  /// Builds the title section with optional icon
  Widget? _buildTitle(
    BuildContext context,
    ShadcnAlertTheme theme,
    _AlertDialogColors colors,
  ) {
    if (title == null && icon == null) {
      return null;
    }
    
    final titleStyle = resolveTextStyle(
      context,
      theme.titleTextStyle,
      (textTheme) => textTheme.headlineSmall ?? const TextStyle(),
    ).copyWith(
      color: colors.foreground,
      fontWeight: ShadcnTokens.fontWeightSemibold,
    );
    
    final iconWidget = _buildIcon(context, theme, colors);
    final iconGap = resolveDouble(
      context,
      null,
      theme.iconGap ?? ShadcnTokens.spacing3,
      applyVerticalDensity: false,
    );
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (iconWidget != null) ...[
          iconWidget,
          SizedBox(width: iconGap),
        ],
        if (title != null)
          Flexible(
            child: Text(
              title!,
              style: titleStyle,
            ),
          ),
      ],
    );
  }
  
  /// Builds the icon widget for the dialog
  Widget? _buildIcon(
    BuildContext context,
    ShadcnAlertTheme theme,
    _AlertDialogColors colors,
  ) {
    Widget? iconWidget;
    
    if (icon != null) {
      // Use custom icon
      iconWidget = icon!;
    } else if (title != null) {
      // Use default variant icon only if we have a title
      final iconSize = resolveDouble(
        context,
        null,
        theme.iconSize ?? ShadcnTokens.iconSizeLg, // Slightly larger for dialogs
        applyVerticalDensity: false,
      );
      
      iconWidget = Icon(
        variant.defaultIcon,
        size: iconSize,
        color: colors.iconColor,
      );
    }
    
    // Ensure icon uses the correct color if it's an Icon widget
    if (iconWidget is Icon) {
      iconWidget = Icon(
        iconWidget.icon,
        size: iconWidget.size ?? theme.iconSize ?? ShadcnTokens.iconSizeLg,
        color: colors.iconColor,
      );
    }
    
    return iconWidget;
  }
  
  /// Builds the content section of the dialog
  Widget _buildContent(
    BuildContext context,
    ShadcnAlertTheme theme,
    _AlertDialogColors colors,
  ) {
    // Use custom content widget if provided
    if (contentWidget != null) {
      return contentWidget!;
    }
    
    // Build text content
    if (content != null) {
      final contentStyle = resolveTextStyle(
        context,
        theme.descriptionTextStyle,
        (textTheme) => textTheme.bodyMedium ?? const TextStyle(),
      ).copyWith(
        color: colors.foreground.withValues(alpha: 0.8),
      );
      
      return Text(
        content!,
        style: contentStyle,
      );
    }
    
    // Fallback (should not reach here due to assertion)
    return const SizedBox.shrink();
  }
  
  /// Builds the actions section of the dialog
  List<Widget>? _buildActions(
    BuildContext context,
    ShadcnAlertTheme theme,
    _AlertDialogColors colors,
  ) {
    if (actions == null || actions!.isEmpty) {
      return null;
    }
    
    if (verticalActions) {
      // For vertical actions, add spacing between buttons
      final spacedActions = <Widget>[];
      for (int i = 0; i < actions!.length; i++) {
        spacedActions.add(
          SizedBox(
            width: double.infinity,
            child: actions![i],
          ),
        );
        
        if (i < actions!.length - 1) {
          spacedActions.add(SizedBox(height: ShadcnTokens.spacing2));
        }
      }
      return spacedActions;
    }
    
    return actions;
  }
  
  /// Builds a semantic label for accessibility
  String _buildSemanticLabel() {
    final parts = <String>['Alert Dialog'];
    
    // Add variant context
    if (variant.isCritical) {
      parts.add('Warning');
    }
    
    // Add title content
    if (title?.isNotEmpty == true) {
      parts.add(title!);
    }
    
    // Add content text
    if (content?.isNotEmpty == true) {
      parts.add(content!);
    }
    
    return parts.join('. ');
  }
  
  /// Shows this alert dialog using the Material dialog system
  /// 
  /// This is a convenience method that wraps the standard [showDialog] function
  /// with proper configuration for the ShadcnAlertDialog.
  /// 
  /// Returns a Future that resolves to the value passed to [Navigator.pop]
  /// when the dialog is dismissed.
  Future<T?> show<T>(BuildContext context) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor,
      barrierLabel: barrierLabel ?? 
          MaterialLocalizations.of(context).modalBarrierDismissLabel,
      builder: (context) => this,
    );
  }
}

/// Helper class to hold resolved colors for an alert dialog variant
class _AlertDialogColors {
  final Color background;
  final Color foreground;
  final Color border;
  final Color iconColor;
  
  const _AlertDialogColors({
    required this.background,
    required this.foreground,
    required this.border,
    required this.iconColor,
  });
}

/// Extension methods to provide convenient static show methods for ShadcnAlertDialog
extension ShadcnAlertDialogExtension on ShadcnAlertDialog {
  /// Shows an info alert dialog with the provided configuration
  static Future<T?> showInfo<T>(
    BuildContext context, {
    String? title,
    String? content,
    Widget? contentWidget,
    List<Widget>? actions,
    bool barrierDismissible = true,
  }) {
    return ShadcnAlertDialog.info(
      title: title,
      content: content,
      contentWidget: contentWidget,
      actions: actions,
      barrierDismissible: barrierDismissible,
    ).show<T>(context);
  }
  
  /// Shows a destructive alert dialog with the provided configuration
  static Future<T?> showDestructive<T>(
    BuildContext context, {
    String? title,
    String? content,
    Widget? contentWidget,
    List<Widget>? actions,
    bool barrierDismissible = true,
  }) {
    return ShadcnAlertDialog.destructive(
      title: title,
      content: content,
      contentWidget: contentWidget,
      actions: actions,
      barrierDismissible: barrierDismissible,
    ).show<T>(context);
  }
  
  /// Shows a confirmation dialog with Yes/No buttons
  /// 
  /// Returns `true` if the user confirms, `false` if they cancel,
  /// or `null` if the dialog is dismissed without selection.
  static Future<bool?> showConfirmation(
    BuildContext context, {
    String title = 'Confirmation',
    required String content,
    String confirmText = 'Yes',
    String cancelText = 'No',
    ShadcnAlertVariant variant = ShadcnAlertVariant.defaultVariant,
    bool barrierDismissible = true,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => ShadcnAlertDialog(
        variant: variant,
        title: title,
        content: content,
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(cancelText),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }
  
  /// Shows a destructive confirmation dialog for delete/remove operations
  /// 
  /// Returns `true` if the user confirms the destructive action, `false` if they cancel,
  /// or `null` if the dialog is dismissed without selection.
  static Future<bool?> showDestructiveConfirmation(
    BuildContext context, {
    String title = 'Confirm Deletion',
    required String content,
    String confirmText = 'Delete',
    String cancelText = 'Cancel',
    bool barrierDismissible = true,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => ShadcnAlertDialog.destructive(
        title: title,
        content: content,
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(cancelText),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }
}