import 'package:flutter/material.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_avatar_theme.dart';
import '../../constants/shadcn_tokens.dart';

/// A shadcn-styled avatar component with image, initials, and fallback support.
/// 
/// This avatar component provides a flexible way to display user avatars with
/// automatic fallback handling. It supports three display modes:
/// 1. Image display (if [imageProvider] is provided)
/// 2. Initials display (if [initials] is provided and image fails to load)
/// 3. Fallback icon display (if no image or initials are available)
/// 
/// The avatar automatically derives its styling from the current theme context,
/// with fallback to Material Design values. All styling is customizable through
/// the [ShadcnAvatarTheme] extension.
/// 
/// Example usage:
/// ```dart
/// // Image avatar
/// ShadcnAvatar(
///   imageProvider: NetworkImage('https://example.com/avatar.jpg'),
///   initials: 'JD', // Fallback if image fails
/// )
/// 
/// // Initials avatar
/// ShadcnAvatar(
///   initials: 'JD',
///   size: ShadcnAvatarSize.large,
/// )
/// 
/// // Icon avatar
/// ShadcnAvatar(
///   fallbackIcon: Icons.person,
/// )
/// ```
class ShadcnAvatar extends ShadcnComponent with ShadcnComponentValidation {
  /// Image provider for the avatar image.
  final ImageProvider? imageProvider;
  
  /// Initials to display if image is not available.
  final String? initials;
  
  /// Size variant for the avatar.
  final ShadcnAvatarSize size;
  
  /// Custom fallback icon to display when no image or initials are available.
  final Widget? fallbackIcon;
  
  /// Custom background color. Overrides theme setting.
  final Color? backgroundColor;
  
  /// Custom foreground color for text and icons. Overrides theme setting.
  final Color? foregroundColor;
  
  /// Custom border color. Overrides theme setting.
  final Color? borderColor;
  
  /// Custom border width. Overrides theme setting.
  final double? borderWidth;
  
  /// Custom border radius. If null, avatar will be circular.
  final BorderRadius? borderRadius;
  
  /// Custom size. Overrides size variant.
  final double? customSize;
  
  /// Callback when the avatar is tapped.
  final VoidCallback? onTap;
  
  /// Callback when the avatar is long pressed.
  final VoidCallback? onLongPress;
  
  /// Callback when image fails to load.
  final VoidCallback? onImageError;
  
  /// Focus node for keyboard navigation.
  final FocusNode? focusNode;
  
  /// Whether the avatar should autofocus when first built.
  final bool autofocus;
  
  /// Tooltip message displayed on hover.
  final String? tooltip;
  
  /// Semantic label for accessibility.
  final String? semanticLabel;
  
  /// Whether to exclude this avatar from semantics tree.
  final bool excludeFromSemantics;
  
  /// Custom mouse cursor when hovering over the avatar.
  final MouseCursor? mouseCursor;
  
  /// Whether to enable haptic feedback on press.
  final bool enableFeedback;
  
  /// Custom splash color for press animations.
  final Color? splashColor;
  
  /// Custom highlight color for press states.
  final Color? highlightColor;
  
  /// Whether the avatar should show a loading state when image is loading.
  final bool showLoadingState;

  const ShadcnAvatar({
    super.key,
    this.imageProvider,
    this.initials,
    this.size = ShadcnAvatarSize.medium,
    this.fallbackIcon,
    this.backgroundColor,
    this.foregroundColor,
    this.borderColor,
    this.borderWidth,
    this.borderRadius,
    this.customSize,
    this.onTap,
    this.onLongPress,
    this.onImageError,
    this.focusNode,
    this.autofocus = false,
    this.tooltip,
    this.semanticLabel,
    this.excludeFromSemantics = false,
    this.mouseCursor,
    this.enableFeedback = true,
    this.splashColor,
    this.highlightColor,
    this.showLoadingState = false,
  }) : assert(
         imageProvider != null || initials != null || fallbackIcon != null,
         'At least one of imageProvider, initials, or fallbackIcon must be provided',
       );

  /// Factory constructor for image avatar with automatic initials fallback.
  factory ShadcnAvatar.image({
    Key? key,
    required ImageProvider imageProvider,
    String? initials,
    ShadcnAvatarSize size = ShadcnAvatarSize.medium,
    VoidCallback? onTap,
    VoidCallback? onImageError,
    String? tooltip,
    String? semanticLabel,
  }) {
    return ShadcnAvatar(
      key: key,
      imageProvider: imageProvider,
      initials: initials,
      size: size,
      onTap: onTap,
      onImageError: onImageError,
      tooltip: tooltip,
      semanticLabel: semanticLabel,
    );
  }

  /// Factory constructor for initials-only avatar.
  factory ShadcnAvatar.initials({
    Key? key,
    required String initials,
    ShadcnAvatarSize size = ShadcnAvatarSize.medium,
    Color? backgroundColor,
    Color? foregroundColor,
    VoidCallback? onTap,
    String? tooltip,
    String? semanticLabel,
  }) {
    return ShadcnAvatar(
      key: key,
      initials: initials,
      size: size,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      onTap: onTap,
      tooltip: tooltip,
      semanticLabel: semanticLabel,
    );
  }

  /// Factory constructor for icon-only avatar.
  factory ShadcnAvatar.icon({
    Key? key,
    Widget? fallbackIcon,
    ShadcnAvatarSize size = ShadcnAvatarSize.medium,
    Color? backgroundColor,
    Color? foregroundColor,
    VoidCallback? onTap,
    String? tooltip,
    String? semanticLabel,
  }) {
    return ShadcnAvatar(
      key: key,
      fallbackIcon: fallbackIcon,
      size: size,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      onTap: onTap,
      tooltip: tooltip,
      semanticLabel: semanticLabel,
    );
  }

  /// Whether the avatar is interactive (has tap callbacks).
  bool get isInteractive => onTap != null || onLongPress != null;

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    // Validate theme context and properties
    validateThemeProperties(context);
    validateVariant(size, ShadcnAvatarSize.values, 'ShadcnAvatar');
    validateCallbacks(
      {'onTap': onTap, 'onLongPress': onLongPress}, 
      'ShadcnAvatar',
    );
    validateAccessibility(
      semanticLabel: semanticLabel,
      tooltip: tooltip,
      excludeFromSemantics: excludeFromSemantics,
      componentName: 'ShadcnAvatar',
    );

    // Resolve avatar theme
    final avatarTheme = resolveTheme<ShadcnAvatarTheme>(
      context,
      ShadcnAvatarTheme.defaultTheme,
    );

    // Resolve size properties
    final sizeProperties = _resolveSizeProperties(avatarTheme, size);
    final effectiveSize = customSize ?? sizeProperties.size;
    
    // Resolve colors
    final effectiveBackgroundColor = backgroundColor ?? 
        avatarTheme.backgroundColor ?? 
        theme.colorScheme.surface;
    final effectiveForegroundColor = foregroundColor ?? 
        avatarTheme.foregroundColor ?? 
        theme.colorScheme.onSurface;
    final effectiveBorderColor = borderColor ?? 
        avatarTheme.borderColor ?? 
        theme.colorScheme.outline.withOpacity(0.12);
    final effectiveBorderWidth = borderWidth ?? 
        avatarTheme.borderWidth ?? 
        ShadcnTokens.borderWidth;

    // Determine border radius
    final effectiveBorderRadius = borderRadius ?? 
        avatarTheme.borderRadius ?? 
        BorderRadius.circular(effectiveSize / 2); // Circular by default

    // Build avatar content
    final avatarContent = _buildAvatarContent(
      context,
      avatarTheme,
      sizeProperties,
      effectiveForegroundColor,
    );

    // Build the avatar container
    Widget avatar = AnimatedContainer(
      duration: avatarTheme.animationDuration ?? ShadcnTokens.durationFast,
      curve: avatarTheme.animationCurve ?? Curves.easeInOut,
      width: effectiveSize,
      height: effectiveSize,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        border: Border.all(
          color: effectiveBorderColor,
          width: effectiveBorderWidth,
        ),
        borderRadius: effectiveBorderRadius,
      ),
      child: ClipRRect(
        borderRadius: effectiveBorderRadius,
        child: avatarContent,
      ),
    );

    // Add interaction handling if needed
    if (isInteractive) {
      avatar = Material(
        type: MaterialType.transparency,
        child: InkWell(
          onTap: onTap,
          onLongPress: onLongPress,
          borderRadius: effectiveBorderRadius,
          splashColor: splashColor,
          highlightColor: highlightColor,
          mouseCursor: mouseCursor ?? SystemMouseCursors.click,
          enableFeedback: enableFeedback,
          excludeFromSemantics: true, // We handle semantics separately
          focusNode: focusNode,
          autofocus: autofocus,
          child: avatar,
        ),
      );
    }

    // Add focus handling
    if (focusNode != null) {
      avatar = Focus(
        focusNode: focusNode,
        child: avatar,
      );
    }

    // Add semantics
    if (!excludeFromSemantics) {
      avatar = Semantics(
        label: semanticLabel ?? _generateSemanticLabel(),
        image: imageProvider != null,
        button: isInteractive,
        focusable: isInteractive,
        child: avatar,
      );
    }

    // Add tooltip
    if (tooltip != null) {
      avatar = Tooltip(
        message: tooltip!,
        child: avatar,
      );
    }

    return avatar;
  }

  /// Resolves size properties for the current size variant.
  _AvatarSizeProperties _resolveSizeProperties(
    ShadcnAvatarTheme theme, 
    ShadcnAvatarSize size,
  ) {
    switch (size) {
      case ShadcnAvatarSize.small:
        return _AvatarSizeProperties(
          size: theme.smallSize ?? ShadcnTokens.avatarSizeSm,
          textStyle: theme.smallTextStyle ?? TextStyle(
            fontSize: ShadcnTokens.fontSizeSm,
            fontWeight: ShadcnTokens.fontWeightMedium,
          ),
          iconSize: theme.smallIconSize ?? ShadcnTokens.iconSizeSm,
        );

      case ShadcnAvatarSize.medium:
        return _AvatarSizeProperties(
          size: theme.mediumSize ?? ShadcnTokens.avatarSizeMd,
          textStyle: theme.mediumTextStyle ?? TextStyle(
            fontSize: ShadcnTokens.fontSizeMd,
            fontWeight: ShadcnTokens.fontWeightMedium,
          ),
          iconSize: theme.mediumIconSize ?? ShadcnTokens.iconSizeMd,
        );

      case ShadcnAvatarSize.large:
        return _AvatarSizeProperties(
          size: theme.largeSize ?? ShadcnTokens.avatarSizeLg,
          textStyle: theme.largeTextStyle ?? TextStyle(
            fontSize: ShadcnTokens.fontSizeLg,
            fontWeight: ShadcnTokens.fontWeightMedium,
          ),
          iconSize: theme.largeIconSize ?? ShadcnTokens.iconSizeLg,
        );
    }
  }

  /// Builds the main content of the avatar.
  Widget _buildAvatarContent(
    BuildContext context,
    ShadcnAvatarTheme theme,
    _AvatarSizeProperties sizeProps,
    Color foregroundColor,
  ) {
    // Try to display image first
    if (imageProvider != null) {
      return _buildImageContent(theme, sizeProps, foregroundColor);
    }

    // Then try initials
    if (initials != null && initials!.isNotEmpty) {
      return _buildInitialsContent(sizeProps, foregroundColor);
    }

    // Finally fallback to icon
    return _buildIconContent(theme, sizeProps, foregroundColor);
  }

  /// Builds image content with error handling.
  Widget _buildImageContent(
    ShadcnAvatarTheme theme,
    _AvatarSizeProperties sizeProps,
    Color foregroundColor,
  ) {
    return Image(
      image: imageProvider!,
      fit: BoxFit.cover,
      width: sizeProps.size,
      height: sizeProps.size,
      errorBuilder: (context, error, stackTrace) {
        // Call error callback if provided
        onImageError?.call();
        
        // Fallback to initials or icon
        if (initials != null && initials!.isNotEmpty) {
          return _buildInitialsContent(sizeProps, foregroundColor);
        } else {
          return _buildIconContent(theme, sizeProps, foregroundColor);
        }
      },
      loadingBuilder: showLoadingState 
        ? (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Center(
              child: SizedBox(
                width: sizeProps.iconSize,
                height: sizeProps.iconSize,
                child: CircularProgressIndicator(
                  strokeWidth: 2.0,
                  valueColor: AlwaysStoppedAnimation<Color>(foregroundColor),
                  value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded / 
                      loadingProgress.expectedTotalBytes!
                    : null,
                ),
              ),
            );
          }
        : null,
    );
  }

  /// Builds initials content.
  Widget _buildInitialsContent(
    _AvatarSizeProperties sizeProps,
    Color foregroundColor,
  ) {
    // Take first two characters and uppercase them
    final displayInitials = initials!.length >= 2 
      ? initials!.substring(0, 2).toUpperCase()
      : initials!.toUpperCase();

    return Center(
      child: Text(
        displayInitials,
        style: sizeProps.textStyle.copyWith(color: foregroundColor),
        textAlign: TextAlign.center,
      ),
    );
  }

  /// Builds icon content.
  Widget _buildIconContent(
    ShadcnAvatarTheme theme,
    _AvatarSizeProperties sizeProps,
    Color foregroundColor,
  ) {
    final iconWidget = fallbackIcon ?? Icon(
      theme.fallbackIcon ?? Icons.person,
      size: sizeProps.iconSize,
      color: foregroundColor,
    );

    return Center(child: iconWidget);
  }

  /// Generates semantic label for accessibility.
  String _generateSemanticLabel() {
    if (semanticLabel != null) return semanticLabel!;
    
    if (initials != null && initials!.isNotEmpty) {
      return 'Avatar for $initials';
    }
    
    return 'Avatar';
  }
}

/// Helper class to hold resolved avatar size properties.
class _AvatarSizeProperties {
  final double size;
  final TextStyle textStyle;
  final double iconSize;

  const _AvatarSizeProperties({
    required this.size,
    required this.textStyle,
    required this.iconSize,
  });
}