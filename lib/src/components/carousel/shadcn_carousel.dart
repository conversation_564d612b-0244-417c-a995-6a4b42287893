import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_carousel_theme.dart';
import '../../utils/theme_resolver.dart';

/// A carousel component that displays items in a scrollable container with indicators.
/// 
/// This component provides swipe gesture support, navigation buttons, and indicators
/// for displaying multiple items in a scrollable carousel format.
class ShadcnCarousel extends ShadcnComponent {
  /// The list of widgets to display in the carousel
  final List<Widget> items;
  
  /// The initial page index
  final int initialPage;
  
  /// Callback when the page changes
  final ValueChanged<int>? onPageChanged;
  
  /// Whether to show navigation buttons
  final bool showNavigationButtons;
  
  /// Whether to show page indicators
  final bool showIndicators;
  
  /// Whether the carousel should automatically advance
  final bool autoPlay;
  
  /// Duration for auto-play intervals
  final Duration autoPlayInterval;
  
  /// Whether the carousel should loop infinitely
  final bool enableInfiniteScroll;
  
  /// Height of the carousel
  final double? height;
  
  /// Custom controller for the page view
  final PageController? controller;
  
  /// Scroll direction for the carousel
  final Axis scrollDirection;
  
  /// Custom theme for this carousel
  final ShadcnCarouselTheme? theme;

  const ShadcnCarousel({
    super.key,
    required this.items,
    this.initialPage = 0,
    this.onPageChanged,
    this.showNavigationButtons = true,
    this.showIndicators = true,
    this.autoPlay = false,
    this.autoPlayInterval = const Duration(seconds: 3),
    this.enableInfiniteScroll = false,
    this.height,
    this.controller,
    this.scrollDirection = Axis.horizontal,
    this.theme,
  }) : assert(items.length > 0, 'Carousel must have at least one item');

  @override
  Widget buildWithTheme(BuildContext context, ThemeData themeData) {
    if (items.isEmpty) {
      return const SizedBox.shrink();
    }

    final carouselTheme = theme ?? 
        themeData.extension<ShadcnCarouselTheme>() ?? 
        ShadcnCarouselTheme.defaultTheme(themeData.colorScheme);

    return _CarouselWidget(
      items: items,
      initialPage: initialPage,
      onPageChanged: onPageChanged,
      showNavigationButtons: showNavigationButtons,
      showIndicators: showIndicators,
      autoPlay: autoPlay,
      autoPlayInterval: autoPlayInterval,
      enableInfiniteScroll: enableInfiniteScroll,
      height: height,
      controller: controller,
      scrollDirection: scrollDirection,
      theme: carouselTheme,
    );
  }
}

class _CarouselWidget extends StatefulWidget {
  final List<Widget> items;
  final int initialPage;
  final ValueChanged<int>? onPageChanged;
  final bool showNavigationButtons;
  final bool showIndicators;
  final bool autoPlay;
  final Duration autoPlayInterval;
  final bool enableInfiniteScroll;
  final double? height;
  final PageController? controller;
  final Axis scrollDirection;
  final ShadcnCarouselTheme theme;

  const _CarouselWidget({
    required this.items,
    required this.initialPage,
    this.onPageChanged,
    required this.showNavigationButtons,
    required this.showIndicators,
    required this.autoPlay,
    required this.autoPlayInterval,
    required this.enableInfiniteScroll,
    this.height,
    this.controller,
    required this.scrollDirection,
    required this.theme,
  });

  @override
  State<_CarouselWidget> createState() => _CarouselWidgetState();
}

class _CarouselWidgetState extends State<_CarouselWidget>
    with TickerProviderStateMixin {
  late PageController _pageController;
  int _currentIndex = 0;
  Timer? _autoPlayTimer;
  bool _userInteracting = false;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialPage;
    _pageController = widget.controller ?? 
        PageController(initialPage: widget.initialPage);
    
    if (widget.autoPlay) {
      _startAutoPlay();
    }
  }

  @override
  void dispose() {
    _autoPlayTimer?.cancel();
    if (widget.controller == null) {
      _pageController.dispose();
    }
    super.dispose();
  }

  void _startAutoPlay() {
    _autoPlayTimer = Timer.periodic(widget.autoPlayInterval, (timer) {
      if (!_userInteracting && mounted) {
        _nextPage();
      }
    });
  }

  void _stopAutoPlay() {
    _autoPlayTimer?.cancel();
  }

  void _nextPage() {
    if (!mounted) return;
    
    if (widget.enableInfiniteScroll || _currentIndex < widget.items.length - 1) {
      int nextPage = (_currentIndex + 1) % widget.items.length;
      _pageController.animateToPage(
        nextPage,
        duration: widget.theme.animationDuration ?? const Duration(milliseconds: 300),
        curve: widget.theme.animationCurve ?? Curves.easeInOut,
      );
    }
  }

  void _previousPage() {
    if (!mounted) return;
    
    if (widget.enableInfiniteScroll || _currentIndex > 0) {
      int prevPage = _currentIndex == 0 
          ? widget.items.length - 1 
          : _currentIndex - 1;
      _pageController.animateToPage(
        prevPage,
        duration: widget.theme.animationDuration ?? const Duration(milliseconds: 300),
        curve: widget.theme.animationCurve ?? Curves.easeInOut,
      );
    }
  }

  void _onPageChanged(int index) {
    if (mounted) {
      setState(() {
        _currentIndex = index;
      });
      widget.onPageChanged?.call(index);
    }
  }

  Widget _buildNavigationButton({
    required IconData icon,
    required VoidCallback? onPressed,
  }) {
    final isEnabled = onPressed != null;
    
    return MouseRegion(
      cursor: isEnabled ? SystemMouseCursors.click : SystemMouseCursors.forbidden,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: widget.theme.navigationButtonSize ?? 40.0,
        height: widget.theme.navigationButtonSize ?? 40.0,
        decoration: BoxDecoration(
          color: widget.theme.navigationButtonBackground,
          borderRadius: widget.theme.navigationButtonBorderRadius,
          boxShadow: widget.theme.navigationButtonShadow,
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onPressed,
            borderRadius: widget.theme.navigationButtonBorderRadius,
            hoverColor: widget.theme.navigationButtonHover,
            child: Icon(
              icon,
              color: isEnabled 
                  ? widget.theme.navigationButtonForeground 
                  : widget.theme.navigationButtonForeground?.withOpacity(0.4),
              size: 20,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIndicators() {
    if (!widget.showIndicators || widget.items.length <= 1) {
      return const SizedBox.shrink();
    }

    return Container(
      height: widget.theme.indicatorContainerHeight ?? 48.0,
      padding: widget.theme.indicatorContainerPadding,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          widget.items.length,
          (index) => GestureDetector(
            onTap: () {
              _pageController.animateToPage(
                index,
                duration: widget.theme.animationDuration ?? const Duration(milliseconds: 300),
                curve: widget.theme.animationCurve ?? Curves.easeInOut,
              );
            },
            child: AnimatedContainer(
              duration: widget.theme.animationDuration ?? const Duration(milliseconds: 300),
              margin: EdgeInsets.symmetric(
                horizontal: (widget.theme.indicatorSpacing ?? 8.0) / 2,
              ),
              width: widget.theme.indicatorSize ?? 8.0,
              height: widget.theme.indicatorSize ?? 8.0,
              decoration: BoxDecoration(
                color: index == _currentIndex
                    ? widget.theme.indicatorActiveColor
                    : widget.theme.indicatorInactiveColor,
                borderRadius: widget.theme.indicatorBorderRadius,
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final carouselHeight = widget.height ?? 200.0;
    
    return Column(
      children: [
        SizedBox(
          height: carouselHeight,
          child: Stack(
            children: [
              // Main PageView
              Listener(
                onPointerDown: (_) {
                  _userInteracting = true;
                  if (widget.autoPlay) _stopAutoPlay();
                },
                onPointerUp: (_) {
                  _userInteracting = false;
                  if (widget.autoPlay) _startAutoPlay();
                },
                child: PageView.builder(
                  controller: _pageController,
                  scrollDirection: widget.scrollDirection,
                  onPageChanged: _onPageChanged,
                  itemCount: widget.enableInfiniteScroll ? null : widget.items.length,
                  itemBuilder: (context, index) {
                    final itemIndex = widget.enableInfiniteScroll 
                        ? index % widget.items.length 
                        : index;
                    return Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: (widget.theme.itemSpacing ?? 16.0) / 2,
                      ),
                      child: widget.items[itemIndex],
                    );
                  },
                ),
              ),
              
              // Navigation Buttons
              if (widget.showNavigationButtons && widget.items.length > 1) ...[
                // Previous Button
                Positioned(
                  left: 16,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: _buildNavigationButton(
                      icon: Icons.chevron_left,
                      onPressed: widget.enableInfiniteScroll || _currentIndex > 0 
                          ? _previousPage 
                          : null,
                    ),
                  ),
                ),
                
                // Next Button
                Positioned(
                  right: 16,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: _buildNavigationButton(
                      icon: Icons.chevron_right,
                      onPressed: widget.enableInfiniteScroll || _currentIndex < widget.items.length - 1 
                          ? _nextPage 
                          : null,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
        
        // Indicators
        _buildIndicators(),
      ],
    );
  }
}

