import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_toggle_theme.dart';

/// A shadcn-styled toggle button component.
class ShadcnToggle extends ShadcnComponent with ShadcnComponentValidation {
  final String? text;
  final Widget? child;
  final Widget? icon;
  final bool isSelected;
  final ValueChanged<bool>? onChanged;
  final bool enabled;
  final String? tooltip;
  final String? semanticLabel;
  final bool excludeFromSemantics;

  const ShadcnToggle({
    super.key,
    this.text,
    this.child,
    this.icon,
    required this.isSelected,
    this.onChanged,
    this.enabled = true,
    this.tooltip,
    this.semanticLabel,
    this.excludeFromSemantics = false,
  }) : assert(text != null || child != null || icon != null,
         'At least one of text, child, or icon must be provided');

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    validateThemeProperties(context);
    validateAccessibility(
      semanticLabel: semanticLabel,
      tooltip: tooltip,
      excludeFromSemantics: excludeFromSemantics,
      componentName: 'ShadcnToggle',
    );
    
    final toggleTheme = resolveTheme<ShadcnToggleTheme>(
      context,
      ShadcnToggleTheme.defaultTheme,
    );

    // Determine colors based on state
    Color backgroundColor;
    Color foregroundColor;
    Color borderColor;
    
    if (!enabled) {
      backgroundColor = toggleTheme.disabledBackgroundColor ?? theme.colorScheme.onSurface.withOpacity(0.04);
      foregroundColor = toggleTheme.disabledForegroundColor ?? theme.colorScheme.onSurface.withOpacity(0.38);
      borderColor = toggleTheme.borderColor ?? theme.colorScheme.outline;
    } else if (isSelected) {
      backgroundColor = toggleTheme.selectedBackgroundColor ?? theme.colorScheme.primary;
      foregroundColor = toggleTheme.selectedForegroundColor ?? theme.colorScheme.onPrimary;
      borderColor = toggleTheme.selectedBorderColor ?? theme.colorScheme.primary;
    } else {
      backgroundColor = toggleTheme.backgroundColor ?? Colors.transparent;
      foregroundColor = toggleTheme.foregroundColor ?? theme.colorScheme.onSurface;
      borderColor = toggleTheme.borderColor ?? theme.colorScheme.outline;
    }

    Widget result = AnimatedContainer(
      duration: const Duration(milliseconds: 150),
      height: toggleTheme.height,
      padding: toggleTheme.padding,
      decoration: BoxDecoration(
        color: backgroundColor,
        border: Border.all(
          color: borderColor,
          width: toggleTheme.borderWidth ?? 1.0,
        ),
        borderRadius: toggleTheme.borderRadius,
      ),
      child: Material(
        type: MaterialType.transparency,
        child: InkWell(
          onTap: enabled ? () {
            HapticFeedback.selectionClick();
            onChanged?.call(!isSelected);
          } : null,
          borderRadius: toggleTheme.borderRadius,
          hoverColor: toggleTheme.hoverBackgroundColor,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (icon != null) ...[
                  IconTheme(
                    data: IconThemeData(
                      color: foregroundColor,
                      size: toggleTheme.iconSize,
                    ),
                    child: icon!,
                  ),
                  if (text != null || child != null)
                    SizedBox(width: toggleTheme.iconSpacing ?? 8),
                ],
                if (child != null)
                  child!
                else if (text != null)
                  Text(
                    text!,
                    style: (toggleTheme.textStyle ?? const TextStyle())
                        .copyWith(color: foregroundColor),
                  ),
              ],
            ),
          ),
        ),
      ),
    );

    // Add semantics
    if (!excludeFromSemantics) {
      result = Semantics(
        label: semanticLabel ?? text,
        button: true,
        toggled: isSelected,
        enabled: enabled,
        child: result,
      );
    }

    // Add tooltip
    if (tooltip != null) {
      result = Tooltip(
        message: tooltip!,
        child: result,
      );
    }

    return result;
  }
}