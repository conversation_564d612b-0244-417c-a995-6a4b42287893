import 'package:flutter/material.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_separator_theme.dart';
import '../../constants/shadcn_tokens.dart';

/// Orientation options for the separator.
enum ShadcnSeparatorOrientation {
  /// Horizontal separator (divides content vertically)
  horizontal,
  
  /// Vertical separator (divides content horizontally)
  vertical,
}

/// A shadcn-styled separator component for dividing content.
/// 
/// The [ShadcnSeparator] provides a themeable divider that can be oriented
/// horizontally or vertically. It integrates with the Material Design theme
/// system while providing shadcn-specific styling options.
/// 
/// The separator automatically adapts to the theme's color scheme and visual
/// density, ensuring consistent appearance across different screen densities
/// and theme configurations.
/// 
/// Example usage:
/// ```dart
/// // Horizontal separator (default)
/// ShadcnSeparator()
/// 
/// // Vertical separator with custom color
/// ShadcnSeparator(
///   orientation: ShadcnSeparatorOrientation.vertical,
///   color: Colors.blue,
/// )
/// 
/// // Custom thickness and margin
/// ShadcnSeparator(
///   thickness: 2.0,
///   margin: EdgeInsets.symmetric(vertical: 16),
/// )
/// ```
class ShadcnSeparator extends ShadcnComponent with ShadcnComponentValidation {
  /// The orientation of the separator.
  /// 
  /// Determines whether the separator runs horizontally (default) or
  /// vertically. This affects both the visual appearance and the touch
  /// target area.
  final ShadcnSeparatorOrientation orientation;
  
  /// Custom color for the separator line.
  /// 
  /// If null, uses the color from the theme. The color is resolved
  /// through the theme system with appropriate fallbacks.
  final Color? color;
  
  /// Custom thickness of the separator line.
  /// 
  /// If null, uses the thickness from the theme. The thickness is
  /// automatically adjusted for visual density while maintaining
  /// minimum visibility requirements.
  final double? thickness;
  
  /// Custom spacing around the separator.
  /// 
  /// If null, uses the margin from the theme. The margin is applied
  /// outside the separator's touch target area.
  final EdgeInsets? margin;
  
  /// Custom opacity for the separator.
  /// 
  /// If null, uses the opacity from the theme. Values are clamped
  /// between 0.0 and 1.0.
  final double? opacity;
  
  /// Custom height for horizontal separators.
  /// 
  /// This affects the touch target area, not the visual thickness.
  /// If null, uses the height from the theme.
  final double? height;
  
  /// Custom width for vertical separators.
  /// 
  /// This affects the touch target area, not the visual thickness.
  /// If null, uses the width from the theme.
  final double? width;
  
  /// Semantic label for accessibility.
  /// 
  /// Provides a description of the separator's purpose for screen readers.
  /// If null, a default semantic label is provided based on orientation.
  final String? semanticLabel;
  
  /// Whether to exclude this separator from the semantic tree.
  /// 
  /// Decorative separators that don't convey meaningful content structure
  /// can be excluded from accessibility tools.
  final bool excludeFromSemantics;
  
  /// Optional callback for tap interactions.
  /// 
  /// While separators are typically non-interactive, this allows for
  /// custom behavior like collapsing sections or triggering actions.
  final VoidCallback? onTap;
  
  /// Creates a shadcn separator.
  /// 
  /// The [orientation] determines whether the separator is horizontal
  /// or vertical. All other properties are optional and will fall back
  /// to theme values when not specified.
  const ShadcnSeparator({
    super.key,
    this.orientation = ShadcnSeparatorOrientation.horizontal,
    this.color,
    this.thickness,
    this.margin,
    this.opacity,
    this.height,
    this.width,
    this.semanticLabel,
    this.excludeFromSemantics = false,
    this.onTap,
  });
  
  /// Creates a horizontal separator.
  /// 
  /// This is a convenience constructor for creating horizontal separators
  /// with cleaner syntax.
  const ShadcnSeparator.horizontal({
    Key? key,
    Color? color,
    double? thickness,
    EdgeInsets? margin,
    double? opacity,
    double? height,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    VoidCallback? onTap,
  }) : this(
    key: key,
    orientation: ShadcnSeparatorOrientation.horizontal,
    color: color,
    thickness: thickness,
    margin: margin,
    opacity: opacity,
    height: height,
    width: null,
    semanticLabel: semanticLabel,
    excludeFromSemantics: excludeFromSemantics,
    onTap: onTap,
  );
  
  /// Creates a vertical separator.
  /// 
  /// This is a convenience constructor for creating vertical separators
  /// with cleaner syntax.
  const ShadcnSeparator.vertical({
    Key? key,
    Color? color,
    double? thickness,
    EdgeInsets? margin,
    double? opacity,
    double? width,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    VoidCallback? onTap,
  }) : this(
    key: key,
    orientation: ShadcnSeparatorOrientation.vertical,
    color: color,
    thickness: thickness,
    margin: margin,
    opacity: opacity,
    height: null,
    width: width,
    semanticLabel: semanticLabel,
    excludeFromSemantics: excludeFromSemantics,
    onTap: onTap,
  );

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    // Validate component configuration in debug mode
    assert(() {
      validateThemeProperties(context);
      validateRequiredProperties({
        'orientation': orientation,
      });
      validateAccessibility(
        semanticLabel: semanticLabel,
        excludeFromSemantics: excludeFromSemantics,
        componentName: 'ShadcnSeparator',
      );
      return true;
    }());

    // Resolve theme
    final separatorTheme = resolveTheme<ShadcnSeparatorTheme>(
      context,
      ShadcnSeparatorTheme.defaultTheme,
    );

    // Resolve all styling properties
    final resolvedColor = color ?? separatorTheme.getColor(context);
    final resolvedThickness = thickness ?? separatorTheme.getThickness(context);
    final resolvedMargin = margin ?? separatorTheme.getMargin(context);
    final resolvedOpacity = opacity ?? separatorTheme.getOpacity(context);
    final resolvedHeight = height ?? separatorTheme.getHeight(context);
    final resolvedWidth = width ?? separatorTheme.getWidth(context);
    
    // Build the separator widget
    Widget separator = _buildSeparatorContent(
      context,
      resolvedColor,
      resolvedThickness,
      resolvedHeight,
      resolvedWidth,
      resolvedOpacity,
    );

    // Apply margin if specified
    if (resolvedMargin != EdgeInsets.zero) {
      separator = Padding(
        padding: resolvedMargin,
        child: separator,
      );
    }

    // Add interaction handling if callback is provided
    if (onTap != null) {
      separator = GestureDetector(
        onTap: onTap,
        behavior: HitTestBehavior.opaque,
        child: separator,
      );
    }

    // Add semantics for accessibility
    if (!excludeFromSemantics) {
      final defaultLabel = orientation == ShadcnSeparatorOrientation.horizontal
          ? 'Horizontal divider'
          : 'Vertical divider';
      
      separator = Semantics(
        label: semanticLabel ?? defaultLabel,
        child: separator,
      );
    }

    return separator;
  }

  /// Builds the core separator content based on orientation.
  /// 
  /// This method handles the actual visual construction of the separator
  /// line with proper sizing and positioning based on orientation.
  Widget _buildSeparatorContent(
    BuildContext context,
    Color color,
    double thickness,
    double height,
    double width,
    double opacity,
  ) {
    final effectiveColor = color.withOpacity(opacity);
    
    switch (orientation) {
      case ShadcnSeparatorOrientation.horizontal:
        return _buildHorizontalSeparator(
          context,
          effectiveColor,
          thickness,
          height,
        );
      case ShadcnSeparatorOrientation.vertical:
        return _buildVerticalSeparator(
          context,
          effectiveColor,
          thickness,
          width,
        );
    }
  }

  /// Builds a horizontal separator.
  /// 
  /// Creates a horizontal line that spans the available width with
  /// the specified thickness and touch target height.
  Widget _buildHorizontalSeparator(
    BuildContext context,
    Color color,
    double thickness,
    double height,
  ) {
    return SizedBox(
      height: height,
      child: Center(
        child: Container(
          height: thickness,
          color: color,
        ),
      ),
    );
  }

  /// Builds a vertical separator.
  /// 
  /// Creates a vertical line that spans the available height with
  /// the specified thickness and touch target width.
  Widget _buildVerticalSeparator(
    BuildContext context,
    Color color,
    double thickness,
    double width,
  ) {
    return SizedBox(
      width: width,
      child: Center(
        child: Container(
          width: thickness,
          color: color,
        ),
      ),
    );
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ShadcnSeparator('
        'orientation: $orientation, '
        'color: $color, '
        'thickness: $thickness, '
        'margin: $margin, '
        'opacity: $opacity, '
        'height: $height, '
        'width: $width, '
        'excludeFromSemantics: $excludeFromSemantics'
        ')';
  }
}

/// Helper class for creating commonly used separator configurations.
/// 
/// Provides pre-configured separator widgets for common use cases,
/// reducing boilerplate and ensuring consistent styling patterns.
class ShadcnSeparators {
  ShadcnSeparators._();

  /// Creates a thin horizontal separator for subtle content division.
  /// 
  /// Uses minimal thickness and reduced opacity for subtle visual separation.
  static Widget thin({
    Key? key,
    Color? color,
    EdgeInsets? margin,
    String? semanticLabel,
  }) {
    return ShadcnSeparator.horizontal(
      key: key,
      color: color,
      thickness: 0.5,
      opacity: 0.6,
      margin: margin ?? EdgeInsets.zero,
      semanticLabel: semanticLabel,
    );
  }

  /// Creates a thick horizontal separator for prominent content division.
  /// 
  /// Uses increased thickness for more prominent visual separation.
  static Widget thick({
    Key? key,
    Color? color,
    EdgeInsets? margin,
    String? semanticLabel,
  }) {
    return ShadcnSeparator.horizontal(
      key: key,
      color: color,
      thickness: ShadcnTokens.borderWidthThick,
      margin: margin ?? EdgeInsets.zero,
      semanticLabel: semanticLabel,
    );
  }

  /// Creates a spaced horizontal separator with vertical margin.
  /// 
  /// Includes vertical spacing above and below the separator for
  /// better content separation.
  static Widget spaced({
    Key? key,
    Color? color,
    double spacing = ShadcnTokens.spacing4,
    String? semanticLabel,
  }) {
    return ShadcnSeparator.horizontal(
      key: key,
      color: color,
      margin: EdgeInsets.symmetric(vertical: spacing),
      semanticLabel: semanticLabel,
    );
  }

  /// Creates a vertical separator for toolbar or menu items.
  /// 
  /// Optimized height and spacing for use in toolbars and menus.
  static Widget toolbar({
    Key? key,
    Color? color,
    double? height,
    EdgeInsets? margin,
    String? semanticLabel,
  }) {
    return ShadcnSeparator.vertical(
      key: key,
      color: color,
      width: height ?? ShadcnTokens.spacing6,
      margin: margin ?? EdgeInsets.symmetric(horizontal: ShadcnTokens.spacing2),
      semanticLabel: semanticLabel ?? 'Toolbar divider',
    );
  }

  /// Creates a list item separator.
  /// 
  /// Optimized for separating list items with appropriate insets
  /// and subtle styling.
  static Widget listItem({
    Key? key,
    Color? color,
    double indent = ShadcnTokens.spacing4,
    String? semanticLabel,
  }) {
    return ShadcnSeparator.horizontal(
      key: key,
      color: color,
      opacity: 0.5,
      margin: EdgeInsets.only(left: indent),
      semanticLabel: semanticLabel ?? 'List item divider',
    );
  }

  /// Creates a section separator.
  /// 
  /// Used for dividing major sections of content with prominent
  /// styling and spacing.
  static Widget section({
    Key? key,
    Color? color,
    double spacing = ShadcnTokens.spacing8,
    String? semanticLabel,
  }) {
    return ShadcnSeparator.horizontal(
      key: key,
      color: color,
      thickness: ShadcnTokens.borderWidthThick,
      margin: EdgeInsets.symmetric(vertical: spacing),
      semanticLabel: semanticLabel ?? 'Section divider',
    );
  }
}

/// Extension methods for convenient separator usage.
/// 
/// Provides extension methods on common Flutter widgets to add
/// separators with minimal syntax.
extension ShadcnSeparatorExtensions on Widget {
  /// Adds a horizontal separator below this widget.
  /// 
  /// Creates a column with this widget and a separator below it.
  Widget withSeparatorBelow({
    Color? color,
    double? thickness,
    EdgeInsets? margin,
    double? spacing,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        this,
        if (spacing != null) SizedBox(height: spacing),
        ShadcnSeparator.horizontal(
          color: color,
          thickness: thickness,
          margin: margin,
        ),
      ],
    );
  }

  /// Adds a horizontal separator above this widget.
  /// 
  /// Creates a column with a separator and this widget below it.
  Widget withSeparatorAbove({
    Color? color,
    double? thickness,
    EdgeInsets? margin,
    double? spacing,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ShadcnSeparator.horizontal(
          color: color,
          thickness: thickness,
          margin: margin,
        ),
        if (spacing != null) SizedBox(height: spacing),
        this,
      ],
    );
  }

  /// Adds a vertical separator to the right of this widget.
  /// 
  /// Creates a row with this widget and a separator to the right.
  Widget withSeparatorRight({
    Color? color,
    double? thickness,
    EdgeInsets? margin,
    double? spacing,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        this,
        if (spacing != null) SizedBox(width: spacing),
        ShadcnSeparator.vertical(
          color: color,
          thickness: thickness,
          margin: margin,
        ),
      ],
    );
  }

  /// Adds a vertical separator to the left of this widget.
  /// 
  /// Creates a row with a separator and this widget to the right.
  Widget withSeparatorLeft({
    Color? color,
    double? thickness,
    EdgeInsets? margin,
    double? spacing,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        ShadcnSeparator.vertical(
          color: color,
          thickness: thickness,
          margin: margin,
        ),
        if (spacing != null) SizedBox(width: spacing),
        this,
      ],
    );
  }
}