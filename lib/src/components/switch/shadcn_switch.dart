import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_switch_theme.dart';
import '../../constants/shadcn_tokens.dart';

/// A shadcn-styled switch component with comprehensive theming support.
/// 
/// This switch component provides toggle functionality with on/off states
/// while maintaining full Material Design integration. It supports all interactive
/// states (hover, pressed, focused, disabled) with theme-aware styling.
/// 
/// The switch automatically derives its styling from the current theme context,
/// with fallback to Material Design values. All styling is customizable through
/// the [ShadcnSwitchTheme] extension.
/// 
/// Example usage:
/// ```dart
/// ShadcnSwitch(
///   value: isEnabled,
///   onChanged: (value) => setState(() => isEnabled = value),
///   label: 'Enable notifications',
/// )
/// ```
class ShadcnSwitch extends ShadcnComponent with ShadcnComponentValidation {
  /// The current value of the switch (true = on, false = off).
  final bool value;
  
  /// Callback function called when the switch state changes.
  final ValueChanged<bool>? onChanged;
  
  /// The text label displayed next to the switch.
  final String? label;
  
  /// The child widget to display instead of [label]. Takes precedence over [label].
  final Widget? child;
  
  /// Helper text displayed below the switch.
  final String? helperText;
  
  /// Error text displayed when validation fails.
  final String? errorText;
  
  /// The size of the switch.
  final ShadcnSwitchSize size;
  
  /// Custom padding around the switch area.
  final EdgeInsets? padding;
  
  /// Focus node for keyboard navigation.
  final FocusNode? focusNode;
  
  /// Whether the switch should autofocus when first built.
  final bool autofocus;
  
  /// Tooltip message displayed on hover.
  final String? tooltip;
  
  /// Semantic label for accessibility.
  final String? semanticLabel;
  
  /// Whether to exclude this switch from semantics tree.
  final bool excludeFromSemantics;
  
  /// Custom mouse cursor when hovering over the switch.
  final MouseCursor? mouseCursor;
  
  /// Whether to enable haptic feedback on toggle.
  final bool enableFeedback;
  
  /// Animation duration for state transitions.
  final Duration? animationDuration;
  
  /// Whether the label should be clickable to toggle the switch.
  final bool labelClickable;
  
  /// Form field validator function.
  final FormFieldValidator<bool>? validator;
  
  /// Whether this switch is part of a form and should save its value.
  final bool autovalidate;
  
  /// Initial value for form field.
  final bool? initialValue;
  
  /// Form field key for accessing the field state.
  final GlobalKey<FormFieldState<bool>>? formFieldKey;
  
  /// Custom thumb decoration.
  final Decoration? thumbDecoration;
  
  /// Custom track decoration.
  final Decoration? trackDecoration;

  const ShadcnSwitch({
    super.key,
    required this.value,
    required this.onChanged,
    this.label,
    this.child,
    this.helperText,
    this.errorText,
    this.size = ShadcnSwitchSize.medium,
    this.padding,
    this.focusNode,
    this.autofocus = false,
    this.tooltip,
    this.semanticLabel,
    this.excludeFromSemantics = false,
    this.mouseCursor,
    this.enableFeedback = true,
    this.animationDuration,
    this.labelClickable = true,
    this.validator,
    this.autovalidate = false,
    this.initialValue,
    this.formFieldKey,
    this.thumbDecoration,
    this.trackDecoration,
  });

  /// Factory constructor for form field switch with validation support.
  factory ShadcnSwitch.formField({
    Key? key,
    bool? initialValue,
    required ValueChanged<bool>? onChanged,
    FormFieldValidator<bool>? validator,
    bool autovalidate = false,
    GlobalKey<FormFieldState<bool>>? formFieldKey,
    String? label,
    Widget? child,
    String? helperText,
    ShadcnSwitchSize size = ShadcnSwitchSize.medium,
    String? tooltip,
  }) {
    return ShadcnSwitch(
      key: key,
      value: initialValue ?? false,
      onChanged: onChanged,
      validator: validator,
      autovalidate: autovalidate,
      formFieldKey: formFieldKey,
      initialValue: initialValue,
      label: label,
      child: child,
      helperText: helperText,
      size: size,
      tooltip: tooltip,
    );
  }

  /// Whether the switch is enabled (has onChanged callback).
  bool get enabled => onChanged != null;

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    // Validate theme context and properties
    validateThemeProperties(context);
    validateVariant(size, ShadcnSwitchSize.values, 'ShadcnSwitch');
    validateCallbacks({'onChanged': onChanged}, 'ShadcnSwitch');
    validateAccessibility(
      semanticLabel: semanticLabel,
      tooltip: tooltip,
      excludeFromSemantics: excludeFromSemantics,
      componentName: 'ShadcnSwitch',
    );

    // Resolve switch theme
    final switchTheme = resolveTheme<ShadcnSwitchTheme>(
      context,
      ShadcnSwitchTheme.defaultTheme,
    );

    // If this is a form field, wrap with FormField
    if (validator != null || formFieldKey != null) {
      return _buildFormField(context, theme, switchTheme);
    }

    return _buildSwitch(context, theme, switchTheme);
  }

  /// Builds the form field wrapper if validation is needed.
  Widget _buildFormField(BuildContext context, ThemeData theme, ShadcnSwitchTheme switchTheme) {
    return FormField<bool>(
      key: formFieldKey,
      initialValue: initialValue ?? value,
      validator: validator,
      autovalidateMode: autovalidate ? AutovalidateMode.always : AutovalidateMode.disabled,
      builder: (FormFieldState<bool> field) {
        final hasError = field.hasError;
        final currentValue = field.value ?? false;
        
        return _buildSwitch(
          context,
          theme,
          switchTheme,
          formFieldState: field,
          hasError: hasError,
          currentValue: currentValue,
        );
      },
    );
  }

  /// Builds the main switch widget.
  Widget _buildSwitch(
    BuildContext context,
    ThemeData theme,
    ShadcnSwitchTheme switchTheme, {
    FormFieldState<bool>? formFieldState,
    bool hasError = false,
    bool? currentValue,
  }) {
    final effectiveValue = currentValue ?? value;
    final effectiveErrorText = hasError ? (formFieldState?.errorText ?? errorText) : errorText;

    // Resolve colors based on current state
    final colors = _resolveColors(switchTheme, theme.colorScheme, effectiveValue, hasError);
    final sizeProperties = _resolveSizeProperties(switchTheme, size);

    // Build the switch widget
    final switchWidget = _buildSwitchWidget(
      context,
      theme,
      switchTheme,
      colors,
      sizeProperties,
      effectiveValue,
      formFieldState,
    );

    // Build the complete widget with label and helper text
    return _buildCompleteWidget(
      context,
      theme,
      switchTheme,
      switchWidget,
      effectiveErrorText,
      hasError,
    );
  }

  /// Builds the main switch interactive widget.
  Widget _buildSwitchWidget(
    BuildContext context,
    ThemeData theme,
    ShadcnSwitchTheme switchTheme,
    _SwitchColors colors,
    _SwitchSizeProperties sizeProps,
    bool currentValue,
    FormFieldState<bool>? formFieldState,
  ) {
    final effectivePadding = padding ?? switchTheme.padding ?? EdgeInsets.all(ShadcnTokens.spacing1);
    final effectiveAnimationDuration = animationDuration ?? switchTheme.animationDuration ?? ShadcnTokens.durationFast;
    final effectiveTrackBorderRadius = switchTheme.trackBorderRadius ?? BorderRadius.circular(100);

    return GestureDetector(
      onTap: enabled ? () => _handleTap(context, currentValue, formFieldState) : null,
      child: MouseRegion(
        cursor: mouseCursor ?? (enabled ? SystemMouseCursors.click : SystemMouseCursors.basic),
        child: AnimatedContainer(
          duration: effectiveAnimationDuration,
          curve: switchTheme.animationCurve ?? Curves.easeInOut,
          width: sizeProps.trackWidth,
          height: sizeProps.trackHeight,
          padding: effectivePadding,
          decoration: trackDecoration ?? BoxDecoration(
            color: colors.trackColor,
            borderRadius: effectiveTrackBorderRadius,
            border: Border.all(
              color: colors.borderColor,
              width: switchTheme.borderWidth ?? ShadcnTokens.borderWidth,
            ),
            boxShadow: switchTheme.trackShadow,
          ),
          child: Stack(
            children: [
              AnimatedPositioned(
                duration: effectiveAnimationDuration,
                curve: switchTheme.animationCurve ?? Curves.easeInOut,
                left: currentValue 
                  ? sizeProps.trackWidth - sizeProps.thumbSize - effectivePadding.horizontal - (switchTheme.borderWidth ?? ShadcnTokens.borderWidth) * 2
                  : 0,
                top: (sizeProps.trackHeight - sizeProps.thumbSize - effectivePadding.vertical - (switchTheme.borderWidth ?? ShadcnTokens.borderWidth) * 2) / 2,
                child: Container(
                  width: sizeProps.thumbSize,
                  height: sizeProps.thumbSize,
                  decoration: thumbDecoration ?? BoxDecoration(
                    color: colors.thumbColor,
                    shape: BoxShape.circle,
                    boxShadow: switchTheme.thumbShadow,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the complete widget with label and helper text.
  Widget _buildCompleteWidget(
    BuildContext context,
    ThemeData theme,
    ShadcnSwitchTheme switchTheme,
    Widget switchWidget,
    String? errorText,
    bool hasError,
  ) {
    final List<Widget> children = [];

    // Build main row with switch and label
    final List<Widget> rowChildren = [switchWidget];

    if (child != null || (label != null && label!.isNotEmpty)) {
      rowChildren.add(SizedBox(width: switchTheme.labelSpacing ?? ShadcnTokens.spacing2));
      
      Widget labelWidget = child ?? Text(
        label!,
        style: resolveTextStyle(
          context,
          switchTheme.labelStyle,
          (textTheme) => textTheme.bodyMedium ?? const TextStyle(),
        ),
      );

      if (labelClickable && enabled) {
        labelWidget = GestureDetector(
          onTap: () => _handleTap(context, value, null),
          child: labelWidget,
        );
      }

      rowChildren.add(Expanded(child: labelWidget));
    }

    children.add(
      Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: rowChildren,
      ),
    );

    // Add helper text
    if (helperText != null && helperText!.isNotEmpty && !hasError) {
      children.add(const SizedBox(height: ShadcnTokens.spacing1));
      children.add(
        Padding(
          padding: EdgeInsets.only(
            left: (switchTheme.resolveTrackWidthForVariant(size) + (switchTheme.labelSpacing ?? ShadcnTokens.spacing2)),
          ),
          child: Text(
            helperText!,
            style: resolveTextStyle(
              context,
              switchTheme.helperStyle,
              (textTheme) => textTheme.bodySmall ?? const TextStyle(),
            ),
          ),
        ),
      );
    }

    // Add error text
    if (errorText != null && errorText!.isNotEmpty) {
      children.add(const SizedBox(height: ShadcnTokens.spacing1));
      children.add(
        Padding(
          padding: EdgeInsets.only(
            left: (switchTheme.resolveTrackWidthForVariant(size) + (switchTheme.labelSpacing ?? ShadcnTokens.spacing2)),
          ),
          child: Text(
            errorText!,
            style: resolveTextStyle(
              context,
              switchTheme.errorStyle,
              (textTheme) => textTheme.bodySmall?.copyWith(color: theme.colorScheme.error) ?? TextStyle(color: theme.colorScheme.error),
            ),
          ),
        ),
      );
    }

    Widget result = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: children,
    );

    // Add focus support
    if (focusNode != null) {
      result = Focus(
        focusNode: focusNode,
        autofocus: autofocus,
        child: result,
      );
    }

    // Add semantics
    if (!excludeFromSemantics) {
      result = Semantics(
        label: semanticLabel ?? label,
        toggled: value,
        enabled: enabled,
        focusable: enabled,
        child: result,
      );
    }

    // Add tooltip
    if (tooltip != null) {
      result = Tooltip(
        message: tooltip!,
        child: result,
      );
    }

    return result;
  }

  /// Resolves colors for the current state.
  _SwitchColors _resolveColors(
    ShadcnSwitchTheme theme,
    ColorScheme colorScheme,
    bool value,
    bool hasError,
  ) {
    if (!enabled) {
      // Disabled state
      if (value) {
        return _SwitchColors(
          trackColor: theme.disabledOnTrackColor ?? colorScheme.onSurface.withOpacity(0.12),
          thumbColor: theme.disabledOnThumbColor ?? colorScheme.surface,
          borderColor: theme.disabledOnBorderColor ?? colorScheme.onSurface.withOpacity(0.12),
        );
      } else {
        return _SwitchColors(
          trackColor: theme.disabledOffTrackColor ?? colorScheme.onSurface.withOpacity(0.12),
          thumbColor: theme.disabledOffThumbColor ?? colorScheme.surface,
          borderColor: theme.disabledOffBorderColor ?? colorScheme.onSurface.withOpacity(0.12),
        );
      }
    } else if (hasError) {
      // Error state
      return _SwitchColors(
        trackColor: value ? colorScheme.error : (theme.offTrackColor ?? colorScheme.onSurface.withOpacity(0.12)),
        thumbColor: theme.onThumbColor ?? colorScheme.onError,
        borderColor: colorScheme.error,
      );
    } else if (value) {
      // On state
      return _SwitchColors(
        trackColor: theme.onTrackColor ?? colorScheme.primary,
        thumbColor: theme.onThumbColor ?? colorScheme.onPrimary,
        borderColor: theme.onBorderColor ?? colorScheme.primary,
      );
    } else {
      // Off state
      return _SwitchColors(
        trackColor: theme.offTrackColor ?? colorScheme.onSurface.withOpacity(0.12),
        thumbColor: theme.offThumbColor ?? colorScheme.surface,
        borderColor: theme.offBorderColor ?? colorScheme.outline,
      );
    }
  }

  /// Resolves size properties for the current size.
  _SwitchSizeProperties _resolveSizeProperties(ShadcnSwitchTheme theme, ShadcnSwitchSize size) {
    return _SwitchSizeProperties(
      trackWidth: theme.resolveTrackWidthForVariant(size),
      trackHeight: theme.resolveTrackHeightForVariant(size),
      thumbSize: theme.resolveThumbSizeForVariant(size),
    );
  }

  /// Handles switch toggle with haptic feedback.
  void _handleTap(BuildContext context, bool currentValue, FormFieldState<bool>? formFieldState) {
    if (enableFeedback) {
      HapticFeedback.lightImpact();
    }

    final newValue = !currentValue;

    // Update form field if present
    formFieldState?.didChange(newValue);
    
    // Call the onChanged callback
    onChanged?.call(newValue);
  }
}

/// Helper class to hold resolved switch colors for different states.
class _SwitchColors {
  final Color trackColor;
  final Color thumbColor;
  final Color borderColor;

  const _SwitchColors({
    required this.trackColor,
    required this.thumbColor,
    required this.borderColor,
  });
}

/// Helper class to hold resolved switch size properties.
class _SwitchSizeProperties {
  final double trackWidth;
  final double trackHeight;
  final double thumbSize;

  const _SwitchSizeProperties({
    required this.trackWidth,
    required this.trackHeight,
    required this.thumbSize,
  });
}