import 'package:flutter/material.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_menubar_theme.dart';
import '../../constants/shadcn_tokens.dart';

/// A shadcn-styled menubar component.
class ShadcnMenubar extends ShadcnComponent with ShadcnComponentValidation {
  final List<ShadcnMenubarItem> items;
  final ValueChanged<int>? onSelectionChanged;
  final int selectedIndex;
  final bool enabled;

  const ShadcnMenubar({
    super.key,
    required this.items,
    this.onSelectionChanged,
    this.selectedIndex = -1,
    this.enabled = true,
  });

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    validateThemeProperties(context);
    
    final menubarTheme = resolveTheme<ShadcnMenubarTheme>(
      context,
      ShadcnMenubarTheme.defaultTheme,
    );

    return Container(
      height: menubarTheme.height,
      padding: menubarTheme.padding,
      decoration: BoxDecoration(
        color: menubarTheme.backgroundColor,
        borderRadius: menubarTheme.borderRadius,
        border: Border.all(
          color: menubarTheme.borderColor ?? theme.colorScheme.outline,
          width: menubarTheme.borderWidth ?? 1.0,
        ),
      ),
      child: Row(
        children: items.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          final isSelected = index == selectedIndex;
          
          return Padding(
            padding: EdgeInsets.only(
              right: index < items.length - 1 ? (menubarTheme.itemSpacing ?? 0) : 0,
            ),
            child: _ShadcnMenubarItemWidget(
              item: item,
              isSelected: isSelected,
              enabled: enabled && item.enabled,
              theme: menubarTheme,
              materialTheme: theme,
              onTap: () => onSelectionChanged?.call(index),
            ),
          );
        }).toList(),
      ),
    );
  }
}

class ShadcnMenubarItem {
  final String text;
  final Widget? icon;
  final VoidCallback? onTap;
  final bool enabled;
  final String? tooltip;

  const ShadcnMenubarItem({
    required this.text,
    this.icon,
    this.onTap,
    this.enabled = true,
    this.tooltip,
  });
}

class _ShadcnMenubarItemWidget extends StatelessWidget {
  final ShadcnMenubarItem item;
  final bool isSelected;
  final bool enabled;
  final ShadcnMenubarTheme theme;
  final ThemeData materialTheme;
  final VoidCallback? onTap;

  const _ShadcnMenubarItemWidget({
    required this.item,
    required this.isSelected,
    required this.enabled,
    required this.theme,
    required this.materialTheme,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final backgroundColor = isSelected 
        ? (theme.selectedColor ?? materialTheme.colorScheme.primary)
        : Colors.transparent;
    
    final foregroundColor = isSelected
        ? (theme.selectedForegroundColor ?? materialTheme.colorScheme.onPrimary)
        : (theme.foregroundColor ?? materialTheme.colorScheme.onSurface);

    Widget result = AnimatedContainer(
      duration: theme.animationDuration ?? ShadcnTokens.durationFast,
      curve: theme.animationCurve ?? Curves.easeInOut,
      padding: theme.itemPadding,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: theme.borderRadius,
      ),
      child: Material(
        type: MaterialType.transparency,
        child: InkWell(
          onTap: enabled ? (onTap ?? item.onTap) : null,
          borderRadius: theme.borderRadius,
          hoverColor: theme.hoverColor,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (item.icon != null) ...[
                  IconTheme(
                    data: IconThemeData(color: foregroundColor),
                    child: item.icon!,
                  ),
                  const SizedBox(width: 8),
                ],
                Text(
                  item.text,
                  style: (theme.textStyle ?? const TextStyle())
                      .copyWith(color: foregroundColor),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    if (item.tooltip != null) {
      result = Tooltip(
        message: item.tooltip!,
        child: result,
      );
    }

    return result;
  }
}