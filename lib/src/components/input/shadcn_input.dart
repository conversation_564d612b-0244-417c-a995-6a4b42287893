import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_input_theme.dart';
import '../../constants/shadcn_tokens.dart';

/// A shadcn-styled input component with Material compatibility.
/// 
/// This input component provides shadcn styling while maintaining full compatibility
/// with Flutter's Material TextField behavior. It supports all standard input features
/// including validation, focus management, text selection, and accessibility.
/// 
/// The input automatically derives its styling from the current theme context,
/// with fallback to Material Design values. All styling is customizable through
/// the [ShadcnInputTheme] extension.
/// 
/// Example usage:
/// ```dart
/// ShadcnInput(
///   placeholder: 'Enter your name',
///   onChanged: (value) => print('Input: $value'),
///   size: ShadcnInputSize.medium,
/// )
/// ```
class ShadcnInput extends ShadcnComponent with ShadcnComponentValidation {
  /// The controller for managing the input's text content.
  final TextEditingController? controller;

  /// The initial value for the input field.
  final String? initialValue;

  /// Placeholder text displayed when the input is empty.
  final String? placeholder;

  /// Helper text displayed below the input.
  final String? helperText;

  /// Error text displayed below the input in error state.
  final String? errorText;

  /// Label text displayed above the input.
  final String? label;

  /// Callback function called when the input text changes.
  final ValueChanged<String>? onChanged;

  /// Callback function called when the input is submitted.
  final ValueChanged<String>? onSubmitted;

  /// Callback function called when the input gains or loses focus.
  final ValueChanged<bool>? onFocusChange;

  /// The size variant of the input.
  final ShadcnInputSize size;

  /// Whether the input is enabled for user interaction.
  final bool enabled;

  /// Whether the input should be obscured (for passwords).
  final bool obscureText;

  /// Whether the input is read-only.
  final bool readOnly;

  /// Whether the input should autofocus when first built.
  final bool autofocus;

  /// Maximum number of lines for the input.
  final int? maxLines;

  /// Minimum number of lines for the input.
  final int? minLines;

  /// Maximum length of the input text.
  final int? maxLength;

  /// The type of keyboard to display.
  final TextInputType keyboardType;

  /// The type of action button to display on the keyboard.
  final TextInputAction textInputAction;

  /// Input formatters to apply to the text.
  final List<TextInputFormatter>? inputFormatters;

  /// Validation function for the input value.
  final String? Function(String?)? validator;

  /// Focus node for keyboard navigation and focus management.
  final FocusNode? focusNode;

  /// Style override for the input text.
  final TextStyle? style;

  /// Widget to display as a prefix inside the input.
  final Widget? prefixIcon;

  /// Widget to display as a suffix inside the input.
  final Widget? suffixIcon;

  /// Text to display as prefix (outside the border).
  final String? prefixText;

  /// Text to display as suffix (outside the border).
  final String? suffixText;

  /// Custom decoration for the input field.
  final InputDecoration? decoration;

  /// Cursor color override.
  final Color? cursorColor;

  /// Cursor width override.
  final double? cursorWidth;

  /// Cursor height override.
  final double? cursorHeight;

  /// Cursor radius override.
  final Radius? cursorRadius;

  /// Selection color override.
  final Color? selectionColor;

  /// Text alignment for the input content.
  final TextAlign textAlign;

  /// Text alignment for hint text.
  final TextAlign textAlignVertical;

  /// Text capitalization behavior.
  final TextCapitalization textCapitalization;

  /// Semantic label for accessibility.
  final String? semanticLabel;

  /// Whether to exclude from semantics tree.
  final bool excludeFromSemantics;

  /// Auto validation mode for form integration.
  final AutovalidateMode? autovalidateMode;

  /// Custom border radius override.
  final BorderRadius? borderRadius;

  /// Custom content padding override.
  final EdgeInsets? contentPadding;

  /// Custom height override.
  final double? height;

  /// Custom width override.
  final double? width;

  const ShadcnInput({
    super.key,
    this.controller,
    this.initialValue,
    this.placeholder,
    this.helperText,
    this.errorText,
    this.label,
    this.onChanged,
    this.onSubmitted,
    this.onFocusChange,
    this.size = ShadcnInputSize.medium,
    this.enabled = true,
    this.obscureText = false,
    this.readOnly = false,
    this.autofocus = false,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.keyboardType = TextInputType.text,
    this.textInputAction = TextInputAction.done,
    this.inputFormatters,
    this.validator,
    this.focusNode,
    this.style,
    this.prefixIcon,
    this.suffixIcon,
    this.prefixText,
    this.suffixText,
    this.decoration,
    this.cursorColor,
    this.cursorWidth,
    this.cursorHeight,
    this.cursorRadius,
    this.selectionColor,
    this.textAlign = TextAlign.start,
    this.textAlignVertical = TextAlign.center,
    this.textCapitalization = TextCapitalization.none,
    this.semanticLabel,
    this.excludeFromSemantics = false,
    this.autovalidateMode,
    this.borderRadius,
    this.contentPadding,
    this.height,
    this.width,
  });

  /// Factory constructor for small input variant.
  factory ShadcnInput.small({
    Key? key,
    TextEditingController? controller,
    String? placeholder,
    String? helperText,
    String? errorText,
    String? label,
    ValueChanged<String>? onChanged,
    ValueChanged<String>? onSubmitted,
    bool enabled = true,
    bool obscureText = false,
    Widget? prefixIcon,
    Widget? suffixIcon,
    String? Function(String?)? validator,
  }) {
    return ShadcnInput(
      key: key,
      controller: controller,
      placeholder: placeholder,
      helperText: helperText,
      errorText: errorText,
      label: label,
      onChanged: onChanged,
      onSubmitted: onSubmitted,
      size: ShadcnInputSize.small,
      enabled: enabled,
      obscureText: obscureText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      validator: validator,
    );
  }

  /// Factory constructor for medium input variant (default).
  factory ShadcnInput.medium({
    Key? key,
    TextEditingController? controller,
    String? placeholder,
    String? helperText,
    String? errorText,
    String? label,
    ValueChanged<String>? onChanged,
    ValueChanged<String>? onSubmitted,
    bool enabled = true,
    bool obscureText = false,
    Widget? prefixIcon,
    Widget? suffixIcon,
    String? Function(String?)? validator,
  }) {
    return ShadcnInput(
      key: key,
      controller: controller,
      placeholder: placeholder,
      helperText: helperText,
      errorText: errorText,
      label: label,
      onChanged: onChanged,
      onSubmitted: onSubmitted,
      size: ShadcnInputSize.medium,
      enabled: enabled,
      obscureText: obscureText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      validator: validator,
    );
  }

  /// Factory constructor for large input variant.
  factory ShadcnInput.large({
    Key? key,
    TextEditingController? controller,
    String? placeholder,
    String? helperText,
    String? errorText,
    String? label,
    ValueChanged<String>? onChanged,
    ValueChanged<String>? onSubmitted,
    bool enabled = true,
    bool obscureText = false,
    Widget? prefixIcon,
    Widget? suffixIcon,
    String? Function(String?)? validator,
  }) {
    return ShadcnInput(
      key: key,
      controller: controller,
      placeholder: placeholder,
      helperText: helperText,
      errorText: errorText,
      label: label,
      onChanged: onChanged,
      onSubmitted: onSubmitted,
      size: ShadcnInputSize.large,
      enabled: enabled,
      obscureText: obscureText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      validator: validator,
    );
  }

  /// Factory constructor for password input.
  factory ShadcnInput.password({
    Key? key,
    TextEditingController? controller,
    String? placeholder = 'Enter password',
    String? helperText,
    String? errorText,
    String? label,
    ValueChanged<String>? onChanged,
    ValueChanged<String>? onSubmitted,
    ShadcnInputSize size = ShadcnInputSize.medium,
    bool enabled = true,
    String? Function(String?)? validator,
  }) {
    return ShadcnInput(
      key: key,
      controller: controller,
      placeholder: placeholder,
      helperText: helperText,
      errorText: errorText,
      label: label,
      onChanged: onChanged,
      onSubmitted: onSubmitted,
      size: size,
      enabled: enabled,
      obscureText: true,
      keyboardType: TextInputType.visiblePassword,
      textInputAction: TextInputAction.done,
      validator: validator,
    );
  }

  /// Factory constructor for email input.
  factory ShadcnInput.email({
    Key? key,
    TextEditingController? controller,
    String? placeholder = 'Enter email',
    String? helperText,
    String? errorText,
    String? label,
    ValueChanged<String>? onChanged,
    ValueChanged<String>? onSubmitted,
    ShadcnInputSize size = ShadcnInputSize.medium,
    bool enabled = true,
    String? Function(String?)? validator,
  }) {
    return ShadcnInput(
      key: key,
      controller: controller,
      placeholder: placeholder,
      helperText: helperText,
      errorText: errorText,
      label: label,
      onChanged: onChanged,
      onSubmitted: onSubmitted,
      size: size,
      enabled: enabled,
      keyboardType: TextInputType.emailAddress,
      textInputAction: TextInputAction.next,
      textCapitalization: TextCapitalization.none,
      validator: validator,
    );
  }

  /// Whether the input is in an error state.
  bool get hasError => errorText != null && errorText!.isNotEmpty;

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    // Validate theme context and properties
    validateThemeProperties(context);
    validateVariant(size, ShadcnInputSize.values, 'ShadcnInput');
    validateSize(
      height: height,
      width: width,
      componentName: 'ShadcnInput',
    );
    validateAccessibility(
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      componentName: 'ShadcnInput',
    );

    // Resolve input theme
    final inputTheme = resolveTheme<ShadcnInputTheme>(
      context,
      ShadcnInputTheme.defaultTheme,
    );

    // Resolve size properties
    final sizeProperties = _resolveSizeProperties(inputTheme, size);
    final colors = _resolveColors(inputTheme, theme.colorScheme);
    final textStyles = _resolveTextStyles(inputTheme, theme.textTheme);

    // Build the input field with theme-aware decoration
    final effectiveDecoration = _buildDecoration(
      context,
      inputTheme,
      colors,
      sizeProperties,
      theme.colorScheme,
    );

    // Create the text field
    Widget textField = TextFormField(
      controller: controller,
      initialValue: initialValue,
      focusNode: focusNode,
      style: style ?? textStyles.text,
      enabled: enabled,
      obscureText: obscureText,
      readOnly: readOnly,
      autofocus: autofocus,
      maxLines: maxLines,
      minLines: minLines,
      maxLength: maxLength,
      keyboardType: keyboardType,
      textInputAction: textInputAction,
      inputFormatters: inputFormatters,
      validator: validator,
      onChanged: onChanged,
      onFieldSubmitted: onSubmitted,
      textAlign: textAlign,
      textCapitalization: textCapitalization,
      autovalidateMode: autovalidateMode,
      cursorColor: cursorColor ?? colors.focused,
      cursorWidth: cursorWidth ?? 2.0,
      cursorHeight: cursorHeight,
      cursorRadius: cursorRadius ?? const Radius.circular(2.0),
      selectionControls: MaterialTextSelectionControls(),
      decoration: effectiveDecoration,
    );

    // Wrap with focus handling for onFocusChange callback
    if (onFocusChange != null) {
      textField = Focus(
        onFocusChange: onFocusChange,
        child: textField,
      );
    }

    // Apply size constraints
    final effectiveHeight = height ?? sizeProperties.height;
    if (effectiveHeight > 0 || width != null) {
      textField = SizedBox(
        height: maxLines == 1 ? effectiveHeight : null,
        width: width,
        child: textField,
      );
    }

    // Add semantic label if provided
    if (semanticLabel != null && !excludeFromSemantics) {
      textField = Semantics(
        label: semanticLabel,
        textField: true,
        enabled: enabled,
        child: textField,
      );
    }

    return textField;
  }

  /// Resolves size properties for the current size variant.
  _InputSizeProperties _resolveSizeProperties(ShadcnInputTheme theme, ShadcnInputSize size) {
    switch (size) {
      case ShadcnInputSize.small:
        return _InputSizeProperties(
          height: theme.smallHeight ?? ShadcnTokens.inputHeightSm,
          padding: contentPadding ?? theme.smallPadding ?? 
              const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          fontSize: theme.textStyle?.fontSize ?? ShadcnTokens.fontSizeSm,
          iconSize: theme.smallIconSize ?? ShadcnTokens.iconSizeSm,
        );

      case ShadcnInputSize.medium:
        return _InputSizeProperties(
          height: theme.mediumHeight ?? ShadcnTokens.inputHeightMd,
          padding: contentPadding ?? theme.mediumPadding ?? 
              const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          fontSize: theme.textStyle?.fontSize ?? ShadcnTokens.fontSizeMd,
          iconSize: theme.mediumIconSize ?? ShadcnTokens.iconSizeMd,
        );

      case ShadcnInputSize.large:
        return _InputSizeProperties(
          height: theme.largeHeight ?? ShadcnTokens.inputHeightLg,
          padding: contentPadding ?? theme.largePadding ?? 
              const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          fontSize: theme.textStyle?.fontSize ?? ShadcnTokens.fontSizeLg,
          iconSize: theme.largeIconSize ?? ShadcnTokens.iconSizeLg,
        );
    }
  }

  /// Resolves colors for different input states.
  _InputColors _resolveColors(ShadcnInputTheme theme, ColorScheme colorScheme) {
    return _InputColors(
      background: enabled 
          ? (hasError 
              ? theme.errorBackgroundColor ?? theme.backgroundColor ?? colorScheme.surface
              : theme.backgroundColor ?? colorScheme.surface)
          : theme.disabledBackgroundColor ?? colorScheme.surface.withOpacity(0.12),
      
      border: enabled
          ? (hasError
              ? theme.errorBorderColor ?? colorScheme.error
              : theme.borderColor ?? colorScheme.outline)
          : theme.disabledBorderColor ?? colorScheme.onSurface.withOpacity(0.12),
      
      focused: theme.focusedBorderColor ?? colorScheme.primary,
      hovered: theme.hoveredBorderColor ?? colorScheme.onSurface,
      
      text: enabled
          ? (hasError
              ? theme.errorTextColor ?? theme.textColor ?? colorScheme.onSurface
              : theme.textColor ?? colorScheme.onSurface)
          : theme.disabledTextColor ?? colorScheme.onSurface.withOpacity(0.38),
      
      placeholder: theme.placeholderColor ?? colorScheme.onSurface.withOpacity(0.6),
      
      icon: enabled
          ? theme.iconColor ?? colorScheme.onSurface.withOpacity(0.6)
          : theme.disabledIconColor ?? colorScheme.onSurface.withOpacity(0.38),
          
      errorMessage: theme.errorMessageColor ?? colorScheme.error,
    );
  }

  /// Resolves text styles for different text elements.
  _InputTextStyles _resolveTextStyles(ShadcnInputTheme theme, TextTheme textTheme) {
    return _InputTextStyles(
      text: theme.textStyle ?? textTheme.bodyMedium ?? const TextStyle(),
      placeholder: theme.placeholderStyle ?? textTheme.bodyMedium ?? const TextStyle(),
      error: theme.errorTextStyle ?? textTheme.bodySmall ?? const TextStyle(),
    );
  }

  /// Builds the input decoration with theme-aware styling.
  InputDecoration _buildDecoration(
    BuildContext context,
    ShadcnInputTheme theme,
    _InputColors colors,
    _InputSizeProperties sizeProps,
    ColorScheme colorScheme,
  ) {
    // Use custom decoration if provided
    if (decoration != null) {
      return decoration!;
    }

    final effectiveBorderRadius = borderRadius ?? theme.borderRadius ?? 
        BorderRadius.circular(ShadcnTokens.radiusMd);
    
    final borderWidth = theme.borderWidth ?? ShadcnTokens.borderWidth;

    // Build border styles for different states
    final enabledBorder = OutlineInputBorder(
      borderRadius: effectiveBorderRadius,
      borderSide: BorderSide(
        color: colors.border,
        width: borderWidth,
      ),
    );

    final focusedBorder = OutlineInputBorder(
      borderRadius: effectiveBorderRadius,
      borderSide: BorderSide(
        color: colors.focused,
        width: borderWidth,
      ),
    );

    final errorBorder = OutlineInputBorder(
      borderRadius: effectiveBorderRadius,
      borderSide: BorderSide(
        color: hasError ? colors.errorMessage : colors.border,
        width: borderWidth,
      ),
    );

    final focusedErrorBorder = OutlineInputBorder(
      borderRadius: effectiveBorderRadius,
      borderSide: BorderSide(
        color: hasError ? colors.errorMessage : colors.focused,
        width: borderWidth,
      ),
    );

    final disabledBorder = OutlineInputBorder(
      borderRadius: effectiveBorderRadius,
      borderSide: BorderSide(
        color: colors.border,
        width: borderWidth,
      ),
    );

    return InputDecoration(
      labelText: label,
      hintText: placeholder,
      helperText: helperText,
      errorText: errorText,
      prefixIcon: prefixIcon != null 
          ? IconTheme(
              data: IconThemeData(
                color: colors.icon,
                size: sizeProps.iconSize,
              ),
              child: prefixIcon!,
            )
          : null,
      suffixIcon: suffixIcon != null 
          ? IconTheme(
              data: IconThemeData(
                color: colors.icon,
                size: sizeProps.iconSize,
              ),
              child: suffixIcon!,
            )
          : null,
      prefixText: prefixText,
      suffixText: suffixText,
      filled: true,
      fillColor: colors.background,
      contentPadding: sizeProps.padding,
      border: enabledBorder,
      enabledBorder: enabledBorder,
      focusedBorder: focusedBorder,
      errorBorder: errorBorder,
      focusedErrorBorder: focusedErrorBorder,
      disabledBorder: disabledBorder,
      hintStyle: _resolveTextStyles(theme, Theme.of(context).textTheme).placeholder.copyWith(
        color: colors.placeholder,
      ),
      labelStyle: TextStyle(
        color: colors.text,
        fontSize: sizeProps.fontSize,
      ),
      helperStyle: _resolveTextStyles(theme, Theme.of(context).textTheme).error.copyWith(
        color: colors.text.withOpacity(0.6),
      ),
      errorStyle: _resolveTextStyles(theme, Theme.of(context).textTheme).error.copyWith(
        color: colors.errorMessage,
      ),
      counterStyle: _resolveTextStyles(theme, Theme.of(context).textTheme).error.copyWith(
        color: colors.text.withOpacity(0.6),
      ),
      isDense: true,
      isCollapsed: false,
    );
  }
}

/// Helper class to hold resolved input colors for different states.
class _InputColors {
  final Color background;
  final Color border;
  final Color focused;
  final Color hovered;
  final Color text;
  final Color placeholder;
  final Color icon;
  final Color errorMessage;

  const _InputColors({
    required this.background,
    required this.border,
    required this.focused,
    required this.hovered,
    required this.text,
    required this.placeholder,
    required this.icon,
    required this.errorMessage,
  });
}

/// Helper class to hold resolved input size properties.
class _InputSizeProperties {
  final double height;
  final EdgeInsets padding;
  final double fontSize;
  final double iconSize;

  const _InputSizeProperties({
    required this.height,
    required this.padding,
    required this.fontSize,
    required this.iconSize,
  });
}

/// Helper class to hold resolved input text styles.
class _InputTextStyles {
  final TextStyle text;
  final TextStyle placeholder;
  final TextStyle error;

  const _InputTextStyles({
    required this.text,
    required this.placeholder,
    required this.error,
  });
}