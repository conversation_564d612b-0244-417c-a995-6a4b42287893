import 'package:flutter/material.dart';

/// Utility class for safe theme resolution with fallback handling.
/// 
/// This class provides methods to safely resolve theme extensions and
/// properties with a robust fallback chain to ensure components always
/// have valid styling values.
class ShadcnThemeResolver {
  ShadcnThemeResolver._();
  
  /// Safely resolves a theme extension with fallback to default factory.
  /// 
  /// Resolution order:
  /// 1. Theme extension from current context
  /// 2. Default factory using Material ColorScheme
  /// 3. Emergency fallback using hardcoded Material theme
  static T resolveThemeExtension<T extends ThemeExtension<T>>(
    BuildContext context,
    T Function(ColorScheme) defaultFactory,
  ) {
    try {
      final theme = Theme.of(context);
      final extension = theme.extension<T>();
      
      if (extension != null) {
        return extension;
      }
      
      // Fallback to default factory with current color scheme
      return defaultFactory(theme.colorScheme);
    } catch (e) {
      debugPrint('Failed to resolve theme extension $T: $e');
      
      // Emergency fallback: create with hardcoded Material theme
      final fallbackColorScheme = ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.light,
      );
      return defaultFactory(fallbackColorScheme);
    }
  }
  
  /// Resolves a color with theme-aware fallback chain.
  /// 
  /// Resolution order:
  /// 1. Custom color (if provided)
  /// 2. Material theme color
  /// 3. Hardcoded fallback color
  static Color resolveColor(
    BuildContext context,
    Color? customColor,
    Color Function(ThemeData) materialColorGetter, [
    Color fallbackColor = Colors.grey,
  ]) {
    if (customColor != null) return customColor;
    
    try {
      final theme = Theme.of(context);
      return materialColorGetter(theme);
    } catch (e) {
      debugPrint('Failed to resolve color from theme: $e');
      return fallbackColor;
    }
  }
  
  /// Resolves a text style with theme-aware merging.
  /// 
  /// Resolution order:
  /// 1. Merge custom style with Material base style
  /// 2. Material base style only
  /// 3. Fallback text style
  static TextStyle resolveTextStyle(
    BuildContext context,
    TextStyle? customStyle,
    TextStyle Function(TextTheme) materialStyleGetter, [
    TextStyle? fallbackStyle,
  ]) {
    try {
      final theme = Theme.of(context);
      final baseStyle = materialStyleGetter(theme.textTheme);
      
      if (customStyle != null) {
        return baseStyle.merge(customStyle);
      }
      
      return baseStyle;
    } catch (e) {
      debugPrint('Failed to resolve text style from theme: $e');
      return fallbackStyle ?? const TextStyle();
    }
  }
  
  /// Resolves spacing values with visual density consideration.
  static EdgeInsets resolveSpacing(
    BuildContext context,
    EdgeInsets? customSpacing,
    EdgeInsets defaultSpacing,
  ) {
    if (customSpacing != null) return customSpacing;
    
    try {
      final theme = Theme.of(context);
      final visualDensity = theme.visualDensity;
      final adjustment = visualDensity.baseSizeAdjustment;
      
      return EdgeInsets.fromLTRB(
        defaultSpacing.left + adjustment.dx,
        defaultSpacing.top + adjustment.dy,
        defaultSpacing.right + adjustment.dx,
        defaultSpacing.bottom + adjustment.dy,
      );
    } catch (e) {
      debugPrint('Failed to resolve spacing with visual density: $e');
      return defaultSpacing;
    }
  }
  
  /// Resolves border radius with theme consistency.
  static BorderRadius resolveBorderRadius(
    BuildContext context,
    BorderRadius? customRadius,
    BorderRadius defaultRadius,
  ) {
    if (customRadius != null) return customRadius;
    
    // Apply theme-based adjustments if needed
    // For now, return default radius directly
    return defaultRadius;
  }
  
  /// Resolves double values with visual density adjustments.
  static double resolveDouble(
    BuildContext context,
    double? customValue,
    double defaultValue, {
    bool applyVerticalDensity = true,
  }) {
    if (customValue != null) return customValue;
    
    try {
      final theme = Theme.of(context);
      if (!applyVerticalDensity) return defaultValue;
      
      final visualDensity = theme.visualDensity;
      final adjustment = visualDensity.baseSizeAdjustment;
      return (defaultValue + adjustment.dy).clamp(0.0, double.infinity);
    } catch (e) {
      debugPrint('Failed to resolve double value with visual density: $e');
      return defaultValue;
    }
  }
  
  /// Validates that required theme properties are available.
  static void validateThemeContext(BuildContext context) {
    assert(() {
      final theme = Theme.of(context);
      
      // ColorScheme and TextTheme are non-nullable in Flutter 3.0+
      // These checks are kept for documentation purposes but are not needed
      final colorScheme = theme.colorScheme;
      final textTheme = theme.textTheme;
      
      // Validate that essential theme properties are available
      // In Flutter 3.0+, these properties are non-nullable but we keep the validation
      // for consistency and to catch potential theme configuration issues
      assert((colorScheme.primary.a * 255.0).round() >= 0, 'ColorScheme.primary is required');
      assert(textTheme.bodyMedium?.fontSize != null, 'TextTheme.bodyMedium is required');
      
      return true;
    }());
  }
  
  /// Validates theme property requirements for components.
  /// 
  /// This method performs comprehensive validation of theme properties
  /// to ensure components have all required values for proper rendering.
  static bool validateThemeProperty<T>(
    T? value,
    String propertyName, {
    bool throwOnError = false,
  }) {
    if (value == null) {
      final errorMessage = 'Required theme property "$propertyName" is null';
      if (throwOnError) {
        throw ThemeException(errorMessage);
      }
      debugPrint('Theme validation warning: $errorMessage');
      return false;
    }
    return true;
  }
  
  /// Validates color scheme completeness.
  /// 
  /// Ensures all essential color scheme properties are available and valid.
  static bool validateColorScheme(
    ColorScheme colorScheme, {
    bool throwOnError = false,
  }) {
    try {
      final requiredColors = [
        ('primary', colorScheme.primary),
        ('onPrimary', colorScheme.onPrimary),
        ('secondary', colorScheme.secondary),
        ('onSecondary', colorScheme.onSecondary),
        ('surface', colorScheme.surface),
        ('onSurface', colorScheme.onSurface),
        ('error', colorScheme.error),
        ('onError', colorScheme.onError),
      ];
      
      for (final (name, color) in requiredColors) {
        if (!validateThemeProperty(color, 'colorScheme.$name', throwOnError: throwOnError)) {
          return false;
        }
      }
      
      return true;
    } catch (e) {
      final errorMessage = 'ColorScheme validation failed: $e';
      if (throwOnError) {
        throw ThemeException(errorMessage);
      }
      debugPrint(errorMessage);
      return false;
    }
  }
  
  /// Validates text theme completeness.
  /// 
  /// Ensures all essential text theme properties are available and valid.
  static bool validateTextTheme(
    TextTheme textTheme, {
    bool throwOnError = false,
  }) {
    try {
      final requiredStyles = [
        ('displayLarge', textTheme.displayLarge),
        ('displayMedium', textTheme.displayMedium),
        ('displaySmall', textTheme.displaySmall),
        ('headlineLarge', textTheme.headlineLarge),
        ('headlineMedium', textTheme.headlineMedium),
        ('headlineSmall', textTheme.headlineSmall),
        ('titleLarge', textTheme.titleLarge),
        ('titleMedium', textTheme.titleMedium),
        ('titleSmall', textTheme.titleSmall),
        ('bodyLarge', textTheme.bodyLarge),
        ('bodyMedium', textTheme.bodyMedium),
        ('bodySmall', textTheme.bodySmall),
        ('labelLarge', textTheme.labelLarge),
        ('labelMedium', textTheme.labelMedium),
        ('labelSmall', textTheme.labelSmall),
      ];
      
      for (final (name, style) in requiredStyles) {
        if (!validateThemeProperty(style, 'textTheme.$name', throwOnError: throwOnError)) {
          return false;
        }
      }
      
      return true;
    } catch (e) {
      final errorMessage = 'TextTheme validation failed: $e';
      if (throwOnError) {
        throw ThemeException(errorMessage);
      }
      debugPrint(errorMessage);
      return false;
    }
  }
  
  /// Validates theme extension properties.
  /// 
  /// Performs validation on a theme extension to ensure all required
  /// properties are properly configured.
  static bool validateThemeExtension<T extends ThemeExtension<T>>(
    T? extension,
    List<String> requiredProperties, {
    bool throwOnError = false,
  }) {
    if (extension == null) {
      final errorMessage = 'Theme extension ${T.toString()} is null';
      if (throwOnError) {
        throw ThemeException(errorMessage);
      }
      debugPrint('Theme validation warning: $errorMessage');
      return false;
    }
    
    // Note: In a real implementation, we would use reflection or implement
    // a validation interface to check specific properties. For now, we
    // just validate that the extension exists.
    return true;
  }
  
  /// Performs comprehensive theme validation.
  /// 
  /// Validates all aspects of the theme to ensure component compatibility.
  static bool validateCompleteTheme(
    BuildContext context, {
    bool throwOnError = false,
  }) {
    try {
      final theme = Theme.of(context);
      
      // Validate core theme data exists
      validateThemeProperty(theme, 'theme', throwOnError: throwOnError);
      
      // Validate color scheme
      if (!validateColorScheme(theme.colorScheme, throwOnError: throwOnError)) {
        return false;
      }
      
      // Validate text theme
      if (!validateTextTheme(theme.textTheme, throwOnError: throwOnError)) {
        return false;
      }
      
      // Validate visual density
      validateThemeProperty(theme.visualDensity, 'visualDensity', throwOnError: throwOnError);
      
      return true;
    } catch (e) {
      final errorMessage = 'Complete theme validation failed: $e';
      if (throwOnError) {
        throw ThemeException(errorMessage);
      }
      debugPrint(errorMessage);
      return false;
    }
  }
  
  /// Gets the current Material ColorScheme safely.
  static ColorScheme getColorScheme(BuildContext context) {
    try {
      return Theme.of(context).colorScheme;
    } catch (e) {
      debugPrint('Failed to get ColorScheme: $e');
      return ColorScheme.fromSeed(seedColor: Colors.blue);
    }
  }
  
  /// Gets the current Material TextTheme safely.
  static TextTheme getTextTheme(BuildContext context) {
    try {
      return Theme.of(context).textTheme;
    } catch (e) {
      debugPrint('Failed to get TextTheme: $e');
      return ThemeData().textTheme;
    }
  }
}

/// Exception thrown when theme validation fails.
/// 
/// This exception is used to provide detailed information about
/// theme configuration issues that prevent proper component rendering.
class ThemeException implements Exception {
  /// The error message describing the theme validation failure.
  final String message;
  
  /// Creates a new theme exception with the given error message.
  const ThemeException(this.message);
  
  @override
  String toString() => 'ThemeException: $message';
}