/// # Flutter Shadcn Component Library
/// 
/// A comprehensive Flutter component library inspired by shadcn/ui that provides
/// 51 themed UI components with full Material Design integration. The library
/// follows a theme-first design approach where all styling is derived from 
/// Flutter's theme system using ThemeExtension patterns.
/// 
/// ## Features
/// 
/// - **Complete Component Set**: 51 shadcn-inspired components covering all common UI needs
/// - **Theme Integration**: Full Material Design theme compatibility with automatic light/dark mode support
/// - **Customization**: Easy component and theme customization through ThemeExtension patterns
/// - **Performance**: Optimized theme resolution and rendering for smooth user experiences
/// - **Accessibility**: Built-in accessibility support following Material Design guidelines
/// - **Type Safety**: Full Dart type safety with comprehensive null safety support
/// 
/// ## Quick Start
/// 
/// ```dart
/// import 'package:flutter/material.dart';
/// import 'package:shadcn/shadcn.dart';
/// 
/// class MyApp extends StatelessWidget {
///   @override
///   Widget build(BuildContext context) {
///     return MaterialApp(
///       theme: ShadcnTheme.lightTheme(), // Complete theme with all components
///       darkTheme: ShadcnTheme.darkTheme(), // Automatic dark mode support
///       home: Scaffold(
///         appBar: AppBar(
///           title: Text('Shadcn Flutter Demo'),
///           actions: [
///             ShadcnThemeSwitcher.createThemeToggleButton(
///               onToggle: () {
///                 // Handle theme switching
///               },
///             ),
///           ],
///         ),
///         body: Center(
///           child: Column(
///             mainAxisAlignment: MainAxisAlignment.center,
///             children: [
///               ShadcnCard(
///                 child: Padding(
///                   padding: EdgeInsets.all(16),
///                   child: Column(
///                     children: [
///                       Text('Welcome to Shadcn Flutter!'),
///                       SizedBox(height: 16),
///                       ShadcnButton(
///                         onPressed: () => print('Button pressed'),
///                         child: Text('Get Started'),
///                       ),
///                     ],
///                   ),
///                 ),
///               ),
///             ],
///           ),
///         ),
///       ),
///     );
///   }
/// }
/// ```
/// 
/// ## Component Categories
/// 
/// ### Form Components
/// Components for user input and form interactions:
/// - [ShadcnButton] - Versatile button with multiple variants and states
/// - [ShadcnInput] - Text input fields with Material Design integration
/// - [ShadcnCheckbox] - Checkbox inputs with custom theming
/// - [ShadcnSwitch] - Toggle switches for boolean values
/// - [ShadcnRadioGroup] - Radio button groups for single selection
/// - [ShadcnSelect] - Dropdown selection components
/// - [ShadcnLabel] - Form field labels with proper association
/// 
/// ### Layout Components
/// Components for organizing and structuring content:
/// - [ShadcnCard] - Container components with elevation and borders
/// - [ShadcnSeparator] - Dividers for visual separation
/// - [ShadcnTabs] - Tab navigation for organized content
/// - [ShadcnAccordion] - Collapsible content sections
/// - [ShadcnAspectRatio] - Maintain aspect ratios in layouts
/// - [ShadcnScrollArea] - Custom scrollable containers
/// 
/// ### Feedback Components
/// Components for user feedback and notifications:
/// - [ShadcnAlert] - Alert messages and notifications
/// - [ShadcnToast] - Temporary notification messages
/// - [ShadcnDialog] - Modal dialog windows
/// - [ShadcnProgress] - Progress indicators for loading states
/// - [ShadcnSkeleton] - Skeleton loaders for loading states
/// 
/// ### Navigation Components
/// Components for app navigation and wayfinding:
/// - [ShadcnBreadcrumb] - Breadcrumb navigation trails
/// - [ShadcnNavigationMenu] - Navigation menus and dropdowns
/// - [ShadcnContextMenu] - Right-click context menus
/// - [ShadcnCommand] - Command palette interfaces
/// 
/// ### Overlay Components
/// Components that float above other content:
/// - [ShadcnPopover] - Floating content containers
/// - [ShadcnTooltip] - Hover information tooltips
/// - [ShadcnHoverCard] - Hover-activated information cards
/// 
/// ### Data Display Components
/// Components for displaying and organizing data:
/// - [ShadcnTable] - Data tables with sorting and selection
/// - [ShadcnBadge] - Labels and status indicators
/// - [ShadcnAvatar] - User profile images and placeholders
/// - [ShadcnCalendar] - Date picker calendars
/// 
/// ### Advanced Components
/// Specialized components for complex interactions:
/// - [ShadcnCarousel] - Image and content carousels
/// - [ShadcnResizable] - Resizable panel components
/// 
/// ## Theme System
/// 
/// The library provides a comprehensive theme system with complete light and dark themes:
/// 
/// ```dart
/// // Use complete pre-built themes
/// MaterialApp(
///   theme: ShadcnTheme.lightTheme(),
///   darkTheme: ShadcnTheme.darkTheme(),
///   themeMode: ThemeMode.system, // Follows system theme
///   home: MyApp(),
/// )
/// 
/// // Create custom themes
/// MaterialApp(
///   theme: ShadcnTheme.customTheme(
///     colorScheme: ColorScheme.fromSeed(seedColor: Colors.purple),
///     fontFamily: 'Roboto',
///   ),
///   home: MyApp(),
/// )
/// 
/// // Extend existing themes
/// MaterialApp(
///   theme: ShadcnTheme.fromExistingTheme(
///     baseTheme: yourExistingTheme,
///   ),
///   home: MyApp(),
/// )
/// 
/// // Theme switching with utilities
/// ShadcnThemeProvider(
///   lightTheme: ShadcnTheme.lightTheme(),
///   darkTheme: ShadcnTheme.darkTheme(),
///   child: MyApp(),
/// )
/// 
/// // Theme validation
/// final result = ShadcnThemeValidator.validateTheme(theme: myTheme);
/// if (!result.isValid) {
///   print('Theme issues: ${result.errors}');
/// }
/// ```
/// 
/// ## Migration from Material Components
/// 
/// The library provides seamless migration paths from Flutter's built-in Material components:
/// 
/// ```dart
/// // Before (Material)
/// ElevatedButton(
///   onPressed: () {},
///   child: Text('Click me'),
/// )
/// 
/// // After (Shadcn)
/// ShadcnButton(
///   onPressed: () {},
///   child: Text('Click me'),
/// )
/// ```
/// 
/// See the migration guide for complete migration instructions.
/// 
/// ## Performance
/// 
/// The library is optimized for performance with:
/// - Efficient theme resolution using InheritedWidget patterns
/// - Minimal rebuilds during theme changes
/// - Memory-efficient component rendering
/// - Optimized widget tree structures
/// 
/// ## Testing
/// 
/// Use the provided test helpers for component testing:
/// 
/// ```dart
/// import 'package:flutter_test/flutter_test.dart';
/// import 'package:shadcn/shadcn.dart';
/// import 'package:shadcn/test_helpers.dart';
/// 
/// testWidgets('Button tap test', (tester) async {
///   bool tapped = false;
///   
///   await tester.pumpWidget(
///     TestApp(
///       child: ShadcnButton(
///         onPressed: () => tapped = true,
///         child: Text('Test'),
///       ),
///     ),
///   );
///   
///   await tester.tap(find.byType(ShadcnButton));
///   expect(tapped, isTrue);
/// });
/// ```
library flutter_shadcn;

// Core theme system exports
export 'src/theme/shadcn_theme.dart';
export 'src/theme/shadcn_theme_switcher.dart';
export 'src/theme/shadcn_theme_validator.dart';
export 'src/theme/extensions/shadcn_theme_extension.dart';
export 'src/theme/extensions/shadcn_accordion_theme.dart';
export 'src/theme/extensions/shadcn_alert_theme.dart';
export 'src/theme/extensions/shadcn_avatar_theme.dart';
export 'src/theme/extensions/shadcn_badge_theme.dart';
export 'src/theme/extensions/shadcn_button_theme.dart';
export 'src/theme/extensions/shadcn_card_theme.dart';
export 'src/theme/extensions/shadcn_input_theme.dart';
export 'src/theme/extensions/shadcn_label_theme.dart';
export 'src/theme/extensions/shadcn_select_theme.dart';
export 'src/theme/extensions/shadcn_toast_theme.dart';
export 'src/theme/extensions/shadcn_dialog_theme.dart';
export 'src/theme/extensions/shadcn_table_theme.dart';
export 'src/theme/extensions/shadcn_progress_theme.dart';
export 'src/theme/extensions/shadcn_skeleton_theme.dart';
export 'src/theme/extensions/shadcn_carousel_theme.dart';
export 'src/theme/extensions/shadcn_resizable_theme.dart';
export 'src/theme/extensions/shadcn_calendar_theme.dart';
export 'src/theme/extensions/shadcn_popover_theme.dart';
export 'src/theme/extensions/shadcn_tooltip_theme.dart';
export 'src/theme/extensions/shadcn_command_theme.dart';
export 'src/theme/extensions/shadcn_context_menu_theme.dart';
export 'src/theme/extensions/shadcn_hover_card_theme.dart';
export 'src/theme/extensions/shadcn_separator_theme.dart';
export 'src/theme/extensions/shadcn_aspect_ratio_theme.dart';
export 'src/theme/extensions/shadcn_scroll_area_theme.dart';
export 'src/theme/extensions/shadcn_tabs_theme.dart';
export 'src/theme/extensions/shadcn_sheet_theme.dart';
export 'src/theme/extensions/shadcn_breadcrumb_theme.dart';
export 'src/theme/extensions/shadcn_navigation_menu_theme.dart';
export 'src/theme/color_schemes/shadcn_color_scheme.dart';
export 'src/constants/shadcn_tokens.dart';
export 'src/utils/theme_resolver.dart';

// Base component exports
export 'src/components/shadcn_component.dart';

// Component exports
export 'src/components/accordion/accordion.dart';
export 'src/components/alert/alert.dart';
export 'src/components/avatar/avatar.dart';
export 'src/components/badge/badge.dart';
export 'src/components/button/button.dart';
export 'src/components/input/input.dart';
export 'src/components/label/label.dart';
export 'src/components/card/card.dart';
export 'src/components/select/select.dart';
export 'src/components/toast/toast.dart';
export 'src/components/dialog/dialog.dart';
export 'src/components/table/table.dart';
export 'src/components/progress/progress.dart';
export 'src/components/skeleton/skeleton.dart';
export 'src/components/carousel/carousel.dart';
export 'src/components/resizable/resizable.dart';
export 'src/components/calendar/calendar.dart';
export 'src/components/popover/popover.dart';
export 'src/components/tooltip/tooltip.dart';
export 'src/components/command/command.dart';
export 'src/components/context_menu/context_menu.dart';
export 'src/components/hover_card/hover_card.dart';
export 'src/components/separator/separator.dart';
export 'src/components/aspect_ratio/aspect_ratio.dart';
export 'src/components/scroll_area/scroll_area.dart';
export 'src/components/tabs/tabs.dart';
export 'src/components/sheet/sheet.dart';
export 'src/components/breadcrumb/breadcrumb.dart';
export 'src/components/navigation_menu/navigation_menu.dart';
export 'src/components/pagination/pagination.dart';