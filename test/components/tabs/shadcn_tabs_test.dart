import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/src/components/tabs/tabs.dart';
import 'package:shadcn/src/theme/extensions/shadcn_tabs_theme.dart';

void main() {
  group('ShadcnTabs', () {
    late List<ShadcnTabData> testTabs;

    setUp(() {
      testTabs = [
        const ShadcnTabData(
          id: 'tab1',
          label: 'Tab 1',
          content: Text('Content 1'),
        ),
        const ShadcnTabData(
          id: 'tab2',
          label: 'Tab 2',
          content: Text('Content 2'),
        ),
        const ShadcnTabData(
          id: 'tab3',
          label: 'Tab 3',
          content: Text('Content 3'),
          disabled: true,
        ),
      ];
    });

    testWidgets('renders tabs correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SizedBox(
              height: 400,
              child: ShadcnTabs(
                tabs: testTabs,
              ),
            ),
          ),
        ),
      );

      // Verify all tabs are rendered
      expect(find.text('Tab 1'), findsOneWidget);
      expect(find.text('Tab 2'), findsOneWidget);
      expect(find.text('Tab 3'), findsOneWidget);

      // Verify first tab content is shown by default
      expect(find.text('Content 1'), findsOneWidget);
    });

    testWidgets('switches tabs on tap', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SizedBox(
              height: 400,
              child: ShadcnTabs(
                tabs: testTabs,
              ),
            ),
          ),
        ),
      );

      // Tap on second tab
      await tester.tap(find.text('Tab 2'));
      await tester.pumpAndSettle();

      // Verify second tab content is shown
      expect(find.text('Content 2'), findsOneWidget);
    });

    testWidgets('respects initial tab selection', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SizedBox(
              height: 400,
              child: ShadcnTabs(
                tabs: testTabs,
                initialTabId: 'tab2',
              ),
            ),
          ),
        ),
      );

      // Verify second tab content is shown initially
      expect(find.text('Content 2'), findsOneWidget);
    });

    testWidgets('calls onTabChanged callback', (WidgetTester tester) async {
      String? selectedTabId;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SizedBox(
              height: 400,
              child: ShadcnTabs(
                tabs: testTabs,
                onTabChanged: (tabId) {
                  selectedTabId = tabId;
                },
              ),
            ),
          ),
        ),
      );

      // Tap on second tab
      await tester.tap(find.text('Tab 2'));
      await tester.pumpAndSettle();

      expect(selectedTabId, equals('tab2'));
    });

    group('ShadcnTabsTheme', () {
      test('creates default theme correctly', () {
        const colorScheme = ColorScheme.light();
        final theme = ShadcnTabsTheme.defaultTheme(colorScheme);

        expect(theme.backgroundColor, equals(colorScheme.surface));
        expect(theme.tabActiveForeground, equals(colorScheme.onSurface));
        expect(theme.indicatorColor, equals(colorScheme.primary));
      });

      test('copyWith works correctly', () {
        const original = ShadcnTabsTheme(tabBackground: Colors.red);
        final updated = original.copyWith(tabBackground: Colors.blue);

        expect(updated.tabBackground, equals(Colors.blue));
      });

      test('lerp works correctly', () {
        const theme1 = ShadcnTabsTheme(tabBackground: Colors.red);
        const theme2 = ShadcnTabsTheme(tabBackground: Colors.blue);
        final lerped = theme1.lerp(theme2, 0.5);

        expect(lerped.tabBackground, isA<Color>());
      });

      test('validate works correctly', () {
        const validTheme = ShadcnTabsTheme(
          tabHeight: 36.0,
          indicatorHeight: 2.0,
        );
        
        expect(validTheme.validate(), isTrue);
      });
    });
  });
}