# Shadcn Flutter Components Demo

This is a comprehensive demo application that showcases all components from the Shadcn Flutter library with their default styles and behaviors.

## What's Included

The demo demonstrates:

- **All 51 shadcn-inspired components** organized by category
- **Component variants** (primary, secondary, destructive, outline, ghost, link)
- **Size variants** (small, medium, large) where applicable
- **Interactive states** (enabled, disabled, loading, focused, etc.)
- **Theme integration** with light/dark mode switching
- **Responsive design** that works on different screen sizes

## Component Categories

### 🎛️ Form Components
- Buttons with all variants and sizes
- Text input fields with different states
- Checkboxes, switches, and radio buttons
- Sliders for range input

### 📐 Layout Components
- Cards for content organization
- Separators for visual division
- Tabs for content navigation
- Accordion for collapsible content
- Aspect ratio containers
- Scrollable areas

### 💬 Feedback Components
- Alert messages and notifications
- Progress indicators
- Skeleton loaders for loading states

### 🧭 Navigation Components
- Breadcrumb trails
- Navigation menus
- Pagination controls

### 🎈 Overlay Components
- Tooltips for hover information
- Popovers for floating content

### 📊 Data Display Components
- Badges and status indicators
- Avatar components
- Data tables

### ⚡ Advanced Components
- Calendar date pickers
- Interactive command palettes

## Running the Demo

### Prerequisites

- Flutter SDK (3.10.0 or later)
- Dart SDK (3.0.0 or later)

### Installation

1. Navigate to the example directory:
   ```bash
   cd example
   ```

2. Get dependencies:
   ```bash
   flutter pub get
   ```

3. Run the demo:
   ```bash
   flutter run
   ```

### Platform Support

The demo runs on:
- ✅ Android
- ✅ iOS  
- ✅ Web
- ✅ macOS
- ✅ Windows
- ✅ Linux

## Features

### Theme Switching
- Toggle between light and dark themes using the theme button in the app bar
- All components automatically adapt to the selected theme
- Proper color contrast maintained in both modes

### Interactive Examples
- Real button interactions with proper feedback
- Working form controls with state management
- Interactive progress bars and sliders
- Functional tab navigation and accordion

### Responsive Design
- Components adapt to different screen sizes
- Proper spacing and sizing on mobile and desktop
- Touch-friendly interfaces on mobile devices

### Accessibility
- Proper semantic labels for screen readers
- Keyboard navigation support
- Focus indicators for interactive elements
- High contrast color schemes

## Code Structure

```
lib/
├── main.dart                 # Main demo application
└── README.md                # This file

pubspec.yaml                 # Dependencies and configuration
```

## Key Components Demonstrated

- **ShadcnButton** - All button variants and sizes
- **ShadcnInput** - Text inputs with validation states
- **ShadcnCard** - Container components
- **ShadcnTabs** - Tab navigation
- **ShadcnAlert** - Notification messages
- **ShadcnProgress** - Loading indicators
- **ShadcnBadge** - Status labels
- **ShadcnAvatar** - User profile images
- **ShadcnCalendar** - Date selection
- And many more...

## Customization Examples

The demo shows how components can be customized through:

- Different variants (primary, secondary, destructive, etc.)
- Size variations (small, medium, large)
- State changes (enabled, disabled, loading)
- Icon integration
- Theme-aware styling

## Learning Resources

This demo serves as:

- 📚 **Visual Reference** - See how components look and behave
- 💡 **Implementation Guide** - Example code for each component
- 🎨 **Design System** - Consistent styling patterns
- ♿ **Accessibility Examples** - Proper semantic structure
- 📱 **Responsive Patterns** - Mobile-first design approaches

## Next Steps

After exploring the demo:

1. **Browse the source code** to understand implementation patterns
2. **Check the main library documentation** for detailed API references
3. **Start building** your own app using the components
4. **Customize themes** to match your brand
5. **Contribute** improvements or report issues

## Support

For questions, issues, or contributions:

- 📖 **Documentation**: Check the main library README
- 🐛 **Bug Reports**: Use the GitHub issues tracker  
- 💡 **Feature Requests**: Submit enhancement ideas
- 🤝 **Contributing**: Follow the contribution guidelines

---

**Happy coding with Shadcn Flutter! 🚀**