import 'package:flutter/material.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  runApp(const ShadcnComponentDemo());
}

/// Comprehensive demo application showcasing all Shadcn Flutter components
/// with their default styles and behavior.
///
/// This demo demonstrates:
/// - All 51 shadcn-inspired components
/// - Component variants (primary, secondary, destructive, outline, ghost, link)
/// - Size variants (small, medium, large)
/// - Interactive states (enabled, disabled, loading)
/// - Theme integration with light/dark mode support
class ShadcnComponentDemo extends StatelessWidget {
  const ShadcnComponentDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Shadcn Flutter Components Demo',
      theme: ShadcnTheme.lightTheme(),
      darkTheme: ShadcnTheme.darkTheme(),
      themeMode: ThemeMode.system,
      home: const ComponentDemoScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

/// Main screen that displays all components organized by category.
class ComponentDemoScreen extends StatefulWidget {
  const ComponentDemoScreen({super.key});

  @override
  State<ComponentDemoScreen> createState() => _ComponentDemoScreenState();
}

class _ComponentDemoScreenState extends State<ComponentDemoScreen> {
  ThemeMode _themeMode = ThemeMode.system;

  void _toggleTheme() {
    setState(() {
      _themeMode =
          _themeMode == ThemeMode.light ? ThemeMode.dark : ThemeMode.light;
    });
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Shadcn Flutter Components Demo',
      theme: ShadcnTheme.lightTheme(),
      darkTheme: ShadcnTheme.darkTheme(),
      themeMode: _themeMode,
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Shadcn Flutter Components Demo'),
          actions: [
            IconButton(
              onPressed: _toggleTheme,
              icon: Icon(_themeMode == ThemeMode.light
                  ? Icons.dark_mode
                  : Icons.light_mode),
              tooltip: 'Toggle Theme',
            ),
          ],
        ),
        body: const SingleChildScrollView(
          padding: EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header section
              _SectionHeader(
                title: 'Shadcn Flutter Components',
                subtitle:
                    'Complete showcase of all 51 components with their default styles and behaviors',
              ),
              SizedBox(height: 32),

              // Form Components Section
              _FormComponentsSection(),
              SizedBox(height: 48),

              // Layout Components Section
              _LayoutComponentsSection(),
              SizedBox(height: 48),

              // Feedback Components Section
              _FeedbackComponentsSection(),
              SizedBox(height: 48),

              // Navigation Components Section
              _NavigationComponentsSection(),
              SizedBox(height: 48),

              // Overlay Components Section
              _OverlayComponentsSection(),
              SizedBox(height: 48),

              // Data Display Components Section
              _DataDisplayComponentsSection(),
              SizedBox(height: 48),

              // Advanced Components Section
              _AdvancedComponentsSection(),
            ],
          ),
        ),
      ),
    );
  }
}

/// Reusable section header widget
class _SectionHeader extends StatelessWidget {
  const _SectionHeader({
    required this.title,
    this.subtitle,
  });

  final String title;
  final String? subtitle;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        if (subtitle != null) ...[
          const SizedBox(height: 8),
          Text(
            subtitle!,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
          ),
        ],
      ],
    );
  }
}

/// Component showcase wrapper with title and description
class _ComponentShowcase extends StatelessWidget {
  const _ComponentShowcase({
    required this.title,
    required this.description,
    required this.child,
  });

  final String title;
  final String description;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        const SizedBox(height: 4),
        Text(
          description,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
        ),
        const SizedBox(height: 16),
        child,
      ],
    );
  }
}

/// Form Components Section - Interactive input and control elements
class _FormComponentsSection extends StatefulWidget {
  const _FormComponentsSection();

  @override
  State<_FormComponentsSection> createState() => _FormComponentsSectionState();
}

class _FormComponentsSectionState extends State<_FormComponentsSection> {
  bool _switchValue = false;
  bool _checkboxValue = false;
  String _selectedRadio = 'option1';
  final TextEditingController _textController = TextEditingController();

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const _SectionHeader(
          title: 'Form Components',
          subtitle:
              'Interactive input and control elements for user interaction',
        ),
        const SizedBox(height: 24),

        // Buttons
        _ComponentShowcase(
          title: 'Buttons',
          description: 'All button variants with different sizes and states',
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Primary buttons
              const Text('Primary Buttons:',
                  style: TextStyle(fontWeight: FontWeight.w500)),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  ShadcnButton.primary(
                    text: 'Small',
                    size: ShadcnButtonSize.small,
                    onPressed: () {},
                  ),
                  ShadcnButton.primary(
                    text: 'Medium',
                    size: ShadcnButtonSize.medium,
                    onPressed: () {},
                  ),
                  ShadcnButton.primary(
                    text: 'Large',
                    size: ShadcnButtonSize.large,
                    onPressed: () {},
                  ),
                  ShadcnButton.primary(
                    text: 'Disabled',
                    onPressed: null,
                  ),
                  ShadcnButton.primary(
                    text: 'Loading',
                    isLoading: true,
                    onPressed: () {},
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Secondary buttons
              const Text('Secondary Buttons:',
                  style: TextStyle(fontWeight: FontWeight.w500)),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  ShadcnButton.secondary(
                    text: 'Secondary',
                    onPressed: () {},
                  ),
                  ShadcnButton.destructive(
                    text: 'Destructive',
                    onPressed: () {},
                  ),
                  ShadcnButton.outline(
                    text: 'Outline',
                    onPressed: () {},
                  ),
                  ShadcnButton.ghost(
                    text: 'Ghost',
                    onPressed: () {},
                  ),
                  ShadcnButton.link(
                    text: 'Link',
                    onPressed: () {},
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Buttons with icons
              const Text('With Icons:',
                  style: TextStyle(fontWeight: FontWeight.w500)),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  ShadcnButton.primary(
                    text: 'Save',
                    leading: const Icon(Icons.save),
                    onPressed: () {},
                  ),
                  ShadcnButton.secondary(
                    text: 'Edit',
                    trailing: const Icon(Icons.edit),
                    onPressed: () {},
                  ),
                  ShadcnButton.destructive(
                    text: 'Delete',
                    leading: const Icon(Icons.delete),
                    onPressed: () {},
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 32),

        // Inputs
        _ComponentShowcase(
          title: 'Input Fields',
          description: 'Text input components with different sizes and states',
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Basic inputs
              const Text('Basic Inputs:',
                  style: TextStyle(fontWeight: FontWeight.w500)),
              const SizedBox(height: 8),
              Column(
                children: [
                  ShadcnInput(
                    placeholder: 'Enter your name',
                    size: ShadcnInputSize.small,
                    controller: _textController,
                  ),
                  const SizedBox(height: 12),
                  ShadcnInput(
                    placeholder: 'Medium size input (default)',
                    size: ShadcnInputSize.medium,
                  ),
                  const SizedBox(height: 12),
                  ShadcnInput(
                    placeholder: 'Large size input',
                    size: ShadcnInputSize.large,
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Input with label and helper text
              const Text('With Labels and Helpers:',
                  style: TextStyle(fontWeight: FontWeight.w500)),
              const SizedBox(height: 8),
              Column(
                children: [
                  ShadcnInput(
                    label: 'Email Address',
                    placeholder: 'Enter your email',
                    helperText:
                        'We\'ll never share your email with anyone else.',
                    keyboardType: TextInputType.emailAddress,
                  ),
                  const SizedBox(height: 12),
                  ShadcnInput(
                    label: 'Password',
                    placeholder: 'Enter your password',
                    obscureText: true,
                    suffixIcon: const Icon(Icons.visibility_off),
                  ),
                  const SizedBox(height: 12),
                  ShadcnInput(
                    label: 'Error State',
                    placeholder: 'This field has an error',
                    errorText: 'This field is required',
                    initialValue: 'Invalid input',
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Disabled and read-only inputs
              const Text('States:',
                  style: TextStyle(fontWeight: FontWeight.w500)),
              const SizedBox(height: 8),
              Column(
                children: [
                  ShadcnInput(
                    placeholder: 'Disabled input',
                    enabled: false,
                  ),
                  const SizedBox(height: 12),
                  ShadcnInput(
                    initialValue: 'Read-only input',
                    readOnly: true,
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 32),

        // Checkboxes and Switches
        _ComponentShowcase(
          title: 'Toggle Controls',
          description: 'Checkboxes, switches, and radio buttons for selection',
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Checkboxes
              const Text('Checkboxes:',
                  style: TextStyle(fontWeight: FontWeight.w500)),
              const SizedBox(height: 8),
              Column(
                children: [
                  CheckboxListTile(
                    title: const Text('Enabled checkbox'),
                    value: _checkboxValue,
                    onChanged: (value) =>
                        setState(() => _checkboxValue = value ?? false),
                    controlAffinity: ListTileControlAffinity.leading,
                  ),
                  CheckboxListTile(
                    title: const Text('Checked checkbox'),
                    value: true,
                    onChanged: (value) {},
                    controlAffinity: ListTileControlAffinity.leading,
                  ),
                  CheckboxListTile(
                    title: const Text('Disabled checkbox'),
                    value: false,
                    onChanged: null,
                    controlAffinity: ListTileControlAffinity.leading,
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Switches
              const Text('Switches:',
                  style: TextStyle(fontWeight: FontWeight.w500)),
              const SizedBox(height: 8),
              Column(
                children: [
                  SwitchListTile(
                    title: const Text('Enable notifications'),
                    value: _switchValue,
                    onChanged: (value) => setState(() => _switchValue = value),
                  ),
                  SwitchListTile(
                    title: const Text('Always enabled'),
                    value: true,
                    onChanged: (value) {},
                  ),
                  SwitchListTile(
                    title: const Text('Disabled switch'),
                    value: false,
                    onChanged: null,
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Radio buttons
              const Text('Radio Buttons:',
                  style: TextStyle(fontWeight: FontWeight.w500)),
              const SizedBox(height: 8),
              Column(
                children: [
                  RadioListTile<String>(
                    title: const Text('Option 1'),
                    value: 'option1',
                    groupValue: _selectedRadio,
                    onChanged: (value) =>
                        setState(() => _selectedRadio = value ?? 'option1'),
                  ),
                  RadioListTile<String>(
                    title: const Text('Option 2'),
                    value: 'option2',
                    groupValue: _selectedRadio,
                    onChanged: (value) =>
                        setState(() => _selectedRadio = value ?? 'option1'),
                  ),
                  RadioListTile<String>(
                    title: const Text('Option 3 (Disabled)'),
                    value: 'option3',
                    groupValue: _selectedRadio,
                    onChanged: null,
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 32),

        // Sliders
        _ComponentShowcase(
          title: 'Sliders',
          description: 'Range input controls for selecting numeric values',
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Basic Slider:',
                  style: TextStyle(fontWeight: FontWeight.w500)),
              const SizedBox(height: 8),
              StatefulBuilder(
                builder: (context, setState) {
                  double sliderValue = 50;
                  return Column(
                    children: [
                      Text('Value: ${sliderValue.round()}'),
                      Slider(
                        value: sliderValue,
                        min: 0,
                        max: 100,
                        divisions: 100,
                        onChanged: (value) =>
                            setState(() => sliderValue = value),
                      ),
                    ],
                  );
                },
              ),
              const SizedBox(height: 16),
              const Text('Range Slider:',
                  style: TextStyle(fontWeight: FontWeight.w500)),
              const SizedBox(height: 8),
              StatefulBuilder(
                builder: (context, setState) {
                  RangeValues rangeValues = const RangeValues(20, 80);
                  return Column(
                    children: [
                      Text(
                          'Range: ${rangeValues.start.round()} - ${rangeValues.end.round()}'),
                      RangeSlider(
                        values: rangeValues,
                        min: 0,
                        max: 100,
                        divisions: 100,
                        onChanged: (values) =>
                            setState(() => rangeValues = values),
                      ),
                    ],
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// Layout Components Section - Components for organizing and structuring content
class _LayoutComponentsSection extends StatefulWidget {
  const _LayoutComponentsSection();

  @override
  State<_LayoutComponentsSection> createState() =>
      _LayoutComponentsSectionState();
}

class _LayoutComponentsSectionState extends State<_LayoutComponentsSection>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isAccordionExpanded = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Widget buildTab(BuildContext context) {
    // Tabs
    return const ShadcnTabs(
      tabs: [
        ShadcnTabData(
          id: 'account',
          label: 'Account',
          content: Padding(
            padding: EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Account Settings',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                    'Make changes to your account here. Click save when you\'re done.'),
                SizedBox(height: 16),
                TextField(
                  decoration: InputDecoration(
                    labelText: 'Name',
                    hintText: 'Enter your name',
                    border: OutlineInputBorder(),
                  ),
                ),
                SizedBox(height: 12),
                TextField(
                  decoration: InputDecoration(
                    labelText: 'Username',
                    hintText: 'Enter your username',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
          ),
        ),
        ShadcnTabData(
          id: 'password',
          label: 'Password',
          content: Padding(
            padding: EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  'Password Settings',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                    'Change your password here. After saving, you\'ll be logged out.'),
                SizedBox(height: 16),
                TextField(
                  obscureText: true,
                  decoration: InputDecoration(
                    labelText: 'Current password',
                    border: OutlineInputBorder(),
                  ),
                ),
                SizedBox(height: 12),
                TextField(
                  obscureText: true,
                  decoration: InputDecoration(
                    labelText: 'New password',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
          ),
        ),
        ShadcnTabData(
          id: 'settings',
          label: 'Settings',
          content: Padding(
            padding: EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  'General Settings',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8),
                Text('Configure your general preferences here.'),
                SizedBox(height: 16),
                SwitchListTile(
                  title: Text('Enable notifications'),
                  subtitle: Text('Receive notifications about updates'),
                  value: true,
                  onChanged: null,
                ),
                SwitchListTile(
                  title: Text('Auto-save changes'),
                  subtitle: Text('Automatically save changes as you type'),
                  value: false,
                  onChanged: null,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const _SectionHeader(
          title: 'Layout Components',
          subtitle: 'Components for organizing and structuring content',
        ),
        const SizedBox(height: 24),
        buildTab(context),
        const SizedBox(height: 24),
        // Cards
        _ComponentShowcase(
          title: 'Cards',
          description: 'Container components with elevation and borders',
          child: Column(
            children: [
              // Basic cards
              Row(
                children: [
                  const Expanded(
                    child: ShadcnCard(
                      child: Padding(
                        padding: EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            Text(
                              'Basic Card',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            SizedBox(height: 8),
                            Text(
                              'This is a basic card with some content. Cards are useful for grouping related information.',
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ShadcnCard(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            const Text(
                              'Card with Actions',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              'This card includes action buttons at the bottom.',
                            ),
                            const SizedBox(height: 16),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: <Widget>[
                                ShadcnButton.outline(
                                  text: 'Cancel',
                                  size: ShadcnButtonSize.small,
                                  onPressed: () {},
                                ),
                                const SizedBox(width: 8),
                                ShadcnButton.primary(
                                  text: 'Save',
                                  size: ShadcnButtonSize.small,
                                  onPressed: () {},
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 32),

        // Separators
        _ComponentShowcase(
          title: 'Separators',
          description: 'Dividers for visual separation of content',
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Horizontal Separators:',
                  style: TextStyle(fontWeight: FontWeight.w500)),
              const SizedBox(height: 16),
              const Text('Content above separator'),
              const SizedBox(height: 8),
              const ShadcnSeparator(),
              const SizedBox(height: 8),
              const Text('Content below separator'),
              const SizedBox(height: 24),
              const Text('Vertical Separators:',
                  style: TextStyle(fontWeight: FontWeight.w500)),
              const SizedBox(height: 16),
              SizedBox(
                height: 50,
                child: Row(
                  children: [
                    const Text('Left content'),
                    const SizedBox(width: 16),
                    Container(
                      height: 40,
                      width: 1,
                      color: Theme.of(context).dividerColor,
                    ),
                    const SizedBox(width: 16),
                    const Text('Right content'),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 32),

        // Accordion
        const _ComponentShowcase(
          title: 'Accordion',
          description: 'Collapsible content sections',
          child: Column(
            children: [
              ShadcnAccordion(
                items: [
                  ShadcnAccordionItem(
                    value: 'item1',
                    header: Text('What is Shadcn Flutter?'),
                    content: Text(
                        'Shadcn Flutter is a comprehensive component library inspired by shadcn/ui that provides 51 themed UI components with full Material Design integration.'),
                  ),
                  ShadcnAccordionItem(
                    value: 'item2',
                    header: Text('How do I customize themes?'),
                    content: Text(
                        'You can customize themes using the ThemeExtension pattern. Each component has its own theme extension that allows you to customize colors, sizes, and behavior.'),
                  ),
                  ShadcnAccordionItem(
                    value: 'item3',
                    header: Text('Is it compatible with Material Design?'),
                    content: Text(
                        'Yes! All components maintain full compatibility with Flutter\'s Material Design system while providing shadcn styling on top.'),
                  ),
                  ShadcnAccordionItem(
                    value: 'item4',
                    header: Text('Can I use it in production?'),
                    content: Text(
                        'Absolutely! The library is designed for production use with comprehensive testing, accessibility support, and performance optimization.'),
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 32),

        // Aspect Ratio
        _ComponentShowcase(
          title: 'Aspect Ratio',
          description: 'Maintain aspect ratios in layouts',
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('16:9 Aspect Ratio:',
                  style: TextStyle(fontWeight: FontWeight.w500)),
              const SizedBox(height: 8),
              ShadcnAspectRatio(
                aspectRatio: 16 / 9,
                child: Container(
                  decoration: BoxDecoration(
                    color:
                        Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.primary,
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: Text(
                      '16:9 Aspect Ratio Container',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              const Text('1:1 (Square) Aspect Ratio:',
                  style: TextStyle(fontWeight: FontWeight.w500)),
              const SizedBox(height: 8),
              SizedBox(
                width: 200,
                child: ShadcnAspectRatio(
                  aspectRatio: 1,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context)
                          .colorScheme
                          .secondary
                          .withOpacity(0.1),
                      border: Border.all(
                        color: Theme.of(context).colorScheme.secondary,
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        '1:1 Square',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.secondary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 32),

        // Scroll Area
        _ComponentShowcase(
          title: 'Scroll Area',
          description: 'Custom scrollable containers',
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Scrollable Content:',
                  style: TextStyle(fontWeight: FontWeight.w500)),
              const SizedBox(height: 8),
              SizedBox(
                height: 200,
                child: ShadcnScrollArea(
                  child: Column(
                    children: List.generate(
                      20,
                      (index) => ListTile(
                        leading: CircleAvatar(
                          child: Text('${index + 1}'),
                        ),
                        title: Text('Item ${index + 1}'),
                        subtitle: Text(
                            'This is the description for item ${index + 1}'),
                        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// Feedback Components Section - User feedback and notifications
class _FeedbackComponentsSection extends StatefulWidget {
  const _FeedbackComponentsSection();

  @override
  State<_FeedbackComponentsSection> createState() =>
      _FeedbackComponentsSectionState();
}

class _FeedbackComponentsSectionState
    extends State<_FeedbackComponentsSection> {
  double _progressValue = 0.7;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const _SectionHeader(
          title: 'Feedback Components',
          subtitle: 'User feedback, notifications, and progress indicators',
        ),
        const SizedBox(height: 24),

        // Alerts
        _ComponentShowcase(
          title: 'Alerts',
          description: 'Alert messages and notifications',
          child: Column(
            children: [
              ShadcnAlert(
                title: Text('Heads up!'),
                description: Text(
                    'You can add components and dependencies to your app using the cli.'),
                variant: ShadcnAlertVariant.defaultVariant,
              ),
              const SizedBox(height: 12),
              ShadcnAlert(
                title: Text('Error'),
                description:
                    Text('Your session has expired. Please log in again.'),
                variant: ShadcnAlertVariant.destructive,
              ),
            ],
          ),
        ),
        const SizedBox(height: 32),

        // Progress
        _ComponentShowcase(
          title: 'Progress',
          description: 'Progress indicators for loading states',
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Linear Progress:',
                  style: TextStyle(fontWeight: FontWeight.w500)),
              const SizedBox(height: 8),
              ShadcnProgress(value: _progressValue),
              const SizedBox(height: 8),
              Text('${(_progressValue * 100).round()}% complete'),
              const SizedBox(height: 16),
              Row(
                children: [
                  ShadcnButton.outline(
                    text: 'Reset',
                    size: ShadcnButtonSize.small,
                    onPressed: () => setState(() => _progressValue = 0),
                  ),
                  const SizedBox(width: 8),
                  ShadcnButton.primary(
                    text: 'Increase',
                    size: ShadcnButtonSize.small,
                    onPressed: () => setState(() => _progressValue =
                        (_progressValue + 0.1).clamp(0.0, 1.0)),
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 32),

        // Skeletons
        _ComponentShowcase(
          title: 'Skeleton',
          description: 'Skeleton loaders for loading states',
          child: Column(
            children: [
              Row(
                children: [
                  const ShadcnSkeleton.circle(size: 50),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ShadcnSkeleton.text(width: 200),
                        const SizedBox(height: 8),
                        ShadcnSkeleton.text(width: 150),
                        const SizedBox(height: 8),
                        ShadcnSkeleton.text(width: 100),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              const ShadcnSkeleton.rect(
                width: double.infinity,
                height: 200,
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// Navigation Components Section - Navigation and wayfinding
class _NavigationComponentsSection extends StatefulWidget {
  const _NavigationComponentsSection();

  @override
  State<_NavigationComponentsSection> createState() =>
      _NavigationComponentsSectionState();
}

class _NavigationComponentsSectionState
    extends State<_NavigationComponentsSection> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const _SectionHeader(
          title: 'Navigation Components',
          subtitle: 'Navigation menus and wayfinding elements',
        ),
        const SizedBox(height: 24),

        // Breadcrumbs
        _ComponentShowcase(
          title: 'Breadcrumbs',
          description: 'Breadcrumb navigation trails',
          child: ShadcnBreadcrumb(
            items: [
              ShadcnBreadcrumbItem(text: 'Home', onTap: () {}),
              ShadcnBreadcrumbItem(text: 'Components', onTap: () {}),
              ShadcnBreadcrumbItem(text: 'Navigation', onTap: () {}),
              ShadcnBreadcrumbItem(text: 'Breadcrumbs'),
            ],
          ),
        ),
        const SizedBox(height: 32),

        // Pagination
        _ComponentShowcase(
          title: 'Pagination',
          description: 'Page navigation controls',
          child: StatefulBuilder(
            builder: (context, setState) {
              int currentPage = 1;
              int totalPages = 10;

              return ShadcnPagination(
                currentPage: currentPage,
                totalPages: totalPages,
                onPageChanged: (page) => setState(() => currentPage = page),
                showFirstLast: true,
                showPreviousNext: true,
              );
            },
          ),
        ),
      ],
    );
  }
}

/// Overlay Components Section - Floating content above other elements
class _OverlayComponentsSection extends StatefulWidget {
  const _OverlayComponentsSection();

  @override
  State<_OverlayComponentsSection> createState() =>
      _OverlayComponentsSectionState();
}

class _OverlayComponentsSectionState extends State<_OverlayComponentsSection> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const _SectionHeader(
          title: 'Overlay Components',
          subtitle: 'Components that float above other content',
        ),
        const SizedBox(height: 24),

        // Tooltips
        _ComponentShowcase(
          title: 'Tooltips',
          description: 'Hover information tooltips',
          child: Wrap(
            spacing: 16,
            runSpacing: 16,
            children: [
              ShadcnTooltip(
                message: 'This is a tooltip',
                child: ShadcnButton.outline(
                  text: 'Hover me',
                  onPressed: () {},
                ),
              ),
              ShadcnTooltip(
                message:
                    'This tooltip has a long message that wraps to multiple lines to demonstrate text handling',
                child: ShadcnButton.secondary(
                  text: 'Long tooltip',
                  onPressed: () {},
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 32),

        // Popovers
        _ComponentShowcase(
          title: 'Popovers',
          description: 'Floating content containers',
          child: Wrap(
            spacing: 16,
            runSpacing: 16,
            children: [
              ShadcnPopover(
                content: const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Popover Title',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                          'This is the popover content. It can contain any widget.'),
                    ],
                  ),
                ),
                child: ShadcnButton.outline(
                  text: 'Open Popover',
                  onPressed: () {},
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// Data Display Components Section - Components for displaying data
class _DataDisplayComponentsSection extends StatefulWidget {
  const _DataDisplayComponentsSection();

  @override
  State<_DataDisplayComponentsSection> createState() =>
      _DataDisplayComponentsSectionState();
}

class _DataDisplayComponentsSectionState
    extends State<_DataDisplayComponentsSection> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const _SectionHeader(
          title: 'Data Display Components',
          subtitle: 'Components for displaying and organizing data',
        ),
        const SizedBox(height: 24),

        // Badges
        _ComponentShowcase(
          title: 'Badges',
          description: 'Labels and status indicators',
          child: Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              ShadcnBadge(
                text: 'Default',
                variant: ShadcnBadgeVariant.defaultVariant,
              ),
              ShadcnBadge(
                text: 'Secondary',
                variant: ShadcnBadgeVariant.secondary,
              ),
              ShadcnBadge(
                text: 'Destructive',
                variant: ShadcnBadgeVariant.destructive,
              ),
              ShadcnBadge(
                text: 'Outline',
                variant: ShadcnBadgeVariant.outline,
              ),
            ],
          ),
        ),
        const SizedBox(height: 32),

        // Avatars
        _ComponentShowcase(
          title: 'Avatars',
          description: 'User profile images and placeholders',
          child: Wrap(
            spacing: 16,
            runSpacing: 16,
            children: [
              const ShadcnAvatar(
                size: ShadcnAvatarSize.small,
                initials: 'S',
              ),
              const ShadcnAvatar(
                size: ShadcnAvatarSize.medium,
                initials: 'M',
              ),
              const ShadcnAvatar(
                size: ShadcnAvatarSize.large,
                initials: 'L',
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// Advanced Components Section - Specialized complex components
class _AdvancedComponentsSection extends StatefulWidget {
  const _AdvancedComponentsSection();

  @override
  State<_AdvancedComponentsSection> createState() =>
      _AdvancedComponentsSectionState();
}

class _AdvancedComponentsSectionState
    extends State<_AdvancedComponentsSection> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const _SectionHeader(
          title: 'Advanced Components',
          subtitle: 'Specialized components for complex interactions',
        ),
        const SizedBox(height: 24),

        // Calendar
        _ComponentShowcase(
          title: 'Calendar',
          description: 'Date picker calendars',
          child: ShadcnCalendar(
            selectedDate: DateTime.now(),
            onDateSelected: (date) {},
            minDate: DateTime.now().subtract(const Duration(days: 365)),
            maxDate: DateTime.now().add(const Duration(days: 365)),
          ),
        ),
        const SizedBox(height: 48),

        // Footer
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            border: Border.all(color: Theme.of(context).dividerColor),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              const Text(
                '🎉 Demo Complete!',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                'You\'ve seen the major components in the Shadcn Flutter library.',
                style: Theme.of(context).textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              const Text(
                'Each component is fully customizable, theme-aware, and maintains Material Design compatibility.',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                alignment: WrapAlignment.center,
                children: [
                  ShadcnButton.primary(
                    text: 'View Documentation',
                    leading: const Icon(Icons.book),
                    onPressed: () {},
                  ),
                  ShadcnButton.outline(
                    text: 'GitHub Repository',
                    leading: const Icon(Icons.code),
                    onPressed: () {},
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}
