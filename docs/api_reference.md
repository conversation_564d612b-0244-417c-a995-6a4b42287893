# API Reference

## Core Components

### ShadcnButton

A versatile button component with multiple variants and states.

```dart
ShadcnButton({
  Key? key,
  required VoidCallback? onPressed,
  required Widget child,
  ShadcnButtonVariant variant = ShadcnButtonVariant.primary,
  ShadcnButtonSize size = ShadcnButtonSize.medium,
  bool isLoading = false,
  Widget? icon,
  String? tooltip,
  String? semanticLabel,
})
```

**Variants:**
- `primary` - Primary action button with filled background
- `secondary` - Secondary action with subtle background
- `destructive` - Destructive actions with error coloring
- `outline` - Outlined button with transparent background
- `ghost` - Minimal button with no background
- `link` - Text-only button resembling a link

**Sizes:**
- `small` - Compact button (32px height)
- `medium` - Default button (40px height)
- `large` - Large button (48px height)

**Example:**
```dart
ShadcnButton(
  variant: ShadcnButtonVariant.primary,
  size: ShadcnButtonSize.large,
  onPressed: () => print('But<PERSON> pressed'),
  child: Text('Get Started'),
)
```

### ShadcnCard

A container component with elevation and customizable borders.

```dart
ShadcnCard({
  Key? key,
  required Widget child,
  ShadcnCardVariant variant = ShadcnCardVariant.elevated,
  EdgeInsets? padding,
  double? width,
  double? height,
  String? semanticLabel,
})
```

**Variants:**
- `elevated` - Card with shadow elevation
- `outlined` - Card with border and no elevation
- `filled` - Card with filled background

**Example:**
```dart
ShadcnCard(
  variant: ShadcnCardVariant.outlined,
  child: Padding(
    padding: EdgeInsets.all(16),
    child: Column(
      children: [
        Text('Card Title'),
        Text('Card content goes here'),
      ],
    ),
  ),
)
```

### ShadcnInput

A text input field with Material Design integration.

```dart
ShadcnInput({
  Key? key,
  String? label,
  String? placeholder,
  String? helperText,
  String? errorMessage,
  ValueChanged<String>? onChanged,
  TextEditingController? controller,
  TextInputType? keyboardType,
  bool obscureText = false,
  bool enabled = true,
  int? maxLength,
  int? maxLines = 1,
  Widget? prefixIcon,
  Widget? suffixIcon,
})
```

**Example:**
```dart
ShadcnInput(
  label: 'Email Address',
  placeholder: 'Enter your email',
  keyboardType: TextInputType.emailAddress,
  onChanged: (value) => handleEmailChange(value),
  prefixIcon: Icon(Icons.email),
)
```

### ShadcnCheckbox

A checkbox input with custom theming.

```dart
ShadcnCheckbox({
  Key? key,
  required bool value,
  required ValueChanged<bool?>? onChanged,
  String? label,
  bool tristate = false,
  String? semanticLabel,
})
```

**Example:**
```dart
ShadcnCheckbox(
  value: isAccepted,
  onChanged: (value) => setState(() => isAccepted = value ?? false),
  label: 'I accept the terms and conditions',
)
```

### ShadcnSwitch

A toggle switch for boolean values.

```dart
ShadcnSwitch({
  Key? key,
  required bool value,
  required ValueChanged<bool>? onChanged,
  String? label,
  bool enabled = true,
})
```

**Example:**
```dart
ShadcnSwitch(
  value: isDarkMode,
  onChanged: (value) => toggleTheme(value),
  label: 'Dark Mode',
)
```

## Layout Components

### ShadcnSeparator

A divider for visual separation.

```dart
ShadcnSeparator({
  Key? key,
  ShadcnSeparatorOrientation orientation = ShadcnSeparatorOrientation.horizontal,
  double? spacing,
  Color? color,
  double? thickness,
})
```

**Example:**
```dart
ShadcnSeparator(
  orientation: ShadcnSeparatorOrientation.horizontal,
  spacing: 16,
)
```

### ShadcnTabs

Tab navigation for organized content.

```dart
ShadcnTabs({
  Key? key,
  required List<ShadcnTabItem> tabs,
  int initialIndex = 0,
  ValueChanged<int>? onTabChanged,
  ShadcnTabsVariant variant = ShadcnTabsVariant.default,
})
```

**Example:**
```dart
ShadcnTabs(
  tabs: [
    ShadcnTabItem(
      label: 'Overview',
      content: OverviewWidget(),
    ),
    ShadcnTabItem(
      label: 'Details',
      content: DetailsWidget(),
    ),
  ],
)
```

## Feedback Components

### ShadcnAlert

Alert messages and notifications.

```dart
ShadcnAlert({
  Key? key,
  required Widget title,
  Widget? description,
  Widget? icon,
  ShadcnAlertVariant variant = ShadcnAlertVariant.default,
  List<Widget>? actions,
})
```

**Variants:**
- `default` - Informational alert
- `destructive` - Error or warning alert

**Example:**
```dart
ShadcnAlert(
  variant: ShadcnAlertVariant.destructive,
  title: Text('Error'),
  description: Text('Something went wrong. Please try again.'),
  icon: Icon(Icons.error),
)
```

### ShadcnProgress

Progress indicators for loading states.

```dart
ShadcnProgress({
  Key? key,
  double? value,
  Color? backgroundColor,
  Color? valueColor,
  double? strokeWidth,
  String? semanticLabel,
})
```

**Example:**
```dart
ShadcnProgress(
  value: 0.7, // 70% complete
  semanticLabel: 'Upload progress: 70%',
)
```

### ShadcnSkeleton

Skeleton loaders for loading states.

```dart
ShadcnSkeleton({
  Key? key,
  double? width,
  double? height,
  BorderRadius? borderRadius,
  Color? baseColor,
  Color? highlightColor,
})
```

**Example:**
```dart
Column(
  children: [
    ShadcnSkeleton(width: 200, height: 20),
    SizedBox(height: 8),
    ShadcnSkeleton(width: 150, height: 20),
  ],
)
```

## Data Display Components

### ShadcnBadge

Labels and status indicators.

```dart
ShadcnBadge({
  Key? key,
  required Widget child,
  ShadcnBadgeVariant variant = ShadcnBadgeVariant.default,
  Color? backgroundColor,
  Color? textColor,
})
```

**Variants:**
- `default` - Default badge styling
- `secondary` - Secondary badge with muted colors
- `destructive` - Error or warning badge
- `outline` - Outlined badge with transparent background

**Example:**
```dart
ShadcnBadge(
  variant: ShadcnBadgeVariant.destructive,
  child: Text('Error'),
)
```

### ShadcnAvatar

User profile images and placeholders.

```dart
ShadcnAvatar({
  Key? key,
  String? imageUrl,
  Widget? fallback,
  double radius = 20,
  String? semanticLabel,
})
```

**Example:**
```dart
ShadcnAvatar(
  imageUrl: 'https://example.com/avatar.jpg',
  fallback: Text('JD'),
  radius: 24,
  semanticLabel: 'John Doe profile picture',
)
```

## Theme System

### ShadcnThemeExtension

Base class for all shadcn theme extensions.

```dart
abstract class ShadcnThemeExtension<T extends ShadcnThemeExtension<T>> 
    extends ThemeExtension<T> {
  Color resolveColor(BuildContext context, Color? customColor, 
    Color Function(ThemeData) materialColorGetter, [Color fallbackColor]);
  
  TextStyle resolveTextStyle(BuildContext context, TextStyle? customStyle,
    TextStyle Function(TextTheme) materialStyleGetter, [TextStyle? fallbackStyle]);
  
  EdgeInsets resolveSpacing(BuildContext context, EdgeInsets? customSpacing,
    EdgeInsets defaultSpacing);
}
```

### ShadcnButtonTheme

Theme extension for button components.

```dart
class ShadcnButtonTheme extends ShadcnThemeExtension<ShadcnButtonTheme> {
  const ShadcnButtonTheme({
    this.primaryBackground,
    this.primaryForeground,
    this.secondaryBackground,
    this.secondaryForeground,
    this.destructiveBackground,
    this.destructiveForeground,
    this.outlineBorder,
    this.ghostHover,
    this.height,
    this.padding,
    this.borderRadius,
    this.textStyle,
  });

  static ShadcnButtonTheme defaultTheme(ColorScheme colorScheme) => 
    ShadcnButtonTheme(
      height: 40.0,
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      borderRadius: BorderRadius.circular(6),
      primaryBackground: colorScheme.primary,
      primaryForeground: colorScheme.onPrimary,
      // ... other defaults
    );
}
```

## Utilities

### ShadcnThemeResolver

Utility for safe theme resolution with fallbacks.

```dart
class ShadcnThemeResolver {
  static T resolveThemeExtension<T extends ThemeExtension<T>>(
    BuildContext context,
    T Function(ColorScheme) defaultFactory,
  );

  static Color resolveColor(
    BuildContext context,
    Color? customColor,
    Color Function(ThemeData) materialColorGetter,
    [Color fallbackColor = Colors.grey],
  );

  static TextStyle resolveTextStyle(
    BuildContext context,
    TextStyle? customStyle,
    TextStyle Function(TextTheme) materialStyleGetter,
    [TextStyle? fallbackStyle],
  );
}
```

### Test Helpers

Testing utilities for component testing.

```dart
// Test app wrapper
TestApp({
  required Widget child,
  ThemeData? theme,
  ThemeData? darkTheme,
  ThemeMode themeMode = ThemeMode.light,
})

// Create test theme
ThemeData createTestTheme({
  Brightness brightness = Brightness.light,
  List<ThemeExtension> extensions = const [],
})
```

## Migration Patterns

### From Material to Shadcn

**Button Migration:**
```dart
// Before
ElevatedButton(
  onPressed: () {},
  child: Text('Click me'),
)

// After
ShadcnButton(
  onPressed: () {},
  child: Text('Click me'),
)
```

**Card Migration:**
```dart
// Before
Card(
  child: Padding(
    padding: EdgeInsets.all(16),
    child: Text('Content'),
  ),
)

// After
ShadcnCard(
  child: Padding(
    padding: EdgeInsets.all(16),
    child: Text('Content'),
  ),
)
```

**Input Migration:**
```dart
// Before
TextField(
  decoration: InputDecoration(
    labelText: 'Email',
    hintText: 'Enter email',
  ),
)

// After
ShadcnInput(
  label: 'Email',
  placeholder: 'Enter email',
)
```

## Best Practices

### Theme Integration

Always extend your app's theme with shadcn extensions:

```dart
MaterialApp(
  theme: ThemeData(
    colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
    extensions: [
      ShadcnButtonTheme.defaultTheme(colorScheme),
      ShadcnCardTheme.defaultTheme(colorScheme),
      // Add other component themes
    ],
  ),
  home: MyApp(),
)
```

### Component Composition

Compose components for complex layouts:

```dart
ShadcnCard(
  child: Column(
    children: [
      ShadcnAlert(
        title: Text('Notice'),
        description: Text('Important information'),
      ),
      ShadcnSeparator(),
      Row(
        children: [
          ShadcnButton(
            variant: ShadcnButtonVariant.ghost,
            onPressed: onCancel,
            child: Text('Cancel'),
          ),
          ShadcnButton(
            onPressed: onConfirm,
            child: Text('Confirm'),
          ),
        ],
      ),
    ],
  ),
)
```

### Accessibility

Provide semantic information for accessibility:

```dart
ShadcnButton(
  onPressed: onDelete,
  semanticLabel: 'Delete selected item',
  child: Icon(Icons.delete),
)
```

### Testing

Use provided test helpers for consistent testing:

```dart
testWidgets('Button interaction', (tester) async {
  bool pressed = false;
  
  await tester.pumpWidget(
    TestApp(
      child: ShadcnButton(
        onPressed: () => pressed = true,
        child: Text('Test'),
      ),
    ),
  );
  
  await tester.tap(find.byType(ShadcnButton));
  expect(pressed, isTrue);
});
```