# Migration Guide: From Material Components to Shadcn Components

This guide provides detailed instructions for migrating from Flutter's built-in Material components to the shadcn component library. It includes component mappings, theme migration strategies, and common patterns.

## Overview

The shadcn Flutter library provides drop-in replacements for most Material components while offering enhanced theming capabilities and shadcn-style design. Migration is typically straightforward, with most components requiring only import changes and minor API adjustments.

## Quick Migration Checklist

- [ ] Add shadcn dependency to `pubspec.yaml`
- [ ] Update imports from `material.dart` to `shadcn.dart`
- [ ] Replace Material component names with Shadcn equivalents
- [ ] Update theme configuration with shadcn theme extensions
- [ ] Test theme switching and component interactions
- [ ] Update widget tests to use shadcn test helpers

## Installation

### 1. Add Dependency

```yaml
# pubspec.yaml
dependencies:
  flutter:
    sdk: flutter
  shadcn: ^1.0.0  # Use latest version
```

### 2. Import Library

```dart
// Before
import 'package:flutter/material.dart';

// After  
import 'package:flutter/material.dart';  // Still needed for MaterialApp, etc.
import 'package:shadcn/shadcn.dart';
```

## Component Migration Map

### Form Components

#### Button Migration

```dart
// Before - Material Button
ElevatedButton(
  onPressed: () => print('pressed'),
  child: Text('Submit'),
)

TextButton(
  onPressed: () => print('pressed'),
  child: Text('Cancel'),
)

OutlinedButton(
  onPressed: () => print('pressed'),
  child: Text('Outline'),
)

// After - Shadcn Button
ShadcnButton(
  onPressed: () => print('pressed'),
  child: Text('Submit'),
)

ShadcnButton(
  variant: ShadcnButtonVariant.ghost,
  onPressed: () => print('pressed'),
  child: Text('Cancel'),
)

ShadcnButton(
  variant: ShadcnButtonVariant.outline,
  onPressed: () => print('pressed'),
  child: Text('Outline'),
)
```

**Button Variant Mappings:**
- `ElevatedButton` → `ShadcnButton()` (default primary)
- `TextButton` → `ShadcnButton(variant: ShadcnButtonVariant.ghost)`
- `OutlinedButton` → `ShadcnButton(variant: ShadcnButtonVariant.outline)`
- Destructive actions → `ShadcnButton(variant: ShadcnButtonVariant.destructive)`

#### Input Field Migration

```dart
// Before - Material TextField
TextField(
  decoration: InputDecoration(
    labelText: 'Email',
    hintText: 'Enter your email',
    border: OutlineInputBorder(),
  ),
  onChanged: (value) => handleInput(value),
)

// After - Shadcn Input
ShadcnInput(
  label: 'Email',
  placeholder: 'Enter your email',
  onChanged: (value) => handleInput(value),
)
```

**Input Migration Notes:**
- `decoration.labelText` → `label`
- `decoration.hintText` → `placeholder`  
- `decoration.errorText` → `errorMessage`
- Border styling handled automatically by theme

#### Checkbox Migration

```dart
// Before - Material Checkbox
Checkbox(
  value: isChecked,
  onChanged: (value) => setState(() => isChecked = value ?? false),
)

// After - Shadcn Checkbox  
ShadcnCheckbox(
  value: isChecked,
  onChanged: (value) => setState(() => isChecked = value ?? false),
)
```

#### Switch Migration

```dart
// Before - Material Switch
Switch(
  value: isEnabled,
  onChanged: (value) => setState(() => isEnabled = value),
)

// After - Shadcn Switch
ShadcnSwitch(
  value: isEnabled,
  onChanged: (value) => setState(() => isEnabled = value),
)
```

### Layout Components

#### Card Migration

```dart
// Before - Material Card
Card(
  elevation: 2,
  child: Padding(
    padding: EdgeInsets.all(16),
    child: Column(
      children: [
        Text('Card Title'),
        Text('Card content goes here'),
      ],
    ),
  ),
)

// After - Shadcn Card
ShadcnCard(
  child: Padding(
    padding: EdgeInsets.all(16),
    child: Column(
      children: [
        Text('Card Title'),
        Text('Card content goes here'),
      ],
    ),
  ),
)
```

**Card Variant Mappings:**
- `Card()` → `ShadcnCard()` (default)
- `Card(shape: RoundedRectangleBorder(...))` → `ShadcnCard(variant: ShadcnCardVariant.outlined)`
- High elevation → `ShadcnCard(variant: ShadcnCardVariant.elevated)`

#### Divider Migration

```dart
// Before - Material Divider
Divider(thickness: 1)
Divider(height: 20, thickness: 1, indent: 16, endIndent: 16)

// After - Shadcn Separator  
ShadcnSeparator()
ShadcnSeparator(
  orientation: ShadcnSeparatorOrientation.horizontal,
  spacing: 20,
)
```

### Feedback Components

#### Dialog Migration

```dart
// Before - Material AlertDialog
showDialog(
  context: context,
  builder: (context) => AlertDialog(
    title: Text('Confirm Action'),
    content: Text('Are you sure you want to proceed?'),
    actions: [
      TextButton(
        onPressed: () => Navigator.pop(context),
        child: Text('Cancel'),
      ),
      ElevatedButton(
        onPressed: () => handleConfirm(),
        child: Text('Confirm'),
      ),
    ],
  ),
);

// After - Shadcn Dialog
showDialog(
  context: context,
  builder: (context) => ShadcnDialog(
    title: 'Confirm Action',
    description: 'Are you sure you want to proceed?',
    actions: [
      ShadcnButton(
        variant: ShadcnButtonVariant.ghost,
        onPressed: () => Navigator.pop(context),
        child: Text('Cancel'),
      ),
      ShadcnButton(
        onPressed: () => handleConfirm(),
        child: Text('Confirm'),
      ),
    ],
  ),
);
```

#### SnackBar to Toast Migration

```dart
// Before - Material SnackBar
ScaffoldMessenger.of(context).showSnackBar(
  SnackBar(
    content: Text('Operation completed successfully'),
    action: SnackBarAction(
      label: 'Undo',
      onPressed: handleUndo,
    ),
  ),
);

// After - Shadcn Toast
ShadcnToast.show(
  context,
  title: 'Success',
  description: 'Operation completed successfully',
  action: ShadcnButton(
    variant: ShadcnButtonVariant.ghost,
    onPressed: handleUndo,
    child: Text('Undo'),
  ),
);
```

#### Progress Indicator Migration

```dart
// Before - Material Progress
LinearProgressIndicator(value: 0.6)
CircularProgressIndicator(value: 0.6)

// After - Shadcn Progress
ShadcnProgress(value: 0.6)
ShadcnProgress(
  value: 0.6,
  variant: ShadcnProgressVariant.circular,
)
```

### Navigation Components

#### Tab Migration

```dart
// Before - Material TabBar
DefaultTabController(
  length: 3,
  child: Scaffold(
    appBar: AppBar(
      bottom: TabBar(
        tabs: [
          Tab(text: 'Tab 1'),
          Tab(text: 'Tab 2'), 
          Tab(text: 'Tab 3'),
        ],
      ),
    ),
    body: TabBarView(
      children: [
        Text('Content 1'),
        Text('Content 2'),
        Text('Content 3'),
      ],
    ),
  ),
)

// After - Shadcn Tabs
ShadcnTabs(
  tabs: [
    ShadcnTabItem(label: 'Tab 1', content: Text('Content 1')),
    ShadcnTabItem(label: 'Tab 2', content: Text('Content 2')),
    ShadcnTabItem(label: 'Tab 3', content: Text('Content 3')),
  ],
)
```

## Theme Migration

### Basic Theme Setup

```dart
// Before - Material Theme Only
MaterialApp(
  theme: ThemeData(
    colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
    useMaterial3: true,
  ),
  home: MyApp(),
)

// After - Shadcn Theme Integration  
MaterialApp(
  theme: ThemeData(
    colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
    useMaterial3: true,
    extensions: [
      // Shadcn theme extensions
      ShadcnButtonTheme.defaultTheme(
        ColorScheme.fromSeed(seedColor: Colors.blue),
      ),
      ShadcnCardTheme.defaultTheme(
        ColorScheme.fromSeed(seedColor: Colors.blue),
      ),
      ShadcnInputTheme.defaultTheme(
        ColorScheme.fromSeed(seedColor: Colors.blue),
      ),
      // Add other component themes as needed
    ],
  ),
  darkTheme: ThemeData(
    colorScheme: ColorScheme.fromSeed(
      seedColor: Colors.blue,
      brightness: Brightness.dark,
    ),
    extensions: [
      // Dark theme extensions
      ShadcnButtonTheme.defaultTheme(
        ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.dark,
        ),
      ),
      // ... other dark themes
    ],
  ),
  home: MyApp(),
)
```

### Advanced Theme Customization

```dart
// Create custom theme factory
ThemeData createCustomTheme(Brightness brightness) {
  final colorScheme = ColorScheme.fromSeed(
    seedColor: Colors.purple,
    brightness: brightness,
  );
  
  return ThemeData(
    colorScheme: colorScheme,
    extensions: [
      ShadcnButtonTheme(
        primaryBackground: colorScheme.primary,
        primaryForeground: colorScheme.onPrimary,
        height: 48, // Custom button height
        borderRadius: BorderRadius.circular(8), // Custom border radius
      ),
      ShadcnCardTheme(
        backgroundColor: colorScheme.surface,
        borderColor: colorScheme.outline,
        elevation: 4, // Custom elevation
      ),
      // Add other customized themes
    ],
  );
}

// Use in MaterialApp
MaterialApp(
  theme: createCustomTheme(Brightness.light),
  darkTheme: createCustomTheme(Brightness.dark),
  home: MyApp(),
)
```

## Component-Specific Migration

### Data Tables

```dart
// Before - Material DataTable
DataTable(
  columns: [
    DataColumn(label: Text('Name')),
    DataColumn(label: Text('Age')),
    DataColumn(label: Text('Actions')),
  ],
  rows: users.map((user) => DataRow(
    cells: [
      DataCell(Text(user.name)),
      DataCell(Text(user.age.toString())),
      DataCell(
        ElevatedButton(
          onPressed: () => editUser(user),
          child: Text('Edit'),
        ),
      ),
    ],
  )).toList(),
)

// After - Shadcn DataTable
ShadcnDataTable<User>(
  columns: [
    ShadcnDataColumn(label: 'Name', field: 'name'),
    ShadcnDataColumn(label: 'Age', field: 'age'),
    ShadcnDataColumn(label: 'Actions', field: 'actions'),
  ],
  data: users,
  buildCell: (user, columnField) {
    switch (columnField) {
      case 'name':
        return Text(user.name);
      case 'age':
        return Text(user.age.toString());
      case 'actions':
        return ShadcnButton(
          variant: ShadcnButtonVariant.ghost,
          onPressed: () => editUser(user),
          child: Text('Edit'),
        );
      default:
        return SizedBox();
    }
  },
)
```

### Form Validation

```dart
// Before - Material Form
Form(
  key: _formKey,
  child: Column(
    children: [
      TextFormField(
        decoration: InputDecoration(labelText: 'Email'),
        validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
      ),
      TextFormField(
        decoration: InputDecoration(labelText: 'Password'),
        obscureText: true,
        validator: (value) => value?.length ?? 0 < 6 ? 'Too short' : null,
      ),
      ElevatedButton(
        onPressed: () {
          if (_formKey.currentState?.validate() ?? false) {
            submitForm();
          }
        },
        child: Text('Submit'),
      ),
    ],
  ),
)

// After - Shadcn Form Pattern
Column(
  children: [
    ShadcnInput(
      label: 'Email',
      errorMessage: emailError,
      onChanged: (value) => validateEmail(value),
    ),
    ShadcnInput(
      label: 'Password',
      obscureText: true,
      errorMessage: passwordError,
      onChanged: (value) => validatePassword(value),
    ),
    ShadcnButton(
      onPressed: isFormValid ? submitForm : null,
      child: Text('Submit'),
    ),
  ],
)
```

## Testing Migration

### Widget Test Updates

```dart
// Before - Material Testing
testWidgets('Button tap test', (tester) async {
  bool tapped = false;
  
  await tester.pumpWidget(
    MaterialApp(
      home: ElevatedButton(
        onPressed: () => tapped = true,
        child: Text('Test'),
      ),
    ),
  );
  
  await tester.tap(find.byType(ElevatedButton));
  expect(tapped, isTrue);
});

// After - Shadcn Testing  
testWidgets('Button tap test', (tester) async {
  bool tapped = false;
  
  await tester.pumpWidget(
    TestApp(  // Use shadcn test helper
      child: ShadcnButton(
        onPressed: () => tapped = true,
        child: Text('Test'),
      ),
    ),
  );
  
  await tester.tap(find.byType(ShadcnButton));
  expect(tapped, isTrue);
});
```

### Theme Testing

```dart
testWidgets('Component respects theme', (tester) async {
  final customTheme = ThemeData(
    extensions: [
      ShadcnButtonTheme(
        primaryBackground: Colors.red,
        height: 60,
      ),
    ],
  );
  
  await tester.pumpWidget(
    TestApp(
      theme: customTheme,
      child: ShadcnButton(
        onPressed: () {},
        child: Text('Test'),
      ),
    ),
  );
  
  // Test theme application
  final button = tester.widget<ShadcnButton>(find.byType(ShadcnButton));
  // Add assertions for theme properties
});
```

## Common Migration Patterns

### Pattern 1: Replacing Button Groups

```dart
// Before
Row(
  children: [
    ElevatedButton(onPressed: onSave, child: Text('Save')),
    SizedBox(width: 8),
    TextButton(onPressed: onCancel, child: Text('Cancel')),
    SizedBox(width: 8),
    OutlinedButton(
      onPressed: onDelete,
      child: Text('Delete'),
      style: OutlinedButton.styleFrom(foregroundColor: Colors.red),
    ),
  ],
)

// After
Row(
  children: [
    ShadcnButton(onPressed: onSave, child: Text('Save')),
    SizedBox(width: 8),
    ShadcnButton(
      variant: ShadcnButtonVariant.ghost,
      onPressed: onCancel,
      child: Text('Cancel'),
    ),
    SizedBox(width: 8),
    ShadcnButton(
      variant: ShadcnButtonVariant.destructive,
      onPressed: onDelete,
      child: Text('Delete'),
    ),
  ],
)
```

### Pattern 2: Card with Actions

```dart
// Before
Card(
  child: Padding(
    padding: EdgeInsets.all(16),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Card Title', style: Theme.of(context).textTheme.titleLarge),
        SizedBox(height: 8),
        Text('Card content goes here'),
        SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(onPressed: onEdit, child: Text('Edit')),
            SizedBox(width: 8),
            ElevatedButton(onPressed: onSave, child: Text('Save')),
          ],
        ),
      ],
    ),
  ),
)

// After
ShadcnCard(
  child: Padding(
    padding: EdgeInsets.all(16),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Card Title', style: Theme.of(context).textTheme.titleLarge),
        SizedBox(height: 8),
        Text('Card content goes here'),
        SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            ShadcnButton(
              variant: ShadcnButtonVariant.ghost,
              onPressed: onEdit,
              child: Text('Edit'),
            ),
            SizedBox(width: 8),
            ShadcnButton(onPressed: onSave, child: Text('Save')),
          ],
        ),
      ],
    ),
  ),
)
```

## Troubleshooting

### Common Issues

#### Issue 1: Theme Not Applied

**Problem:** Components don't reflect custom theme.

**Solution:** Ensure theme extensions are properly added to MaterialApp:

```dart
MaterialApp(
  theme: ThemeData(
    extensions: [
      ShadcnButtonTheme.defaultTheme(colorScheme),
      // Don't forget other component themes
    ],
  ),
)
```

#### Issue 2: Performance Issues

**Problem:** Slow rendering with many components.

**Solution:** Use provided performance optimization patterns:

```dart
// Use const constructors where possible
const ShadcnButton(
  variant: ShadcnButtonVariant.secondary,
  child: Text('Static Button'),
)

// Cache theme resolutions in build methods
@override
Widget build(BuildContext context) {
  final buttonTheme = ShadcnThemeExtension.resolve<ShadcnButtonTheme>(
    context,
    ShadcnButtonTheme.defaultTheme,
  );
  
  return // ... use cached theme
}
```

#### Issue 3: Test Failures

**Problem:** Widget tests fail after migration.

**Solution:** Update test setup to use shadcn test helpers:

```dart
import 'package:shadcn/test_helpers.dart';

testWidgets('My test', (tester) async {
  await tester.pumpWidget(
    TestApp(  // Instead of MaterialApp
      child: MyWidget(),
    ),
  );
});
```

### Migration Checklist

- [ ] **Dependencies**: Added shadcn to pubspec.yaml
- [ ] **Imports**: Updated component imports
- [ ] **Components**: Replaced Material with Shadcn components
- [ ] **Themes**: Added theme extensions to MaterialApp
- [ ] **Variants**: Updated component variants and properties
- [ ] **Tests**: Updated widget tests with new component types
- [ ] **Performance**: Verified no performance regressions
- [ ] **Accessibility**: Verified accessibility features work
- [ ] **Dark Theme**: Tested light/dark theme switching
- [ ] **Custom Themes**: Applied and tested custom theme configurations

## Gradual Migration Strategy

For large applications, consider a gradual migration approach:

### Phase 1: Foundation
1. Add shadcn dependency
2. Set up basic theme configuration
3. Migrate core components (Button, Input, Card)

### Phase 2: Forms  
1. Migrate form components (Checkbox, Switch, Select)
2. Update form validation patterns
3. Test form functionality

### Phase 3: Layout
1. Migrate layout components (Tabs, Dialogs, Navigation)
2. Update navigation patterns
3. Test responsive behavior

### Phase 4: Advanced
1. Migrate data display components (Tables, Charts)
2. Add overlay components (Tooltips, Popovers)
3. Optimize performance

### Phase 5: Polish
1. Add advanced theming
2. Implement custom variants
3. Final testing and optimization

This gradual approach allows you to migrate incrementally while maintaining application stability.

## Getting Help

- **Documentation**: Check component-specific documentation
- **Examples**: Review example applications in the repository
- **Issues**: Report bugs or ask questions on GitHub
- **Community**: Join discussions for migration help

Remember that migration is an iterative process. Start with critical components and gradually expand coverage while testing thoroughly at each stage.