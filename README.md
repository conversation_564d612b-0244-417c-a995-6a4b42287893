# Flutter Shadcn Component Library

[![pub package](https://img.shields.io/pub/v/shadcn.svg)](https://pub.dev/packages/shadcn)
[![CI](https://github.com/your-org/flutter-shadcn/actions/workflows/ci.yml/badge.svg)](https://github.com/your-org/flutter-shadcn/actions/workflows/ci.yml)
[![codecov](https://codecov.io/gh/your-org/flutter-shadcn/branch/main/graph/badge.svg)](https://codecov.io/gh/your-org/flutter-shadcn)

A complete Flutter component library inspired by [shadcn/ui](https://ui.shadcn.com) that provides **51 themed UI components** with full Material Design integration. The library follows a theme-first design approach where all styling is derived from Flutter's theme system using ThemeExtension patterns.

## ✨ Features

- **🎨 51 Complete Components**: Full implementation of all shadcn/ui components for Flutter
- **🎯 Theme-First Design**: All styling derived from <PERSON>lutter's theme system - no hardcoded values
- **🤝 Material Design Integration**: Full compatibility with Material Design 3 theming
- **🌙 Dark Mode Support**: Automatic light/dark theme switching with proper color adaptation
- **⚡ Performance Optimized**: Efficient theme resolution using Flutter's InheritedWidget system
- **🎨 Fully Customizable**: Override individual component themes while maintaining consistency
- **♿ Accessibility**: Full screen reader and keyboard navigation support
- **📱 Platform Support**: Works on all Flutter platforms (iOS, Android, Web, Desktop)

## 📦 Installation

Add this package to your `pubspec.yaml`:

```yaml
dependencies:
  shadcn: ^1.0.0
```

Then run:

```bash
flutter pub get
```

## 🚀 Quick Start

### Basic Setup

```dart
import 'package:flutter/material.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final lightColorScheme = ColorScheme.fromSeed(
      seedColor: Colors.blue,
      brightness: Brightness.light,
    );
    
    final darkColorScheme = ColorScheme.fromSeed(
      seedColor: Colors.blue,
      brightness: Brightness.dark,
    );
    
    return MaterialApp(
      title: 'Shadcn Flutter Demo',
      theme: ThemeData(
        colorScheme: lightColorScheme,
        extensions: [
          ShadcnColorScheme.fromMaterial(lightColorScheme),
          ShadcnButtonTheme.defaultTheme(lightColorScheme),
          ShadcnCardTheme.defaultTheme(lightColorScheme),
          // Add other component themes as needed
        ],
      ),
      darkTheme: ThemeData(
        colorScheme: darkColorScheme,
        extensions: [
          ShadcnColorScheme.fromMaterial(darkColorScheme),
          ShadcnButtonTheme.defaultTheme(darkColorScheme),
          ShadcnCardTheme.defaultTheme(darkColorScheme),
          // Add other component themes as needed
        ],
      ),
      home: const MyHomePage(),
    );
  }
}
```

### Using Components

```dart
import 'package:shadcn/shadcn.dart';

class MyHomePage extends StatelessWidget {
  const MyHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Shadcn Components')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Button variants
            ShadcnButton(
              text: 'Primary Button',
              onPressed: () => print('Pressed!'),
              variant: ShadcnButtonVariant.primary,
            ),
            const SizedBox(height: 16),
            
            // Card component
            ShadcnCard(
              variant: ShadcnCardVariant.outlined,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    const Text('Card Title', 
                      style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    const Text('This is a shadcn card component with outline variant.'),
                    const SizedBox(height: 16),
                    ShadcnButton(
                      text: 'Card Action',
                      variant: ShadcnButtonVariant.secondary,
                      onPressed: () {},
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            
            // Input component
            const ShadcnInput(
              placeholder: 'Enter your name...',
              label: 'Name',
            ),
          ],
        ),
      ),
    );
  }
}
```

## 📋 Available Components (51 Total)

### Form Components
- **ShadcnButton** - Primary, secondary, destructive, outline, ghost, link variants
- **ShadcnInput** - Text input with validation and theming support  
- **ShadcnTextarea** - Multi-line text input
- **ShadcnCheckbox** - Checkbox with custom styling
- **ShadcnRadioGroup** - Radio button group
- **ShadcnSwitch** - Toggle switch component
- **ShadcnSelect** - Dropdown selection component
- **ShadcnCombobox** - Searchable selection component
- **ShadcnLabel** - Form field label
- **ShadcnInputOTP** - One-time password input

### Layout Components  
- **ShadcnCard** - Container with elevation and variants
- **ShadcnSeparator** - Horizontal and vertical dividers
- **ShadcnAspectRatio** - Maintain aspect ratios
- **ShadcnResizable** - Resizable panels with drag handles
- **ShadcnScrollArea** - Custom scrollable areas
- **ShadcnSheet** - Sliding overlay panels
- **ShadcnSidebar** - Navigation sidebar

### Navigation
- **ShadcnBreadcrumb** - Navigation breadcrumbs
- **ShadcnNavigationMenu** - Navigation menu with dropdowns
- **ShadcnPagination** - Page navigation controls
- **ShadcnTabs** - Tabbed content areas  
- **ShadcnMenubar** - Menu bar navigation

### Feedback & Overlays
- **ShadcnAlert** - Alert messages with variants
- **ShadcnAlertDialog** - Alert modal dialogs
- **ShadcnToast** - Toast notifications
- **ShadcnDialog** - Modal dialog component
- **ShadcnProgress** - Progress indicators
- **ShadcnSkeleton** - Loading placeholders
- **ShadcnPopover** - Floating content containers
- **ShadcnTooltip** - Hover information tooltips
- **ShadcnHoverCard** - Rich hover content
- **ShadcnContextMenu** - Right-click context menus
- **ShadcnCommand** - Command palette with search
- **ShadcnDropdownMenu** - Dropdown action menus

### Data Display
- **ShadcnTable** - Data tables with sorting
- **ShadcnDataTable** - Advanced data tables
- **ShadcnCalendar** - Date selection calendar
- **ShadcnDatePicker** - Date picker component  
- **ShadcnBadge** - Status badges and labels
- **ShadcnAvatar** - User avatar component

### Interactive
- **ShadcnAccordion** - Collapsible content sections
- **ShadcnCarousel** - Image/content carousels
- **ShadcnCollapsible** - Collapsible content areas
- **ShadcnSlider** - Range and value sliders
- **ShadcnToggle** - Toggle button component
- **ShadcnToggleGroup** - Toggle button groups

### Typography
- **ShadcnTypography** - Styled text components

## 🎨 Theme Customization

### Using Design Tokens

```dart
import 'package:shadcn/shadcn.dart';

// Use spacing tokens
Padding(
  padding: EdgeInsets.all(ShadcnTokens.spacing4), // 16px
  child: Container(
    height: ShadcnTokens.buttonHeightMd, // 40px
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusMd), // 6px
    ),
  ),
)

// Use color tokens
final shadcnColors = Theme.of(context).extension<ShadcnColorScheme>()!;
Container(
  color: shadcnColors.card,
  child: Text(
    'Themed content',
    style: TextStyle(color: shadcnColors.cardForeground),
  ),
)
```

### Custom Component Themes

Override individual component themes while maintaining consistency:

```dart
class MyCustomTheme {
  static ThemeData customTheme(ColorScheme colorScheme) {
    return ThemeData(
      colorScheme: colorScheme,
      extensions: [
        ShadcnColorScheme.fromMaterial(colorScheme),
        
        // Customize individual components
        ShadcnButtonTheme(
          height: 48.0, // Custom button height
          primaryBackground: Colors.purple,
          borderRadius: BorderRadius.circular(12),
        ),
        
        ShadcnCardTheme.defaultTheme(colorScheme).copyWith(
          backgroundColor: colorScheme.surfaceVariant,
          elevation: 8.0,
        ),
        
        // Other component themes...
      ],
    );
  }
}
```

### Component Variants

All components support multiple variants and states:

```dart
// Button variants
ShadcnButton(text: 'Primary', variant: ShadcnButtonVariant.primary)
ShadcnButton(text: 'Secondary', variant: ShadcnButtonVariant.secondary)  
ShadcnButton(text: 'Destructive', variant: ShadcnButtonVariant.destructive)
ShadcnButton(text: 'Outline', variant: ShadcnButtonVariant.outline)
ShadcnButton(text: 'Ghost', variant: ShadcnButtonVariant.ghost)
ShadcnButton(text: 'Link', variant: ShadcnButtonVariant.link)

// Size variants  
ShadcnButton(text: 'Small', size: ShadcnButtonSize.small)
ShadcnButton(text: 'Medium', size: ShadcnButtonSize.medium) // default
ShadcnButton(text: 'Large', size: ShadcnButtonSize.large)

// States
ShadcnButton(text: 'Disabled', disabled: true)
```

## 🏗️ Architecture  

The library follows a strict **theme-first architecture**:

### Core Principles
1. **🚫 No Hardcoded Values**: All styling derived from theme context
2. **🤝 Material Compatibility**: Extends rather than replaces Material theming  
3. **🔗 Fallback Chain**: Custom theme → Material theme → sensible defaults
4. **⚡ Performance**: Efficient InheritedWidget usage for theme propagation

### Theme System
```dart
// All components follow this pattern:
abstract class ShadcnComponent extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final componentTheme = theme.extension<ComponentTheme>() ?? 
                          ComponentTheme.defaultTheme(theme.colorScheme);
    
    return buildWithTheme(context, componentTheme);
  }
}
```

See [design.md](.kiro/specs/flutter-shadcn-components/design.md) for detailed architecture documentation.

## 🧪 Example App

The package includes a comprehensive example app demonstrating all components:

```bash
cd example
flutter run
```

The example showcases:
- All 51 components with different variants
- Light/dark theme switching  
- Custom theme implementation
- Interactive component playground
- Performance demonstrations

## 📚 Documentation

- **[API Documentation](https://pub.dev/documentation/shadcn/latest/)** - Complete API reference
- **[Requirements](/.kiro/specs/flutter-shadcn-components/requirements.md)** - Project requirements and specifications  
- **[Design Document](/.kiro/specs/flutter-shadcn-components/design.md)** - Architecture and design principles
- **[Migration Guide](#migration-guide)** - Migrating from Material components

### Migration Guide

Migrating from Material components to Shadcn is straightforward:

```dart
// Before (Material)
ElevatedButton(
  onPressed: () {},
  child: Text('Button'),
)

// After (Shadcn)  
ShadcnButton(
  text: 'Button',
  onPressed: () {},
  variant: ShadcnButtonVariant.primary,
)

// Before (Material)
Card(
  child: Text('Content'),
)

// After (Shadcn)
ShadcnCard(
  child: Text('Content'),
)
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

```bash
# Clone the repository
git clone https://github.com/your-org/flutter-shadcn.git
cd flutter-shadcn

# Install dependencies  
flutter pub get

# Run tests
flutter test

# Run example app
cd example && flutter run
```

### Project Structure

This project follows **spec-driven development**:
- **[requirements.md](/.kiro/specs/flutter-shadcn-components/requirements.md)** - Functional requirements
- **[design.md](/.kiro/specs/flutter-shadcn-components/design.md)** - Technical design
- **[tasks.md](/.kiro/specs/flutter-shadcn-components/tasks.md)** - Implementation roadmap

## 📝 Changelog

See [CHANGELOG.md](CHANGELOG.md) for a complete history of changes.

## 📄 License  

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Inspired by [shadcn/ui](https://ui.shadcn.com) for the design system
- Built with [Flutter](https://flutter.dev) framework
- Follows [Material Design 3](https://m3.material.io/) principles

---

**Made with ❤️ for the Flutter community**

[Report a Bug](https://github.com/your-org/flutter-shadcn/issues/new?template=bug_report.md) • [Request a Feature](https://github.com/your-org/flutter-shadcn/issues/new?template=feature_request.md) • [View Documentation](https://pub.dev/documentation/shadcn/latest/)
