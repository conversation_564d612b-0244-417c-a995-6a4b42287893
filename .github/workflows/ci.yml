name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    name: Test on Flutter ${{ matrix.flutter-version }}
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        flutter-version: ['3.0.x', '3.10.x', 'stable']
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ matrix.flutter-version }}
          channel: stable
          cache: true
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Verify formatting
        run: dart format --output=none --set-exit-if-changed .
        
      - name: Analyze code
        run: flutter analyze
        
      - name: Run tests
        run: flutter test --coverage
        
      - name: Upload coverage to Codecov
        if: matrix.flutter-version == 'stable'
        uses: codecov/codecov-action@v3
        with:
          file: coverage/lcov.info
          
  package-analysis:
    name: Package Analysis
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: stable
          channel: stable
          cache: true
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Package analysis
        run: |
          dart pub global activate pana
          pana --no-warning
          
      - name: Check pub publish
        run: dart pub publish --dry-run

  documentation:
    name: Documentation
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: stable
          channel: stable
          cache: true
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Generate documentation
        run: dart doc --validate-links