name: Publish to pub.dev

on:
  push:
    tags:
      - 'v*'

jobs:
  publish:
    name: Publish Package
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: stable
          channel: stable
          cache: true
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Verify formatting
        run: dart format --output=none --set-exit-if-changed .
        
      - name: Analyze code
        run: flutter analyze
        
      - name: Run tests
        run: flutter test
        
      - name: Package analysis
        run: |
          dart pub global activate pana
          pana --no-warning
          
      - name: Check pub publish
        run: dart pub publish --dry-run
        
      - name: Setup pub.dev credentials
        run: |
          mkdir -p ~/.pub-cache
          echo '${{ secrets.PUB_DEV_TOKEN }}' > ~/.pub-cache/credentials.json
          
      - name: Publish to pub.dev
        run: dart pub publish --force